package main

import (
	"encoding/json"
	"log"
	"net/http"
	"strings"
)

// APIServer API服务器
type APIServer struct {
	taskManager *TaskManager
	port        string
}

// NewAPIServer 创建新的API服务器
func NewAPIServer(taskManager *TaskManager, port string) *APIServer {
	return &APIServer{
		taskManager: taskManager,
		port:        port,
	}
}

// Start 启动API服务器
func (api *APIServer) Start() error {
	// 注册路由
	http.HandleFunc("/task", api.handleTask)
	http.HandleFunc("/task/stop", api.handleStopTask)
	http.HandleFunc("/tasks", api.handleGetTasks)

	// 新增期权系统相关API路由
	http.HandleFunc("/api/tasks", api.handleAPITasks)
	http.HandleFunc("/api/tasks/", api.handleAPITasksWithPath)

	log.Printf("API服务器启动，端口: %s", api.port)
	return http.ListenAndServe(":"+api.port, nil)
}

// handleTask 处理任务相关请求
func (api *APIServer) handleTask(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	switch r.Method {
	case "POST":
		api.handleCreateTask(w, r)
	case "GET":
		api.handleGetTask(w, r)
	default:
		api.sendErrorResponse(w, http.StatusMethodNotAllowed, "不支持的请求方法")
	}
}

// handleCreateTask 处理创建任务请求
func (api *APIServer) handleCreateTask(w http.ResponseWriter, r *http.Request) {
	var req TaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "请求参数解析失败: "+err.Error())
		return
	}

	// 创建任务
	task, err := api.taskManager.AddTask(&req)
	if err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "创建任务失败: "+err.Error())
		return
	}

	// 返回成功响应
	response := TaskResponse{
		TaskID:  task.ID,
		Status:  "success",
		Message: "任务创建成功",
	}

	api.sendSuccessResponse(w, response)
	log.Printf("API: 创建任务成功 - %s", task.ID)
}

// handleGetTask 处理获取单个任务请求
func (api *APIServer) handleGetTask(w http.ResponseWriter, r *http.Request) {
	// 从URL路径中提取任务ID
	path := strings.TrimPrefix(r.URL.Path, "/task/")
	if path == "" || path == r.URL.Path {
		api.sendErrorResponse(w, http.StatusBadRequest, "缺少任务ID")
		return
	}

	task, err := api.taskManager.GetTask(path)
	if err != nil {
		api.sendErrorResponse(w, http.StatusNotFound, err.Error())
		return
	}

	api.sendSuccessResponse(w, task)
}

// handleStopTask 处理停止任务请求
func (api *APIServer) handleStopTask(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		api.sendErrorResponse(w, http.StatusMethodNotAllowed, "不支持的请求方法")
		return
	}

	var req StopTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "请求参数解析失败: "+err.Error())
		return
	}

	// 停止任务
	err := api.taskManager.StopTask(req.TaskID)
	if err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "停止任务失败: "+err.Error())
		return
	}

	// 返回成功响应
	response := TaskResponse{
		TaskID:  req.TaskID,
		Status:  "success",
		Message: "任务停止成功",
	}

	api.sendSuccessResponse(w, response)
	log.Printf("API: 停止任务成功 - %s", req.TaskID)
}

// handleGetTasks 处理获取所有任务请求
func (api *APIServer) handleGetTasks(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "GET" {
		api.sendErrorResponse(w, http.StatusMethodNotAllowed, "不支持的请求方法")
		return
	}

	tasks := api.taskManager.GetAllTasks()
	api.sendSuccessResponse(w, tasks)
}

// sendSuccessResponse 发送成功响应
func (api *APIServer) sendSuccessResponse(w http.ResponseWriter, data interface{}) {
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(data); err != nil {
		log.Printf("API: 响应编码失败: %v", err)
	}
}

// sendErrorResponse 发送错误响应
func (api *APIServer) sendErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.WriteHeader(statusCode)
	response := map[string]interface{}{
		"status":  "error",
		"message": message,
	}
	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("API: 错误响应编码失败: %v", err)
	}
}

// handleAPITasks 处理期权系统的任务API请求
func (api *APIServer) handleAPITasks(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	switch r.Method {
	case "POST":
		api.handleCreateTaskForOptions(w, r)
	case "GET":
		api.handleGetTasks(w, r)
	default:
		api.sendErrorResponse(w, http.StatusMethodNotAllowed, "不支持的请求方法")
	}
}

// handleAPITasksWithPath 处理带路径的期权系统任务API请求
func (api *APIServer) handleAPITasksWithPath(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, PUT, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 解析路径
	path := strings.TrimPrefix(r.URL.Path, "/api/tasks/")
	pathParts := strings.Split(path, "/")

	if len(pathParts) >= 2 && pathParts[1] == "amount" {
		// 处理更新任务数量请求: PUT /api/tasks/{task_id}/amount
		if r.Method == "PUT" {
			api.handleUpdateTaskAmount(w, r, pathParts[0])
		} else {
			api.sendErrorResponse(w, http.StatusMethodNotAllowed, "不支持的请求方法")
		}
	} else if strings.HasPrefix(path, "by-option/") {
		// 处理根据期权合约查询任务请求: GET /api/tasks/by-option/{option_contract}
		if r.Method == "GET" {
			optionContract := strings.TrimPrefix(path, "by-option/")
			api.handleGetTaskByOption(w, r, optionContract)
		} else {
			api.sendErrorResponse(w, http.StatusMethodNotAllowed, "不支持的请求方法")
		}
	} else {
		// 处理获取单个任务请求: GET /api/tasks/{task_id}
		if r.Method == "GET" {
			api.handleGetTask(w, r)
		} else {
			api.sendErrorResponse(w, http.StatusMethodNotAllowed, "不支持的请求方法")
		}
	}
}

// handleCreateTaskForOptions 为期权系统处理创建任务请求
func (api *APIServer) handleCreateTaskForOptions(w http.ResponseWriter, r *http.Request) {
	var req TaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "请求参数解析失败: "+err.Error())
		return
	}

	// 创建任务
	task, err := api.taskManager.AddTask(&req)
	if err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "创建任务失败: "+err.Error())
		return
	}

	// 返回成功响应
	response := TaskResponse{
		TaskID:  task.ID,
		Status:  "success",
		Message: "任务创建成功",
	}

	api.sendSuccessResponse(w, response)
	log.Printf("API: 期权系统创建任务成功 - %s (来源: %s)", task.ID, task.Source)
}

// handleUpdateTaskAmount 处理更新任务数量请求
func (api *APIServer) handleUpdateTaskAmount(w http.ResponseWriter, r *http.Request, taskID string) {
	var req UpdateTaskAmountRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "请求参数解析失败: "+err.Error())
		return
	}

	// 更新任务数量
	response, err := api.taskManager.UpdateTaskAmount(taskID, req.Amount, req.UpdateType)
	if err != nil {
		api.sendErrorResponse(w, http.StatusBadRequest, "更新任务数量失败: "+err.Error())
		return
	}

	api.sendSuccessResponse(w, response)
	log.Printf("API: 任务数量更新成功 - %s (%.6f -> %.6f)", taskID, response.OldAmount, response.NewAmount)
}

// handleGetTaskByOption 处理根据期权合约查询任务请求
func (api *APIServer) handleGetTaskByOption(w http.ResponseWriter, r *http.Request, optionContract string) {
	response, err := api.taskManager.GetTaskByOptionContract(optionContract)
	if err != nil {
		api.sendErrorResponse(w, http.StatusInternalServerError, "查询任务失败: "+err.Error())
		return
	}

	api.sendSuccessResponse(w, response)
	log.Printf("API: 期权合约查询完成 - %s (存在: %t)", optionContract, response.Exists)
}
