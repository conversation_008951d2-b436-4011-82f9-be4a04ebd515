# 动态对冲系统配置文件

# 币安API配置
binance:
  api_key: "cNnJC8hzGWl0Lho4qtL8lIVI2S0U3qvNYoDum9Q4gMIT7EVQDUJxPTqEVwZvbRyJ"
  secret_key: "1YlxoJ8O7mWb0EvfAG457yALdgMiyG4bNFnmc8PaudTRI9aPHAlHoHWoPyJA0yqP"

# 交易配置
trading:
  maker_fee_rate: 0.0002      # 挂单费率 0.02% (Maker)
  taker_fee_rate: 0.0004      # 吃单费率 0.04% (Taker)

# 服务器配置
server:
  port: 5879
  host: "localhost"

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  file: "hedge.log"
  max_size: 100  # MB
  max_backups: 3
  max_age: 28    # days

# 默认交易参数
defaults:
  slippage_tolerance: 0.0005  # 0.05%
  stop_slippage: 0.0005       # 0.05%
  market_timeout: 5           # 秒
  volume_threshold: 0.5       # 50%

# 风险控制
risk:
  max_tasks: 20               # 最大并发任务数
  max_position_size: 1.0      # 最大单个仓位大小
  daily_loss_limit: 1000.0    # 日损失限制 (USDT)

# 监控配置
monitoring:
  status_interval: 10         # 状态输出间隔 (秒)
  health_check_interval: 30   # 健康检查间隔 (秒)

# ⚠️  重要提醒 ⚠️
# 本配置文件已设置为主网模式
# 系统将直接连接币安主网进行实盘交易
# 请确保你完全理解风险并做好资金管理 