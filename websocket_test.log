2025-06-06 21:59:16,308 - INFO - 🚀 开始WebSocket智能下单综合测试
2025-06-06 21:59:16,308 - INFO - ============================================================
2025-06-06 21:59:16,308 - INFO - 📋 步骤1: 创建listenKey
2025-06-06 21:59:16,862 - ERROR - ❌ 创建listenKey失败: 401 - {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-06-06 21:59:16,863 - INFO - 
📋 步骤2: 测试市场数据WebSocket
2025-06-06 21:59:16,863 - INFO - 🔍 测试市场数据WebSocket连接...
2025-06-06 21:59:17,621 - INFO - ✅ 市场数据WebSocket连接成功: wss://stream.binancefuture.com/ws/ethusdt@depth5@100ms
2025-06-06 21:59:18,380 - INFO - 📊 盘口数据 1: 买一=2520.01, 卖一=2527.26
2025-06-06 21:59:19,521 - INFO - 📊 盘口数据 2: 买一=2520.0, 卖一=2527.26
2025-06-06 21:59:19,783 - INFO - 📊 盘口数据 3: 买一=2520.01, 卖一=2527.26
2025-06-06 21:59:19,783 - INFO - ✅ 市场数据WebSocket测试完成
2025-06-06 21:59:28,209 - INFO - 
📋 步骤3: 测试用户数据流WebSocket
2025-06-06 21:59:28,210 - INFO - 🔍 测试用户数据流WebSocket...
2025-06-06 21:59:28,210 - ERROR - ❌ 缺少listenKey，无法测试用户数据流
2025-06-06 21:59:28,210 - INFO - 
📋 步骤4: 测试交易WebSocket连接
2025-06-06 21:59:28,210 - INFO - 🔍 测试交易WebSocket连接...
2025-06-06 21:59:28,698 - INFO - ✅ 交易WebSocket连接成功: wss://testnet.binancefuture.com/ws-fapi/v1
2025-06-06 21:59:28,701 - INFO - 📤 发送ping请求: {'id': 'test_request_1749218368_1', 'method': 'ping'}
2025-06-06 21:59:28,801 - INFO - 📥 收到响应: {'id': 'test_request_1749218368_1', 'status': 200, 'result': {}, 'rateLimits': [{'rateLimitType': 'REQUEST_WEIGHT', 'interval': 'MINUTE', 'intervalNum': 1, 'limit': 6000, 'count': 7}]}
2025-06-06 21:59:28,801 - INFO - ✅ 交易WebSocket ping测试成功
2025-06-06 21:59:28,906 - INFO - 
📋 步骤5: 测试WebSocket下单
2025-06-06 21:59:28,907 - INFO - 🔍 测试WebSocket下单功能...
2025-06-06 21:59:29,265 - INFO - ✅ 交易WebSocket连接成功
2025-06-06 21:59:29,265 - INFO - 📤 发送下单请求:
2025-06-06 21:59:29,265 - INFO -    ID: test_request_1749218369_2
2025-06-06 21:59:29,265 - INFO -    Method: order.place
2025-06-06 21:59:29,265 - INFO -    Symbol: ETHUSDT
2025-06-06 21:59:29,265 - INFO -    Side: BUY
2025-06-06 21:59:29,265 - INFO -    Quantity: 0.01
2025-06-06 21:59:29,265 - INFO -    TimeInForce: GTX
2025-06-06 21:59:29,265 - INFO -    PriceMatch: QUEUE
2025-06-06 21:59:29,265 - INFO -    Signature: 73f33b9694f987c81c60...
2025-06-06 21:59:29,447 - INFO - 📥 收到下单响应: {'id': 'test_request_1749218369_2', 'status': 401, 'error': {'code': -2015, 'msg': 'Invalid API-key, IP, or permissions for action'}, 'rateLimits': [{'rateLimitType': 'REQUEST_WEIGHT', 'interval': 'MINUTE', 'intervalNum': 1, 'limit': -1, 'count': -1}]}
2025-06-06 21:59:29,448 - ERROR - ❌ 下单失败: {'code': -2015, 'msg': 'Invalid API-key, IP, or permissions for action'}
2025-06-06 21:59:29,448 - INFO - 
============================================================
2025-06-06 21:59:29,448 - INFO - 📊 测试结果汇总:
2025-06-06 21:59:29,449 - INFO - ============================================================
2025-06-06 21:59:29,449 - INFO - listen_key           : ❌ 失败
2025-06-06 21:59:29,449 - INFO - market_data          : ✅ 通过
2025-06-06 21:59:29,449 - INFO - user_data            : ❌ 失败
2025-06-06 21:59:29,449 - INFO - trading_connection   : ✅ 通过
2025-06-06 21:59:29,449 - INFO - order_place          : ❌ 失败
2025-06-06 21:59:29,449 - INFO - ============================================================
2025-06-06 21:59:29,449 - INFO - 总测试数: 5, 通过: 2, 失败: 3
2025-06-06 21:59:29,449 - WARNING - ⚠️ 有 3 个测试失败，需要检查配置
2025-06-06 22:03:18,466 - INFO - 🚀 开始WebSocket智能下单综合测试
2025-06-06 22:03:18,467 - INFO - ============================================================
2025-06-06 22:03:18,467 - INFO - 📋 步骤1: 创建listenKey
2025-06-06 22:03:18,952 - INFO - ✅ listenKey创建成功: tPDCj3UgBxk3ShAQ1gJaLc9bdPFtvTYtkLVZwxFXbo0Jt8hSmgKrZuEG6ovMW4PQ
2025-06-06 22:03:18,955 - INFO - 
📋 步骤2: 测试市场数据WebSocket
2025-06-06 22:03:18,955 - INFO - 🔍 测试市场数据WebSocket连接...
2025-06-06 22:03:19,690 - INFO - ✅ 市场数据WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth5@100ms
2025-06-06 22:03:19,691 - INFO - 📊 盘口数据 1: 买一=2495.99, 卖一=2496.0
2025-06-06 22:03:19,729 - INFO - 📊 盘口数据 2: 买一=2495.99, 卖一=2496.0
2025-06-06 22:03:19,730 - INFO - 📊 盘口数据 3: 买一=2495.99, 卖一=2496.0
2025-06-06 22:03:19,730 - INFO - ✅ 市场数据WebSocket测试完成
2025-06-06 22:03:29,627 - INFO - 
📋 步骤3: 测试用户数据流WebSocket
2025-06-06 22:03:29,627 - INFO - 🔍 测试用户数据流WebSocket...
2025-06-06 22:03:29,896 - INFO - ✅ 用户数据流WebSocket连接成功
2025-06-06 22:03:34,898 - INFO - ℹ️ 5秒内无用户数据事件（正常）
2025-06-06 22:03:34,900 - INFO - ✅ 用户数据流WebSocket测试完成
2025-06-06 22:03:42,827 - INFO - 
📋 步骤4: 测试交易WebSocket连接
2025-06-06 22:03:42,830 - INFO - 🔍 测试交易WebSocket连接...
2025-06-06 22:03:43,188 - INFO - ✅ 交易WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025-06-06 22:03:43,189 - INFO - 📤 发送ping请求: {'id': 'test_request_1749218623_1', 'method': 'ping'}
2025-06-06 22:03:43,294 - INFO - 📥 收到响应: {'id': 'test_request_1749218623_1', 'status': 200, 'result': {}, 'rateLimits': [{'rateLimitType': 'REQUEST_WEIGHT', 'interval': 'MINUTE', 'intervalNum': 1, 'limit': 2400, 'count': 6}]}
2025-06-06 22:03:43,295 - INFO - ✅ 交易WebSocket ping测试成功
2025-06-06 22:03:43,480 - INFO - 
📋 步骤5: 测试WebSocket下单
2025-06-06 22:03:43,480 - INFO - 🔍 测试WebSocket下单功能...
2025-06-06 22:03:43,720 - INFO - ✅ 交易WebSocket连接成功
2025-06-06 22:03:43,721 - INFO - 📤 发送下单请求:
2025-06-06 22:03:43,723 - INFO -    ID: test_request_1749218623_2
2025-06-06 22:03:43,723 - INFO -    Method: order.place
2025-06-06 22:03:43,723 - INFO -    Symbol: ETHUSDT
2025-06-06 22:03:43,723 - INFO -    Side: BUY
2025-06-06 22:03:43,724 - INFO -    Quantity: 0.01
2025-06-06 22:03:43,724 - INFO -    TimeInForce: GTX
2025-06-06 22:03:43,724 - INFO -    PriceMatch: QUEUE
2025-06-06 22:03:43,724 - INFO -    Signature: 976502c77e01a9ccea13...
2025-06-06 22:03:43,834 - INFO - 📥 收到下单响应: {'id': 'test_request_1749218623_2', 'status': 400, 'error': {'code': -1022, 'msg': 'Signature for this request is not valid.'}, 'rateLimits': [{'rateLimitType': 'REQUEST_WEIGHT', 'interval': 'MINUTE', 'intervalNum': 1, 'limit': -1, 'count': -1}]}
2025-06-06 22:03:43,835 - ERROR - ❌ 下单失败: {'code': -1022, 'msg': 'Signature for this request is not valid.'}
2025-06-06 22:03:43,835 - INFO - 
============================================================
2025-06-06 22:03:43,835 - INFO - 📊 测试结果汇总:
2025-06-06 22:03:43,835 - INFO - ============================================================
2025-06-06 22:03:43,835 - INFO - listen_key           : ✅ 通过
2025-06-06 22:03:43,836 - INFO - market_data          : ✅ 通过
2025-06-06 22:03:43,836 - INFO - user_data            : ✅ 通过
2025-06-06 22:03:43,836 - INFO - trading_connection   : ✅ 通过
2025-06-06 22:03:43,836 - INFO - order_place          : ❌ 失败
2025-06-06 22:03:43,836 - INFO - ============================================================
2025-06-06 22:03:43,836 - INFO - 总测试数: 5, 通过: 4, 失败: 1
2025-06-06 22:03:43,836 - WARNING - ⚠️ 有 1 个测试失败，需要检查配置
