# WebSocket智能下单测试程序

这是一套专门用于测试和诊断币安期货WebSocket API的Python程序，用于验证Go程序中智能订单系统的WebSocket实现。

## 📁 文件说明

### 核心测试程序
- `test_websocket_trading.py` - 完整的WebSocket智能下单功能测试
- `advanced_websocket_test.py` - 高级WebSocket诊断工具
- `config_example.py` - 配置文件示例

### 配置文件
- `config.py` - 实际配置文件（需要自己创建）
- `websocket_test.log` - 测试日志文件（自动生成）
- `websocket_diagnostic_report.json` - 诊断报告（自动生成）

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install websockets aiohttp
```

### 2. 配置API密钥
```bash
# 复制配置文件模板
cp config_example.py config.py

# 编辑配置文件，填入真实的API密钥
nano config.py
```

### 3. 运行基础诊断
```bash
# 运行高级诊断（不需要真实API密钥）
python advanced_websocket_test.py
```

### 4. 运行完整测试
```bash
# 运行完整的智能下单测试（需要真实API密钥）
python test_websocket_trading.py
```

## 🔧 测试程序功能

### `advanced_websocket_test.py` - 诊断工具

#### 测试项目：
1. **基础网络连接**
   - DNS解析测试
   - TCP连接测试
   - 多个币安端点测试

2. **WebSocket协议兼容性**
   - 不同压缩算法测试
   - 心跳机制测试
   - 数据接收测试

3. **并发连接处理**
   - 多个WebSocket同时连接
   - 连接稳定性测试
   - 资源管理测试

4. **API认证机制**
   - ping/pong测试
   - 签名验证测试
   - 错误响应测试

5. **错误场景处理**
   - 无效URL处理
   - 连接断开恢复
   - 无效数据处理

#### 使用场景：
- 诊断Go程序中的WebSocket连接问题
- 验证网络环境和防火墙设置
- 测试币安API的可用性

### `test_websocket_trading.py` - 功能测试

#### 测试项目：
1. **listenKey管理**
   - 创建用户数据流密钥
   - 验证API权限

2. **市场数据WebSocket**
   - 订单簿数据订阅
   - 实时数据接收

3. **用户数据流WebSocket**
   - 用户事件监听
   - 订单状态更新

4. **交易API WebSocket**
   - 连接建立和认证
   - ping/pong机制

5. **智能下单流程**
   - 下单请求构建
   - API签名生成
   - 响应处理

#### 使用场景：
- 验证完整的智能下单流程
- 测试API密钥权限
- 调试下单参数格式

## 📊 测试结果解读

### 成功指标
```
✅ 所有测试通过！WebSocket智能下单系统正常
```

### 常见问题及解决方案

#### 1. 网络连接失败
```
❌ fstream.binance.com TCP连接失败
```
**解决方案：**
- 检查网络连接
- 确认防火墙设置
- 尝试使用VPN

#### 2. API权限不足
```
❌ listenKey创建失败: 401 Unauthorized
```
**解决方案：**
- 检查API密钥是否正确
- 确认API密钥有期货交易权限
- 验证IP白名单设置

#### 3. WebSocket协议错误
```
❌ websocket: RSV2 set, RSV3 set
```
**解决方案：**
- 检查WebSocket库版本
- 尝试不同的连接参数
- 验证服务器端点

#### 4. 签名验证失败
```
❌ 下单失败: {"code": -1022, "msg": "Signature for this request is not valid"}
```
**解决方案：**
- 检查签名算法实现
- 验证时间戳格式
- 确认参数编码方式

## 🔍 Go程序问题诊断

### 对应Go程序中的问题

#### 1. WebSocket连接断开
**Go程序日志：**
```
websocket: close 1008 (policy violation): disconnected
```

**Python测试验证：**
```bash
python advanced_websocket_test.py
```
查看"API认证"测试结果，确认是否为认证问题。

#### 2. 协议错误
**Go程序日志：**
```
websocket: RSV2 set, RSV3 set
```

**Python测试验证：**
```bash
python advanced_websocket_test.py
```
查看"协议兼容"测试结果，确认WebSocket库兼容性。

#### 3. 下单失败
**Go程序现象：**
- 触发下单但无响应
- 超时后主动查询

**Python测试验证：**
```bash
python test_websocket_trading.py
```
查看完整的下单流程测试结果。

## 📝 测试报告

### 自动生成的报告文件

1. **websocket_test.log**
   - 详细的测试执行日志
   - 包含所有请求和响应

2. **websocket_diagnostic_report.json**
   - 结构化的测试结果
   - 可用于自动化分析

### 手动分析要点

1. **连接稳定性**
   - 连接建立成功率
   - 数据接收连续性
   - 重连机制效果

2. **API兼容性**
   - 请求格式正确性
   - 响应解析准确性
   - 错误处理完整性

3. **性能指标**
   - 连接建立时间
   - 数据传输延迟
   - 并发处理能力

## 🛠️ 自定义测试

### 修改测试参数
编辑配置文件中的测试参数：
```python
TEST_CONFIG = {
    "timeout": 10,        # 调整超时时间
    "max_retries": 3,     # 调整重试次数
    "test_mode": True,    # 是否为测试模式
}
```

### 添加自定义测试
在测试程序中添加新的测试方法：
```python
async def test_custom_scenario(self):
    """自定义测试场景"""
    # 实现你的测试逻辑
    pass
```

## ⚠️ 注意事项

### 安全提醒
- 使用测试网进行测试
- 不要在代码中硬编码API密钥
- 定期轮换API密钥

### 测试环境
- 建议在与Go程序相同的网络环境中测试
- 使用相同的操作系统和网络配置
- 注意时区和时间同步

### 资源管理
- 测试完成后及时关闭连接
- 避免创建过多并发连接
- 监控API调用频率限制

## 📞 技术支持

如果测试过程中遇到问题：

1. 查看详细日志文件
2. 对比Python和Go的实现差异
3. 参考币安官方API文档
4. 检查网络和系统环境

---

**版本：** 1.0  
**更新时间：** 2024年1月  
**兼容性：** Python 3.7+, 币安期货API v1
