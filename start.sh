#!/bin/bash

# 动态对冲系统启动脚本

echo "🚀 动态对冲系统启动脚本"
echo "========================"

# 检查是否已编译
if [ ! -f "./hedge-system" ]; then
    echo "📦 正在编译程序..."
    go build -o hedge-system *.go
    if [ $? -ne 0 ]; then
        echo "❌ 编译失败"
        exit 1
    fi
    echo "✅ 编译成功"
fi

# 显示菜单
echo ""
echo "请选择运行模式:"
echo "1. 本地测试模式 (--test)"
echo "2. API 服务模式 (--api)"
echo "3. 自定义端口 API 服务模式"
echo "4. 显示帮助信息"
echo "5. 退出"
echo ""

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "🧪 启动本地测试模式..."
        ./hedge-system --test
        ;;
    2)
        echo "🌐 启动 API 服务模式 (端口 5879)..."
        ./hedge-system --api
        ;;
    3)
        read -p "请输入端口号: " port
        echo "🌐 启动 API 服务模式 (端口 $port)..."
        ./hedge-system --api --port $port
        ;;
    4)
        echo "📖 显示帮助信息..."
        ./hedge-system
        ;;
    5)
        echo "👋 退出"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac 