#!/usr/bin/env python3
"""
币安API配置文件示例
请复制此文件为 config.py 并填入真实的API密钥
"""

# 币安API配置
BINANCE_CONFIG = {
    # REST API密钥（HMAC SHA256）- 现有密钥
    "rest_api_key": "cNnJC8hzGWl0Lho4qtL8lIVI2S0U3qvNYoDum9Q4gMIT7EVQDUJxPTqEVwZvbRyJ",
    "rest_secret_key": "1YlxoJ8O7mWb0EvfAG457yALdgMiyG4bNFnmc8PaudTRI9aPHAlHoHWoPyJA0yqP",

    # WebSocket API密钥（Ed25519）- 需要在币安创建新的Ed25519 API密钥
    "ws_api_key": "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso",  # 替换为Ed25519 API密钥

    # 向后兼容（用于现有测试）
    "api_key": "cNnJC8hzGWl0Lho4qtL8lIVI2S0U3qvNYoDum9Q4gMIT7EVQDUJxPTqEVwZvbRyJ",
    "secret_key": "1YlxoJ8O7mWb0EvfAG457yALdgMiyG4bNFnmc8PaudTRI9aPHAlHoHWoPyJA0yqP",

    # 是否使用测试网（建议测试时使用）
    "use_testnet": False,

    # 测试参数
    "test_symbol": "ETHUSDT",
    "test_quantity": "0.01",
    "test_side": "BUY",
}

# WebSocket端点配置
WEBSOCKET_ENDPOINTS = {
    # 主网端点
    "mainnet": {
        "market_data": "wss://fstream.binance.com/ws/",
        "trading_api": "wss://ws-fapi.binance.com/ws-fapi/v1",
        "rest_api": "https://fapi.binance.com"
    },
    
    # 测试网端点
    "testnet": {
        "market_data": "wss://stream.binancefuture.com/ws/",
        "trading_api": "wss://testnet.binancefuture.com/ws-fapi/v1",
        "rest_api": "https://testnet.binancefuture.com"
    }
}

# 测试配置
TEST_CONFIG = {
    # 测试超时时间（秒）
    "timeout": 10,
    
    # 重试次数
    "max_retries": 3,
    
    # 是否启用详细日志
    "verbose_logging": True,
    
    # 测试模式（True=不实际下单）
    "test_mode": False,
}

# 智能订单配置
SMART_ORDER_CONFIG = {
    # 订单超时时间（秒）
    "order_timeout": 3,
    
    # 最大重试次数
    "max_retries": 3,
    
    # 价格修改阈值（百分比）
    "modify_threshold": 0.0001,  # 0.01%
    
    # 最大修改次数
    "max_modify_count": 10,
    
    # 修改间隔（秒）
    "modify_interval": 1,
}

# 如何获取API密钥的说明
API_SETUP_GUIDE = """
🔧 币安API密钥设置指南:

1. 登录币安账户 (https://www.binance.com)

2. 进入API管理页面:
   账户 → API管理 → 创建API

3. 创建API密钥:
   - 输入API密钥标签（如：WebSocket测试）
   - 完成安全验证
   - 保存API Key和Secret Key

4. 设置API权限:
   ✅ 启用期货交易
   ✅ 启用现货和杠杆交易（如需要）
   ✅ 启用读取权限
   ❌ 禁用提现权限（安全考虑）

5. IP白名单（可选但推荐）:
   - 添加你的服务器IP地址
   - 提高安全性

6. 测试网设置（推荐用于测试）:
   - 访问: https://testnet.binancefuture.com
   - 使用测试网API密钥进行测试
   - 测试网不涉及真实资金

⚠️ 安全提醒:
- 永远不要分享你的Secret Key
- 定期轮换API密钥
- 使用最小权限原则
- 在生产环境中使用IP白名单
"""

if __name__ == "__main__":
    print(API_SETUP_GUIDE)
