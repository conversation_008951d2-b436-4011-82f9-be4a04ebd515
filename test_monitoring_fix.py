#!/usr/bin/env python3
"""
测试监控修复
验证用户数据流和REST API监控是否正常工作
"""

import asyncio
import aiohttp
import time
import base64
import json
import websockets
from cryptography.hazmat.primitives.asymmetric import ed25519

class MonitoringFixTester:
    def __init__(self):
        # 使用与主程序相同的API配置
        self.api_key = "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso"
        self.private_key_hex = "6744730860246040c6389827880a2d52e14ebce72d18eea45dda64b76415d5e8"
        self.rest_base = "https://fapi.binance.com"  # 使用正确的属性名
        
        # 转换私钥
        private_key_bytes = bytes.fromhex(self.private_key_hex)
        self.ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(private_key_bytes)
        
    def generate_ed25519_signature_rest(self, params: dict) -> str:
        """生成Ed25519签名（REST API格式 - 不排序）"""
        # 构建查询字符串（不排序，保持原始顺序）
        query_parts = []
        for key, value in params.items():
            if key != "signature":
                query_parts.append(f"{key}={value}")
        
        query_string = "&".join(query_parts)
        
        # 生成Ed25519签名
        signature_bytes = self.ed25519_private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        return signature_base64

    async def test_get_listen_key(self):
        """测试获取listenKey"""
        print("🔧 测试获取listenKey...")
        
        try:
            timestamp = int(time.time() * 1000)
            
            # 构建请求参数
            params = {
                'timestamp': timestamp
            }
            
            # 生成签名
            signature = self.generate_ed25519_signature_rest(params)
            params['signature'] = signature
            
            # 发送REST API请求
            url = f"{self.rest_base}/fapi/v1/listenKey"  # 使用正确的属性名
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            print(f"🔍 请求URL: {url}")
            print(f"🔍 请求参数: {params}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=params, headers=headers) as response:
                    print(f"🔍 响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        listen_key = data.get('listenKey')
                        print(f"✅ 获取listenKey成功: {listen_key[:20]}...")
                        return listen_key
                    else:
                        error_text = await response.text()
                        print(f"❌ 获取listenKey失败: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ 获取listenKey异常: {e}")
            return None

    async def test_user_data_stream_connection(self, listen_key):
        """测试用户数据流连接"""
        print(f"\n🔧 测试用户数据流连接...")
        
        try:
            # 连接用户数据流WebSocket
            user_stream_url = f"wss://fstream.binance.com/ws/{listen_key}"
            print(f"🔍 连接URL: {user_stream_url}")
            
            async with websockets.connect(user_stream_url) as user_ws:
                print("✅ 用户数据流WebSocket连接成功")
                
                # 监听5秒钟的消息
                start_time = time.time()
                message_count = 0
                
                while time.time() - start_time < 5:
                    try:
                        message = await asyncio.wait_for(user_ws.recv(), timeout=1.0)
                        data = json.loads(message)
                        message_count += 1
                        
                        event_type = data.get('e', 'unknown')
                        print(f"📥 消息#{message_count}: {event_type}")
                        
                        # 如果是ORDER_TRADE_UPDATE事件，显示详细信息
                        if event_type == 'ORDER_TRADE_UPDATE':
                            order_data = data.get('o', {})
                            order_id = order_data.get('i')
                            status = order_data.get('X')
                            print(f"   🔍 ORDER_TRADE_UPDATE: orderId={order_id}, status={status}")
                        
                    except asyncio.TimeoutError:
                        # 超时是正常的，继续循环
                        continue
                    except Exception as e:
                        print(f"❌ 消息处理异常: {e}")
                
                print(f"✅ 用户数据流测试完成，收到{message_count}条消息")
                return True
                
        except Exception as e:
            print(f"❌ 用户数据流连接异常: {e}")
            return False

    async def test_rest_api_query(self, order_id):
        """测试REST API查询"""
        print(f"\n🔧 测试REST API查询...")
        
        try:
            timestamp = int(time.time() * 1000)
            
            # 构建查询参数
            params = {
                'symbol': 'ETHUSDT',
                'orderId': order_id,
                'timestamp': timestamp
            }
            
            # 生成签名
            signature = self.generate_ed25519_signature_rest(params)
            params['signature'] = signature
            
            # 发送REST API请求
            url = f"{self.rest_base}/fapi/v1/order"  # 使用正确的属性名
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            print(f"🔍 查询订单: {order_id}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    print(f"🔍 响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        status = data.get('status')
                        executed_qty = data.get('executedQty', '0')
                        print(f"✅ 查询成功: 状态={status}, 成交={executed_qty}")
                        return data
                    else:
                        error_text = await response.text()
                        print(f"❌ 查询失败: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            return None

    async def run_monitoring_tests(self):
        """运行监控测试"""
        print("🔧 监控修复测试")
        print("="*60)
        
        # 测试1: 获取listenKey
        listen_key = await self.test_get_listen_key()
        if not listen_key:
            print("❌ listenKey测试失败，跳过用户数据流测试")
        else:
            # 测试2: 用户数据流连接
            await self.test_user_data_stream_connection(listen_key)
        
        # 测试3: REST API查询
        test_order_id = "8389765902078015020"  # 使用已知的订单ID
        await self.test_rest_api_query(test_order_id)
        
        print("\n🎉 监控修复测试完成!")

async def main():
    """主函数"""
    tester = MonitoringFixTester()
    await tester.run_monitoring_tests()

if __name__ == "__main__":
    try:
        import aiohttp
        import websockets
        from cryptography.hazmat.primitives.asymmetric import ed25519
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install aiohttp websockets cryptography")
        exit(1)
    
    asyncio.run(main())
