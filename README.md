# 期权动态对冲下单系统

一个智能的期权动态对冲交易系统，基于实时价格变动和预设阈值自动执行开仓和平仓操作。

## 主要功能

- 🚀 实时价格监控和动态对冲执行
- 🎯 精确的滑点控制和风险管理
- 📊 详细的交易统计和价格损耗分析
- ⚡ 支持多任务并发执行
- 🔧 交互式命令行控制
- 🌐 RESTful API服务模式
- 📋 状态输出控制和详细数据导出
- 💼 配置内嵌部署，无需外部配置文件
- 💾 本地数据持久化存储，程序重启后自动恢复任务状态
- 📝 完整的事件日志系统，记录所有交易和状态变化
- 🎛️ 批量任务控制，支持一键启动/停止所有任务
- 🗑️ 任务管理功能，支持安全删除任务及其所有记录

## 价格损耗计算逻辑

系统会记录每次交易的价格损耗（相对于理论最优价格的偏差），帮助评估交易效率：

### 计算公式
- **做多开仓**: 损耗 = 实际价 - 理论价 (实际买入价高于理论价为正损耗)
- **做多平仓**: 损耗 = 理论价 - 实际价 (实际卖出价低于理论价为正损耗)
- **做空开仓**: 损耗 = 理论价 - 实际价 (实际卖出价低于理论价为正损耗)
- **做空平仓**: 损耗 = 实际价 - 理论价 (实际买入价高于理论价为正损耗)

### 损耗解读
- **正值**: 表示不利的价格差异，增加了交易成本
- **负值**: 表示有利的价格差异，获得了价格优势
- **统计意义**: 帮助优化下单策略和评估市场流动性影响

### 示例
```
做多开仓: 理论价3500, 实际成交3501 → 损耗 = +1.00 USDT (不利)
做多平仓: 理论价3500, 实际成交3499 → 损耗 = +1.00 USDT (不利)
做空开仓: 理论价3500, 实际成交3499 → 损耗 = +1.00 USDT (不利)
做空平仓: 理论价3500, 实际成交3501 → 损耗 = +1.00 USDT (不利)
```

## 数据持久化功能

系统具备完整的数据持久化能力，确保程序重启后能够恢复所有任务状态和历史记录：

### 数据存储结构
- **`data/tasks.json`**: 存储所有任务的实时状态（任务参数、仓位信息、统计数据）
- **`data/events.json`**: 存储所有任务的历史事件记录（开仓、平仓、成交记录等）
- **`data/backup/`**: 自动备份目录，每次保存前会备份原数据文件

### 自动保存机制
- **实时触发**: 任务状态变化时（开仓、平仓、订单成交）自动保存
- **定时保存**: 默认每5分钟自动保存一次（可配置间隔）
- **退出保存**: 程序正常退出时强制保存所有数据

### 数据恢复功能
- **启动恢复**: 程序启动时自动加载本地数据文件
- **状态重建**: 根据保存的数据重建任务对象和统计信息
- **安全机制**: 恢复的任务默认为停止状态，需要手动重启

### 数据安全保障
- **备份保护**: 每次写入前自动备份原文件
- **JSON格式**: 使用标准JSON格式，便于查看和维护
- **错误处理**: 完善的异常处理，确保数据完整性

### 程序默认设置
- **状态输出**: 默认关闭（可使用status命令开启）
- **限价模式**: 默认开启POST_ONLY模式（仅限挂单成交）
- **自动保存**: 默认每5分钟自动保存一次
- **备份功能**: 默认关闭（可使用backup命令开启）
- **退出保护**: 程序退出时自动跳过平仓，保留仓位状态

## 依赖包安装

运行前需要安装Go依赖包：

```bash
# 初始化模块并安装依赖
go mod init hedge_main
go mod tidy

# 或手动安装主要依赖
go get github.com/adshao/go-binance/v2
go get github.com/shopspring/decimal
go get github.com/gorilla/websocket
go get github.com/gin-gonic/gin
go get github.com/atotto/clipboard
```

## 配置说明

系统使用内嵌配置，API密钥等配置已编译到程序中，无需外部配置文件。

### 默认配置参数

- **交易对**: ETHUSDT
- **滑点容忍度**: 1% (0.01)
- **停止滑点**: 1% (0.01)
- **市价单超时**: 5秒
- **盘口量阈值**: 50% (0.5)
- **限价单模式**: 允许立即成交 (false)

### 运行时参数调整

可以通过交互命令动态调整默认参数：

```bash
# 设置交易对
set symbol BTCUSDT

# 设置滑点容忍度（小数形式，如0.01表示1%）
set slippage 0.005

# 设置停止滑点
set stop_slippage 0.015

# 设置市价单超时时间（秒）
set timeout 10

# 设置盘口量阈值（小数形式，如0.3表示30%）
set volume 0.3

# 查看当前设置
show defaults
```

## 使用方法

### 交互模式（默认）

```bash
# 编译程序
go build -o hedge_main *.go

# 运行交互模式（默认模式，推荐用于实盘）
./hedge_main

# 查看帮助
./hedge_main -help
```

### API服务模式

```bash
# 启动API服务模式（在交互模式基础上额外开启API服务，默认端口5879）
./hedge_main --api

# 指定端口
./hedge_main --api --port 5879
```

### 模式说明

- **交互模式（默认）**: 支持命令行交互操作，可手动创建和管理对冲任务
- **API服务模式**: 在交互模式基础上额外提供HTTP API接口，支持程序化调用
- **API模式优势**: 既保留了所有交互命令的便利性，又提供了外部程序集成能力

## 交互命令

### 任务管理命令

```bash
# 添加做多对冲任务（当价格>目标价+阈值时开多仓，<目标价-阈值时平仓）
add long 3500 0.0005

# 添加做空对冲任务（当价格<目标价-阈值时开空仓，>目标价+阈值时平仓）
add short 3500 0.0005

# 使用完整参数创建任务
add long 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5

# 停止任务
stop task_1234567890

# 列出所有任务
list
```

### 状态控制命令

```bash
# 查看当前状态输出设置
status

# 启动状态输出（默认10秒间隔）
status on

# 启动状态输出并设置间隔（5-300秒）
status on 15

# 停止状态输出
status off
```

### 数据导出命令

```bash
# 导出详细状态报告到剪切板
export

# 或使用
detail
```

详细状态报告包含：
- 任务总览和统计汇总
- 每个任务的详细信息
- 完整的交易记录和价格损耗
- 运行时间和仓位状态

### 数据管理命令

```bash
# 手动保存数据到本地文件
save

# 设置自动保存间隔（分钟）
saveinterval 10

# 控制备份功能
backup on    # 启用备份
backup off   # 禁用备份
backup       # 查看当前状态
```

数据管理功能：
- 自动保存：任务状态变化时自动触发保存
- 定时保存：默认每5分钟自动保存一次（可配置）
- 手动保存：使用save命令立即保存
- 间隔设置：使用saveinterval命令调整自动保存频率
- 备份控制：可开启/关闭.bak备份文件生成（默认关闭）
- 启动恢复：程序启动时自动加载历史数据

### 事件查看命令

```bash
# 查看指定任务的所有事件日志
events <task_id>

# 示例
events 1234567
```

事件日志功能：
- 原始日志：完整保存程序的原始日志输出，过滤掉交互展示数据
- 任务专注：只显示任务执行相关的日志（触发条件、下单、成交等）
- 实时保存：事件发生时异步保存到日志文件，不影响主程序性能
- 按日分割：每天生成独立的日志文件，便于管理
- 快速查询：支持按任务ID快速查询相关事件
- 格式保持：保持原始的时间戳和日志格式，便于问题追踪
- 智能过滤：自动过滤掉list、export等命令的输出，只保留核心执行日志

### 批量控制命令

```bash
# 启动所有已停止的任务
startall

# 停止所有运行中的任务
stopall

# 停止所有任务但跳过平仓（保留仓位）
stopall --skip-close
```

批量控制功能：
- 一键启动：批量启动所有已停止的任务
- 一键停止：批量停止所有运行中的任务
- 跳过平仓：停止任务时可选择保留当前仓位
- 状态统计：显示操作影响的任务数量
- 退出保护：程序退出时自动跳过平仓，确保仓位安全

### 任务管理命令

```bash
# 删除指定任务及其所有记录
delete <task_id>

# 示例
delete 1234567
```

任务管理功能：
- 完全删除：删除任务的所有数据，包括任务配置、事件记录、统计信息
- 安全确认：需要输入'yes'确认删除，防止误操作
- 即时生效：删除后立即从list和export命令中消失
- 数据同步：自动触发数据保存，确保删除操作持久化

### 参数配置命令

```bash
# 设置默认参数
set symbol BTCUSDT
set slippage 0.005
set stop_slippage 0.01
set timeout 10
set volume 0.3

# 设置限价单模式（是否只允许挂单成交）
set post_only true   # 启用POST_ONLY模式，只允许挂单成交
set post_only false  # 允许立即成交（默认）

# 显示当前默认参数
show defaults
```

### 系统命令

```bash
# 显示帮助
help

# 退出程序
quit
# 或
exit
```

## 交易逻辑

### 做多对冲 (Long Hedge)
- **开仓条件**: 当前价格 > 目标价 + (目标价 × 阈值)
- **平仓条件**: 当前价格 < 目标价 - (目标价 × 阈值)

### 做空对冲 (Short Hedge)
- **开仓条件**: 当前价格 < 目标价 - (目标价 × 阈值)
- **平仓条件**: 当前价格 > 目标价 + (目标价 × 阈值)

### 滑点控制策略

1. **限价单优先**: 优先使用限价单以获得更好价格
2. **滑点检查**: 如果限价单价格偏离市价超过滑点阈值，转为市价单
3. **超时保护**: 限价单等待超时后自动转为市价单
4. **盘口深度**: 盘口深度不足时直接使用市价单

## Linux服务器部署

### 交叉编译

```bash
# 在本地编译Linux版本
GOOS=linux GOARCH=amd64 go build -o hedge_main_linux *.go
```

### 服务器运行

```bash
# 上传到服务器后
chmod +x hedge_main_linux

# 直接运行（支持SSH交互）
./hedge_main_linux

# 启动API服务模式
./hedge_main_linux --api

# 使用tmux保持会话（推荐）
tmux new-session -d -s hedge './hedge_main_linux'
tmux attach -t hedge  # 连接到会话
# Ctrl+B, D 退出会话但保持运行

# 使用screen保持会话
screen -S hedge ./hedge_main_linux
# Ctrl+A, D 退出会话但保持运行
# screen -r hedge 重新连接
```

## API接口

当使用API模式时，可以通过HTTP接口管理任务：

### 基础API接口

```bash
# 创建做多任务
curl -X POST http://localhost:5879/task \
  -H "Content-Type: application/json" \
  -d '{"direction":"long","target_price":3500,"stop_rate":0.0005,"amount":0.01,"symbol":"ETHUSDT"}'

# 获取所有任务
curl http://localhost:5879/tasks

# 获取单个任务
curl http://localhost:5879/task/{task_id}

# 停止任务
curl -X POST http://localhost:5879/task/stop \
  -H "Content-Type: application/json" \
  -d '{"task_id":"1234567"}'
```

### 期权系统专用API接口

为期权交易系统提供的专用API接口，支持来源标识和期权合约绑定：

```bash
# 创建对冲任务（期权系统）
curl -X POST http://localhost:5879/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "direction": "long",
    "target_price": 2500,
    "stop_rate": 0.0002,
    "amount": 0.1,
    "symbol": "ETHUSDT",
    "source": "options",
    "option_contract": "ETHUSDT-2JUN25-2500-C"
  }'

# 更新任务数量
curl -X PUT http://localhost:5879/api/tasks/{task_id}/amount \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 0.3,
    "update_type": "sell_open",
    "source": "options",
    "option_contract": "ETHUSDT-2JUN25-2500-C"
  }'

# 根据期权合约查询绑定的对冲任务
curl http://localhost:5879/api/tasks/by-option/ETHUSDT-2JUN25-2500-C
```

#### API响应格式

**创建任务响应**：
```json
{
  "task_id": "1234567",
  "status": "success",
  "message": "任务创建成功"
}
```

**更新任务数量响应**：
```json
{
  "success": true,
  "task_id": "1234567",
  "old_amount": 0.1,
  "new_amount": 0.3,
  "message": "任务数量更新成功"
}
```

**查询期权绑定任务响应**：
```json
{
  "success": true,
  "task_id": "1234567",
  "exists": true
}
```

#### 更新类型说明

- `sell_open`: 卖出开仓（期权成交）
- `buy_close`: 买入平仓（期权平仓）
- `exercise`: 期权行权

## 注意事项

⚠️ **风险提示**：
- 本系统仅用于教育和研究目的
- 实盘交易前请充分测试和验证策略
- 确保API密钥安全，不要在不安全的环境中运行
- 建议先使用小资金进行测试
- 监控网络连接稳定性，避免因断网导致的风险

💡 **使用建议**：
- 合理设置阈值，避免过于频繁的交易
- 定期检查滑点设置，根据市场情况调整
- 使用tmux/screen保持长期运行的稳定性
- 定期导出详细状态报告进行策略分析

## 技术特性

- **实时WebSocket**: 基于币安WebSocket获取实时价格数据
- **并发安全**: 支持多任务并发执行，线程安全设计
- **精确计算**: 使用decimal库确保价格计算精度
- **错误处理**: 完善的错误处理和重连机制
- **内存优化**: 高效的内存使用和资源管理
- **日志系统**: 详细的日志记录便于调试和监控
- **数据持久化**: JSON格式本地存储，自动备份和恢复机制
- **状态管理**: 完整的任务生命周期管理和状态追踪
- **事件记录**: 异步事件日志系统，不影响主程序性能
- **批量操作**: 支持批量任务控制和状态管理

## 版本更新记录

### v1.4.0 (2025-06-01)

#### 🚀 运行模式优化
- **默认交互模式**: 程序默认启动交互模式，无需指定参数
- **简化命令行**: 移除 `--test` 参数，交互模式成为默认运行方式
- **API模式增强**: API模式在保留交互功能基础上额外提供HTTP API服务
- **统一体验**: API模式下可使用所有交互命令，实现双重控制方式
- **向后兼容**: 保持原有API功能完全不变，只是启动方式更简洁

#### 📖 文档更新
- **帮助信息更新**: 更新程序内置帮助信息，反映新的运行模式
- **README重写**: 全面更新文档说明，优化使用指南和示例命令
- **部署指南**: 更新Linux服务器部署命令，简化启动流程

#### 🔧 用户体验提升
- **启动便利性**: 直接运行程序即可开始使用，降低学习成本
- **模式切换**: 通过单一参数即可在交互模式和API模式间切换
- **功能完整性**: 确保所有模式下功能完整性和一致性

### v1.3.0 (2025-05-31)

#### 🗑️ 任务删除功能
- **新增删除命令**: 支持 `delete <task_id>` 命令完全删除任务及其所有记录
- **安全确认机制**: 删除操作需要输入 'yes' 确认，防止误操作
- **数据完整性**: 删除任务时同时清理任务配置、事件记录、统计信息
- **即时生效**: 删除后立即从所有命令输出中移除
- **持久化同步**: 删除操作自动触发数据保存，确保更改持久化

#### 🔧 交互体验优化
- **修复输入冲突**: 解决删除确认时的输入流竞争问题
- **状态管理**: 优化删除确认的状态管理机制
- **错误处理**: 完善删除操作的错误处理和用户反馈

#### 📋 命令系统增强
- **命令别名**: delete 命令支持 `del`、`remove`、`rm` 别名
- **输入验证**: 增强任务ID存在性验证
- **用户体验**: 改进命令提示和帮助信息

#### 🔄 期权系统集成
- **新增API接口**: 为期权交易系统提供专用API接口
- **来源标识**: 支持区分手动创建和期权程序创建的任务
- **任务数量更新**: 支持动态更新对冲任务的数量
- **期权合约绑定**: 支持根据期权合约查询绑定的对冲任务
- **向后兼容**: 新API接口完全向后兼容，不影响现有功能

#### 📊 数据导出功能
- **文件导出**: 新增导出功能，支持将报告保存到 `export/` 目录
- **时间戳命名**: 导出文件使用时间戳命名，格式为 `hedge_report_YYYYMMDD_HHMMSS.txt`
- **完整报告**: 导出文件包含任务列表和详细状态报告
- **自动目录**: 自动创建 export 目录，无需手动创建

#### 🎛️ 批量控制功能
- **批量启动**: `startall` 命令一键启动所有已停止的任务
- **批量停止**: `stopall` 命令一键停止所有运行中的任务
- **跳过平仓**: `stopall --skip-close` 支持停止任务但保留仓位
- **操作统计**: 显示批量操作影响的任务数量
- **退出保护**: 程序退出时自动跳过平仓，确保仓位安全

#### 📝 事件日志系统
- **事件查看**: 新增 `events <task_id>` 命令查看任务专属事件日志
- **原始日志**: 完整保存程序的原始日志输出，过滤交互展示数据
- **异步保存**: 事件发生时异步保存到日志文件，不影响主程序性能
- **按日分割**: 每天生成独立的日志文件，便于管理和查询
- **智能过滤**: 自动过滤 list、export 等命令输出，只保留核心执行日志
- **格式保持**: 保持原始时间戳和日志格式，便于问题追踪

#### 💾 数据管理增强
- **保存间隔**: 新增 `saveinterval <分钟>` 命令设置自动保存频率
- **备份控制**: 新增 `backup [on|off]` 命令控制备份功能开关
- **手动保存**: `save` 命令支持立即手动保存所有数据
- **状态查询**: 各命令支持查询当前配置状态

#### 🔧 系统优化
- **默认设置**: 优化程序默认设置，状态输出默认关闭，备份功能默认关闭
- **内存管理**: 优化事件日志的内存使用和文件I/O性能
- **错误处理**: 增强各功能模块的错误处理和异常恢复能力
- **用户体验**: 改进命令提示信息和帮助文档

### v1.1.0 (2025-05-29)

#### 💾 数据持久化系统
- **本地存储**: 实现完整的JSON格式数据持久化
- **自动恢复**: 程序启动时自动加载历史任务和事件数据
- **实时保存**: 任务状态变化时自动触发数据保存
- **定时保存**: 默认每5分钟自动保存一次（可配置）
- **备份机制**: 每次保存前自动备份原数据文件

#### 📊 统计信息增强
- **POST_ONLY模式**: 新增限价单模式显示，支持仅挂单成交设置
- **任务ID优化**: 任务ID缩短至7位时间戳，便于识别和操作
- **盈亏统计**: 完善总盈亏和平均损耗的计算和显示
- **状态追踪**: 增强任务状态管理和生命周期追踪

#### 🔧 配置管理
- **内嵌配置**: 实现配置内嵌部署，无需外部配置文件
- **参数调整**: 支持运行时动态调整默认参数
- **POST_ONLY设置**: 新增限价单模式配置选项

### v1.0.0 (2025-05-28)

#### 🚀 核心功能
- **动态对冲**: 实现基于价格阈值的自动开仓平仓逻辑
- **实时监控**: WebSocket实时价格数据获取和处理
- **滑点控制**: 精确的滑点控制和风险管理机制
- **多任务支持**: 支持多个对冲任务并发执行

#### 📋 交互系统
- **命令行界面**: 完整的交互式命令行控制系统
- **状态输出**: 可配置的定时状态输出功能
- **数据导出**: 详细的交易统计和状态报告导出

#### 🌐 API服务
- **RESTful API**: 完整的HTTP API服务模式
- **任务管理**: 通过API创建、查询、停止任务
- **状态查询**: API方式获取任务状态和统计信息

#### 🔒 安全特性
- **错误处理**: 完善的错误处理和重连机制
- **并发安全**: 线程安全的多任务执行设计
- **精确计算**: 使用decimal库确保价格计算精度