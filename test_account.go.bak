package main

import (
	"context"
	"log"
	"os"
	"strings"

	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"
	"github.com/shopspring/decimal"
	"gopkg.in/yaml.v2"
)

// Config 配置结构（简化版）
type TestConfig struct {
	Binance struct {
		APIKey    string `yaml:"api_key"`
		SecretKey string `yaml:"secret_key"`
	} `yaml:"binance"`
}

func main() {
	log.Println("🧪 统一账户交易能力测试")

	// 读取配置文件
	data, err := os.ReadFile("config.yaml")
	if err != nil {
		log.Fatalf("读取配置文件失败: %v", err)
	}

	var config TestConfig
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		log.Fatalf("解析配置文件失败: %v", err)
	}

	apiKey := config.Binance.APIKey
	secretKey := config.Binance.SecretKey

	// 创建期货客户端
	futuresClient := binance.NewFuturesClient(apiKey, secretKey)

	// 创建现货客户端
	spotClient := binance.NewClient(apiKey, secretKey)

	log.Println(strings.Repeat("=", 50))

	// 测试1：期货交易所信息
	log.Println("📊 测试1: 期货交易所信息")
	exchangeInfo, err := futuresClient.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		log.Printf("❌ 期货交易所信息获取失败: %v", err)
	} else {
		log.Printf("✅ 期货交易所信息获取成功，交易对数量: %d", len(exchangeInfo.Symbols))
	}

	// 测试2：期货账户信息
	log.Println("\n📊 测试2: 期货账户信息")
	account, err := futuresClient.NewGetAccountService().Do(context.Background())
	if err != nil {
		log.Printf("❌ 期货账户信息获取失败: %v", err)
	} else {
		log.Printf("✅ 期货账户信息获取成功")
		log.Printf("   总余额: %s USDT", account.TotalWalletBalance)
		log.Printf("   可用余额: %s USDT", account.AvailableBalance)
	}

	// 测试3：期货余额信息
	log.Println("\n📊 测试3: 期货余额信息")
	balances, err := futuresClient.NewGetBalanceService().Do(context.Background())
	if err != nil {
		log.Printf("❌ 期货余额信息获取失败: %v", err)
	} else {
		log.Printf("✅ 期货余额信息获取成功")
		for _, balance := range balances {
			bal, _ := decimal.NewFromString(balance.Balance)
			if bal.GreaterThan(decimal.Zero) {
				log.Printf("   %s: %s", balance.Asset, balance.Balance)
			}
		}
	}

	// 测试4：期货仓位信息
	log.Println("\n📊 测试4: 期货仓位信息")
	positions, err := futuresClient.NewGetPositionRiskService().Do(context.Background())
	if err != nil {
		log.Printf("❌ 期货仓位信息获取失败: %v", err)
	} else {
		log.Printf("✅ 期货仓位信息获取成功，仓位数量: %d", len(positions))
		activePositions := 0
		for _, pos := range positions {
			positionAmt, _ := decimal.NewFromString(pos.PositionAmt)
			if !positionAmt.IsZero() {
				activePositions++
				log.Printf("   %s: 数量=%s, 标记价格=%s", pos.Symbol, pos.PositionAmt, pos.MarkPrice)
			}
		}
		log.Printf("   活跃仓位数量: %d", activePositions)
	}

	// 测试5：现货账户信息
	log.Println("\n📊 测试5: 现货账户信息")
	spotAccount, err := spotClient.NewGetAccountService().Do(context.Background())
	if err != nil {
		log.Printf("❌ 现货账户信息获取失败: %v", err)
	} else {
		log.Printf("✅ 现货账户信息获取成功")
		for _, balance := range spotAccount.Balances {
			free, _ := decimal.NewFromString(balance.Free)
			if free.GreaterThan(decimal.Zero) {
				log.Printf("   %s: %s", balance.Asset, balance.Free)
			}
		}
	}

	// 测试6：获取ETHUSDT价格
	log.Println("\n📊 测试6: 获取ETHUSDT价格")
	depth, err := futuresClient.NewDepthService().Symbol("ETHUSDT").Limit(5).Do(context.Background())
	if err != nil {
		log.Printf("❌ ETHUSDT价格获取失败: %v", err)
	} else {
		log.Printf("✅ ETHUSDT价格获取成功")
		log.Printf("   买一价: %s", depth.Bids[0].Price)
		log.Printf("   卖一价: %s", depth.Asks[0].Price)
	}

	// 测试7：尝试下单测试（极低价格，不会成交）
	log.Println("\n📊 测试7: 下单权限测试")
	if depth != nil && len(depth.Bids) > 0 {
		bidPrice, _ := decimal.NewFromString(depth.Bids[0].Price)
		testPrice := bidPrice.Mul(decimal.NewFromFloat(0.1)) // 市价的10%，绝对不会成交
		testQuantity := decimal.NewFromFloat(0.001)

		log.Printf("   测试价格: %s (市价的10%%)", testPrice.String())
		log.Printf("   测试数量: %s ETH", testQuantity.String())

		order, err := futuresClient.NewCreateOrderService().
			Symbol("ETHUSDT").
			Side(futures.SideTypeBuy).
			Type(futures.OrderTypeLimit).
			TimeInForce(futures.TimeInForceTypeGTC).
			Quantity(testQuantity.String()).
			Price(testPrice.String()).
			Do(context.Background())

		if err != nil {
			log.Printf("❌ 测试下单失败: %v", err)
		} else {
			log.Printf("✅ 测试下单成功! 订单ID: %d", order.OrderID)

			// 立即撤销
			log.Println("   🔄 撤销测试订单...")
			_, err = futuresClient.NewCancelOrderService().
				Symbol("ETHUSDT").
				OrderID(order.OrderID).
				Do(context.Background())

			if err != nil {
				log.Printf("   ❌ 撤销失败: %v", err)
			} else {
				log.Println("   ✅ 测试订单已撤销")
			}
		}
	}

	log.Println("\n" + strings.Repeat("=", 50))
	log.Println("🎯 测试完成！")
}
