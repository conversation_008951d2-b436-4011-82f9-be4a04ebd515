#!/usr/bin/env python3
"""
将PEM格式的Ed25519私钥转换为Hex格式
"""

import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ed25519

def convert_pem_to_hex():
    # PEM格式的私钥
    pem_private_key = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIGdEcwhgJGBAxjiYJ4gKLVLhTrznLRjupF3aZLdkFdXo
-----END PRIVATE KEY-----"""

    # 加载私钥
    private_key = serialization.load_pem_private_key(
        pem_private_key.encode('utf-8'),
        password=None
    )
    
    # 获取原始私钥字节
    private_key_bytes = private_key.private_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PrivateFormat.Raw,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    # 转换为Hex格式
    private_key_hex = private_key_bytes.hex()
    
    # 获取公钥用于验证
    public_key = private_key.public_key()
    public_key_bytes = public_key.public_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PublicFormat.Raw
    )
    public_key_hex = public_key_bytes.hex()
    public_key_base64 = base64.b64encode(public_key_bytes).decode('utf-8')
    
    print("🔑 私钥转换结果:")
    print("=" * 60)
    print(f"PEM格式私钥:")
    print(pem_private_key)
    print()
    print(f"Hex格式私钥 (用于Go程序): {private_key_hex}")
    print()
    print(f"对应的公钥 (Hex): {public_key_hex}")
    print(f"对应的公钥 (Base64): {public_key_base64}")
    print()
    print("✅ 转换完成！")
    
    return private_key_hex

if __name__ == "__main__":
    convert_pem_to_hex()
