# 币安动态对冲系统配置文件
# 请复制此文件为 config.yaml 并填入你的API密钥

binance:
  # 币安API密钥 (必填)
  api_key: "你的API密钥"
  
  # 币安密钥 (必填)
  secret_key: "你的密钥"

# 服务器配置
server:
  # HTTP API服务端口
  port: 5879

# 日志配置
logging:
  # 日志级别: debug, info, warn, error
  level: "info"

# ⚠️  重要提醒 ⚠️
# 本系统直接连接币安主网进行实盘交易
# 请确保：
# 1. 你完全理解系统的工作原理
# 2. 已经充分测试过所有功能
# 3. 设置了合理的风险控制参数
# 4. 账户中只有你愿意承担风险的资金

# 使用说明:
# 1. 将此文件复制为 config.yaml
# 2. 替换 api_key 和 secret_key 为你的真实密钥
# 3. 运行程序: ./hedge-system --test 