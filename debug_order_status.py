#!/usr/bin/env python3
"""
调试订单状态工具
用于手动查询币安期货订单状态
"""

import asyncio
import aiohttp
import time
import base64
import json
from cryptography.hazmat.primitives.asymmetric import ed25519

class OrderStatusDebugger:
    def __init__(self):
        # 使用与主程序相同的API配置
        self.api_key = "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso"
        self.private_key_hex = "6744730860246040c6389827880a2d52e14ebce72d18eea45dda64b76415d5e8"
        self.base_url = "https://fapi.binance.com"
        
        # 转换私钥
        private_key_bytes = bytes.fromhex(self.private_key_hex)
        self.private_key = ed25519.Ed25519PrivateKey.from_private_bytes(private_key_bytes)
        
    def generate_ed25519_signature_rest(self, params: dict) -> str:
        """生成Ed25519签名（REST API格式 - 不排序）"""
        # 构建查询字符串（不排序，保持原始顺序）
        query_parts = []
        for key, value in params.items():
            if key != "signature":
                query_parts.append(f"{key}={value}")
        
        query_string = "&".join(query_parts)
        print(f"🔍 签名字符串: {query_string}")
        
        # 生成Ed25519签名
        signature_bytes = self.private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        print(f"🔍 Ed25519签名: {signature_base64}")
        return signature_base64

    async def query_order_status(self, order_id):
        """查询订单状态"""
        try:
            timestamp = int(time.time() * 1000)
            
            # 构建查询参数
            params = {
                'symbol': 'ETHUSDT',
                'orderId': order_id,
                'timestamp': timestamp
            }
            
            # 生成签名
            signature = self.generate_ed25519_signature_rest(params)
            params['signature'] = signature
            
            # 发送REST API请求
            url = f"{self.base_url}/fapi/v1/order"
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            print(f"🔍 请求URL: {url}")
            print(f"🔍 请求参数: {params}")
            print(f"🔍 请求头: {headers}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    print(f"🔍 响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 查询成功!")
                        return data
                    else:
                        error_text = await response.text()
                        print(f"❌ 查询失败: {response.status}")
                        print(f"❌ 错误信息: {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            return None

    async def debug_order(self, order_id):
        """调试订单状态"""
        print(f"\n🔧 调试订单状态")
        print(f"订单ID: {order_id}")
        print("="*60)
        
        # 查询订单状态
        order_info = await self.query_order_status(order_id)
        
        if order_info:
            print("\n📊 订单详细信息:")
            print("="*60)
            for key, value in order_info.items():
                print(f"   {key}: {value}")
            
            # 特别关注关键字段
            status = order_info.get('status')
            executed_qty = order_info.get('executedQty', '0')
            orig_qty = order_info.get('origQty', '0')
            avg_price = order_info.get('avgPrice', '0')
            
            print(f"\n🎯 关键状态:")
            print("="*60)
            print(f"   - 订单状态: {status}")
            print(f"   - 原始数量: {orig_qty}")
            print(f"   - 成交数量: {executed_qty}")
            print(f"   - 平均价格: {avg_price}")
            
            if float(orig_qty) > 0:
                fill_percentage = float(executed_qty) / float(orig_qty) * 100
                print(f"   - 成交比例: {fill_percentage:.2f}%")
            else:
                print(f"   - 成交比例: 0%")
            
            if status in ['FILLED', 'PARTIALLY_FILLED']:
                print("\n✅ 订单已成交！")
                return True
            else:
                print("\n⏳ 订单未成交")
                return False
        else:
            print("\n❌ 无法获取订单信息")
            return False

async def main():
    """主函数"""
    print("🔧 订单状态调试工具")
    print("="*60)
    
    # 从终端日志中获取的订单ID（最新的）
    order_id = "8389765902078015020"
    
    debugger = OrderStatusDebugger()
    result = await debugger.debug_order(order_id)
    
    if result:
        print(f"\n🎉 订单 {order_id} 已成交!")
    else:
        print(f"\n⏳ 订单 {order_id} 未成交")

if __name__ == "__main__":
    try:
        import aiohttp
        from cryptography.hazmat.primitives.asymmetric import ed25519
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install aiohttp cryptography")
        exit(1)
    
    asyncio.run(main())
