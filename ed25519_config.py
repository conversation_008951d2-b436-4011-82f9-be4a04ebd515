# Ed25519密钥配置 (用于币安WebSocket API)

# 私钥 (PEM格式) - 用于程序中签名
ED25519_PRIVATE_KEY_PEM = '''
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIGdEcwhgJGBAxjiYJ4gKLVLhTrznLRjupF3aZLdkFdXo
-----END PRIVATE KEY-----
'''

# 公钥 (PEM格式) - 上传到币安账户
ED25519_PUBLIC_KEY_PEM = '''
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VwAyEA+1GOY1Ua26AJWjpGVVZd9AzTi2StuaZYxppiLVgkGJE=
-----END PUBLIC KEY-----
'''

# 私钥 (Base64格式) - 备用格式
ED25519_PRIVATE_KEY_BASE64 = "Z0RzCGAkYEDGOJgniAotUuFOvOctGO6kXdpkt2QV1eg="

# 公钥 (Base64格式) - 备用格式  
ED25519_PUBLIC_KEY_BASE64 = "+1GOY1Ua26AJWjpGVVZd9AzTi2StuaZYxppiLVgkGJE="

# 私钥 (Hex格式) - 用于Go程序
ED25519_PRIVATE_KEY_HEX = "6744730860246040c6389827880a2d52e14ebce72d18eea45dda64b76415d5e8"

# 公钥 (Hex格式) - 用于Go程序
ED25519_PUBLIC_KEY_HEX = "fb518e63551adba0095a3a4655565df40cd38b64adb9a658c69a622d58241891"
