#!/usr/bin/env python3
"""
对比Python和Go的签名结果
"""

import base64
from cryptography.hazmat.primitives.asymmetric import ed25519
from cryptography.hazmat.primitives import serialization
from ed25519_config import ED25519_PRIVATE_KEY_PEM

def test_fixed_signature():
    # 加载Ed25519私钥
    ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
        serialization.load_pem_private_key(
            ED25519_PRIVATE_KEY_PEM.encode('utf-8'),
            password=None
        ).private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
    )
    
    # 显示私钥信息
    private_key_hex = ed25519_private_key.private_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PrivateFormat.Raw,
        encryption_algorithm=serialization.NoEncryption()
    ).hex()
    print(f"Python私钥 (Hex): {private_key_hex}")
    
    # 使用固定的测试数据（与Go程序保持一致）
    test_string = "symbol=ETHUSDT&side=BUY&type=LIMIT&quantity=0.01&price=2500.00&timeInForce=GTC&newClientOrderId=test_12345&timestamp=1749225000000&test=true"
    
    print(f"测试字符串: {test_string}")
    
    # 生成签名
    signature_bytes = ed25519_private_key.sign(test_string.encode('utf-8'))
    signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
    
    print(f"Python Ed25519签名: {signature_base64}")
    
    # 验证签名
    public_key = ed25519_private_key.public_key()
    try:
        public_key.verify(signature_bytes, test_string.encode('utf-8'))
        print("✅ Python签名验证成功")
    except Exception as e:
        print(f"❌ Python签名验证失败: {e}")
    
    print("\n" + "="*60)
    print("请在Go程序中使用相同的测试字符串进行签名对比")
    print("="*60)

if __name__ == "__main__":
    test_fixed_signature()
