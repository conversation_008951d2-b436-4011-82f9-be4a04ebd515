#!/usr/bin/env python3
"""
统一Ed25519方案测试程序
验证REST API和WebSocket API都使用同一个Ed25519密钥
"""

import asyncio
import websockets
import json
import time
import base64
import logging
import aiohttp
from cryptography.hazmat.primitives.asymmetric import ed25519
from cryptography.hazmat.primitives import serialization
from config import BINANCE_CONFIG
from ed25519_config import ED25519_PRIVATE_KEY_PEM

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedEd25519Tester:
    def __init__(self):
        # 统一使用Ed25519密钥
        self.api_key = BINANCE_CONFIG.get("ws_api_key", "your_ed25519_api_key_here")
        
        # 加载Ed25519私钥
        self.ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
            serialization.load_pem_private_key(
                ED25519_PRIVATE_KEY_PEM.encode('utf-8'),
                password=None
            ).private_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PrivateFormat.Raw,
                encryption_algorithm=serialization.NoEncryption()
            )
        )

        # 调试：显示私钥信息
        private_key_hex = self.ed25519_private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        ).hex()
        logger.info(f"🔍 Python程序使用的私钥 (Hex): {private_key_hex}")
        
        # 端点配置
        self.rest_base = "https://fapi.binance.com"
        self.ws_api = "wss://ws-fapi.binance.com/ws-fapi/v1"
        
        self.request_id = 0
    
    def generate_ed25519_signature_rest(self, params: dict) -> str:
        """生成Ed25519签名（REST API格式 - 不排序）"""
        # 构建查询字符串（不排序，保持原始顺序）
        query_parts = []
        for key, value in params.items():
            if key != "signature":
                query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 生成Ed25519签名
        signature_bytes = self.ed25519_private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        logger.info(f"🔍 REST签名字符串: {query_string}")
        logger.info(f"🔍 REST Ed25519签名: {signature_base64}")
        
        return signature_base64
    
    def generate_ed25519_signature_websocket(self, params: dict) -> str:
        """生成Ed25519签名（WebSocket API格式 - 排序）"""
        # 排除signature参数并排序
        filtered_params = {k: v for k, v in params.items() if k != 'signature'}
        sorted_params = sorted(filtered_params.items())
        
        # 构建查询字符串
        query_parts = []
        for key, value in sorted_params:
            query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 生成Ed25519签名
        signature_bytes = self.ed25519_private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        logger.debug(f"WebSocket签名字符串: {query_string}")
        logger.debug(f"WebSocket Ed25519签名: {signature_base64}")
        
        return signature_base64
    
    async def test_rest_api_ed25519(self):
        """测试REST API使用Ed25519签名"""
        logger.info("🔍 测试REST API使用Ed25519签名...")
        
        if self.api_key == "your_ed25519_api_key_here":
            logger.error("❌ 请先配置Ed25519 API密钥")
            return False
        
        timestamp = int(time.time() * 1000)
        
        # REST API参数（保持原始顺序）
        from collections import OrderedDict
        params = OrderedDict([
            ("symbol", "ETHUSDT"),
            ("side", "BUY"),
            ("type", "LIMIT"),
            ("quantity", "0.01"),
            ("price", "2400.00"),
            ("timeInForce", "GTC"),
            ("newClientOrderId", f"rest_ed25519_test_{timestamp}"),
            ("timestamp", timestamp),
            ("test", "true")  # 测试模式
        ])
        
        # 生成Ed25519签名（REST格式）
        signature = self.generate_ed25519_signature_rest(params)
        params["signature"] = signature
        
        # 发送REST API请求
        url = f"{self.rest_base}/fapi/v1/order"
        headers = {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=params) as response:
                    result = await response.json()
                    
                    logger.info(f"📥 REST API Ed25519响应: {result}")
                    
                    if response.status == 200:
                        logger.info("✅ REST API Ed25519签名成功")
                        return True
                    else:
                        logger.error(f"❌ REST API Ed25519签名失败: {result}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ REST API Ed25519测试异常: {e}")
            return False
    
    async def test_websocket_session_ed25519(self):
        """测试WebSocket会话使用Ed25519签名"""
        logger.info("🔍 测试WebSocket会话使用Ed25519签名...")
        
        if self.api_key == "your_ed25519_api_key_here":
            logger.error("❌ 请先配置Ed25519 API密钥")
            return False, None
        
        try:
            ws = await websockets.connect(self.ws_api)
            logger.info("✅ WebSocket连接成功")
            
            # 构建登录请求
            timestamp = int(time.time() * 1000)
            login_params = {
                "apiKey": self.api_key,
                "timestamp": timestamp
            }
            
            # 生成Ed25519签名（WebSocket格式）
            signature = self.generate_ed25519_signature_websocket(login_params)
            login_params["signature"] = signature
            
            login_request = {
                "id": f"unified_login_{timestamp}",
                "method": "session.logon",
                "params": login_params
            }
            
            logger.info(f"📤 发送WebSocket登录请求（统一Ed25519签名）")
            await ws.send(json.dumps(login_request))
            
            # 等待登录响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)
            
            logger.info(f"📥 WebSocket登录响应: {data}")
            
            if data.get('status') == 200:
                logger.info("✅ WebSocket Ed25519会话登录成功")
                return True, ws
            else:
                error = data.get('error', {})
                logger.error(f"❌ WebSocket Ed25519会话登录失败: {error}")
                await ws.close()
                return False, None
                
        except Exception as e:
            logger.error(f"❌ WebSocket Ed25519登录测试异常: {e}")
            return False, None
    
    async def test_websocket_authenticated_order_ed25519(self, ws):
        """测试已认证WebSocket下单（统一Ed25519）"""
        logger.info("🔍 测试已认证WebSocket下单（统一Ed25519）...")

        try:
            timestamp = int(time.time() * 1000)

            # 构建下单请求（已认证会话，添加timestamp）
            order_params = {
                "symbol": "ETHUSDT",
                "side": "BUY",
                "type": "LIMIT",
                "quantity": "0.01",
                # "price": "2500.00",
                "priceMatch": "QUEUE_20",
                "timeInForce": "GTX",
                "newClientOrderId": f"ws_ed25519_test_{timestamp}",
                "timestamp": timestamp,  # 添加timestamp
                "test": True  # 测试模式
            }

            order_request = {
                "id": f"unified_order_{timestamp}",
                "method": "order.place",
                "params": order_params
            }

            logger.info(f"📤 发送WebSocket下单请求（已认证，无需签名）")
            await ws.send(json.dumps(order_request))

            # 等待下单响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)

            logger.info(f"📥 WebSocket下单响应: {data}")

            if data.get('status') == 200:
                logger.info("✅ WebSocket已认证下单成功（统一Ed25519）")
                return True
            else:
                error = data.get('error', {})
                logger.error(f"❌ WebSocket已认证下单失败: {error}")
                return False

        except Exception as e:
            logger.error(f"❌ WebSocket下单测试异常: {e}")
            return False

    def get_price_match_from_rank(self, rank):
        """根据排名参数获取对应的priceMatch值"""
        if rank == 1:
            return "QUEUE"
        elif 2 <= rank <= 20:
            return f"QUEUE_{rank}"
        else:
            raise ValueError(f"排名参数必须在1-20之间，当前值: {rank}")

    def get_interactive_parameters(self):
        """获取交互参数"""
        print("\n" + "="*60)
        print("🎯 交互式订单修改功能")
        print("="*60)

        try:
            # 获取排名参数
            while True:
                try:
                    rank = int(input("请输入排名参数 (1-20, 1=QUEUE, 2=QUEUE_2, ...): "))
                    if 1 <= rank <= 20:
                        price_match = self.get_price_match_from_rank(rank)
                        print(f"✅ 排名参数: {rank} → priceMatch: {price_match}")
                        break
                    else:
                        print("❌ 排名参数必须在1-20之间")
                except ValueError:
                    print("❌ 请输入有效的数字")

            # 获取发送间隔
            while True:
                try:
                    interval_ms = int(input("请输入发送间隔 (毫秒, 如1000): "))
                    if interval_ms > 0:
                        print(f"✅ 发送间隔: {interval_ms} 毫秒")
                        break
                    else:
                        print("❌ 发送间隔必须大于0")
                except ValueError:
                    print("❌ 请输入有效的数字")

            # 获取订单数量
            while True:
                try:
                    quantity = float(input("请输入订单数量 (如0.01): "))
                    if quantity > 0:
                        print(f"✅ 订单数量: {quantity}")
                        break
                    else:
                        print("❌ 订单数量必须大于0")
                except ValueError:
                    print("❌ 请输入有效的数字")

            # 获取循环次数
            while True:
                try:
                    loop_count = int(input("请输入循环次数 (如100): "))
                    if loop_count > 0:
                        print(f"✅ 循环次数: {loop_count}")
                        break
                    else:
                        print("❌ 循环次数必须大于0")
                except ValueError:
                    print("❌ 请输入有效的数字")

            return {
                'rank': rank,
                'price_match': price_match,
                'interval_ms': interval_ms,
                'quantity': str(quantity),
                'loop_count': loop_count
            }

        except KeyboardInterrupt:
            print("\n❌ 用户取消操作")
            return None

    async def interactive_order_modify_session(self, ws):
        """交互式订单修改会话"""
        logger.info("🎯 开始交互式订单修改会话...")

        # 获取交互参数
        params = self.get_interactive_parameters()
        if not params:
            logger.info("❌ 未获取到有效参数，退出交互功能")
            return False

        try:
            # 第一步：下单
            logger.info(f"\n📋 步骤1: 发送初始订单")
            logger.info(f"   - 数量: {params['quantity']} ETH")
            logger.info(f"   - priceMatch: {params['price_match']}")

            order_id = await self.place_initial_order(ws, params)
            if not order_id:
                logger.error("❌ 初始订单下单失败")
                return False

            logger.info(f"✅ 初始订单成功，订单ID: {order_id}")

            # 第二步：启动异步监控和循环修改订单
            logger.info(f"\n📋 步骤2: 开始循环修改订单（带成交监控）")
            logger.info(f"   - 循环次数: {params['loop_count']}")
            logger.info(f"   - 发送间隔: {params['interval_ms']} 毫秒")
            logger.info(f"   - priceMatch: {params['price_match']}")
            logger.info(f"   - 用户数据流监控: 实时ORDER_TRADE_UPDATE事件")
            logger.info(f"   - REST API监控: 每0.2秒查询状态（高频版）")

            # 创建共享的停止事件
            stop_event = asyncio.Event()

            # 启动异步监控和修改任务
            tasks = await self.start_monitoring_and_modify_tasks(ws, order_id, params, stop_event)

            # 等待任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 分析结果
            sent_count, fill_info = self.analyze_task_results(results, params)

            logger.info(f"\n🎉 交互式订单修改完成!")
            logger.info(f"   - 总循环次数: {params['loop_count']}")
            logger.info(f"   - 已发送次数: {sent_count}")
            if fill_info:
                logger.info(f"   - 成交信息: {fill_info}")
            logger.info(f"   - 发送模式: 异步发送（带成交监控）")

            return True

        except Exception as e:
            logger.error(f"❌ 交互式订单修改异常: {e}")
            return False

    async def start_monitoring_and_modify_tasks(self, ws, order_id, params, stop_event):
        """启动监控和修改任务"""
        tasks = []

        # 任务1: 用户数据流监听（检测成交）
        user_stream_task = asyncio.create_task(
            self.user_data_stream_monitor(order_id, stop_event)
        )
        tasks.append(user_stream_task)

        # 任务2: REST API轮询（每1秒查询订单状态）
        rest_monitor_task = asyncio.create_task(
            self.rest_api_fill_monitor(order_id, stop_event)
        )
        tasks.append(rest_monitor_task)

        # 任务3: 循环修改订单（带停止检查）
        modify_task = asyncio.create_task(
            self.modify_order_loop_with_monitoring(ws, order_id, params, stop_event)
        )
        tasks.append(modify_task)

        return tasks

    def analyze_task_results(self, results, params):
        """分析任务结果"""
        sent_count = 0
        fill_info = None

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"任务 {i+1} 异常: {result}")
            elif isinstance(result, dict) and 'fill_info' in result:
                fill_info = result['fill_info']
                logger.info(f"✅ 检测到成交信息: {fill_info}")
            elif isinstance(result, int):
                sent_count = result

        return sent_count, fill_info

    async def user_data_stream_monitor(self, order_id, stop_event):
        """用户数据流监听器 - 监听ORDER_TRADE_UPDATE事件"""
        logger.info("🔍 启动用户数据流成交监控...")

        try:
            # 获取listenKey
            listen_key = await self.get_listen_key()
            if not listen_key:
                logger.error("❌ 获取listenKey失败，跳过用户数据流监控")
                return None

            logger.info(f"✅ 获取listenKey成功: {listen_key[:20]}...")

            # 连接用户数据流WebSocket
            user_stream_url = f"wss://fstream.binance.com/ws/{listen_key}"

            async with websockets.connect(user_stream_url) as user_ws:
                logger.info("✅ 用户数据流WebSocket连接成功")

                while not stop_event.is_set():
                    try:
                        # 设置较短的超时，以便定期检查stop_event
                        message = await asyncio.wait_for(user_ws.recv(), timeout=1.0)
                        data = json.loads(message)

                        # 检查是否是ORDER_TRADE_UPDATE事件
                        if self.is_order_trade_update(data, order_id):
                            fill_info = self.extract_fill_info(data)
                            if fill_info:
                                logger.info(f"🎯 用户数据流检测到订单成交!")
                                logger.info(f"   - 订单ID: {order_id}")
                                logger.info(f"   - 成交信息: {fill_info}")
                                stop_event.set()  # 通知其他任务停止
                                return {'fill_info': fill_info, 'source': 'user_data_stream'}

                    except asyncio.TimeoutError:
                        # 超时是正常的，继续循环检查stop_event
                        continue
                    except Exception as e:
                        logger.warning(f"用户数据流监听异常: {e}")
                        await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"❌ 用户数据流监控异常: {e}")
            logger.info("🔄 将依赖REST API监控...")

        logger.info("🔍 用户数据流成交监控结束")
        return None

    async def get_listen_key(self):
        """获取用户数据流listenKey"""
        try:
            timestamp = int(time.time() * 1000)

            # 构建请求参数
            params = {
                'timestamp': timestamp
            }

            # 生成签名
            signature = self.generate_ed25519_signature_rest(params)
            params['signature'] = signature

            # 发送REST API请求
            url = f"{self.rest_base}/fapi/v1/listenKey"
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('listenKey')
                    else:
                        logger.error(f"获取listenKey失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"获取listenKey异常: {e}")
            return None

    async def websocket_fill_monitor(self, ws, order_id, stop_event):
        """WebSocket消息监听器 - 检测订单成交"""
        logger.info("🔍 启动WebSocket成交监控...")

        try:
            while not stop_event.is_set():
                try:
                    # 设置较短的超时，以便定期检查stop_event
                    message = await asyncio.wait_for(ws.recv(), timeout=1.0)
                    data = json.loads(message)

                    # 检查是否是ORDER_TRADE_UPDATE事件
                    if self.is_order_trade_update(data, order_id):
                        fill_info = self.extract_fill_info(data)
                        if fill_info:
                            logger.info(f"🎯 WebSocket检测到订单成交!")
                            logger.info(f"   - 订单ID: {order_id}")
                            logger.info(f"   - 成交信息: {fill_info}")
                            stop_event.set()  # 通知其他任务停止
                            return {'fill_info': fill_info, 'source': 'websocket'}

                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环检查stop_event
                    continue
                except Exception as e:
                    logger.warning(f"WebSocket监听异常: {e}")
                    await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"❌ WebSocket监控异常: {e}")

        logger.info("🔍 WebSocket成交监控结束")
        return None

    def is_order_trade_update(self, data, target_order_id):
        """检查是否是目标订单的交易更新事件"""
        try:
            # 检查是否是ORDER_TRADE_UPDATE事件
            if data.get('e') == 'ORDER_TRADE_UPDATE':
                order_data = data.get('o', {})
                order_id = order_data.get('i')  # 订单ID
                status = order_data.get('X')    # 订单状态

                # 检查是否是目标订单且状态为FILLED
                if str(order_id) == str(target_order_id) and status in ['FILLED', 'PARTIALLY_FILLED']:
                    return True

            return False
        except Exception as e:
            logger.debug(f"解析ORDER_TRADE_UPDATE事件异常: {e}")
            return False

    def extract_fill_info(self, data):
        """提取成交信息"""
        try:
            order_data = data.get('o', {})

            fill_info = {
                'orderId': order_data.get('i'),
                'symbol': order_data.get('s'),
                'status': order_data.get('X'),
                'executedQty': order_data.get('z'),
                'avgPrice': order_data.get('ap'),
                'cumQuote': order_data.get('Z'),
                'updateTime': order_data.get('T'),
                'side': order_data.get('S'),
                'type': order_data.get('o')
            }

            return fill_info
        except Exception as e:
            logger.error(f"提取成交信息异常: {e}")
            return None

    async def rest_api_fill_monitor(self, order_id, stop_event):
        """REST API轮询监控器 - 每0.2秒查询订单状态（高频版）"""
        logger.info("🔍 启动REST API成交监控（每0.2秒查询）...")

        last_status = None
        query_count = 0

        try:
            while not stop_event.is_set():
                try:
                    query_count += 1
                    # 查询订单状态
                    order_info = await self.query_order_status(order_id)

                    if order_info:
                        current_status = order_info.get('status')
                        executed_qty = order_info.get('executedQty', '0')

                        # 记录状态变化
                        if current_status != last_status:
                            logger.info(f"📊 订单状态变化: {last_status} → {current_status} (查询#{query_count})")
                            last_status = current_status

                        # 检查是否成交
                        if current_status in ['FILLED', 'PARTIALLY_FILLED']:
                            fill_info = {
                                'orderId': order_info.get('orderId'),
                                'symbol': order_info.get('symbol'),
                                'status': current_status,
                                'executedQty': executed_qty,
                                'avgPrice': order_info.get('avgPrice'),
                                'cumQuote': order_info.get('cumQuote'),
                                'updateTime': order_info.get('updateTime'),
                                'side': order_info.get('side'),
                                'type': order_info.get('type'),
                                'query_count': query_count
                            }

                            logger.info(f"🎯 REST API检测到订单成交! (查询#{query_count})")
                            logger.info(f"   - 订单ID: {order_id}")
                            logger.info(f"   - 状态: {current_status}")
                            logger.info(f"   - 成交数量: {executed_qty}")
                            logger.info(f"   - 平均价格: {order_info.get('avgPrice')}")
                            stop_event.set()  # 通知其他任务停止
                            return {'fill_info': fill_info, 'source': 'rest_api'}

                        # 显示进度（每5次查询显示一次，更频繁的反馈）
                        if query_count % 5 == 0:
                            logger.info(f"📊 REST API监控进度: 查询#{query_count}, 状态={current_status}, 成交={executed_qty}")
                    else:
                        logger.warning(f"⚠️  查询#{query_count}: 未获取到订单信息")

                    # 等待0.2秒后继续查询（更高频率）
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.warning(f"REST API查询#{query_count}异常: {e}")
                    await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"❌ REST API监控异常: {e}")

        logger.info(f"🔍 REST API成交监控结束 (共查询{query_count}次)")
        return None

    async def query_order_status(self, order_id):
        """查询订单状态"""
        try:
            timestamp = int(time.time() * 1000)

            # 构建查询参数
            params = {
                'symbol': 'ETHUSDT',
                'orderId': order_id,
                'timestamp': timestamp
            }

            # 生成签名
            signature = self.generate_ed25519_signature_rest(params)
            params['signature'] = signature

            # 发送REST API请求
            url = f"{self.rest_base}/fapi/v1/order"
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.debug(f"✅ 查询订单成功: {data}")
                        return data
                    else:
                        error_text = await response.text()
                        logger.warning(f"❌ 查询订单状态失败: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.debug(f"查询订单状态异常: {e}")
            return None

    async def debug_order_status(self, order_id):
        """调试订单状态 - 详细查询订单信息"""
        logger.info(f"🔧 调试模式: 查询订单 {order_id} 的详细状态...")

        try:
            order_info = await self.query_order_status(order_id)
            if order_info:
                logger.info("📊 订单详细信息:")
                for key, value in order_info.items():
                    logger.info(f"   {key}: {value}")

                # 特别关注关键字段
                status = order_info.get('status')
                executed_qty = order_info.get('executedQty', '0')
                orig_qty = order_info.get('origQty', '0')

                logger.info(f"🎯 关键状态:")
                logger.info(f"   - 订单状态: {status}")
                logger.info(f"   - 原始数量: {orig_qty}")
                logger.info(f"   - 成交数量: {executed_qty}")
                logger.info(f"   - 成交比例: {float(executed_qty)/float(orig_qty)*100:.2f}%" if float(orig_qty) > 0 else "   - 成交比例: 0%")

                if status in ['FILLED', 'PARTIALLY_FILLED']:
                    logger.info("✅ 订单已成交！")
                    return True
                else:
                    logger.info("⏳ 订单未成交")
                    return False
            else:
                logger.error("❌ 无法获取订单信息")
                return False

        except Exception as e:
            logger.error(f"❌ 调试查询异常: {e}")
            return False

    async def modify_order_loop_with_monitoring(self, ws, order_id, params, stop_event):
        """循环修改订单（带成交监控）"""
        interval_seconds = params['interval_ms'] / 1000.0
        sent_count = 0

        logger.info(f"🔄 开始循环修改订单 (订单ID: {order_id})")
        logger.info(f"⚡ 异步发送模式：只关注发送间隔，监控成交状态")

        try:
            for i in range(params['loop_count']):
                # 检查是否需要停止
                if stop_event.is_set():
                    logger.info(f"🛑 检测到成交，停止修改循环 (已发送 {sent_count} 次)")
                    break

                try:
                    # 异步发送订单修改请求（不等待响应）
                    await self.send_order_modify_async(ws, order_id, params, i + 1)
                    sent_count += 1
                    logger.info(f"📤 已发送修改 {sent_count}/{params['loop_count']}")
     
                    # 等待指定间隔（除了最后一次）
                    if i < params['loop_count'] - 1:
                        # 分段等待，以便及时响应stop_event
                        wait_time = 0
                        while wait_time < interval_seconds and not stop_event.is_set():
                            sleep_duration = min(0.1, interval_seconds - wait_time)
                            await asyncio.sleep(sleep_duration)
                            wait_time += sleep_duration

                        # 如果在等待期间检测到成交，立即停止
                        if stop_event.is_set():
                            logger.info(f"🛑 等待期间检测到成交，停止修改循环 (已发送 {sent_count} 次)")
                            break

                except Exception as e:
                    logger.error(f"❌ 发送修改 {i+1} 异常: {e}")

        except KeyboardInterrupt:
            logger.info(f"\n⚠️  用户中断，已发送 {sent_count} 次修改")
        except Exception as e:
            logger.error(f"❌ 修改循环异常: {e}")

        if not stop_event.is_set():
            logger.info(f"🎉 所有修改请求已发送完成！(共 {sent_count} 次)")

        return sent_count

    async def place_initial_order(self, ws, params):
        """发送初始订单并获取订单ID"""
        try:
            timestamp = int(time.time() * 1000)

            # 构建下单请求（真实交易，移除test参数）
            order_params = {
                "symbol": "ETHUSDT",
                "side": "BUY",
                "type": "LIMIT",
                "quantity": params['quantity'],
                "priceMatch": params['price_match'],
                "timeInForce": "GTX",
                "newClientOrderId": f"interactive_order_{timestamp}",
                "timestamp": timestamp
            }

            order_request = {
                "id": f"place_order_{timestamp}",
                "method": "order.place",
                "params": order_params
            }

            logger.info(f"📤 发送初始订单请求...")
            await ws.send(json.dumps(order_request))

            # 等待下单响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)

            logger.info(f"📥 下单响应: {data}")

            if data.get('status') == 200:
                result = data.get('result', {})
                order_id = result.get('orderId')
                client_order_id = result.get('clientOrderId')

                if order_id:
                    logger.info(f"✅ 订单绑定成功:")
                    logger.info(f"   - orderId: {order_id}")
                    logger.info(f"   - clientOrderId: {client_order_id}")
                    return order_id
                else:
                    logger.error("❌ 响应中未找到orderId")
                    return None
            else:
                error = data.get('error', {})
                logger.error(f"❌ 下单失败: {error}")
                return None

        except Exception as e:
            logger.error(f"❌ 下单异常: {e}")
            return None



    async def send_order_modify_async(self, ws, order_id, params, attempt_num):
        """异步发送订单修改请求（不等待响应）"""
        try:
            timestamp = int(time.time() * 1000)

            # 构建修改订单请求
            modify_params = {
                "orderId": order_id,
                "symbol": "ETHUSDT",
                "side": "BUY",
                "quantity": params['quantity'],
                "priceMatch": params['price_match'],
                "timestamp": timestamp
            }

            modify_request = {
                "id": f"modify_order_{timestamp}_{attempt_num}",
                "method": "order.modify",
                "params": modify_params
            }

            logger.debug(f"📤 发送修改请求 #{attempt_num}: priceMatch={params['price_match']}")
            # 只发送请求，不等待响应
            await ws.send(json.dumps(modify_request))

        except Exception as e:
            logger.error(f"❌ 发送修改请求 #{attempt_num} 异常: {e}")
            raise

    async def send_order_modify(self, ws, order_id, params, attempt_num):
        """发送单次订单修改请求（同步模式，保留用于其他用途）"""
        try:
            timestamp = int(time.time() * 1000)

            # 构建修改订单请求
            modify_params = {
                "orderId": order_id,
                "symbol": "ETHUSDT",
                "side": "BUY",
                "quantity": params['quantity'],
                "priceMatch": params['price_match'],
                "timestamp": timestamp
            }

            modify_request = {
                "id": f"modify_order_{timestamp}_{attempt_num}",
                "method": "order.modify",
                "params": modify_params
            }

            logger.debug(f"📤 发送修改请求 #{attempt_num}: priceMatch={params['price_match']}")
            await ws.send(json.dumps(modify_request))

            # 等待修改响应
            response = await asyncio.wait_for(ws.recv(), timeout=5)
            data = json.loads(response)

            if data.get('status') == 200:
                result = data.get('result', {})
                logger.debug(f"📥 修改响应 #{attempt_num}: 成功 (updateTime: {result.get('updateTime')})")
                return True
            else:
                error = data.get('error', {})
                logger.warning(f"📥 修改响应 #{attempt_num}: 失败 - {error}")
                return False

        except asyncio.TimeoutError:
            logger.warning(f"⏰ 修改请求 #{attempt_num} 超时")
            return False
        except Exception as e:
            logger.error(f"❌ 修改请求 #{attempt_num} 异常: {e}")
            return False
    
    async def run_unified_test(self):
        """运行统一Ed25519方案测试"""
        logger.info("🚀 开始统一Ed25519方案综合测试")
        logger.info("验证REST API和WebSocket API使用同一个Ed25519密钥")
        logger.info("=" * 80)
        
        results = {}
        
        # 测试1: REST API Ed25519签名
        logger.info("\n📋 测试1: REST API使用Ed25519签名")
        rest_success = await self.test_rest_api_ed25519()
        results['rest_api_ed25519'] = rest_success
        
        # 测试2: WebSocket会话Ed25519登录
        logger.info("\n📋 测试2: WebSocket会话使用Ed25519签名")
        ws_login_success, authenticated_ws = await self.test_websocket_session_ed25519()
        results['websocket_ed25519_login'] = ws_login_success
        
        # 测试3: 已认证WebSocket下单
        if ws_login_success and authenticated_ws:
            logger.info("\n📋 测试3: 已认证WebSocket下单")
            ws_order_success = await self.test_websocket_authenticated_order_ed25519(authenticated_ws)
            results['websocket_authenticated_order'] = ws_order_success

            # 测试4: 交互式订单修改功能（可选）
            if ws_order_success:
                logger.info("\n📋 测试4: 交互式订单修改功能")
                try:
                    choice = input("\n是否启动交互式订单修改功能？(y/n): ").lower().strip()
                    if choice in ['y', 'yes', '是']:
                        interactive_success = await self.interactive_order_modify_session(authenticated_ws)
                        results['interactive_order_modify'] = interactive_success
                    else:
                        logger.info("⏭️  跳过交互式订单修改功能")
                        results['interactive_order_modify'] = None
                except KeyboardInterrupt:
                    logger.info("\n⚠️  用户取消交互功能")
                    results['interactive_order_modify'] = None
            else:
                results['interactive_order_modify'] = False

            # 关闭WebSocket连接
            await authenticated_ws.close()
        else:
            results['websocket_authenticated_order'] = False
            results['interactive_order_modify'] = False
        
        # 输出测试结果
        logger.info("\n" + "=" * 80)
        logger.info("📊 统一Ed25519方案测试结果:")
        logger.info("=" * 80)
        
        test_descriptions = {
            'rest_api_ed25519': 'REST API (Ed25519)',
            'websocket_ed25519_login': 'WebSocket登录 (Ed25519)',
            'websocket_authenticated_order': 'WebSocket下单 (已认证)',
            'interactive_order_modify': '交互式订单修改'
        }
        
        for test_name, result in results.items():
            if result is None:
                status = "⏭️  跳过"
            elif result is True:
                status = "✅ 通过"
            else:
                status = "❌ 失败"
            description = test_descriptions.get(test_name, test_name)
            logger.info(f"{description:30} : {status}")

        # 计算统计信息（排除跳过的测试）
        executed_tests = {k: v for k, v in results.items() if v is not None}
        total_tests = len(executed_tests)
        passed_tests = sum(1 for v in executed_tests.values() if v is True)
        
        logger.info("=" * 80)
        logger.info(f"总测试数: {total_tests}, 通过: {passed_tests}, 失败: {total_tests - passed_tests}")
        
        # 分析结果
        core_tests_passed = (
            results.get('rest_api_ed25519', False) and
            results.get('websocket_ed25519_login', False) and
            results.get('websocket_authenticated_order', False)
        )

        if core_tests_passed:
            logger.info("🎉 统一Ed25519方案核心功能成功!")
            logger.info("💡 Go程序可以使用统一架构:")
            logger.info("   - 单一Ed25519 API密钥")
            logger.info("   - REST API和WebSocket API共用")
            logger.info("   - 保持现有程序逻辑")
            logger.info("   - 最佳性能和安全性")

            # 显示交互功能结果
            interactive_result = results.get('interactive_order_modify')
            if interactive_result is True:
                logger.info("   - 交互式订单修改功能验证成功")
            elif interactive_result is None:
                logger.info("   - 交互式订单修改功能已跳过")
            else:
                logger.info("   - 交互式订单修改功能需要调试")

            return True
        else:
            logger.info("❌ 统一Ed25519方案需要配置")

            if not results.get('rest_api_ed25519'):
                logger.info("   - REST API Ed25519签名需要调试")

            if not results.get('websocket_ed25519_login'):
                logger.info("   - WebSocket Ed25519登录需要调试")

            if not results.get('websocket_authenticated_order'):
                logger.info("   - WebSocket下单功能需要调试")

            return False

async def main():
    """主函数"""
    print("🔧 统一Ed25519方案测试程序")
    print("验证REST API和WebSocket API使用同一个Ed25519密钥")
    print("=" * 70)
    
    tester = UnifiedEd25519Tester()
    success = await tester.run_unified_test()
    
    if success:
        print("\n🎯 Go程序实施方案:")
        print("1. 使用单一Ed25519密钥")
        print("2. REST API: Ed25519签名（不排序参数）")
        print("3. WebSocket API: Ed25519签名（排序参数）")
        print("4. 保持现有智能订单逻辑")
        print("5. 修复side参数格式（long→BUY）")
        print("6. 支持交互式订单修改功能")
        print("   - priceMatch参数动态调整")
        print("   - 异步发送模式（不等待响应）")
        print("   - 精确间隔时间控制")
        print("   - 实时订单绑定机制")
        print("   - 双重成交监控机制:")
        print("     * 用户数据流实时监听ORDER_TRADE_UPDATE")
        print("     * REST API每0.2秒轮询订单状态（高频版）")
        print("     * 任一检测到成交立即停止循环")
        print("     * 自动获取listenKey建立用户数据流连接")
    else:
        print("\n📋 需要完成的配置:")
        print("1. 确保Ed25519 API密钥正确配置")
        print("2. 检查API权限设置")
        print("3. 重新运行测试")
        print("4. 验证交互式功能权限")

if __name__ == "__main__":
    try:
        import aiohttp
        import websockets
        from cryptography.hazmat.primitives.asymmetric import ed25519
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install aiohttp websockets cryptography")
        exit(1)
    
    asyncio.run(main())
