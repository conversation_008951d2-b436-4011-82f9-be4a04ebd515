# 终端交互功能测试指南

## 🧪 测试目标
验证在test模式中新增的终端交互功能是否正常工作，同时确保不影响主要交易逻辑性能。

## 🚀 启动测试

### 1. 启动系统
```bash
./hedge-system --test
```

### 2. 预期启动输出
```
2025/05/28 15:56:16 动态对冲系统启动...
2025/05/28 15:56:16 📄 加载配置文件: config.yaml
2025/05/28 15:56:16 ✅ 配置文件加载成功: config.yaml
2025/05/28 15:56:16 🔧 API密钥: cNnJC8hz...
2025/05/28 15:56:16 🔴 使用币安主网 - 请确保你了解风险！
2025/05/28 15:56:16 🔴 初始化币安API连接...
2025/05/28 15:56:16 启动本地测试模式...
2025/05/28 15:56:16 测试任务创建成功: task_1748418967790444000
2025/05/28 15:56:16 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2639, 对冲阈值=0.005%

=== 交互命令帮助 ===
在运行过程中，你可以输入以下命令：
  add long 3500 0.002 0.01    - 新增做多对冲任务 (目标价 阈值 数量)
  add short 3500 0.002 0.01   - 新增做空对冲任务 (目标价 阈值 数量)
  stop <task_id>               - 停止指定任务
  list                         - 列出所有任务
  help                         - 显示此帮助信息
  quit                         - 退出程序
==================
```

## 🎮 交互测试步骤

### 测试1: 帮助命令
```bash
help
```
**预期输出**: 重新显示命令帮助信息

### 测试2: 列出当前任务
```bash
list
```
**预期输出**: 
```
📋 当前运行的任务:
  task_1748418967790444000 | ETHUSDT | long | 目标价: 2639.00 | 阈值: 0.0050% | 仓位: 无仓位 | 交易: 0
```

### 测试3: 新增做多对冲任务
```bash
add long 3500 0.002 0.01
```
**预期输出**:
```
✅ 任务创建成功: task_1748418967790444001
📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=3500.00, 阈值=0.2000%, 数量=0.0100
```

### 测试4: 新增做空对冲任务
```bash
add short 3480 0.001 0.005
```
**预期输出**:
```
✅ 任务创建成功: task_1748418967790444002
📊 任务参数: 交易对=ETHUSDT, 方向=short, 目标价=3480.00, 阈值=0.1000%, 数量=0.0050
```

### 测试5: 再次列出任务
```bash
list
```
**预期输出**: 显示3个任务

### 测试6: 停止指定任务
```bash
stop task_1748418967790444001
```
**预期输出**:
```
✅ 任务 task_1748418967790444001 已停止
```

### 测试7: 错误命令测试
```bash
add invalid_direction 3500 0.002 0.01
```
**预期输出**:
```
❌ 方向必须是 'long' 或 'short'
```

```bash
add long invalid_price 0.002 0.01
```
**预期输出**:
```
❌ 目标价格格式错误: invalid_price
```

```bash
unknown_command
```
**预期输出**:
```
❌ 未知命令: unknown_command，输入 'help' 查看帮助
```

## ⚡ 性能测试

### 测试8: 快速连续命令
快速输入以下命令序列，观察系统响应：
```bash
add long 3500 0.002 0.01
add short 3480 0.001 0.005
list
add long 3520 0.003 0.008
add short 3460 0.0015 0.006
list
```

**观察要点**:
- 命令响应是否及时
- WebSocket数据接收是否正常（观察日志中的盘口数据）
- 系统状态输出是否正常（每10秒一次）

### 测试9: 系统状态监控
等待系统自动输出状态（每10秒），观察：
- 是否显示所有任务的状态
- 任务状态信息是否完整
- 输出格式是否正确

## 🔍 关键验证点

### 1. 性能验证
- ✅ 命令输入不应阻塞WebSocket数据处理
- ✅ 下单逻辑执行不应受到影响
- ✅ 系统响应时间应保持正常

### 2. 功能验证
- ✅ 所有命令都能正确执行
- ✅ 错误输入有适当的错误提示
- ✅ 任务管理功能正常（新增、停止、列出）

### 3. 稳定性验证
- ✅ 长时间运行不应出现内存泄漏
- ✅ 错误命令不应影响系统稳定性
- ✅ 多任务并发运行正常

## 🚪 退出测试

### 方式1: 命令退出
```bash
quit
```

### 方式2: 信号退出
按 `Ctrl+C`

**预期行为**: 
- 系统会停止所有运行的任务
- 等待2秒确保任务完全停止
- 安全退出程序

## 📊 测试报告模板

```
测试时间: 2025/05/28
测试环境: macOS/Linux
Go版本: 1.19+

功能测试结果:
[ ] 帮助命令正常
[ ] 列出任务正常
[ ] 新增任务正常
[ ] 停止任务正常
[ ] 错误处理正常

性能测试结果:
[ ] 命令响应及时
[ ] WebSocket不受影响
[ ] 下单逻辑不受影响
[ ] 内存使用正常

稳定性测试结果:
[ ] 长时间运行稳定
[ ] 多任务并发正常
[ ] 错误恢复正常

总体评价: ✅ 通过 / ❌ 失败
备注: 
```

## 🔧 故障排除

### 常见问题

1. **命令无响应**
   - 检查是否有语法错误
   - 确认系统是否正在处理其他任务

2. **任务创建失败**
   - 检查参数格式是否正确
   - 确认币安API连接是否正常

3. **性能问题**
   - 观察CPU和内存使用情况
   - 检查WebSocket连接状态

### 调试技巧

1. **查看详细日志**
   - 观察系统输出的所有日志信息
   - 注意错误和警告信息

2. **监控系统资源**
   - 使用`top`或`htop`监控CPU使用
   - 观察内存使用情况

3. **网络连接检查**
   - 确认币安API连接正常
   - 检查WebSocket连接状态 