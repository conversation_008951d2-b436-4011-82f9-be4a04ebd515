package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// EventLogger 事件日志管理器
type EventLogger struct {
	logDir     string
	logFile    *os.File
	mutex      sync.Mutex
	enabled    bool
	bufferChan chan string
	stopChan   chan struct{}
}

// NewEventLogger 创建新的事件日志管理器
func NewEventLogger() *EventLogger {
	logDir := "logs"
	el := &EventLogger{
		logDir:     logDir,
		enabled:    true,
		bufferChan: make(chan string, 1000), // 大缓冲区，避免阻塞主程序
		stopChan:   make(chan struct{}),
	}

	// 确保日志目录存在
	if err := os.MkdirAll(logDir, 0755); err != nil {
		log.Printf("❌ 创建日志目录失败: %v", err)
		el.enabled = false
		return el
	}

	// 创建日志文件
	if err := el.createLogFile(); err != nil {
		log.Printf("❌ 创建日志文件失败: %v", err)
		el.enabled = false
		return el
	}

	// 启动异步写入协程
	go el.writeLoop()

	return el
}

// createLogFile 创建日志文件
func (el *EventLogger) createLogFile() error {
	now := time.Now()
	filename := fmt.Sprintf("events_%s.log", now.Format("20060102"))
	filepath := filepath.Join(el.logDir, filename)

	file, err := os.OpenFile(filepath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}

	el.logFile = file
	return nil
}

// LogEvent 记录事件日志（异步，不阻塞主程序）
func (el *EventLogger) LogEvent(taskID, eventType, message string) {
	if !el.enabled {
		return
	}

	timestamp := time.Now().Format("2006/01/02 15:04:05")
	logEntry := fmt.Sprintf("%s %s\n", timestamp, message)

	// 非阻塞发送到缓冲通道
	select {
	case el.bufferChan <- logEntry:
		// 日志已发送到缓冲区
	default:
		// 缓冲区满了，丢弃日志（避免阻塞主程序）
	}
}

// LogRawMessage 记录原始日志消息（用于保存程序的原始输出）
func (el *EventLogger) LogRawMessage(message string) {
	if !el.enabled {
		return
	}

	// 直接记录原始消息，不添加额外格式
	logEntry := message + "\n"

	// 非阻塞发送到缓冲通道
	select {
	case el.bufferChan <- logEntry:
		// 日志已发送到缓冲区
	default:
		// 缓冲区满了，丢弃日志（避免阻塞主程序）
	}
}

// LogTaskEvent 记录任务事件
func (el *EventLogger) LogTaskEvent(taskID string, event Event) {
	if !el.enabled {
		return
	}

	message := fmt.Sprintf("事件类型: %s | 订单类型: %s | 成交方式: %s | 理论价: %s | 实际价: %s | 损耗: %s | 手续费: %s | 订单ID: %s",
		event.Type,
		event.OrderType,
		event.ExecutionType,
		event.TheoreticalPrice.String(),
		event.ActualPrice.String(),
		event.PriceLoss.String(),
		event.Fee.String(),
		event.OrderID)

	el.LogEvent(taskID, "TRADE", message)
}

// LogTaskStatus 记录任务状态变化
func (el *EventLogger) LogTaskStatus(taskID, oldStatus, newStatus, reason string) {
	if !el.enabled {
		return
	}

	message := fmt.Sprintf("状态变化: %s -> %s | 原因: %s", oldStatus, newStatus, reason)
	el.LogEvent(taskID, "STATUS", message)
}

// LogTaskPosition 记录仓位变化
func (el *EventLogger) LogTaskPosition(taskID string, position *Position, action string) {
	if !el.enabled {
		return
	}

	var message string
	if position == nil {
		message = fmt.Sprintf("仓位变化: %s | 当前仓位: 无", action)
	} else {
		message = fmt.Sprintf("仓位变化: %s | 方向: %s | 大小: %s | 开仓价: %s | 订单ID: %s",
			action, position.Side, position.Size.String(), position.EntryPrice.String(), position.OrderID)
	}

	el.LogEvent(taskID, "POSITION", message)
}

// writeLoop 异步写入循环
func (el *EventLogger) writeLoop() {
	ticker := time.NewTicker(1 * time.Second) // 每秒批量写入一次
	defer ticker.Stop()

	var buffer []string

	for {
		select {
		case logEntry := <-el.bufferChan:
			buffer = append(buffer, logEntry)

		case <-ticker.C:
			// 定时批量写入
			if len(buffer) > 0 {
				el.flushBuffer(buffer)
				buffer = buffer[:0] // 清空缓冲区
			}

		case <-el.stopChan:
			// 程序退出前，写入剩余的日志
			for len(el.bufferChan) > 0 {
				buffer = append(buffer, <-el.bufferChan)
			}
			if len(buffer) > 0 {
				el.flushBuffer(buffer)
			}
			return
		}
	}
}

// flushBuffer 批量写入缓冲区内容
func (el *EventLogger) flushBuffer(buffer []string) {
	if el.logFile == nil {
		return
	}

	el.mutex.Lock()
	defer el.mutex.Unlock()

	for _, entry := range buffer {
		if _, err := el.logFile.WriteString(entry); err != nil {
			// 写入失败，但不影响主程序
			continue
		}
	}

	// 强制刷新到磁盘
	el.logFile.Sync()
}

// GetTaskEvents 获取指定任务的事件日志
func (el *EventLogger) GetTaskEvents(taskID string) ([]string, error) {
	if !el.enabled {
		return nil, fmt.Errorf("事件日志功能未启用")
	}

	// 读取今天的日志文件
	now := time.Now()
	filename := fmt.Sprintf("events_%s.log", now.Format("20060102"))
	filepath := filepath.Join(el.logDir, filename)

	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("读取日志文件失败: %v", err)
	}

	lines := []string{}
	content := string(data)

	// 简单的行分割和过滤
	for _, line := range splitLines(content) {
		if line != "" && el.isTaskRelatedLog(line, taskID) {
			lines = append(lines, line)
		}
	}

	return lines, nil
}

// isTaskRelatedLog 判断日志行是否与指定任务相关
func (el *EventLogger) isTaskRelatedLog(line, taskID string) bool {
	// 检查是否包含任务ID
	if !containsTaskID(line, taskID) {
		return false
	}

	// 只保留任务执行相关的日志，过滤掉状态查询等交互信息
	taskRelatedPatterns := []string{
		"触发",                // 触发开仓/平仓条件
		"开始执行下单",            // 开始执行下单
		"计算理论价格",            // 计算理论价格
		"挂买入限价单",            // 挂买入限价单
		"挂卖出限价单",            // 挂卖出限价单
		"限价单已下达",            // 限价单已下达
		"订单已撤销",             // 订单已撤销
		"订单被撤销",             // 订单被撤销
		"订单超时",              // 订单超时
		"订单已成交",             // 订单已成交
		"订单成交",              // 订单成交
		"下单完成",              // 下单完成
		"事件类型:",             // 事件记录
		"记录事件和更新仓位",         // 记录事件
		"不更新仓位状态",           // 不更新仓位状态
		"尝试撤销",              // 尝试撤销
		"检查是否市价",            // 检查市价
		"市价单已下达",            // 市价单已下达
		"binance_client.go", // 币安客户端相关日志
	}

	// 检查是否包含任务相关的关键词
	for _, pattern := range taskRelatedPatterns {
		if strings.Contains(line, pattern) {
			return true
		}
	}

	return false
}

// splitLines 分割字符串为行
func splitLines(content string) []string {
	var lines []string
	var current string

	for _, char := range content {
		if char == '\n' {
			if current != "" {
				lines = append(lines, current)
				current = ""
			}
		} else {
			current += string(char)
		}
	}

	if current != "" {
		lines = append(lines, current)
	}

	return lines
}

// containsTaskID 检查日志行是否包含指定任务ID
func containsTaskID(line, taskID string) bool {
	target := fmt.Sprintf("[%s]", taskID)
	for i := 0; i <= len(line)-len(target); i++ {
		if line[i:i+len(target)] == target {
			return true
		}
	}
	return false
}

// Close 关闭事件日志管理器
func (el *EventLogger) Close() {
	if !el.enabled {
		return
	}

	close(el.stopChan)

	if el.logFile != nil {
		el.logFile.Close()
	}
}

// CleanupOldLogs 清理旧日志文件（保留最近7天）
func (el *EventLogger) CleanupOldLogs() error {
	files, err := os.ReadDir(el.logDir)
	if err != nil {
		return err
	}

	cutoff := time.Now().AddDate(0, 0, -7) // 7天前

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		info, err := file.Info()
		if err != nil {
			continue
		}

		if info.ModTime().Before(cutoff) {
			filepath := filepath.Join(el.logDir, file.Name())
			os.Remove(filepath)
		}
	}

	return nil
}
