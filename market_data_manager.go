package main

import (
	"fmt"
	"log"
	"strings"
	"sync"
	"time"
)

// 全局市场数据管理器
type MarketDataManager struct {
	connections map[string]*WebSocketManager       // symbol -> WebSocket管理器
	subscribers map[string][]*MarketDataSubscriber // symbol -> 订阅者列表
	latestData  map[string]*OrderBookData          // symbol -> 最新订单簿数据
	mutex       sync.RWMutex
	config      SmartOrderConfig
	isRunning   bool
}

// 全局市场数据管理器实例
var globalMarketDataManager *MarketDataManager
var marketDataManagerOnce sync.Once

// 获取全局市场数据管理器
func GetMarketDataManager() *MarketDataManager {
	marketDataManagerOnce.Do(func() {
		globalMarketDataManager = NewMarketDataManager(DefaultSmartOrderConfig())
	})
	return globalMarketDataManager
}

// 创建市场数据管理器
func NewMarketDataManager(config SmartOrderConfig) *MarketDataManager {
	return &MarketDataManager{
		connections: make(map[string]*WebSocketManager),
		subscribers: make(map[string][]*MarketDataSubscriber),
		latestData:  make(map[string]*OrderBookData),
		config:      config,
	}
}

// 启动市场数据管理器
func (mdm *MarketDataManager) Start() error {
	mdm.mutex.Lock()
	defer mdm.mutex.Unlock()

	if mdm.isRunning {
		return nil
	}

	mdm.isRunning = true
	log.Printf("市场数据管理器已启动")
	return nil
}

// 停止市场数据管理器
func (mdm *MarketDataManager) Stop() error {
	mdm.mutex.Lock()
	defer mdm.mutex.Unlock()

	if !mdm.isRunning {
		return nil
	}

	// 关闭所有连接
	for symbol, wsm := range mdm.connections {
		log.Printf("关闭%s市场数据连接", symbol)
		wsm.Disconnect()
	}

	// 清理所有订阅者
	for symbol, subscribers := range mdm.subscribers {
		for _, subscriber := range subscribers {
			close(subscriber.Channel)
		}
		log.Printf("清理%s的%d个订阅者", symbol, len(subscribers))
	}

	mdm.connections = make(map[string]*WebSocketManager)
	mdm.subscribers = make(map[string][]*MarketDataSubscriber)
	mdm.isRunning = false

	log.Printf("市场数据管理器已停止")
	return nil
}

// 订阅市场数据
func (mdm *MarketDataManager) Subscribe(taskID, symbol string) (chan *OrderBookData, error) {
	mdm.mutex.Lock()
	defer mdm.mutex.Unlock()

	if !mdm.isRunning {
		return nil, fmt.Errorf("市场数据管理器未启动")
	}

	// 创建订阅者通道 - 大幅增加缓存大小以处理高频数据和防止通道满
	dataChan := make(chan *OrderBookData, 1000)
	subscriber := &MarketDataSubscriber{
		TaskID:  taskID,
		Channel: dataChan,
	}

	// 添加到订阅者列表
	mdm.subscribers[symbol] = append(mdm.subscribers[symbol], subscriber)

	// 检查是否需要创建新的WebSocket连接
	if _, exists := mdm.connections[symbol]; !exists {
		err := mdm.createConnection(symbol)
		if err != nil {
			// 移除刚添加的订阅者
			mdm.removeSubscriber(symbol, taskID)
			return nil, fmt.Errorf("创建%s市场数据连接失败: %v", symbol, err)
		}
	}

	log.Printf("任务%s成功订阅%s市场数据，当前订阅者数量: %d",
		taskID, symbol, len(mdm.subscribers[symbol]))

	return dataChan, nil
}

// 取消订阅
func (mdm *MarketDataManager) Unsubscribe(taskID, symbol string) error {
	mdm.mutex.Lock()
	defer mdm.mutex.Unlock()

	// 移除订阅者
	removed := mdm.removeSubscriber(symbol, taskID)
	if !removed {
		return fmt.Errorf("未找到任务%s对%s的订阅", taskID, symbol)
	}

	log.Printf("任务%s取消订阅%s市场数据，剩余订阅者数量: %d",
		taskID, symbol, len(mdm.subscribers[symbol]))

	// 如果没有订阅者了，关闭连接
	if len(mdm.subscribers[symbol]) == 0 {
		if wsm, exists := mdm.connections[symbol]; exists {
			log.Printf("关闭%s市场数据连接（无订阅者）", symbol)
			wsm.Disconnect()
			delete(mdm.connections, symbol)
		}
		delete(mdm.subscribers, symbol)
	}

	return nil
}

// 创建WebSocket连接
func (mdm *MarketDataManager) createConnection(symbol string) error {
	// 构建WebSocket URL - 使用Partial Book Depth Stream获取完整订单簿
	// 格式: <symbol>@depth<levels>@<speed>
	// levels: 5, 10, 20 (我们使用20档)
	// speed: 100ms, 250ms, 500ms
	streamName := strings.ToLower(symbol) + "@depth20@100ms"
	url := fmt.Sprintf("wss://fstream.binance.com/ws/%s", streamName)

	// 创建WebSocket管理器
	wsm := NewWebSocketManager(url, mdm.config)

	// 连接WebSocket
	err := wsm.Connect()
	if err != nil {
		return err
	}

	// 保存连接
	mdm.connections[symbol] = wsm

	// 启动数据处理协程
	go mdm.handleMarketData(symbol, wsm)

	log.Printf("成功创建%s市场数据连接: %s", symbol, url)
	return nil
}

// 处理市场数据
func (mdm *MarketDataManager) handleMarketData(symbol string, wsm *WebSocketManager) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("市场数据处理协程异常: %v", r)
		}
	}()

	eventChan := wsm.GetEventChannel()

	for {
		select {
		case event, ok := <-eventChan:
			if !ok {
				log.Printf("%s市场数据事件通道已关闭", symbol)
				return
			}

			switch event.Type {
			case WSEventOrderBook:
				// 添加过滤条件：只处理真正的盘口数据
				if mdm.isValidOrderBookData(event.Data) {
					mdm.processOrderBookData(symbol, event.Data)
				} else {
					log.Printf("🔍 %s跳过非盘口数据: %v", symbol, event.Data)
				}
			case WSEventError:
				log.Printf("%s市场数据错误: %v", symbol, event.Data["error"])
			case WSEventReconnect:
				log.Printf("%s市场数据重连成功", symbol)
			}

		case <-time.After(30 * time.Second):
			// 检查连接状态
			if !wsm.IsConnected() {
				log.Printf("%s市场数据连接断开，尝试重连", symbol)
				return
			}
		}
	}
}

// 处理订单簿数据
func (mdm *MarketDataManager) processOrderBookData(symbol string, data map[string]interface{}) {
	// 添加原始数据调试（只显示前几个买卖单）
	if bidsData, ok := data["b"].([]interface{}); ok && len(bidsData) > 0 {
		if bidArray, ok := bidsData[0].([]interface{}); ok && len(bidArray) >= 2 {
			//			log.Printf("🔍 %s原始买一: 价格=%v, 数量=%v", symbol, bidArray[0], bidArray[1])
		}
	}
	if asksData, ok := data["a"].([]interface{}); ok && len(asksData) > 0 {
		if askArray, ok := asksData[0].([]interface{}); ok && len(askArray) >= 2 {
			//	log.Printf("🔍 %s原始卖一: 价格=%v, 数量=%v", symbol, askArray[0], askArray[1])
		}
	}

	// 解析订单簿数据
	orderBook, err := mdm.parseOrderBookData(data)
	if err != nil {
		log.Printf("解析%s订单簿数据失败: %v", symbol, err)
		return
	}

	// 存储最新数据
	mdm.latestData[symbol] = orderBook

	// 精简日志：只在价格显著变化时记录
	// (移除频繁的订单簿更新日志)

	// 分发给所有订阅者
	mdm.mutex.RLock()
	subscribers := mdm.subscribers[symbol]
	mdm.mutex.RUnlock()

	//	log.Printf("📤 分发%s订单簿数据给%d个订阅者", symbol, len(subscribers))

	for _, subscriber := range subscribers {
		select {
		case subscriber.Channel <- orderBook:
			// 成功发送，无需日志
		case <-time.After(10 * time.Millisecond):
			// 通道满时，清理旧数据并重试
			channelLen := len(subscriber.Channel)
			if channelLen > 800 { // 当通道使用率超过80%时开始清理
				// 清理一半的旧数据
				clearCount := channelLen / 2
				cleared := 0
				for i := 0; i < clearCount; i++ {
					select {
					case <-subscriber.Channel:
						// 清理旧数据
						cleared++
					default:
						// 通道已空，停止清理
						goto clearDone
					}
				}
			clearDone:
				log.Printf("🧹 任务%s通道满，已清理%d条旧数据，剩余%d条",
					subscriber.TaskID, cleared, len(subscriber.Channel))
			}

			// 再次尝试发送
			select {
			case subscriber.Channel <- orderBook:
				// 清理后发送成功
			default:
				log.Printf("⚠️ 任务%s通道仍然满，跳过本次数据", subscriber.TaskID)
			}
		}
	}
}

// 验证是否为有效的盘口数据
func (mdm *MarketDataManager) isValidOrderBookData(data map[string]interface{}) bool {
	// 检查必要的盘口数据字段
	if data == nil {
		return false
	}

	// 检查是否包含买单数据 (bids)
	bidsData, hasBids := data["b"].([]interface{})
	if !hasBids || len(bidsData) == 0 {
		return false
	}

	// 检查是否包含卖单数据 (asks)
	asksData, hasAsks := data["a"].([]interface{})
	if !hasAsks || len(asksData) == 0 {
		return false
	}

	// 检查是否包含交易对符号
	symbol, hasSymbol := data["s"].(string)
	if !hasSymbol || symbol == "" {
		return false
	}

	// 验证买单数据格式
	if len(bidsData) > 0 {
		if bidArray, ok := bidsData[0].([]interface{}); ok {
			if len(bidArray) < 2 {
				return false
			}
			// 检查价格和数量是否为有效字符串
			if _, ok := bidArray[0].(string); !ok {
				return false
			}
			if _, ok := bidArray[1].(string); !ok {
				return false
			}
		} else {
			return false
		}
	}

	// 验证卖单数据格式
	if len(asksData) > 0 {
		if askArray, ok := asksData[0].([]interface{}); ok {
			if len(askArray) < 2 {
				return false
			}
			// 检查价格和数量是否为有效字符串
			if _, ok := askArray[0].(string); !ok {
				return false
			}
			if _, ok := askArray[1].(string); !ok {
				return false
			}
		} else {
			return false
		}
	}

	// 排除明显的非盘口数据
	// 检查是否包含订单相关字段（这些不是盘口数据）
	if _, hasOrderId := data["i"]; hasOrderId {
		return false // 可能是订单更新
	}
	if _, hasClientOrderId := data["c"]; hasClientOrderId {
		return false // 可能是订单更新
	}
	if _, hasOrderStatus := data["X"]; hasOrderStatus {
		return false // 可能是订单状态更新
	}
	if _, hasExecutionType := data["x"]; hasExecutionType {
		return false // 可能是执行报告
	}

	// 检查是否为会话相关消息
	if method, hasMethod := data["method"]; hasMethod {
		if methodStr, ok := method.(string); ok {
			if methodStr == "session.logon" || methodStr == "order.place" {
				return false // 会话或订单相关消息
			}
		}
	}

	return true
}

// 解析订单簿数据
func (mdm *MarketDataManager) parseOrderBookData(data map[string]interface{}) (*OrderBookData, error) {
	// 检查数据结构
	bidsData, ok := data["b"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的买单数据")
	}

	asksData, ok := data["a"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的卖单数据")
	}

	// 解析买单
	var bids []PriceLevel
	for _, bidData := range bidsData {
		bidArray, ok := bidData.([]interface{})
		if !ok || len(bidArray) < 2 {
			continue
		}

		priceStr, ok := bidArray[0].(string)
		if !ok {
			continue
		}

		qtyStr, ok := bidArray[1].(string)
		if !ok {
			continue
		}

		price, err := parseDecimal(priceStr)
		if err != nil {
			continue
		}

		qty, err := parseDecimal(qtyStr)
		if err != nil {
			continue
		}

		bids = append(bids, PriceLevel{
			Price:    price,
			Quantity: qty,
		})
	}

	// 解析卖单
	var asks []PriceLevel
	for _, askData := range asksData {
		askArray, ok := askData.([]interface{})
		if !ok || len(askArray) < 2 {
			continue
		}

		priceStr, ok := askArray[0].(string)
		if !ok {
			continue
		}

		qtyStr, ok := askArray[1].(string)
		if !ok {
			continue
		}

		price, err := parseDecimal(priceStr)
		if err != nil {
			continue
		}

		qty, err := parseDecimal(qtyStr)
		if err != nil {
			continue
		}

		asks = append(asks, PriceLevel{
			Price:    price,
			Quantity: qty,
		})
	}

	if len(bids) == 0 || len(asks) == 0 {
		return nil, fmt.Errorf("订单簿数据为空")
	}

	// 创建订单簿数据并设置兼容性字段
	orderBook := &OrderBookData{
		Symbol:    data["s"].(string),
		Bids:      bids,
		Asks:      asks,
		Timestamp: time.Now(),
	}

	// 设置兼容性字段
	if len(bids) > 0 {
		orderBook.BidPrice = bids[0].Price
		orderBook.BidQty = bids[0].Quantity
		//log.Printf("🔧 [市场管理器] %s: 设置BidPrice=%s, BidQty=%s",
		//	orderBook.Symbol, orderBook.BidPrice.String(), orderBook.BidQty.String())
	}
	if len(asks) > 0 {
		orderBook.AskPrice = asks[0].Price
		orderBook.AskQty = asks[0].Quantity
		//log.Printf("🔧 [市场管理器] %s: 设置AskPrice=%s, AskQty=%s",
		//	orderBook.Symbol, orderBook.AskPrice.String(), orderBook.AskQty.String())
	}

	return orderBook, nil
}

// 移除订阅者
func (mdm *MarketDataManager) removeSubscriber(symbol, taskID string) bool {
	subscribers := mdm.subscribers[symbol]
	for i, subscriber := range subscribers {
		if subscriber.TaskID == taskID {
			// 关闭通道
			close(subscriber.Channel)

			// 从切片中移除
			mdm.subscribers[symbol] = append(subscribers[:i], subscribers[i+1:]...)
			return true
		}
	}
	return false
}

// 获取最新订单簿数据
func (mdm *MarketDataManager) GetOrderBook(symbol string) *OrderBookData {
	mdm.mutex.RLock()
	defer mdm.mutex.RUnlock()

	return mdm.latestData[symbol]
}

// 获取连接统计
func (mdm *MarketDataManager) GetStats() map[string]interface{} {
	mdm.mutex.RLock()
	defer mdm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_connections": len(mdm.connections),
		"total_subscribers": 0,
		"connections":       make(map[string]interface{}),
	}

	totalSubscribers := 0
	for symbol, subscribers := range mdm.subscribers {
		subscriberCount := len(subscribers)
		totalSubscribers += subscriberCount

		connectionState := "disconnected"
		if wsm, exists := mdm.connections[symbol]; exists {
			connectionState = string(wsm.GetState())
		}

		stats["connections"].(map[string]interface{})[symbol] = map[string]interface{}{
			"subscribers": subscriberCount,
			"state":       connectionState,
		}
	}

	stats["total_subscribers"] = totalSubscribers
	return stats
}

// 调试：打印当前订阅状态
func (mdm *MarketDataManager) PrintSubscriptionStatus() {
	mdm.mutex.RLock()
	defer mdm.mutex.RUnlock()

	log.Printf("📊 [市场数据管理器] 当前订阅状态:")
	log.Printf("📊 总连接数: %d", len(mdm.connections))
	log.Printf("📊 总订阅者数: %d", len(mdm.subscribers))

	for symbol, subscribers := range mdm.subscribers {
		log.Printf("📊 %s: %d个订阅者", symbol, len(subscribers))
		for _, subscriber := range subscribers {
			log.Printf("📊   - 任务ID: %s", subscriber.TaskID)
		}

		if wsm, exists := mdm.connections[symbol]; exists {
			log.Printf("📊 %s连接状态: %s", symbol, wsm.GetState())
		} else {
			log.Printf("📊 %s连接状态: 无连接", symbol)
		}
	}
}
