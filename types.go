package main

import (
	"sync"
	"sync/atomic"
	"time"

	"github.com/shopspring/decimal"
)

// HedgeTask 对冲任务结构体
type HedgeTask struct {
	ID                string          `json:"id"`                 // 任务唯一标识
	Symbol            string          `json:"symbol"`             // 交易对 (ETHUSDC/ETHUSDT)
	Direction         string          `json:"direction"`          // 对冲方向 (long/short)
	TargetPrice       decimal.Decimal `json:"target_price"`       // 目标价格 T
	StopRate          decimal.Decimal `json:"stop_rate"`          // 对冲阈值 (如 0.002 表示 0.2%)
	Amount            decimal.Decimal `json:"amount"`             // 下单数量
	SlippageTolerance decimal.Decimal `json:"slippage_tolerance"` // 动态对冲滑点容忍度
	StopSlippage      decimal.Decimal `json:"stop_slippage"`      // 停止平仓滑点容忍度
	MarketTimeout     int             `json:"market_timeout"`     // 市价成交检测时间阈值(秒)
	VolumeThreshold   decimal.Decimal `json:"volume_threshold"`   // 盘口量阈值
	PostOnlyMode      bool            `json:"post_only_mode"`     // 是否只允许限价成交（POST_ONLY模式）
	Source            string          `json:"source"`             // 来源标识 (manual/options)
	OptionContract    string          `json:"option_contract"`    // 期权合约名称
	Status            string          `json:"status"`             // 任务状态 (running/stopped/error)
	Position          *Position       `json:"position"`           // 当前仓位信息
	Events            []Event         `json:"events"`             // 事件记录
	Statistics        *Stats          `json:"statistics"`         // 统计信息
	StopChan          chan struct{}   `json:"-"`                  // 停止信号 (不序列化)
	CreatedAt         time.Time       `json:"created_at"`         // 创建时间
	UpdatedAt         time.Time       `json:"updated_at"`         // 最后更新时间
	mutex             sync.RWMutex    `json:"-"`                  // 读写锁保护任务数据 (不序列化)
	onDataChange      func()          `json:"-"`                  // 数据变化回调函数 (不序列化)
	skipCloseOnStop   bool            `json:"-"`                  // 停止时跳过平仓 (不序列化)

	// 智能订单相关字段
	SmartOrderEnabled bool                `json:"smart_order_enabled"` // 是否启用智能订单
	SmartOrderManager *SmartOrderManager  `json:"-"`                   // 智能订单管理器 (不序列化)
	SmartOrderConfig  SmartOrderConfig    `json:"smart_order_config"`  // 智能订单配置
	OrderBookChan     chan *OrderBookData `json:"-"`                   // 订单簿数据通道 (不序列化)

	// 极简订单监控字段
	orderBusy        atomic.Bool   `json:"-"` // 原子操作，防重复触发
	orderInitialized atomic.Bool   `json:"-"` // 原子操作，防重复初始化
	stopChan         chan struct{} `json:"-"` // 统一停止信号

	// 新增：统一任务完成管理
	taskCompleted  atomic.Bool `json:"-"` // 任务是否已完成（成交/超时/达到上限）
	stopChanClosed atomic.Bool `json:"-"` // stopChan是否已关闭
}

// Position 仓位信息
type Position struct {
	Side       string          `json:"side"`        // 仓位方向 (long/short)
	Size       decimal.Decimal `json:"size"`        // 仓位大小
	EntryPrice decimal.Decimal `json:"entry_price"` // 开仓价格
	EntryTime  time.Time       `json:"entry_time"`  // 开仓时间
	OrderID    string          `json:"order_id"`    // 成交订单ID
}

// Event 事件记录
type Event struct {
	ID               string          `json:"id"`                // 事件ID
	Type             string          `json:"type"`              // 事件类型 (open/close)
	OrderType        string          `json:"order_type"`        // 订单类型 (limit/market)
	ExecutionType    string          `json:"execution_type"`    // 实际成交方式 (maker/taker)
	TheoreticalPrice decimal.Decimal `json:"theoretical_price"` // 理论成交价
	ActualPrice      decimal.Decimal `json:"actual_price"`      // 实际成交价
	PriceLoss        decimal.Decimal `json:"price_loss"`        // 价格损耗 (可为负)
	PriceLossRatio   decimal.Decimal `json:"price_loss_ratio"`  // 价格损耗比例
	Fee              decimal.Decimal `json:"fee"`               // 手续费
	FeeRatio         decimal.Decimal `json:"fee_ratio"`         // 手续费比例
	OrderID          string          `json:"order_id"`          // 成交订单ID
	Timestamp        time.Time       `json:"timestamp"`         // 事件时间
}

// Stats 统计信息
type Stats struct {
	TotalTrades       int             `json:"total_trades"`         // 总交易次数
	TotalPnL          decimal.Decimal `json:"total_pnl"`            // 总盈亏
	TotalFees         decimal.Decimal `json:"total_fees"`           // 总手续费
	TotalPriceLoss    decimal.Decimal `json:"total_price_loss"`     // 总价格损耗
	WinRate           decimal.Decimal `json:"win_rate"`             // 胜率
	AvgPriceLossRatio decimal.Decimal `json:"avg_price_loss_ratio"` // 平均价格损耗比例
	AvgFeeRatio       decimal.Decimal `json:"avg_fee_ratio"`        // 平均手续费比例
}

// PriceLevel 价格档位
type PriceLevel struct {
	Price    decimal.Decimal `json:"price"`
	Quantity decimal.Decimal `json:"quantity"`
}

// OrderBookData 盘口数据
type OrderBookData struct {
	Symbol    string          `json:"symbol"`    // 交易对
	Bids      []PriceLevel    `json:"bids"`      // 买单列表
	Asks      []PriceLevel    `json:"asks"`      // 卖单列表
	BidPrice  decimal.Decimal `json:"bid_price"` // 买一价（兼容性）
	BidQty    decimal.Decimal `json:"bid_qty"`   // 买一量（兼容性）
	AskPrice  decimal.Decimal `json:"ask_price"` // 卖一价（兼容性）
	AskQty    decimal.Decimal `json:"ask_qty"`   // 卖一量（兼容性）
	Timestamp time.Time       `json:"timestamp"` // 时间戳
}

// TaskRequest 创建任务请求
type TaskRequest struct {
	Symbol            string  `json:"symbol"`
	Direction         string  `json:"direction"`
	TargetPrice       float64 `json:"target_price"`
	StopRate          float64 `json:"stop_rate"`
	Amount            float64 `json:"amount"`
	SlippageTolerance float64 `json:"slippage_tolerance"`
	StopSlippage      float64 `json:"stop_slippage"`
	MarketTimeout     int     `json:"market_timeout"`
	VolumeThreshold   float64 `json:"volume_threshold"`
	PostOnlyMode      bool    `json:"post_only_mode"`
	Source            string  `json:"source,omitempty"`          // 来源标识 (manual/options)
	OptionContract    string  `json:"option_contract,omitempty"` // 期权合约名称
	SmartOrderEnabled bool    `json:"smart_order_enabled"`       // 是否启用智能订单
}

// TaskResponse 任务响应
type TaskResponse struct {
	TaskID  string `json:"task_id"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// StopTaskRequest 停止任务请求
type StopTaskRequest struct {
	TaskID string `json:"task_id"`
}

// UpdateTaskAmountRequest 更新任务数量请求
type UpdateTaskAmountRequest struct {
	Amount         float64 `json:"amount"`                    // 新的总数量
	UpdateType     string  `json:"update_type"`               // 更新类型 (sell_open/buy_close/exercise)
	Source         string  `json:"source,omitempty"`          // 来源标识
	OptionContract string  `json:"option_contract,omitempty"` // 期权合约名称
}

// UpdateTaskAmountResponse 更新任务数量响应
type UpdateTaskAmountResponse struct {
	Success   bool    `json:"success"`
	TaskID    string  `json:"task_id"`
	OldAmount float64 `json:"old_amount"`
	NewAmount float64 `json:"new_amount"`
	Message   string  `json:"message"`
}

// QueryTaskByOptionResponse 根据期权合约查询任务响应
type QueryTaskByOptionResponse struct {
	Success bool   `json:"success"`
	TaskID  string `json:"task_id,omitempty"`
	Exists  bool   `json:"exists"`
}

// TaskManager 任务管理器
type TaskManager struct {
	tasks       map[string]*HedgeTask
	mutex       sync.RWMutex
	dataManager *DataManager
	eventLogger *EventLogger
}

// 常量定义
const (
	// 任务状态
	StatusRunning = "running"
	StatusStopped = "stopped"
	StatusError   = "error"

	// 对冲方向
	DirectionLong  = "long"
	DirectionShort = "short"

	// 仓位方向
	SideLong  = "long"
	SideShort = "short"

	// 事件类型
	EventTypeOpen  = "open"
	EventTypeClose = "close"

	// 订单类型
	OrderTypeLimit     = "limit"
	OrderTypeMarket    = "market"
	OrderTypeCancelled = "cancelled"
	OrderTypeFailed    = "failed"

	// 成交方式
	ExecutionTypeMaker   = "maker"
	ExecutionTypeTaker   = "taker"
	ExecutionTypeUnknown = "unknown"

	// 交易对
	SymbolETHUSDC = "ETHUSDC"
	SymbolETHUSDT = "ETHUSDT"
)
