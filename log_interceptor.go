package main

import (
	"io"
	"log"
	"os"
	"strings"
)

// LogInterceptor 日志拦截器
type LogInterceptor struct {
	originalOutput io.Writer
	eventLogger    *EventLogger
}

// NewLogInterceptor 创建新的日志拦截器
func NewLogInterceptor(eventLogger *EventLogger) *LogInterceptor {
	return &LogInterceptor{
		originalOutput: log.Writer(),
		eventLogger:    eventLogger,
	}
}

// Write 实现io.Writer接口，拦截日志输出
func (li *LogInterceptor) Write(p []byte) (n int, err error) {
	// 写入到原始输出（控制台）
	n, err = li.originalOutput.Write(p)

	// 同时写入到事件日志（过滤掉不需要的日志）
	if li.eventLogger != nil {
		message := string(p)
		// 移除末尾的换行符，因为LogRawMessage会添加
		message = strings.TrimSuffix(message, "\n")
		if message != "" && li.shouldLogMessage(message) {
			li.eventLogger.LogRawMessage(message)
		}
	}

	return n, err
}

// shouldLogMessage 判断是否应该记录该日志消息
func (li *LogInterceptor) shouldLogMessage(message string) bool {
	// 过滤掉list和export命令的交互展示数据
	filterPatterns := []string{
		"任务ID  状态 方向 目标价", // list命令的表头
		"-------",         // 分隔线
		"合计",              // 合计行
		"📋 任务总览",          // export命令的标题
		"📊 统计汇总",          // export命令的统计
		"📈 交易记录",          // export命令的交易记录标题
		"📋 详细状态报告已复制到剪切板", // export完成提示
		"✅ 详细状态报告已复制到剪切板", // export完成提示
		"💾 手动保存数据",        // 手动保存提示
		"✅ 数据保存成功",        // 保存成功提示
		"📁 备份功能状态",        // 备份状态查询
		"🔄 自动保存间隔已设置为",    // 保存间隔设置
		"✅ 备份功能已启用",       // 备份启用提示
		"❌ 备份功能已禁用",       // 备份禁用提示
		"📋 任务",            // events命令的标题
		"的事件记录",           // events命令的标题
		"🚀 启动所有已停止的任务",    // 批量启动提示
		"⏹️  停止所有任务",      // 批量停止提示
		"✅ 成功启动",          // 批量操作结果
		"✅ 成功停止",          // 批量操作结果
		"✅ 批量启动完成",        // 批量操作完成
		"✅ 批量停止完成",        // 批量操作完成
		"🗑️  任务已删除",       // 删除任务提示
		"⚠️  确认删除任务",      // 删除确认提示
		"输入 'yes' 确认删除",   // 删除确认说明
		"❌ 删除任务失败",        // 删除失败提示
		"✅ 任务",            // 删除成功提示（部分）
		"已成功删除",           // 删除成功提示（部分）
		"❌ 删除操作已取消",       // 删除取消提示
	}

	// 检查是否包含过滤模式
	for _, pattern := range filterPatterns {
		if strings.Contains(message, pattern) {
			return false
		}
	}

	// 过滤掉纯数字开头的任务列表行（如：1234567 运行 L 3500）
	if li.isTaskListLine(message) {
		return false
	}

	return true
}

// isTaskListLine 判断是否是任务列表行
func (li *LogInterceptor) isTaskListLine(message string) bool {
	// 简单的启发式判断：如果消息以7位数字开头，后面跟着状态信息，可能是任务列表行
	parts := strings.Fields(message)
	if len(parts) >= 4 {
		// 检查第一个字段是否是7位数字（任务ID）
		if len(parts[0]) == 7 {
			for _, char := range parts[0] {
				if char < '0' || char > '9' {
					return false
				}
			}
			// 检查第二个字段是否是状态（运行/停止/错误）
			if parts[1] == "运行" || parts[1] == "停止" || parts[1] == "错误" {
				return true
			}
		}
	}
	return false
}

// SetupLogInterceptor 设置日志拦截器
func SetupLogInterceptor(eventLogger *EventLogger) {
	interceptor := NewLogInterceptor(eventLogger)
	log.SetOutput(interceptor)
}

// RestoreOriginalLog 恢复原始日志输出
func RestoreOriginalLog() {
	log.SetOutput(os.Stderr)
}
