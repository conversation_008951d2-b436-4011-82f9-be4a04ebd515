bybit 开发文档网站
https://bybit-exchange.github.io/docs/

帮我整理一个需求
1、我希望根据一个设定的 eth价格 在币安的 usdc/eth 永续合约（可选 usdt/eth 永续合约）中  新建动态对冲任务（分为做多对冲、做空对冲）
  使用场景例如  我设定 2550 这个价格(T)  设定一个对冲阈值 stoprate 例如 0.2%
 则 做多对冲逻辑为  ：当价格超过 T+stoprate 则 做多、当价格低于T-stoprate则平仓
      做空对冲逻辑为  ：当价格低于 T-stoprate 则 做空、当价格高于T+stoprate 则平仓
2、需要进行精细的下单，减少滑点和手续费
     *通过 websocket 订阅实时盘口，实时对比买 1 价，卖 1 价以及挂单量 决定 做多、多空或平仓
    
  
   *当买一价触发做多时，检测下单
         -在设定滑点内（如 0.05%），不停挂买一单（如买一价改变需及时撤单更改最新的买一价重新挂单），直到成交，
         或者 当超过设定滑点后，触发市价成交检测，在设定时间阈值内（如 5 秒），检测卖一盘口量，当超过卖盘设定阈值（如 0.5（可设定） 即  （需要开多的量/卖一盘的挂单量）>0.5 ）
         则直接以市价成交。

   *检测卖一价触发做空时 与上面逻辑一致，方向相反

    当任务为 做多对冲时，它的平仓下单逻辑 与 任务为做空对冲 的开仓逻辑一致
    当任务为 做空对冲时，它的平仓下单逻辑 与 任务为做多对冲 的开仓逻辑一致
3、我希望以新增动态对冲任务的方式 实施这个动态对冲
    具体的使用场景例如
    我 新增一个 eth 2600 下单量为 0.01个 eth 的动态对冲任务
     -当这个任务新增后，它会独立帮我做动态对冲，会单独统计这个动态对冲任务发生的事件、记录每次开仓、平仓的 目标价损耗、手续费损耗、目标价损耗比例、手续费损损耗比例、开平仓盈亏
        我以一个例子展示这些计算 
          例如 t = 1000 stoprate=0.2% 下单量为 1 个 eth
      假如做多动态对冲满足开仓并成交， 成交价为   1003  手续费为 0.5  则
      记录这次事件（以成交 id 绑定） 价格损耗比例为 0.1% 手续费损耗比例为 0.05%  
                              价格损耗为 0.1%*1（下单量）*T=1 美金  手续费损耗为 0.05%*1（下单量）*T=0.5 美金
      然后假如这个做多对冲任务 后面平仓了，则以同样方式 计算目标价损耗、手续费损耗、目标价损耗比例、手续费损损耗比例 四个值，
               当平仓后，计算这个多单的总盈亏（包含手续费）为开平仓盈亏
     -多个动态对冲任务之间要独立管理监控（最好绑定成交记录的Id来监控，如果有更好的建议可以给我提出）,因为如果根据交易所返回的仓位信息，它是没有考虑到我们需要对每个动态对冲任务做单独管理的
     -动态对冲任务建立后，未停止前，只要条件满足就会一直帮我做动态对冲，当我停止某个动态对冲任务时，它会帮我自动平仓，平仓的下单逻辑，相当于动态对冲时的 直接进入检测下单状态（为避免滑点和手续费）
     -一定要以检测、下单效率优先，所有记录、统计、输出，都不能影响（或要以最小代价）检测以及下单效率


=========6.2 日 加入期权下单

1、新建一个文件夹，新建一个期权下单程序（单独，与对冲程序分离）
2、和现在对冲程序币安设置 api 一样，设置bitbyte的api
3、期权下单程序单独一个 readme 
4、通过 bitbyte api 以 websocket 订阅 期权数据
5、与原有对冲程序设置储存参数的方式一致，使用类似embedded_config.go 的方式设置参数
6、与原有对冲程序的 test 模式 一致 可以使用交互命令设置程序的参数
7、期权下单程序有以下参数（可设置）
    *监控行权时间阈值 如 t(以下变量名仅为展示逻辑，实际命名不以此为准)
    *监控范围阈值 如 f
    *单个期权的最高下单量 x 
    *单个期权单次最大下单量 y
    *单个期权盘口检测阈值 g （为百分比）
    *盘口监控阈值 l (默认为 5)
    *动态对冲阈值 dt（默认为 0.02%）
    以上参数的说明：
    根据时间 t，监控行权到期时间少于 t 的所有 期权合约
    根据监控范围阈值 f，获取并监控所有行权价格在 [当前市价*(1-f),当前市价*(1+f)] 之间的期权合约的l档盘口

8、建立并维护下单监控任务 
    -每个下单监控任务拥有独立的参数
       *期权时间价值 z （主要参数）
       *行权时间阈值 zt (主要参数) （以小时为单位 可是一个值 也可以是一个范围，例如 5h或 5h-10h）
       *方向（主要参数）（call 或者 put）
       时间价值的计算z
       如果一个 put 的盘口买 1 价 为b1、行权价为 pt，市价为 st
           如果 st>=pt 则 z=b1
           如果 st<pt  则 z=b1-(pt-st)
       如果一个 call 的盘口卖 1 价 为s1、行权价为 ct，市价为 st
           如果 st<=ct 则 z=s1
           如果 st>ct  则 z=s1-(st-ct)
    -每个下单监控任务拥有独立的下单逻辑
    -监控任务可以通过命令新增，并可以本地持久化数据（重启后依然存在）
    -监控任务可以通过命令删除
    -监控任务可以通过命令查看所有监控任务的状态
    -监控任务可以通过命令查看单个监控任务的详细状态
    -监控任务可以通过命令修改监控任务的参数
    -监控任务可以通过命令启动和停止
    -监控任务启动后，会独立运行，不会影响其他监控任务
    -检测当前监控的所有期权合约的盘口，当以下条件同时成立
       * 如果 zt 为单独值 如 5h 则 当期权合约的行权剩余时间小于 5h 时，满足条件
         如果 zt 为范围值 如 5h-10h 则 当期权合约的行权剩余时间在 5h 到 10h 之间时，满足条件
       * 当期权合约的行权价格在 [当前市价*(1-f),当前市价*(1+f)] 之间时，满足条件
       * 当期权合约的买一价盘口挂单量pl满足 (y/pl)>g 时，满足条件
       * 当期权合约的当前持仓量<x 时，满足条件
       * 当实时计算的期权时间价值z（根据监控盘口，还有市价） 大于 期权监控任务的时间价值 z 时，满足条件
       则开始监控下单，以市价，下单数量为 y，的卖出期权的方式下单，实时监控成交情况，成交后建立成交记录，并记录交易所返回的成交Id和详细成交信息
9、建立并维护 期权跟踪任务 （本地持久化数据、重启后依然存在）
   -当期权有成交后，会建立一个期权跟踪任务，跟踪任务以期权合约名称命名，格式为 ETHUSDT-2JUN25-2500-C 
   -期权跟踪任务会记录每次成交的详细信息，包括成交价、手续费、成交时间、成交Id等
   -期权跟踪任务跟踪维护整个任务的持仓情况，包括当前持仓量、持仓均价、盈亏情况，包括盈亏金额、盈亏比例、手续费等
   -期权跟踪任务可以通过命令查看所有期权跟踪任务的状态
   -期权跟踪任务可以通过命令查看单个期权跟踪任务的详细状态和事件记录
   -期权跟踪任务可以通过命令删除、停止
   -期权跟踪任务会在 期权成交、期权行权、新建、结束对冲任务成功，对冲任务有更新反馈时，建立事件记录
10、与原有对冲程序的交互   
   -当有期权成交时，通过RESTful API 为对冲程序建立动态对冲任务，
      如成交ETHUSDT-2JUN25-2500-C 0.1 个 eth 则 为对冲程序建立一个做多对冲任务，目标价为 2500，对冲阈值为 dt，下单量为 0.1 个 eth
      对冲程序收到 api请求并成功建立对冲任务后，对冲程序会回应返回一个对冲任务的 id （卖出时建立对冲任务）
      每个单独的期权名称（即期权跟踪任务）都对应一个对冲任务id
      如果建立对冲任务的时候，发现已经有绑定的对冲任务 id，则只需要更新对冲任务的下单量即可 
      即 一个期权跟踪任务，可有多个成交事件，多个成交 id，但都绑定同一个对冲任务
   -对动态对冲的任务建立，如果以期权下单下达的话，记录新建任务的来源（原来 test 模式交互标记为手动、通过期权下单程序建立的任务标记为期权）

        
    add_monitor call  24h 0.0015
list_monitors

确认问题
1、可以用 类似M1234567
2、这个我也不太清楚 你最好查一下，有没有 bybit 有没有 api 能够获取某一天行权的的所有期权名，但是我从网页看 命名的规则是 如 ETHUSDT-3JUN25-2475-C 代表 2025 年 6 月 3 日行权价为 2475 的 call 
3、所有期权的到期时间都是 命名的日期当天的 16:00行权的
     例如 ETHUSDT-3JUN25-2475-C 即会在 25 年 6 月 3 日的 16:00 行权
4、websocket 需要的更新频率是很快的，应该是 100ms 左右一次 bybit 如果发现盘口有更新就会推送一次盘口数据，你的设计要根据这个频率来接受盘口并判断需要监控的期权是否满足下单条件。
     防抖是什么意思，如果有一个盘口满足了下单条件后，发送下单前 如果检测到盘口不满足条件了，就不发送下单了，如果已经发送了，那要等订单确定成交详情，如果有成交会增加仓位量等信息，下单成功后要重新判断这些信息，再进行下次下单判断

现在重新根据这些问题整理一个完整的修改需求 确保我们没有误解

start_m M5687000

发现根本问题了！
从API返回的期权合约信息可以看到，真实的期权合约格式包括两种：
USDT计价合约: ETH-25JUL25-2900-C-USDT (symbol) 对应 ETHUSDT-25JUL25-2900-C (displayName)
USDC计价合约: ETH-27JUN25-3100-C (symbol) 对应 ETHUSDC-27JUN25-3100-C (displayName)
而你的程序现在尝试订阅的格式是：ETHUSDT-3JUN25-2200-C，这个格式是错误的。


1、首先确认 bybit 返回的类型是否不确定 还是除了订阅的第一次是Snapshot 后面都是Delta？
2、我觉得需要在本地维护一个订单本缓存，能够实时反映最新的 完整盘口快照信息
3、通过 websocket返回的数据 维护一个完整的订单本缓存
4、如果 websocket 超过5 秒没有推送新的信息，应该首先检查 websocket 的连接状态，如果正常则允许使用旧的订单本缓存 
5、根据维护在本地的完整订单本缓存作为判断下单的依据
6、如果 websocket 超过 10 秒 没有推送新的信息，且连接状态正常，应每隔 10 秒通过 reset api 获取完整的订单本 与 缓存做对比 如果发现无误，则继续使用缓存，如果发现有误，则断开该订阅重新订阅


1、监控期权的时间阈值是 24 小时   为什么会监控ETH-3JUN25 的期权？ 程序判断的具体逻辑是怎样的

📊 期权合约持仓列表:
合约名称                           方向     仓位           价值              入场价格         标记价格         未结盈亏            已结盈亏           
--------------------------------------------------------------------------------------------------------------------------------------------
ETH-4JUN25-2700-C-USDT         Sell   0.1          -0.0514358      0.6          0.51435804   0.00856419      0   

触发订单修改

2095000_L_457228_002
2095000_place_003

8389765901598884864
8389765901598885120
 📋 [主循环] 任务 2095000: 正在下单流程中，跳过调整
2025/06/07 16:20:57 websocket_manager.go:187: 📨 WebSocket接收到消息: {"e":"ORDER_TRADE_UPDATE","T":1749284457561,"E":1749284457561,"o":{"s":"ETHUSDT","c":"2095000_L_457228_002","S":"BUY","o":"LIMIT","f":"GTX","q":"0.01","p":"2484.89","ap":"0","sp":"0","x":"NEW","X":"NEW","i":8389765901598885120,"l":"0","z":"0","L":"0","n":"0","N":"USDT","T":1749284457561,"t":0,"b":"48.87374","a":"0","m":false,"R":false,"wt":"CONTRACT_PRICE","ot":"LIMIT","ps":"BOTH","cp":false,"rp":"0","pP":false,"si":0,"ss":0,"V":"EXPIRE_MAKER","pm":"QUEUE","gtd":0}}


JSON解析问题：
原始JSON中的订单ID: 8389765901718906340
通过json.Unmarshal解析为float64后变成: 8389765901718905856
精度损失了 484 (8389765901718906340 - 8389765901718905856)
根本原因：
Go语言的json.Unmarshal默认将大整数解析为float64
IEEE 754双精度浮点数只有53位有效数字
币安的订单ID是19位数字，超过了float64的精确表示范围
超过9007199254740991（JavaScript安全整数上限）的整数会丢失精度
为什么WebSocket消息显示正确ID：
WebSocket接收到的原始JSON字符串是正确的
但在Go代码中通过json.Unmarshal解析时发生了精度损失