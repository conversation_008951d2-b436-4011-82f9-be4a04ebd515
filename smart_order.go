package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// 智能订单管理器
type SmartOrderManager struct {
	taskID               string
	symbol               string
	config               SmartOrderConfig
	tradingWS            *WebSocketManager
	userDataWS           *WebSocketManager
	listenKeyManager     *ListenKeyManager
	pendingRequests      map[string]*PendingRequest
	orderTracker         *OrderTracker
	isInOrderProcess     bool
	sessionAuthenticated bool // WebSocket会话认证状态
	requestSequence      int64
	mutex                sync.RWMutex
	stats                *SmartOrderStats
	stopChan             chan struct{} // 停止通道
	initResult           *InitResult   // 初始化结果
	initMutex            sync.RWMutex  // 初始化状态锁
}

// 创建智能订单管理器
func NewSmartOrderManager(taskID, symbol string, config SmartOrderConfig) *SmartOrderManager {
	log.Printf("🔧 [%s] NewSmartOrderManager 接收到的配置:", taskID)
	log.Printf("   - InitConfig.MaxRetries: %d", config.InitConfig.MaxRetries)
	log.Printf("   - InitConfig.RetryInterval: %v", config.InitConfig.RetryInterval)
	log.Printf("   - InitConfig.EnableAsyncInit: %t", config.InitConfig.EnableAsyncInit)
	log.Printf("   - InitConfig.EnableRollback: %t", config.InitConfig.EnableRollback)

	som := &SmartOrderManager{
		taskID:          taskID,
		symbol:          symbol,
		config:          config,
		pendingRequests: make(map[string]*PendingRequest),
		stats: &SmartOrderStats{
			LastUpdateTime: time.Now(),
		},
		stopChan: make(chan struct{}),
		initResult: &InitResult{
			Status:      InitStatusPending,
			Step:        InitStepTradingWebSocket,
			StartTime:   time.Now(),
			StepResults: make(map[InitStep]bool),
		},
	}

	log.Printf("🔧 [%s] SmartOrderManager 创建完成，存储的配置:", taskID)
	log.Printf("   - som.config.InitConfig.MaxRetries: %d", som.config.InitConfig.MaxRetries)

	return som
}

// 带重试的连接方法
func (som *SmartOrderManager) connectWithRetry(url string, wsType string, maxRetries int) (*WebSocketManager, error) {
	var lastErr error

	log.Printf("🔧 [%s] %s连接重试配置: maxRetries=%d", som.taskID, wsType, maxRetries)

	if maxRetries <= 0 {
		log.Printf("❌ [%s] %s连接重试次数无效: %d，使用默认值3", som.taskID, wsType, maxRetries)
		maxRetries = 3
	}

	for i := 0; i < maxRetries; i++ {
		log.Printf("🔄 [%s] %s连接尝试 %d/%d", som.taskID, wsType, i+1, maxRetries)

		ws := NewWebSocketManager(url, som.config)
		err := ws.Connect()

		if err == nil {
			log.Printf("✅ [%s] %s连接成功", som.taskID, wsType)
			return ws, nil
		}

		lastErr = err
		log.Printf("❌ [%s] %s连接失败 (尝试 %d/%d): %v", som.taskID, wsType, i+1, maxRetries, err)

		// 如果不是最后一次尝试，等待重试间隔
		if i < maxRetries-1 {
			retryDelay := time.Duration(i+1) * som.config.InitConfig.RetryInterval
			log.Printf("⏳ [%s] %s连接重试等待: %v", som.taskID, wsType, retryDelay)
			time.Sleep(retryDelay)
		}
	}

	return nil, fmt.Errorf("%s连接失败，已达最大重试次数 (%d): %v", wsType, maxRetries, lastErr)
}

// 带重试的认证方法
func (som *SmartOrderManager) authenticateWithRetry(maxRetries int) error {
	var lastErr error

	log.Printf("🔧 [%s] authenticateWithRetry 接收参数: maxRetries=%d", som.taskID, maxRetries)
	log.Printf("🔧 [%s] som.config.InitConfig.MaxRetries=%d", som.taskID, som.config.InitConfig.MaxRetries)

	if maxRetries <= 0 {
		log.Printf("❌ [%s] 认证重试次数无效: %d，使用默认值3", som.taskID, maxRetries)
		maxRetries = 3
	}

	for i := 0; i < maxRetries; i++ {
		log.Printf("🔐 [%s] WebSocket会话认证尝试 %d/%d", som.taskID, i+1, maxRetries)

		err := som.authenticateWebSocketSession()
		if err == nil {
			log.Printf("✅ [%s] WebSocket会话认证成功", som.taskID)
			som.sessionAuthenticated = true
			return nil
		}

		lastErr = err
		log.Printf("❌ [%s] WebSocket会话认证失败 (尝试 %d/%d): %v", som.taskID, i+1, maxRetries, err)

		// 如果不是最后一次尝试，等待重试间隔
		if i < maxRetries-1 {
			retryDelay := time.Duration(i+1) * som.config.InitConfig.RetryInterval
			log.Printf("⏳ [%s] 认证重试等待: %v", som.taskID, retryDelay)
			time.Sleep(retryDelay)
		}
	}

	log.Printf("⚠️ [%s] WebSocket会话认证失败，将使用单独签名模式: %v", som.taskID, lastErr)
	som.sessionAuthenticated = false
	return nil // 认证失败不是致命错误，可以使用签名模式
}

// 带重试的listenKey创建方法
func (som *SmartOrderManager) createListenKeyWithRetry(maxRetries int) (string, error) {
	var lastErr error

	log.Printf("🔧 [%s] createListenKeyWithRetry 接收参数: maxRetries=%d", som.taskID, maxRetries)
	log.Printf("🔧 [%s] som.config.InitConfig.MaxRetries=%d", som.taskID, som.config.InitConfig.MaxRetries)

	if maxRetries <= 0 {
		log.Printf("❌ [%s] listenKey创建重试次数无效: %d，使用默认值3", som.taskID, maxRetries)
		maxRetries = 3
	}

	for i := 0; i < maxRetries; i++ {
		log.Printf("🔑 [%s] 创建listenKey尝试 %d/%d", som.taskID, i+1, maxRetries)

		listenKey, err := som.createListenKey()
		if err == nil {
			log.Printf("✅ [%s] listenKey创建成功: %s...", som.taskID, listenKey[:8])
			return listenKey, nil
		}

		lastErr = err
		log.Printf("❌ [%s] 创建listenKey失败 (尝试 %d/%d): %v", som.taskID, i+1, maxRetries, err)

		// 如果不是最后一次尝试，等待重试间隔
		if i < maxRetries-1 {
			retryDelay := time.Duration(i+1) * som.config.InitConfig.RetryInterval
			log.Printf("⏳ [%s] listenKey创建重试等待: %v", som.taskID, retryDelay)
			time.Sleep(retryDelay)
		}
	}

	return "", fmt.Errorf("创建listenKey失败，已达最大重试次数 (%d): %v", maxRetries, lastErr)
}

// 分阶段初始化方法
func (som *SmartOrderManager) initTradingWebSocket() error {
	som.updateInitStep(InitStepTradingWebSocket)

	tradingURL := "wss://ws-fapi.binance.com/ws-fapi/v1"
	log.Printf("🔗 [%s] 准备连接交易WebSocket: %s", som.taskID, tradingURL)
	log.Printf("🔧 [%s] initTradingWebSocket 使用重试次数: %d", som.taskID, som.config.InitConfig.MaxRetries)

	ws, err := som.connectWithRetry(tradingURL, "交易WebSocket", som.config.InitConfig.MaxRetries)
	if err != nil {
		som.markStepFailed(InitStepTradingWebSocket, err)
		return err
	}

	som.tradingWS = ws

	// 设置交易WebSocket重连函数
	som.tradingWS.SetReconnectFunc(func() error {
		log.Printf("🔄 [%s] 执行交易WebSocket重连...", som.taskID)
		return som.tradingWS.Connect()
	})

	som.markStepSuccess(InitStepTradingWebSocket)
	return nil
}

func (som *SmartOrderManager) initAuthentication() error {
	som.updateInitStep(InitStepAuthentication)

	err := som.authenticateWithRetry(som.config.InitConfig.MaxRetries)
	if err != nil {
		som.markStepFailed(InitStepAuthentication, err)
		return err
	}

	som.markStepSuccess(InitStepAuthentication)
	return nil
}

func (som *SmartOrderManager) initListenKey() error {
	som.updateInitStep(InitStepListenKey)

	log.Printf("🔧 [%s] initListenKey 使用重试次数: %d", som.taskID, som.config.InitConfig.MaxRetries)
	listenKey, err := som.createListenKeyWithRetry(som.config.InitConfig.MaxRetries)
	if err != nil {
		som.markStepFailed(InitStepListenKey, err)
		return err
	}

	// 初始化listenKey管理器
	som.listenKeyManager = &ListenKeyManager{
		listenKey:   listenKey,
		createdAt:   time.Now(),
		lastRenewal: time.Now(),
	}

	som.markStepSuccess(InitStepListenKey)
	return nil
}

func (som *SmartOrderManager) initUserDataWebSocket() error {
	som.updateInitStep(InitStepUserDataWebSocket)

	userDataURL := fmt.Sprintf("wss://fstream.binance.com/ws/%s", som.listenKeyManager.listenKey)
	log.Printf("🔗 [%s] 准备连接用户数据WebSocket: %s", som.taskID, userDataURL)

	ws, err := som.connectWithRetry(userDataURL, "用户数据WebSocket", som.config.InitConfig.MaxRetries)
	if err != nil {
		som.markStepFailed(InitStepUserDataWebSocket, err)
		return err
	}

	som.userDataWS = ws

	// 设置用户数据WebSocket重连函数
	som.userDataWS.SetReconnectFunc(func() error {
		log.Printf("🔄 [%s] 执行用户数据WebSocket重连...", som.taskID)
		// 重连时需要重新创建listenKey
		newListenKey, err := som.createListenKey()
		if err != nil {
			log.Printf("❌ [%s] 重连时创建listenKey失败: %v", som.taskID, err)
			return err
		}
		log.Printf("🔑 [%s] 重连时创建新listenKey成功: %s...", som.taskID, newListenKey[:8])

		som.listenKeyManager.mutex.Lock()
		som.listenKeyManager.listenKey = newListenKey
		som.listenKeyManager.createdAt = time.Now()
		som.listenKeyManager.lastRenewal = time.Now()
		som.listenKeyManager.mutex.Unlock()

		// 更新URL并重连
		newURL := fmt.Sprintf("wss://fstream.binance.com/ws/%s", newListenKey)
		som.userDataWS.connection.url = newURL
		return som.userDataWS.Connect()
	})

	som.markStepSuccess(InitStepUserDataWebSocket)
	return nil
}

// 回滚机制
func (som *SmartOrderManager) rollback(failedStep InitStep) {
	if !som.config.InitConfig.EnableRollback {
		return
	}

	log.Printf("🔄 [%s] 开始回滚初始化步骤，失败步骤: %d", som.taskID, failedStep)

	// 按相反顺序回滚已完成的步骤
	steps := []InitStep{InitStepUserDataWebSocket, InitStepListenKey, InitStepAuthentication, InitStepTradingWebSocket}

	for _, step := range steps {
		if step >= failedStep {
			continue // 跳过失败步骤及之后的步骤
		}

		som.initMutex.RLock()
		success := som.initResult.StepResults[step]
		som.initMutex.RUnlock()

		if !success {
			continue // 跳过未成功的步骤
		}

		switch step {
		case InitStepUserDataWebSocket:
			if som.userDataWS != nil {
				log.Printf("🔄 [%s] 回滚: 关闭用户数据WebSocket", som.taskID)
				som.userDataWS.Disconnect()
				som.userDataWS = nil
			}
		case InitStepListenKey:
			if som.listenKeyManager != nil {
				log.Printf("🔄 [%s] 回滚: 删除listenKey", som.taskID)
				som.deleteListenKey()
				som.listenKeyManager = nil
			}
		case InitStepAuthentication:
			log.Printf("🔄 [%s] 回滚: 重置认证状态", som.taskID)
			som.sessionAuthenticated = false
		case InitStepTradingWebSocket:
			if som.tradingWS != nil {
				log.Printf("🔄 [%s] 回滚: 关闭交易WebSocket", som.taskID)
				som.tradingWS.Disconnect()
				som.tradingWS = nil
			}
		}
	}

	som.initMutex.Lock()
	som.initResult.Status = InitStatusRolledBack
	som.initMutex.Unlock()

	log.Printf("🔄 [%s] 初始化回滚完成", som.taskID)
}

// 更新初始化步骤
func (som *SmartOrderManager) updateInitStep(step InitStep) {
	som.initMutex.Lock()
	defer som.initMutex.Unlock()

	som.initResult.Step = step
	som.initResult.Status = InitStatusInProgress
}

// 标记步骤成功
func (som *SmartOrderManager) markStepSuccess(step InitStep) {
	som.initMutex.Lock()
	defer som.initMutex.Unlock()

	som.initResult.StepResults[step] = true
	log.Printf("✅ [%s] 初始化步骤成功: %d", som.taskID, step)
}

// 标记步骤失败
func (som *SmartOrderManager) markStepFailed(step InitStep, err error) {
	som.initMutex.Lock()
	defer som.initMutex.Unlock()

	som.initResult.StepResults[step] = false
	som.initResult.Error = err
	som.initResult.Status = InitStatusFailed
	log.Printf("❌ [%s] 初始化步骤失败: %d, 错误: %v", som.taskID, step, err)
}

// 异步初始化
func (som *SmartOrderManager) AsyncInitialize() <-chan error {
	errChan := make(chan error, 1)

	if !som.config.InitConfig.EnableAsyncInit {
		// 如果未启用异步初始化，直接同步执行
		go func() {
			defer close(errChan)
			errChan <- som.initializeWithRetry()
		}()
		return errChan
	}

	go func() {
		defer close(errChan)

		log.Printf("🚀 [%s] 开始异步初始化...", som.taskID)
		som.initMutex.Lock()
		som.initResult.Status = InitStatusInProgress
		som.initResult.StartTime = time.Now()
		som.initMutex.Unlock()

		err := som.initializeWithRetry()

		som.initMutex.Lock()
		som.initResult.EndTime = time.Now()
		som.initResult.Duration = som.initResult.EndTime.Sub(som.initResult.StartTime)
		if err != nil {
			som.initResult.Status = InitStatusFailed
			som.initResult.Error = err
		} else {
			som.initResult.Status = InitStatusSuccess
			som.initResult.Step = InitStepComplete
		}
		som.initMutex.Unlock()

		if err != nil {
			log.Printf("❌ [%s] 异步初始化失败: %v", som.taskID, err)
		} else {
			log.Printf("🎉 [%s] 异步初始化成功！耗时: %v", som.taskID, som.initResult.Duration)
		}

		errChan <- err
	}()

	return errChan
}

// 带重试的初始化流程
func (som *SmartOrderManager) initializeWithRetry() error {
	log.Printf("🔵 [%s] 智能订单管理器开始初始化...", som.taskID)

	// 定义初始化步骤
	steps := []func() error{
		som.initTradingWebSocket,
		som.initAuthentication,
		som.initListenKey,
		som.initUserDataWebSocket,
	}

	// 执行初始化步骤
	for i, step := range steps {
		err := step()
		if err != nil {
			log.Printf("❌ [%s] 初始化步骤 %d 失败: %v", som.taskID, i, err)

			// 如果启用回滚，执行回滚
			if som.config.InitConfig.EnableRollback {
				som.rollback(InitStep(i))
			}

			return fmt.Errorf("初始化失败在步骤 %d: %v", i, err)
		}
	}

	// 启动listenKey续期协程
	log.Printf("⏰ [%s] 启动listenKey续期协程...", som.taskID)
	go som.renewListenKeyLoop()

	log.Printf("🎉 [%s] 智能订单管理器初始化成功！", som.taskID)
	return nil
}

// 获取初始化状态
func (som *SmartOrderManager) GetInitStatus() InitResult {
	som.initMutex.RLock()
	defer som.initMutex.RUnlock()

	// 创建副本以避免并发访问问题
	result := *som.initResult
	return result
}

// 等待初始化完成
func (som *SmartOrderManager) WaitForInitialization(timeout time.Duration) error {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	timeoutChan := time.After(timeout)

	for {
		select {
		case <-ticker.C:
			status := som.GetInitStatus()
			switch status.Status {
			case InitStatusSuccess:
				return nil
			case InitStatusFailed:
				return fmt.Errorf("初始化失败: %v", status.Error)
			case InitStatusRolledBack:
				return fmt.Errorf("初始化已回滚")
			}
		case <-timeoutChan:
			return fmt.Errorf("初始化超时")
		}
	}
}

// 初始化WebSocket连接（兼容性方法）
func (som *SmartOrderManager) Initialize() error {
	if som.config.InitConfig.EnableAsyncInit {
		// 使用异步初始化
		errChan := som.AsyncInitialize()
		return <-errChan
	} else {
		// 使用同步初始化
		return som.initializeWithRetry()
	}
}

// 关闭智能订单管理器
func (som *SmartOrderManager) Close() error {
	som.mutex.Lock()
	defer som.mutex.Unlock()

	// 发送停止信号
	select {
	case <-som.stopChan:
		// 已经关闭
	default:
		close(som.stopChan)
	}

	// 关闭WebSocket连接
	if som.tradingWS != nil {
		som.tradingWS.Disconnect()
	}

	if som.userDataWS != nil {
		som.userDataWS.Disconnect()
	}

	// 删除listenKey
	if som.listenKeyManager != nil {
		som.deleteListenKey()
	}

	log.Printf("任务%s智能订单管理器已关闭", som.taskID)
	return nil
}

// 智能下单
func (som *SmartOrderManager) PlaceSmartOrder(side string, quantity decimal.Decimal, postOnly bool, isAuthenticated bool) error {
	log.Printf("🔵 [%s] 下单: %s %s postOnly=%t", som.taskID, side, quantity.String(), postOnly)

	som.mutex.Lock()
	defer som.mutex.Unlock()

	// 检查是否已在下单流程中
	if som.isInOrderProcess {
		log.Printf("🚨 [%s] 正在下单中，跳过重复下单", som.taskID)
		if som.orderTracker != nil {
			log.Printf("🚨 [%s] 活跃订单: %s", som.taskID, som.orderTracker.ClientOrderID)
		}
		return fmt.Errorf("任务%s正在下单流程中，跳过重复下单", som.taskID)
	}

	// 设置下单流程标识
	som.isInOrderProcess = true
	log.Printf("🔒 [下单入口2] [%s] 设置下单流程标识为 true", som.taskID)
	log.Printf("🔍 [下单入口2] [%s] 调用栈追踪 - PlaceSmartOrder 被调用", som.taskID)

	// 修正side参数格式（关键修复）
	var binanceSide string
	if side == "long" {
		binanceSide = "BUY"
	} else if side == "short" {
		binanceSide = "SELL"
	} else {
		binanceSide = side // 已经是正确格式
	}

	// 生成订单参数
	clientOrderID := som.generateClientOrderID(side)
	requestID := som.generateRequestID("place_order")
	timestamp := time.Now().UnixMilli()
	var orderedParams []OrderedParam

	// 根据PostOnly模式设置参数（按字母顺序排列）
	if postOnly {
		// PostOnly模式：使用priceMatch QUEUE，确保不会吃单成交
		orderedParams = []OrderedParam{
			{"newClientOrderId", clientOrderID},
			{"priceMatch", "QUEUE"},
			{"quantity", quantity.String()},
			{"side", binanceSide},
			{"symbol", som.getSymbolFromTaskID()},
			{"timeInForce", "GTX"},
			{"timestamp", timestamp},
			{"type", "LIMIT"},
		}
	} else {
		// 非PostOnly模式：使用priceMatch OPPONENT，允许立即成交
		orderedParams = []OrderedParam{
			{"newClientOrderId", clientOrderID},
			{"priceMatch", "OPPONENT"},
			{"quantity", quantity.String()},
			{"side", binanceSide},
			{"symbol", som.getSymbolFromTaskID()},
			{"timeInForce", "GTC"},
			{"timestamp", timestamp},
			{"type", "LIMIT"},
		}
	}

	// 转换为map用于签名和发送
	log.Printf("📋 [%s] 开始转换参数为map...", som.taskID)
	params := make(map[string]interface{})
	for _, param := range orderedParams {
		params[param.Key] = param.Value
	}
	log.Printf("📋 [%s] 参数转换完成: %+v", som.taskID, params)

	// 使用传入的WebSocket会话认证状态（避免锁竞争）
	log.Printf("🔐 [%s] 使用传入的WebSocket会话认证状态: %t", som.taskID, isAuthenticated)

	if isAuthenticated {
		// 已认证会话：只需要添加timestamp，不需要apiKey和signature
		log.Printf("✅ [%s] 使用已认证WebSocket会话下单（无需签名）", som.taskID)
		// timestamp已经在orderedParams中了，不需要额外处理
	} else {
		// 未认证会话：需要添加apiKey和签名
		log.Printf("🔑 [%s] 使用未认证WebSocket会话下单（需要签名）", som.taskID)

		// 按字母顺序插入apiKey（在newClientOrderId之前）
		log.Printf("🔑 [%s] 添加apiKey到参数中...", som.taskID)
		log.Printf("🔑 [%s] 检查binanceClient状态: %v", som.taskID, binanceClient != nil)
		if binanceClient == nil {
			log.Printf("❌ [%s] binanceClient为空！", som.taskID)
			som.isInOrderProcess = false
			return fmt.Errorf("binanceClient未初始化")
		}
		log.Printf("🔑 [%s] binanceClient.apiKey: %s", som.taskID, binanceClient.apiKey[:10]+"...")
		newOrderedParams := []OrderedParam{{"apiKey", binanceClient.apiKey}}
		newOrderedParams = append(newOrderedParams, orderedParams...)
		orderedParams = newOrderedParams
		log.Printf("🔑 [%s] 添加apiKey后的参数: %+v", som.taskID, orderedParams)

		// 生成WebSocket签名（使用有序参数）
		log.Printf("🔑 [%s] 开始生成WebSocket签名...", som.taskID)
		signature, err := binanceClient.GenerateWebSocketSignatureOrdered(orderedParams)
		if err != nil {
			log.Printf("❌ [%s] 生成签名失败: %v", som.taskID, err)
			som.isInOrderProcess = false
			return fmt.Errorf("生成签名失败: %v", err)
		}
		log.Printf("🔑 [%s] 签名生成成功: %s", som.taskID, signature)

		// 添加签名到有序参数和map中（signature在字母顺序中排在最后）
		log.Printf("🔑 [%s] 添加签名到参数中...", som.taskID)
		orderedParams = append(orderedParams, OrderedParam{"signature", signature})
		params["apiKey"] = binanceClient.apiKey
		params["signature"] = signature
		log.Printf("🔑 [%s] 最终参数: %+v", som.taskID, params)
	}

	// 创建请求
	request := WSRequest{
		ID:     requestID,
		Method: "order.place",
		Params: params,
	}
	// 记录待处理请求
	som.pendingRequests[requestID] = &PendingRequest{
		ID:            requestID,
		Type:          "place_order",
		Timestamp:     time.Now(),
		Data:          params,
		ClientOrderID: clientOrderID,
	}

	// 创建订单跟踪器
	if som.orderTracker != nil {
		log.Printf("🚨 [%s] 警告：已存在订单跟踪器！%s", som.taskID, som.orderTracker.ClientOrderID)
	}

	som.orderTracker = &OrderTracker{
		ClientOrderID: clientOrderID,
		State:         OrderStatePlacing,
		Side:          side,
		Quantity:      quantity,
		RequestTime:   time.Now(),
	}

	// 发送下单请求
	err := som.tradingWS.SendMessage(request)
	if err != nil {
		log.Printf("❌ [%s] 下单请求失败: %v", som.taskID, err)
		// 清理状态
		som.isInOrderProcess = false
		delete(som.pendingRequests, requestID)
		som.orderTracker = nil
		return fmt.Errorf("发送下单请求失败: %v", err)
	}

	// 启动超时检查
	go som.checkOrderTimeout(requestID, som.config.OrderTimeout)

	log.Printf("任务%s发送智能下单请求: %s %s %s",
		som.taskID, side, quantity.String(), clientOrderID)

	som.stats.TotalOrders++
	return nil
}

// 修改订单
func (som *SmartOrderManager) ModifyOrder(newPrice decimal.Decimal) error {
	log.Printf("🔧 [%s] ModifyOrder 开始: newPrice=%s", som.taskID, newPrice.String())

	som.mutex.RLock()
	if som.orderTracker == nil || som.orderTracker.State != OrderStateActive {
		som.mutex.RUnlock()
		log.Printf("❌ [%s] ModifyOrder 失败: 没有活跃订单可修改", som.taskID)
		return fmt.Errorf("没有活跃订单可修改")
	}

	// 检查修改限制
	if som.orderTracker.ModifyCount >= som.config.MaxModifyCount {
		som.mutex.RUnlock()
		log.Printf("❌ [%s] ModifyOrder 失败: 订单修改次数已达上限 (%d/%d)", som.taskID, som.orderTracker.ModifyCount, som.config.MaxModifyCount)
		return fmt.Errorf("订单修改次数已达上限")
	}

	if time.Since(som.orderTracker.LastModifyTime) < som.config.ModifyInterval {
		som.mutex.RUnlock()
		log.Printf("❌ [%s] ModifyOrder 失败: 修改间隔太短", som.taskID)
		return fmt.Errorf("修改间隔太短")
	}

	orderID := som.orderTracker.OrderID
	log.Printf("🔧 [%s] ModifyOrder: 使用权威订单ID=%d (来自ORDER_TRADE_UPDATE), 当前价格=%s, 新价格=%s", som.taskID, orderID, som.orderTracker.Price.String(), newPrice.String())
	som.mutex.RUnlock()

	som.mutex.Lock()
	defer som.mutex.Unlock()

	// 更新修改状态
	som.orderTracker.State = OrderStateModifying
	som.orderTracker.ModifyCount++
	som.orderTracker.LastModifyTime = time.Now()

	// 生成请求ID
	requestID := som.generateRequestID("modify_order")
	log.Printf("🔧 [%s] ModifyOrder: 生成请求ID=%s", som.taskID, requestID)

	// 构建修改参数 - 添加必需的side参数
	var binanceSide string
	if som.orderTracker.Side == "long" {
		binanceSide = "BUY"
	} else if som.orderTracker.Side == "short" {
		binanceSide = "SELL"
	} else {
		binanceSide = som.orderTracker.Side // 已经是正确格式
	}

	params := map[string]interface{}{
		"symbol":    som.getSymbolFromTaskID(),
		"side":      binanceSide,
		"orderId":   orderID,
		"quantity":  som.orderTracker.Quantity.String(),
		"price":     newPrice.String(),
		"timestamp": time.Now().UnixMilli(),
	}

	log.Printf("🔧 [%s] ModifyOrder: 构建参数完成: %+v", som.taskID, params)

	// 如果WebSocket会话未认证，需要添加apiKey和签名
	if !som.sessionAuthenticated {
		params["apiKey"] = binanceClient.apiKey

		// 生成WebSocket签名
		signature, err := binanceClient.GenerateWebSocketSignature(params)
		if err != nil {
			som.orderTracker.State = OrderStateActive
			som.orderTracker.ModifyCount--
			return fmt.Errorf("生成修改签名失败: %v", err)
		}
		params["signature"] = signature
	}

	// 创建请求
	request := WSRequest{
		ID:     requestID,
		Method: "order.modify",
		Params: params,
	}

	// 记录待处理请求
	som.pendingRequests[requestID] = &PendingRequest{
		ID:            requestID,
		Type:          "modify_order",
		Timestamp:     time.Now(),
		Data:          params,
		ClientOrderID: som.orderTracker.ClientOrderID,
	}

	// 发送修改请求
	err := som.tradingWS.SendMessage(request)
	if err != nil {
		// 恢复状态
		som.orderTracker.State = OrderStateActive
		som.orderTracker.ModifyCount--
		delete(som.pendingRequests, requestID)
		return fmt.Errorf("发送修改请求失败: %v", err)
	}

	// 启动超时检查
	go som.checkOrderTimeout(requestID, som.config.OrderTimeout)

	log.Printf("任务%s发送订单修改请求: 订单%d 新价格%s",
		som.taskID, orderID, newPrice.String())

	som.stats.TotalModifies++
	return nil
}

// 查询订单状态
func (som *SmartOrderManager) QueryOrderStatus(clientOrderID string) error {
	som.mutex.Lock()
	defer som.mutex.Unlock()

	// 生成请求ID
	requestID := som.generateRequestID("query_order")

	// 构建查询参数
	params := map[string]interface{}{
		"symbol":            som.getSymbolFromTaskID(),
		"origClientOrderId": clientOrderID,
		"timestamp":         time.Now().UnixMilli(),
	}

	// 如果WebSocket会话未认证，需要添加apiKey和签名
	if !som.sessionAuthenticated {
		params["apiKey"] = binanceClient.apiKey

		// 生成WebSocket签名
		signature, err := binanceClient.GenerateWebSocketSignature(params)
		if err != nil {
			delete(som.pendingRequests, requestID)
			return fmt.Errorf("生成查询签名失败: %v", err)
		}
		params["signature"] = signature
	}

	// 创建请求
	request := WSRequest{
		ID:     requestID,
		Method: "order.status",
		Params: params,
	}

	// 记录待处理请求
	som.pendingRequests[requestID] = &PendingRequest{
		ID:            requestID,
		Type:          "query_order",
		Timestamp:     time.Now(),
		Data:          params,
		ClientOrderID: clientOrderID,
	}

	// 发送查询请求
	err := som.tradingWS.SendMessage(request)
	if err != nil {
		delete(som.pendingRequests, requestID)
		return fmt.Errorf("发送查询请求失败: %v", err)
	}

	log.Printf("任务%s发送订单状态查询: %s", som.taskID, clientOrderID)
	return nil
}

// 生成clientOrderId（符合币安API要求：最多36个字符）
func (som *SmartOrderManager) generateClientOrderID(side string) string {
	som.requestSequence++
	// 使用更短的格式：任务ID_方向_时间戳后6位_序号
	// 例如: 2628000_L_874219_001
	shortSide := "L"
	if side == "short" || side == "SELL" {
		shortSide = "S"
	}
	timestamp := time.Now().UnixMilli() % 1000000 // 取时间戳后6位
	return fmt.Sprintf("%s_%s_%06d_%03d", som.taskID, shortSide, timestamp, som.requestSequence)
}

// 生成请求ID（符合币安API要求：最多36个字符）
func (som *SmartOrderManager) generateRequestID(operation string) string {
	som.requestSequence++
	// 使用更短的格式：任务ID_操作类型_序号
	// 例如: 2628000_place_001, 2628000_modify_002
	return fmt.Sprintf("%s_%s_%03d", som.taskID, som.getShortOperationName(operation), som.requestSequence)
}

// 获取操作的短名称
func (som *SmartOrderManager) getShortOperationName(operation string) string {
	switch operation {
	case "place_order":
		return "place"
	case "modify_order":
		return "modify"
	case "query_order":
		return "query"
	case "cancel_order":
		return "cancel"
	default:
		return "op"
	}
}

// 从任务ID获取交易对
func (som *SmartOrderManager) getSymbolFromTaskID() string {
	log.Printf("📋 [%s] 获取交易对: %s", som.taskID, som.symbol)
	return som.symbol
}

// 获取当前市场价格
// 根据任务类型、方向和开平仓来判断使用买一价还是卖一价
func (som *SmartOrderManager) getCurrentMarketPrice(direction string) float64 {
	// 从全局市场数据管理器获取当前价格
	if globalMarketDataManager == nil {
		return 0
	}

	// 获取订单簿数据
	orderBook := globalMarketDataManager.GetOrderBook(som.symbol)
	if orderBook == nil {
		return 0
	}

	// 检查订单簿数据是否有效
	if len(orderBook.Bids) == 0 || len(orderBook.Asks) == 0 {
		return 0
	}

	bidPrice := orderBook.Bids[0].Price // 买一价
	askPrice := orderBook.Asks[0].Price // 卖一价

	// 根据方向选择价格
	switch direction {
	case "long":
		// 开多仓：需要买入，使用卖一价（ask）
		askFloat, _ := askPrice.Float64()
		return askFloat
	case "short":
		// 开空仓：需要卖出，使用买一价（bid）
		bidFloat, _ := bidPrice.Float64()
		return bidFloat
	default:
		// 默认返回中间价
		midPrice := bidPrice.Add(askPrice).Div(decimal.NewFromInt(2))
		midFloat, _ := midPrice.Float64()
		return midFloat
	}
}

// 检查订单超时
func (som *SmartOrderManager) checkOrderTimeout(requestID string, timeout time.Duration) {
	log.Printf("⏰ [超时检查] [%s] 启动超时检查: RequestID=%s, 超时时间=%v", som.taskID, requestID, timeout)
	time.Sleep(timeout)

	som.mutex.RLock()
	request, exists := som.pendingRequests[requestID]
	som.mutex.RUnlock()

	if !exists {
		// 请求已处理
		log.Printf("✅ [超时检查] [%s] 请求%s已正常处理，无需超时处理", som.taskID, requestID)
		return
	}

	log.Printf("⚠️ [超时处理] [%s] 请求%s超时，主动查询状态", som.taskID, requestID)
	log.Printf("⚠️ [超时处理] [%s] 超时请求详情: Type=%s, ClientOrderID=%s", som.taskID, request.Type, request.ClientOrderID)

	// 根据请求类型处理超时
	switch request.Type {
	case "place_order":
		log.Printf("⚠️ [超时处理] [%s] 下单请求超时，查询订单状态: %s", som.taskID, request.ClientOrderID)
		som.QueryOrderStatus(request.ClientOrderID)
	case "modify_order":
		log.Printf("⚠️ [超时处理] [%s] 修改请求超时，查询订单状态: %s", som.taskID, request.ClientOrderID)
		som.QueryOrderStatus(request.ClientOrderID)
	default:
		log.Printf("⚠️ [超时处理] [%s] 未知请求类型超时: %s", som.taskID, request.Type)
	}
}

// 创建listenKey
func (som *SmartOrderManager) createListenKey() (string, error) {
	// 调用币安API创建listenKey
	if binanceClient == nil {
		return "", fmt.Errorf("币安客户端未初始化")
	}

	listenKey, err := binanceClient.CreateListenKey()
	if err != nil {
		return "", fmt.Errorf("创建listenKey失败: %v", err)
	}

	return listenKey, nil
}

// 删除listenKey
func (som *SmartOrderManager) deleteListenKey() error {
	if som.listenKeyManager == nil {
		return nil
	}

	som.listenKeyManager.mutex.RLock()
	listenKey := som.listenKeyManager.listenKey
	som.listenKeyManager.mutex.RUnlock()

	if listenKey == "" {
		return nil
	}

	// 调用币安API删除listenKey
	if binanceClient == nil {
		return fmt.Errorf("币安客户端未初始化")
	}

	err := binanceClient.DeleteListenKey(listenKey)
	if err != nil {
		return fmt.Errorf("删除listenKey失败: %v", err)
	}

	return nil
}

// listenKey续期循环 - 优化为更频繁的续期
func (som *SmartOrderManager) renewListenKeyLoop() {
	// 使用更频繁的续期间隔：20分钟（币安listenKey有效期60分钟）
	renewalInterval := 20 * time.Minute
	ticker := time.NewTicker(renewalInterval)
	defer ticker.Stop()

	log.Printf("🔄 [%s] listenKey续期循环启动，续期间隔: %v", som.taskID, renewalInterval)

	for {
		select {
		case <-ticker.C:
			if err := som.renewListenKey(); err != nil {
				log.Printf("❌ [%s] listenKey续期失败: %v", som.taskID, err)

				// 续期失败时，尝试重新创建listenKey
				log.Printf("🔄 [%s] 尝试重新创建listenKey...", som.taskID)
				newListenKey, createErr := som.createListenKey()
				if createErr != nil {
					log.Printf("❌ [%s] 重新创建listenKey失败: %v", som.taskID, createErr)
					continue
				}

				// 更新listenKey
				som.listenKeyManager.mutex.Lock()
				oldListenKey := som.listenKeyManager.listenKey
				som.listenKeyManager.listenKey = newListenKey
				som.listenKeyManager.createdAt = time.Now()
				som.listenKeyManager.lastRenewal = time.Now()
				som.listenKeyManager.mutex.Unlock()

				log.Printf("✅ [%s] listenKey重新创建成功: %s... (替换: %s...)",
					som.taskID, newListenKey[:8], oldListenKey[:8])

				// 重新连接用户数据WebSocket
				if som.userDataWS != nil {
					newURL := fmt.Sprintf("wss://fstream.binance.com/ws/%s", newListenKey)
					som.userDataWS.connection.url = newURL
					som.userDataWS.triggerReconnect()
				}
			}
		case <-som.stopChan:
			log.Printf("🛑 [%s] listenKey续期循环停止", som.taskID)
			return
		}
	}
}

// 续期listenKey
func (som *SmartOrderManager) renewListenKey() error {
	som.listenKeyManager.mutex.Lock()
	defer som.listenKeyManager.mutex.Unlock()

	if binanceClient == nil {
		return fmt.Errorf("币安客户端未初始化")
	}

	// 调用币安API续期listenKey
	err := binanceClient.RenewListenKey(som.listenKeyManager.listenKey)
	if err != nil {
		return fmt.Errorf("续期listenKey失败: %v", err)
	}

	som.listenKeyManager.lastRenewal = time.Now()
	log.Printf("任务%s listenKey续期成功", som.taskID)
	return nil
}

// 获取统计信息
func (som *SmartOrderManager) GetStats() *SmartOrderStats {
	som.mutex.RLock()
	defer som.mutex.RUnlock()

	// 创建副本返回
	stats := *som.stats
	return &stats
}

// 重置下单流程标识
func (som *SmartOrderManager) ResetOrderProcess() {
	som.mutex.Lock()
	defer som.mutex.Unlock()
	som.isInOrderProcess = false
}

// 检查是否在下单流程中
func (som *SmartOrderManager) IsInOrderProcess() bool {
	som.mutex.RLock()
	defer som.mutex.RUnlock()
	return som.isInOrderProcess
}

// 检查WebSocket会话是否已认证（内部使用）
func (som *SmartOrderManager) isSessionAuthenticated() bool {
	log.Printf("🔐 [%s] 检查WebSocket会话认证状态...", som.taskID)
	som.mutex.RLock()
	defer som.mutex.RUnlock()
	log.Printf("🔐 [%s] 会话认证状态: %t", som.taskID, som.sessionAuthenticated)
	return som.sessionAuthenticated
}

// 检查WebSocket会话是否已认证（公开方法，用于外部调用）
func (som *SmartOrderManager) IsSessionAuthenticated() bool {
	som.mutex.RLock()
	defer som.mutex.RUnlock()
	return som.sessionAuthenticated
}

// 设置WebSocket会话认证状态
func (som *SmartOrderManager) setSessionAuthenticated(authenticated bool) {
	som.mutex.Lock()
	defer som.mutex.Unlock()
	som.sessionAuthenticated = authenticated
}

// WebSocket会话认证
func (som *SmartOrderManager) authenticateWebSocketSession() error {
	if binanceClient == nil {
		return fmt.Errorf("币安客户端未初始化")
	}

	// 构建登录请求参数
	timestamp := time.Now().UnixMilli()
	loginParams := map[string]interface{}{
		"apiKey":    binanceClient.apiKey,
		"timestamp": timestamp,
	}

	// 生成WebSocket签名（按字母顺序排序）
	signature, err := binanceClient.GenerateWebSocketSignature(loginParams)
	if err != nil {
		return fmt.Errorf("生成登录签名失败: %v", err)
	}
	loginParams["signature"] = signature

	// 生成请求ID（符合币安API要求：最多36个字符）
	som.requestSequence++
	requestID := fmt.Sprintf("%s_login_%03d", som.taskID, som.requestSequence)

	// 创建登录请求
	loginRequest := WSRequest{
		ID:     requestID,
		Method: "session.logon",
		Params: loginParams,
	}

	// 发送登录请求
	err = som.tradingWS.SendMessage(loginRequest)
	if err != nil {
		return fmt.Errorf("发送登录请求失败: %v", err)
	}

	log.Printf("任务%s发送WebSocket会话登录请求", som.taskID)

	// TODO: 这里应该等待登录响应并验证结果
	// 暂时假设登录成功，实际应该监听响应消息

	return nil
}

// WebSocket会话认证
func (som *SmartOrderManager) AuthenticateSession() error {
	timestamp := time.Now().UnixMilli()

	// 构建登录参数
	loginParams := map[string]interface{}{
		"apiKey":    binanceClient.apiKey,
		"timestamp": timestamp,
	}

	// 生成WebSocket签名
	signature, err := binanceClient.GenerateWebSocketSignature(loginParams)
	if err != nil {
		return fmt.Errorf("生成登录签名失败: %v", err)
	}
	loginParams["signature"] = signature

	// 构建登录请求
	loginRequest := WSRequest{
		ID:     som.generateRequestID("session_login"),
		Method: "session.logon",
		Params: loginParams,
	}

	// 发送登录请求
	err = som.tradingWS.SendMessage(loginRequest)
	if err != nil {
		return fmt.Errorf("发送登录请求失败: %v", err)
	}

	log.Printf("任务%s发送WebSocket会话认证请求", som.taskID)
	return nil
}
