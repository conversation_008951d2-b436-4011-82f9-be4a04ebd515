#!/usr/bin/env python3
"""
验证正确的私钥是否对应API密钥
"""

import base64
import aiohttp
import asyncio
import time
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ed25519

async def verify_correct_key():
    # API密钥
    api_key = "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso"
    
    # 正确的PEM私钥
    pem_private_key = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIGdEcwhgJGBAxjiYJ4gKLVLhTrznLRjupF3aZLdkFdXo
-----END PRIVATE KEY-----"""

    # 加载私钥
    private_key = serialization.load_pem_private_key(
        pem_private_key.encode('utf-8'),
        password=None
    )
    
    # 获取私钥Hex
    private_key_hex = private_key.private_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PrivateFormat.Raw,
        encryption_algorithm=serialization.NoEncryption()
    ).hex()
    
    print(f"API密钥: {api_key}")
    print(f"私钥 (Hex): {private_key_hex}")
    
    # 测试账户信息查询
    timestamp = int(time.time() * 1000)
    query_string = f"timestamp={timestamp}"
    
    # 生成签名
    signature_bytes = private_key.sign(query_string.encode('utf-8'))
    signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
    
    print(f"查询字符串: {query_string}")
    print(f"签名: {signature_base64}")
    
    # 发送请求
    url = f"https://fapi.binance.com/fapi/v2/account?{query_string}&signature={signature_base64}"
    headers = {'X-MBX-APIKEY': api_key}
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                result = await response.json()
                print(f"响应状态: {response.status}")
                
                if response.status == 200:
                    print("✅ 验证成功！API密钥和私钥完全匹配！")
                    print(f"账户总余额: {result.get('totalWalletBalance', 'N/A')} USDT")
                    return True
                else:
                    print(f"❌ 验证失败: {result}")
                    return False
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

async def main():
    print("🔍 验证正确的私钥和API密钥匹配性")
    print("=" * 60)
    
    success = await verify_correct_key()
    
    if success:
        print("\n🎉 密钥验证成功！")
        print("现在可以确认embedded_config.go中的配置是正确的")
    else:
        print("\n❌ 密钥验证失败")

if __name__ == "__main__":
    asyncio.run(main())
