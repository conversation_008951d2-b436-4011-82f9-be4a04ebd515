package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"
	"gopkg.in/yaml.v2"
)

// SimpleConfig 简单配置结构
type SimpleConfig struct {
	Binance struct {
		APIKey    string `yaml:"api_key"`
		SecretKey string `yaml:"secret_key"`
	} `yaml:"binance"`
}

func main() {
	fmt.Println("🔍 币安统一账户API测试程序")
	fmt.Println("=" + fmt.Sprintf("%*s", 49, "") + "=")

	// 读取配置
	data, err := os.ReadFile("config.yaml")
	if err != nil {
		log.Fatalf("❌ 读取配置文件失败: %v", err)
	}

	var config SimpleConfig
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		log.Fatalf("❌ 解析配置文件失败: %v", err)
	}

	apiKey := config.Binance.APIKey
	secretKey := config.Binance.SecretKey

	fmt.Printf("🔑 API密钥: %s...\n", apiKey[:8])
	fmt.Println()

	// 测试1: 基础连接测试
	fmt.Println("📊 测试1: 基础连接测试")
	testBasicConnection(apiKey, secretKey)
	fmt.Println()

	// 测试2: 现货API测试
	fmt.Println("📊 测试2: 现货API测试")
	testSpotAPI(apiKey, secretKey)
	fmt.Println()

	// 测试3: 期货API测试
	fmt.Println("📊 测试3: 期货API测试")
	testFuturesAPI(apiKey, secretKey)
	fmt.Println()

	// 测试4: 杠杆API测试
	fmt.Println("📊 测试4: 杠杆API测试")
	testMarginAPI(apiKey, secretKey)
	fmt.Println()

	// 测试5: 期货下单测试
	fmt.Println("📊 测试5: 期货下单测试")
	testFuturesTrading(apiKey, secretKey)
	fmt.Println()

	fmt.Println("=" + fmt.Sprintf("%*s", 49, "") + "=")
	fmt.Println("🎯 测试完成！")
}

// testBasicConnection 测试基础连接
func testBasicConnection(apiKey, secretKey string) {
	// 测试期货交易所信息
	futuresClient := binance.NewFuturesClient(apiKey, secretKey)
	exchangeInfo, err := futuresClient.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 期货交易所信息: %v\n", err)
	} else {
		fmt.Printf("✅ 期货交易所信息: 成功 (交易对数量: %d)\n", len(exchangeInfo.Symbols))
	}

	// 测试现货交易所信息
	spotClient := binance.NewClient(apiKey, secretKey)
	spotExchangeInfo, err := spotClient.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 现货交易所信息: %v\n", err)
	} else {
		fmt.Printf("✅ 现货交易所信息: 成功 (交易对数量: %d)\n", len(spotExchangeInfo.Symbols))
	}
}

// testSpotAPI 测试现货API
func testSpotAPI(apiKey, secretKey string) {
	spotClient := binance.NewClient(apiKey, secretKey)

	// 测试现货账户信息
	account, err := spotClient.NewGetAccountService().Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 现货账户信息: %v\n", err)
		return
	}

	fmt.Printf("✅ 现货账户信息: 成功\n")
	fmt.Printf("   账户类型: %s\n", account.AccountType)
	fmt.Printf("   权限: %v\n", account.Permissions)

	// 显示余额
	fmt.Println("   余额信息:")
	for _, balance := range account.Balances {
		if balance.Free != "0" || balance.Locked != "0" {
			fmt.Printf("     %s: 可用=%s, 锁定=%s\n", balance.Asset, balance.Free, balance.Locked)
		}
	}
}

// testFuturesAPI 测试期货API
func testFuturesAPI(apiKey, secretKey string) {
	futuresClient := binance.NewFuturesClient(apiKey, secretKey)

	// 测试1: 期货账户信息
	account, err := futuresClient.NewGetAccountService().Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 期货账户信息: %v\n", err)
	} else {
		fmt.Printf("✅ 期货账户信息: 成功\n")
		fmt.Printf("   总余额: %s USDT\n", account.TotalWalletBalance)
		fmt.Printf("   可用余额: %s USDT\n", account.AvailableBalance)
	}

	// 测试2: 期货余额信息
	balances, err := futuresClient.NewGetBalanceService().Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 期货余额信息: %v\n", err)
	} else {
		fmt.Printf("✅ 期货余额信息: 成功\n")
		for _, balance := range balances {
			if balance.Balance != "0" {
				fmt.Printf("   %s: %s\n", balance.Asset, balance.Balance)
			}
		}
	}

	// 测试3: 期货仓位信息
	positions, err := futuresClient.NewGetPositionRiskService().Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 期货仓位信息: %v\n", err)
	} else {
		fmt.Printf("✅ 期货仓位信息: 成功 (仓位数量: %d)\n", len(positions))
		activeCount := 0
		for _, pos := range positions {
			if pos.PositionAmt != "0" {
				activeCount++
				fmt.Printf("   %s: 数量=%s, 价格=%s\n", pos.Symbol, pos.PositionAmt, pos.MarkPrice)
			}
		}
		fmt.Printf("   活跃仓位: %d\n", activeCount)
	}
}

// testMarginAPI 测试杠杆API
func testMarginAPI(apiKey, secretKey string) {
	spotClient := binance.NewClient(apiKey, secretKey)

	// 测试杠杆账户信息
	marginAccount, err := spotClient.NewGetMarginAccountService().Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 杠杆账户信息: %v\n", err)
		return
	}

	fmt.Printf("✅ 杠杆账户信息: 成功\n")
	fmt.Printf("   总资产: %s BTC\n", marginAccount.TotalAssetOfBTC)
	fmt.Printf("   总负债: %s BTC\n", marginAccount.TotalLiabilityOfBTC)
	fmt.Printf("   净资产: %s BTC\n", marginAccount.TotalNetAssetOfBTC)

	// 显示资产
	fmt.Println("   资产信息:")
	for _, asset := range marginAccount.UserAssets {
		if asset.Free != "0" || asset.Locked != "0" || asset.Borrowed != "0" {
			fmt.Printf("     %s: 可用=%s, 锁定=%s, 借贷=%s\n",
				asset.Asset, asset.Free, asset.Locked, asset.Borrowed)
		}
	}
}

// testFuturesTrading 测试期货交易
func testFuturesTrading(apiKey, secretKey string) {
	futuresClient := binance.NewFuturesClient(apiKey, secretKey)

	// 获取ETHUSDT价格
	depth, err := futuresClient.NewDepthService().Symbol("ETHUSDT").Limit(1).Do(context.Background())
	if err != nil {
		fmt.Printf("❌ 获取价格失败: %v\n", err)
		return
	}

	if len(depth.Bids) == 0 {
		fmt.Println("❌ 没有价格数据")
		return
	}

	bidPrice := depth.Bids[0].Price
	fmt.Printf("📊 当前ETHUSDT价格: %s\n", bidPrice)

	// 计算测试价格（市价的10%，绝对不会成交）
	testPrice := fmt.Sprintf("%.2f", mustParseFloat(bidPrice)*0.1)
	testQuantity := "0.001"

	fmt.Printf("🧪 测试下单: 价格=%s, 数量=%s\n", testPrice, testQuantity)

	// 尝试下单
	order, err := futuresClient.NewCreateOrderService().
		Symbol("ETHUSDT").
		Side(futures.SideTypeBuy).
		Type(futures.OrderTypeLimit).
		TimeInForce(futures.TimeInForceTypeGTC).
		Quantity(testQuantity).
		Price(testPrice).
		Do(context.Background())

	if err != nil {
		fmt.Printf("❌ 下单失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 下单成功: 订单ID=%d\n", order.OrderID)

	// 立即撤销
	_, err = futuresClient.NewCancelOrderService().
		Symbol("ETHUSDT").
		OrderID(order.OrderID).
		Do(context.Background())

	if err != nil {
		fmt.Printf("⚠️ 撤单失败: %v\n", err)
	} else {
		fmt.Println("✅ 订单已撤销")
	}
}

// mustParseFloat 解析浮点数
func mustParseFloat(s string) float64 {
	var f float64
	fmt.Sscanf(s, "%f", &f)
	return f
}
