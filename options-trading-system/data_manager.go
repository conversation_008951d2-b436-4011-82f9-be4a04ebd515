package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// MonitorData 监控任务数据结构（用于序列化）
type MonitorData struct {
	Monitors map[string]*MonitorTask `json:"monitors"`
	Meta     DataMeta                `json:"meta"`
}

// PositionData 持仓数据结构（用于序列化）
type PositionData struct {
	Positions map[string]*PositionTracker `json:"positions"`
	Meta      DataMeta                     `json:"meta"`
}

// EventData 事件数据结构（用于序列化）
type EventData struct {
	Events map[string][]PositionEvent `json:"events"` // key为合约名称，value为该合约的事件列表
	Meta   DataMeta                    `json:"meta"`
}

// DataMeta 数据元信息
type DataMeta struct {
	Version   string    `json:"version"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Count     int       `json:"count"`
}

// NewDataManager 创建新的数据管理器
func NewDataManager() *DataManager {
	dataDir := "data"
	dm := &DataManager{
		dataDir:       dataDir,
		monitorsFile:  filepath.Join(dataDir, "monitors.json"),
		positionsFile: filepath.Join(dataDir, "positions.json"),
		eventsFile:    filepath.Join(dataDir, "events.json"),
		backupDir:     filepath.Join(dataDir, "backup"),
		autoSave:      true,
		saveChannel:   make(chan struct{}, 100), // 缓冲通道，避免阻塞
		saveInterval:  5 * time.Minute,          // 默认5分钟自动保存
		backupEnabled: false,                    // 默认关闭备份
	}

	// 确保数据目录存在
	if err := dm.ensureDirectories(); err != nil {
		log.Printf("❌ 创建数据目录失败: %v", err)
	}

	return dm
}

// ensureDirectories 确保数据目录存在
func (dm *DataManager) ensureDirectories() error {
	dirs := []string{dm.dataDir, dm.backupDir}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录 %s 失败: %v", dir, err)
		}
	}
	return nil
}

// LoadMonitors 加载监控任务数据
func (dm *DataManager) LoadMonitors() (map[string]*MonitorTask, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 检查文件是否存在
	if _, err := os.Stat(dm.monitorsFile); os.IsNotExist(err) {
		log.Println("📄 监控任务数据文件不存在，返回空任务列表")
		return make(map[string]*MonitorTask), nil
	}

	// 读取文件
	data, err := os.ReadFile(dm.monitorsFile)
	if err != nil {
		return nil, fmt.Errorf("读取监控任务文件失败: %v", err)
	}

	// 解析JSON
	var monitorData MonitorData
	if err := json.Unmarshal(data, &monitorData); err != nil {
		return nil, fmt.Errorf("解析监控任务数据失败: %v", err)
	}

	// 恢复任务的运行时状态
	for _, task := range monitorData.Monitors {
		// 重新初始化不能序列化的字段
		task.StopChan = make(chan struct{})
		task.mutex = sync.RWMutex{}

		// 如果任务之前是运行状态，现在设为停止状态（需要手动重启）
		if task.Status == StatusRunning {
			task.Status = StatusStopped
		}
	}

	log.Printf("✅ 成功加载 %d 个监控任务", len(monitorData.Monitors))
	return monitorData.Monitors, nil
}

// SaveMonitors 保存监控任务数据
func (dm *DataManager) SaveMonitors(monitors map[string]*MonitorTask) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	// 备份现有文件（如果启用备份）
	if dm.backupEnabled {
		if err := dm.backupFile(dm.monitorsFile); err != nil {
			log.Printf("⚠️  备份监控任务文件失败: %v", err)
		}
	}

	// 准备数据
	monitorData := MonitorData{
		Monitors: monitors,
		Meta: DataMeta{
			Version:   "1.0",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Count:     len(monitors),
		},
	}

	// 序列化
	data, err := json.MarshalIndent(monitorData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化监控任务数据失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(dm.monitorsFile, data, 0644); err != nil {
		return fmt.Errorf("写入监控任务文件失败: %v", err)
	}

	log.Printf("💾 监控任务数据已保存 (%d个任务)", len(monitors))
	return nil
}

// LoadPositions 加载持仓数据
func (dm *DataManager) LoadPositions() (map[string]*PositionTracker, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 检查文件是否存在
	if _, err := os.Stat(dm.positionsFile); os.IsNotExist(err) {
		log.Println("📄 持仓数据文件不存在，返回空持仓列表")
		return make(map[string]*PositionTracker), nil
	}

	// 读取文件
	data, err := os.ReadFile(dm.positionsFile)
	if err != nil {
		return nil, fmt.Errorf("读取持仓文件失败: %v", err)
	}

	// 解析JSON
	var positionData PositionData
	if err := json.Unmarshal(data, &positionData); err != nil {
		return nil, fmt.Errorf("解析持仓数据失败: %v", err)
	}

	// 恢复持仓的运行时状态
	for _, position := range positionData.Positions {
		// 重新初始化不能序列化的字段
		position.mutex = sync.RWMutex{}
	}

	log.Printf("✅ 成功加载 %d 个持仓", len(positionData.Positions))
	return positionData.Positions, nil
}

// SavePositions 保存持仓数据
func (dm *DataManager) SavePositions(positions map[string]*PositionTracker) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	// 备份现有文件（如果启用备份）
	if dm.backupEnabled {
		if err := dm.backupFile(dm.positionsFile); err != nil {
			log.Printf("⚠️  备份持仓文件失败: %v", err)
		}
	}

	// 准备数据
	positionData := PositionData{
		Positions: positions,
		Meta: DataMeta{
			Version:   "1.0",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Count:     len(positions),
		},
	}

	// 序列化
	data, err := json.MarshalIndent(positionData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化持仓数据失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(dm.positionsFile, data, 0644); err != nil {
		return fmt.Errorf("写入持仓文件失败: %v", err)
	}

	log.Printf("💾 持仓数据已保存 (%d个持仓)", len(positions))
	return nil
}

// LoadEvents 加载事件数据
func (dm *DataManager) LoadEvents() (map[string][]PositionEvent, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 检查文件是否存在
	if _, err := os.Stat(dm.eventsFile); os.IsNotExist(err) {
		log.Println("📄 事件数据文件不存在，返回空事件列表")
		return make(map[string][]PositionEvent), nil
	}

	// 读取文件
	data, err := os.ReadFile(dm.eventsFile)
	if err != nil {
		return nil, fmt.Errorf("读取事件文件失败: %v", err)
	}

	// 解析JSON
	var eventData EventData
	if err := json.Unmarshal(data, &eventData); err != nil {
		return nil, fmt.Errorf("解析事件数据失败: %v", err)
	}

	log.Printf("✅ 成功加载 %d 个合约的事件数据", len(eventData.Events))
	return eventData.Events, nil
}

// SaveEvents 保存事件数据
func (dm *DataManager) SaveEvents(events map[string][]PositionEvent) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	// 备份现有文件（如果启用备份）
	if dm.backupEnabled {
		if err := dm.backupFile(dm.eventsFile); err != nil {
			log.Printf("⚠️  备份事件文件失败: %v", err)
		}
	}

	// 计算总事件数
	totalEvents := 0
	for _, eventList := range events {
		totalEvents += len(eventList)
	}

	// 准备数据
	eventData := EventData{
		Events: events,
		Meta: DataMeta{
			Version:   "1.0",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Count:     totalEvents,
		},
	}

	// 序列化
	data, err := json.MarshalIndent(eventData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(dm.eventsFile, data, 0644); err != nil {
		return fmt.Errorf("写入事件文件失败: %v", err)
	}

	log.Printf("💾 事件数据已保存 (%d个事件)", totalEvents)
	return nil
}

// backupFile 备份文件
func (dm *DataManager) backupFile(filePath string) error {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil // 文件不存在，无需备份
	}

	// 生成备份文件名
	fileName := filepath.Base(filePath)
	backupFileName := fmt.Sprintf("%s.%s.bak", fileName, time.Now().Format("20060102_150405"))
	backupPath := filepath.Join(dm.backupDir, backupFileName)

	// 复制文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	return os.WriteFile(backupPath, data, 0644)
}

// TriggerSave 触发保存
func (dm *DataManager) TriggerSave() {
	if !dm.autoSave {
		return
	}

	select {
	case dm.saveChannel <- struct{}{}:
		// 保存信号已发送
	default:
		// 通道满了，跳过这次保存
	}
}

// StartAutoSave 启动自动保存
func (dm *DataManager) StartAutoSave(monitorManager *MonitorManager, positionManager *PositionManager) {
	if !dm.autoSave {
		return
	}

	log.Printf("🔄 自动保存已启动 (%v间隔)", dm.saveInterval)

	go func() {
		ticker := time.NewTicker(dm.saveInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 定时保存
				dm.saveAllData(monitorManager, positionManager)
			case <-dm.saveChannel:
				// 触发保存
				dm.saveAllData(monitorManager, positionManager)
			}
		}
	}()
}

// saveAllData 保存所有数据
func (dm *DataManager) saveAllData(monitorManager *MonitorManager, positionManager *PositionManager) {
	// 获取所有监控任务
	monitors := monitorManager.GetAllMonitors()

	// 获取所有持仓
	positions := positionManager.GetAllPositions()

	// 提取事件数据
	events := make(map[string][]PositionEvent)
	for contractName, position := range positions {
		position.mutex.RLock()
		events[contractName] = make([]PositionEvent, len(position.Events))
		copy(events[contractName], position.Events)
		position.mutex.RUnlock()
	}

	// 保存监控任务数据
	if err := dm.SaveMonitors(monitors); err != nil {
		log.Printf("❌ 保存监控任务数据失败: %v", err)
	}

	// 保存持仓数据
	if err := dm.SavePositions(positions); err != nil {
		log.Printf("❌ 保存持仓数据失败: %v", err)
	}

	// 保存事件数据
	if err := dm.SaveEvents(events); err != nil {
		log.Printf("❌ 保存事件数据失败: %v", err)
	}
}

// SetSaveInterval 设置自动保存间隔
func (dm *DataManager) SetSaveInterval(interval time.Duration) {
	dm.saveInterval = interval
	log.Printf("✅ 自动保存间隔已设置为: %v", interval)
}

// EnableBackup 启用备份功能
func (dm *DataManager) EnableBackup() {
	dm.backupEnabled = true
	log.Println("✅ 备份功能已启用")
}

// DisableBackup 禁用备份功能
func (dm *DataManager) DisableBackup() {
	dm.backupEnabled = false
	log.Println("✅ 备份功能已禁用")
}

// IsBackupEnabled 检查备份功能是否启用
func (dm *DataManager) IsBackupEnabled() bool {
	return dm.backupEnabled
}
