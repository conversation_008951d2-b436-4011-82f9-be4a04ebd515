package main

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// OptionContractManager 期权合约管理器
type OptionContractManager struct {
	wsClient      *BybitWSClient
	subscriptions map[string]int // 合约名称 -> 订阅计数
	contractCache map[string]*OptionContract
	mutex         sync.RWMutex
	updateChannel chan string               // 盘口更新通知通道
	checkChannel  chan ContractCheckRequest // 交易条件检查请求通道
	stopChannel   chan struct{}
}

// ContractCheckRequest 交易条件检查请求
type ContractCheckRequest struct {
	TaskID       string
	Contract     string
	Task         *MonitorTask
	ResponseChan chan bool
}

// NewOptionContractManager 创建期权合约管理器
func NewOptionContractManager(wsClient *BybitWSClient) *OptionContractManager {
	ocm := &OptionContractManager{
		wsClient:      wsClient,
		subscriptions: make(map[string]int),
		contractCache: make(map[string]*OptionContract),
		updateChannel: make(chan string, 1000), // 缓冲通道，处理高频更新
		checkChannel:  make(chan ContractCheckRequest, 1000),
		stopChannel:   make(chan struct{}),
	}

	// 启动处理协程
	go ocm.processUpdates()
	go ocm.processChecks()

	return ocm
}

// GetEligibleContracts 获取符合条件的期权合约
func (ocm *OptionContractManager) GetEligibleContracts(task *MonitorTask) ([]string, error) {
	// 获取标的资产现货价格
	underlying := "ETHUSDT" // 简化处理，实际应该从任务配置中获取
	spotPrice, exists := ocm.getSpotPrice(underlying)
	if !exists {
		return nil, fmt.Errorf("无法获取现货价格: %s", underlying)
	}

	// 获取全局默认参数
	defaults := GetGlobalDefaults()

	// 计算价格范围
	priceRange := defaults.PriceRange
	minPrice := spotPrice.Mul(decimal.NewFromFloat(1).Sub(priceRange))
	maxPrice := spotPrice.Mul(decimal.NewFromFloat(1).Add(priceRange))

	// 计算到期时间范围
	expiryDates, err := ocm.calculateExpiryDates(task.ExpiryTimeRange)
	if err != nil {
		return nil, fmt.Errorf("计算到期时间失败: %v", err)
	}

	// 生成期权合约列表
	contracts := ocm.generateOptionContracts(underlying, expiryDates, minPrice, maxPrice, task.OptionType)
	//log.Printf("🎯 任务 %s 筛选期权合约: 现货价格=%.2f, 范围=[%.2f-%.2f], 合约数=%d",
	//task.ID, spotPrice.InexactFloat64(), minPrice.InexactFloat64(), maxPrice.InexactFloat64(), len(contracts))

	return contracts, nil
}

// calculateExpiryDates 计算到期日期 (修正时间逻辑)
func (ocm *OptionContractManager) calculateExpiryDates(expiryTimeRange string) ([]string, error) {
	now := time.Now()
	var dates []string

	if strings.Contains(expiryTimeRange, "-") {
		// 范围格式: "5h-10h"
		parts := strings.Split(expiryTimeRange, "-")
		if len(parts) != 2 {
			return nil, fmt.Errorf("无效的时间范围格式: %s", expiryTimeRange)
		}

		startHours, err := parseHours(strings.TrimSpace(parts[0]))
		if err != nil {
			return nil, err
		}

		endHours, err := parseHours(strings.TrimSpace(parts[1]))
		if err != nil {
			return nil, err
		}

		// 查找在时间范围内的到期日期
		dates = ocm.findExpiryDatesInRange(now, startHours, endHours)
	} else {
		// 单一时间格式: "24h"
		hours, err := parseHours(expiryTimeRange)
		if err != nil {
			return nil, err
		}

		// 查找在指定小时数内到期的期权
		dates = ocm.findExpiryDatesInRange(now, 0, hours)
	}

	return dates, nil
}

// findExpiryDatesInRange 查找在指定时间范围内到期的期权日期
func (ocm *OptionContractManager) findExpiryDatesInRange(now time.Time, minHours, maxHours int) []string {
	var dates []string

	// 检查未来几天的期权到期日期
	for days := 0; days <= 7; days++ {
		checkDate := now.AddDate(0, 0, days)
		// 期权在当天16:00到期
		expiryTime := time.Date(checkDate.Year(), checkDate.Month(), checkDate.Day(), 16, 0, 0, 0, checkDate.Location())

		// 计算距离到期的时间
		timeUntilExpiry := expiryTime.Sub(now)
		hoursUntilExpiry := timeUntilExpiry.Hours()

		// 检查是否在指定范围内（使用精确的浮点数比较）
		if hoursUntilExpiry >= float64(minHours) && hoursUntilExpiry <= float64(maxHours) {
			dateStr := formatExpiryDate(expiryTime)
			dates = append(dates, dateStr)
		}
	}

	return dates
}

// parseHours 解析小时数
func parseHours(timeStr string) (int, error) {
	if !strings.HasSuffix(timeStr, "h") {
		return 0, fmt.Errorf("时间格式必须以'h'结尾: %s", timeStr)
	}

	hourStr := strings.TrimSuffix(timeStr, "h")
	hours, err := strconv.Atoi(hourStr)
	if err != nil {
		return 0, fmt.Errorf("无效的小时数: %s", hourStr)
	}

	return hours, nil
}

// formatExpiryDate 格式化到期日期为Bybit格式
func formatExpiryDate(t time.Time) string {
	// 转换为Bybit期权命名格式: 3JUN25
	months := []string{"", "JAN", "FEB", "MAR", "APR", "MAY", "JUN",
		"JUL", "AUG", "SEP", "OCT", "NOV", "DEC"}

	day := t.Day()
	month := months[t.Month()]
	year := t.Year() % 100

	return fmt.Sprintf("%d%s%02d", day, month, year)
}

// generateOptionContracts 生成期权合约列表 (使用 Bybit 格式)
func (ocm *OptionContractManager) generateOptionContracts(underlying string, expiryDates []string, minPrice, maxPrice decimal.Decimal, optionType string) []string {
	var contracts []string

	// 生成行权价格列表（步长为50）
	strikePrices := ocm.generateStrikePrices(minPrice, maxPrice, 50)

	// 期权类型后缀
	typeSuffix := "C"
	if optionType == OptionTypePut {
		typeSuffix = "P"
	}

	// 转换为 Bybit 格式的基础资产名称
	baseAsset := "ETH" // ETHUSDT -> ETH
	if underlying == "BTCUSDT" {
		baseAsset = "BTC"
	}

	// 生成所有组合 (Bybit 格式: ETH-3JUN25-2500-C-USDT)
	for _, expiryDate := range expiryDates {
		for _, strikePrice := range strikePrices {
			contract := fmt.Sprintf("%s-%s-%d-%s-USDT", baseAsset, expiryDate, strikePrice, typeSuffix)
			contracts = append(contracts, contract)
		}
	}

	return contracts
}

// CheckTradingConditionsWithSummary 检查交易条件并生成汇总日志
func (ocm *OptionContractManager) CheckTradingConditionsWithSummary(taskID string, contracts []string, task *MonitorTask) int {
	// 获取现货价格
	underlying := "ETHUSDT"
	spotPrice, exists := ocm.getSpotPrice(underlying)
	if !exists {
		log.Printf("❌ 无法获取现货价格: %s", underlying)
		return 0
	}

	// 解析行权价范围
	minStrike, maxStrike := ocm.getStrikePriceRange(contracts)

	// 检查每个合约的交易条件
	var contractResults []string
	satisfiedCount := 0
	validContracts := 0

	for _, contract := range contracts {
		// 解析行权价
		strikePrice := ocm.parseStrikePriceFromContract(contract)

		// 尝试从真实数据计算时间价值
		timeValue, err := ocm.calculateRealTimeValue(contract, spotPrice, decimal.NewFromInt(int64(strikePrice)), task.OptionType)
		if err != nil {
			log.Printf("⚠️  期权 %s 数据不可用: %v", contract, err)
			continue
		}

		validContracts++

		// 检查是否满足条件
		satisfied := timeValue.GreaterThan(task.TimeValueTarget)
		if satisfied {
			satisfiedCount++
		}

		// 格式化结果
		result := fmt.Sprintf("%d:%.2f%%", strikePrice, timeValue.Mul(decimal.NewFromInt(100)).InexactFloat64())
		if satisfied {
			result += "✓"
		}
		contractResults = append(contractResults, result)
	}

	if validContracts == 0 {
		log.Printf("❌ 任务 %s: 没有可用的期权数据", taskID)
		return 0
	}

	// 构建汇总日志
	var logParts []string
	logParts = append(logParts, fmt.Sprintf("🎯 %s [%s %s]", taskID, strings.ToUpper(task.OptionType), task.ExpiryTimeRange))
	logParts = append(logParts, fmt.Sprintf("现货:%.2f", spotPrice.InexactFloat64()))
	logParts = append(logParts, fmt.Sprintf("范围:[%d-%d]", minStrike, maxStrike))
	logParts = append(logParts, fmt.Sprintf("阈值:%.2f%%", task.TimeValueTarget.Mul(decimal.NewFromInt(100)).InexactFloat64()))
	logParts = append(logParts, fmt.Sprintf("有效:%d/%d", validContracts, len(contracts)))

	// 组合完整日志
	if len(contractResults) > 0 {
		logParts = append(logParts, "| "+strings.Join(contractResults, " "))
	}

	// 输出汇总日志
	log.Println(strings.Join(logParts, " "))

	return satisfiedCount
}

// calculateRealTimeValue 计算真实时间价值（使用统一价格客户端）
func (ocm *OptionContractManager) calculateRealTimeValue(contract string, spotPrice, strikePrice decimal.Decimal, optionType string) (decimal.Decimal, error) {
	// 获取期权最优买价
	bid1Price, err := ocm.wsClient.GetBestBidPrice(contract)
	if err != nil {
		return decimal.Zero, fmt.Errorf("获取期权 %s 买一价失败: %v", contract, err)
	}

	// 根据期权类型获取对应的现货价格（用于时间价值计算）
	var timeValueSpotPrice decimal.Decimal
	var exists bool

	if optionType == OptionTypeCall {
		// CALL期权使用卖一价
		timeValueSpotPrice, exists = GetSpotPriceForTimeValue(OptionTypeCall)
	} else {
		// PUT期权使用买一价
		timeValueSpotPrice, exists = GetSpotPriceForTimeValue(OptionTypePut)
	}

	if !exists {
		return decimal.Zero, fmt.Errorf("无法获取时间价值计算用的现货价格")
	}

	// 计算时间价值百分比（按照公式，使用正确的现货价格）
	var timeValue decimal.Decimal

	if optionType == OptionTypePut {
		// PUT期权: time_value = ((spot_price >= strike_price) ? bid1_price : bid1_price - (strike_price - spot_price)) / spot_price
		if timeValueSpotPrice.GreaterThanOrEqual(strikePrice) {
			timeValue = bid1Price
		} else {
			timeValue = bid1Price.Sub(strikePrice.Sub(timeValueSpotPrice))
		}
	} else {
		// CALL期权: time_value = ((spot_price <= strike_price) ? bid1_price : bid1_price - (spot_price - strike_price)) / spot_price
		if timeValueSpotPrice.LessThanOrEqual(strikePrice) {
			timeValue = bid1Price
		} else {
			timeValue = bid1Price.Sub(timeValueSpotPrice.Sub(strikePrice))
		}
	}

	// 转换为百分比
	if timeValueSpotPrice.IsZero() {
		return decimal.Zero, fmt.Errorf("现货价格为零")
	}

	timeValuePercent := timeValue.Div(timeValueSpotPrice)

	return timeValuePercent, nil
}

// getStrikePriceRange 获取行权价范围
func (ocm *OptionContractManager) getStrikePriceRange(contracts []string) (int, int) {
	if len(contracts) == 0 {
		return 0, 0
	}

	minStrike := 999999
	maxStrike := 0

	for _, contract := range contracts {
		strike := ocm.parseStrikePriceFromContract(contract)
		if strike < minStrike {
			minStrike = strike
		}
		if strike > maxStrike {
			maxStrike = strike
		}
	}

	return minStrike, maxStrike
}

// parseStrikePriceFromContract 从合约名称解析行权价 (Bybit 格式)
func (ocm *OptionContractManager) parseStrikePriceFromContract(contract string) int {
	// 解析格式: ETH-3JUN25-2500-C-USDT
	parts := strings.Split(contract, "-")
	if len(parts) >= 3 {
		if strike, err := strconv.Atoi(parts[2]); err == nil {
			return strike
		}
	}
	return 0
}

// generateStrikePrices 生成行权价格列表
func (ocm *OptionContractManager) generateStrikePrices(minPrice, maxPrice decimal.Decimal, step int) []int {
	var prices []int

	minInt := int(minPrice.IntPart())
	maxInt := int(maxPrice.IntPart())

	// 向下取整到最近的步长倍数
	minInt = (minInt / step) * step

	// 向上取整到最近的步长倍数
	maxInt = ((maxInt + step - 1) / step) * step

	for price := minInt; price <= maxInt; price += step {
		prices = append(prices, price)
	}

	return prices
}

// SubscribeContracts 订阅期权合约（去重）
func (ocm *OptionContractManager) SubscribeContracts(contracts []string) {
	ocm.mutex.Lock()
	defer ocm.mutex.Unlock()

	// 第一步：过滤掉不存在的合约
	var validContracts []string
	var invalidContracts []string

	for _, contract := range contracts {
		// 检查合约状态
		if ocm.wsClient != nil && ocm.wsClient.contractMonitor != nil {
			if status, exists := ocm.wsClient.contractMonitor.GetContractStatus(contract); exists {
				if status.State == StateNotExist {
					invalidContracts = append(invalidContracts, contract)
					log.Printf("🚫 跳过订阅不存在的合约: %s", contract)
					continue
				}
			}
		}
		validContracts = append(validContracts, contract)
	}

	if len(invalidContracts) > 0 {
		log.Printf("⚠️  已跳过 %d 个不存在的合约: %v", len(invalidContracts), invalidContracts)
	}

	// 第二步：只订阅有效合约
	for _, contract := range validContracts {
		// 增加订阅计数
		ocm.subscriptions[contract]++

		// 如果是第一次订阅，则实际订阅
		if ocm.subscriptions[contract] == 1 {
			// 创建期权合约对象
			optionContract := &OptionContract{
				Symbol:          contract,
				UnderlyingAsset: "ETHUSDT", // 简化处理
				UpdateTime:      time.Now(),
			}

			// 添加到缓存
			ocm.contractCache[contract] = optionContract

			// 添加到WebSocket客户端
			ocm.wsClient.AddOptionContract(contract)

			// 订阅WebSocket
			if err := ocm.wsClient.SubscribeOptionOrderBook(contract); err != nil {
				log.Printf("❌ 订阅期权盘口失败: %s, %v", contract, err)
				// 订阅失败，从缓存中移除
				delete(ocm.contractCache, contract)
				ocm.subscriptions[contract] = 0
			}
			// 静默订阅成功，不输出日志
		}
	}
}

// UnsubscribeContracts 取消订阅期权合约
func (ocm *OptionContractManager) UnsubscribeContracts(contracts []string) {
	ocm.mutex.Lock()
	defer ocm.mutex.Unlock()

	for _, contract := range contracts {
		if count, exists := ocm.subscriptions[contract]; exists && count > 0 {
			// 减少订阅计数
			ocm.subscriptions[contract]--

			// 如果计数归零，则实际取消订阅
			if ocm.subscriptions[contract] == 0 {
				delete(ocm.subscriptions, contract)
				delete(ocm.contractCache, contract)
				// 静默取消订阅，不输出日志
				// TODO: 实现WebSocket取消订阅
			}
		}
	}
}

// getSpotPrice 获取现货价格（已弃用，使用统一价格客户端）
func (ocm *OptionContractManager) getSpotPrice(symbol string) (decimal.Decimal, bool) {
	// 使用统一价格客户端获取监控范围用的现货价格（中间价）
	return GetSpotPriceForMonitorRange()
}

// OnOrderBookUpdate 处理盘口更新
func (ocm *OptionContractManager) OnOrderBookUpdate(contract string) {
	// 发送更新通知到处理通道
	select {
	case ocm.updateChannel <- contract:
		// 通知已发送
	default:
		// 通道满了，跳过这次更新（防止阻塞）
	}
}

// CheckTradingCondition 检查交易条件
func (ocm *OptionContractManager) CheckTradingCondition(taskID string, contract string, task *MonitorTask) bool {
	// 创建检查请求
	request := ContractCheckRequest{
		TaskID:       taskID,
		Contract:     contract,
		Task:         task,
		ResponseChan: make(chan bool, 1),
	}

	// 发送检查请求
	select {
	case ocm.checkChannel <- request:
		// 等待检查结果
		select {
		case result := <-request.ResponseChan:
			return result
		case <-time.After(100 * time.Millisecond):
			// 超时返回false
			return false
		}
	default:
		// 通道满了，返回false
		return false
	}
}

// processUpdates 处理盘口更新
func (ocm *OptionContractManager) processUpdates() {
	for {
		select {
		case contract := <-ocm.updateChannel:
			// TODO: 触发相关监控任务的交易条件检查
			// 静默处理盘口更新
			_ = contract
		case <-ocm.stopChannel:
			return
		}
	}
}

// processChecks 处理交易条件检查
func (ocm *OptionContractManager) processChecks() {
	for {
		select {
		case request := <-ocm.checkChannel:
			// 执行交易条件检查
			result := ocm.doTradingConditionCheck(request.TaskID, request.Contract, request.Task)

			// 发送结果
			select {
			case request.ResponseChan <- result:
			default:
			}
		case <-ocm.stopChannel:
			return
		}
	}
}

// doTradingConditionCheck 执行具体的交易条件检查
func (ocm *OptionContractManager) doTradingConditionCheck(taskID, contract string, task *MonitorTask) bool {
	// 1. 获取现货价格
	underlying := "ETHUSDT"
	spotPrice, exists := ocm.getSpotPrice(underlying)
	if !exists {
		return false
	}

	// 2. 解析行权价
	strikePrice := ocm.parseStrikePriceFromContract(contract)
	if strikePrice == 0 {
		return false
	}

	// 3. 计算时间价值
	timeValue, err := ocm.calculateRealTimeValue(contract, spotPrice, decimal.NewFromInt(int64(strikePrice)), task.OptionType)
	if err != nil {
		return false
	}

	// 4. 检查时间价值是否满足条件
	if !timeValue.GreaterThan(task.TimeValueTarget) {
		return false
	}

	// 5. 验证合约状态（必须为Trading状态）
	if ocm.wsClient != nil && ocm.wsClient.contractMonitor != nil {
		if status, exists := ocm.wsClient.contractMonitor.GetContractStatus(contract); exists {
			if status.State != StateTrading || !status.OrderBookValid {
				return false
			}
		} else {
			return false
		}
	}

	// 6. 检查盘口深度（获取买一价和买一量）
	_, err = ocm.wsClient.GetBestBidPrice(contract)
	if err != nil {
		return false
	}

	// 获取盘口数据检查买一量
	orderBook, exists := ocm.wsClient.GetOptionContract(contract)
	if !exists {
		return false
	}

	_, bid1Qty := orderBook.GetBestBid()
	if bid1Qty.IsZero() {
		return false // 没有买盘量
	}

	// 7. 检查盘口深度条件（简化版本，使用默认参数）
	defaults := GetGlobalDefaults()
	orderSize := defaults.OrderSize
	depthThreshold := defaults.DepthThreshold

	// 检查盘口深度：(order_size / bid1_qty) <= depth_threshold
	depthRatio := orderSize.Div(bid1Qty)
	if depthRatio.GreaterThan(depthThreshold) {
		return false // 盘口深度不足
	}

	// 所有条件都满足
	return true
}

// Stop 停止期权合约管理器
func (ocm *OptionContractManager) Stop() {
	close(ocm.stopChannel)
}

// GetSubscribedContracts 获取已订阅的合约列表
func (ocm *OptionContractManager) GetSubscribedContracts() []string {
	ocm.mutex.RLock()
	defer ocm.mutex.RUnlock()

	var contracts []string
	for contract := range ocm.subscriptions {
		contracts = append(contracts, contract)
	}

	return contracts
}
