package main

import (
	"log"
	"time"

	"github.com/shopspring/decimal"
)

// 内嵌的Bybit API配置
const (
	// Bybit API配置
	BybitAPIKey    = "42uBEXPg7dkVlGPG7Z"
	BybitSecretKey = "gewxB54t9L6sfM7lAvoMLFWFS4MbFKrEWX7j"
	BybitBaseURL   = "https://api.bybit.com"
	BybitWSURL     = "wss://stream.bybit.com/v5/public/option"

	// 是否使用测试网
	UseTestnet = false
)

// 全局默认参数实例
var globalDefaults = &GlobalDefaults{
	ExpiryThreshold: "24h",                        // 监控行权时间阈值：24小时
	PriceRange:      decimal.NewFromFloat(0.05),   // 监控价格范围阈值：10%
	MaxPosition:     decimal.NewFromFloat(0.1),    // 单个期权最高持仓量：1.0
	OrderSize:       decimal.NewFromFloat(0.1),    // 单次下单量：0.1
	DepthThreshold:  decimal.NewFromFloat(0.05),   // 盘口深度检测阈值：5%
	DepthLevels:     5,                            // 盘口监控档数：5档
	HedgeThreshold:  decimal.NewFromFloat(0.0002), // 动态对冲阈值：0.02%
	HedgeAPIURL:     "http://localhost:5879",      // 对冲系统API地址
}

// LoadEmbeddedConfig 加载内嵌配置
func LoadEmbeddedConfig() error {
	log.Println("📄 加载内嵌配置...")

	// 验证API密钥
	if BybitAPIKey == "your_bybit_api_key_here" || BybitSecretKey == "your_bybit_secret_key_here" {
		log.Println("⚠️  警告: 使用默认API密钥，请在embedded_config.go中设置真实的Bybit API密钥")
	}

	// 显示配置信息
	log.Printf("🔧 Bybit API密钥: %s...", BybitAPIKey[:8])
	if UseTestnet {
		log.Println("🟡 使用Bybit测试网")
	} else {
		log.Println("🔴 使用Bybit主网 - 请确保你了解风险！")
	}

	log.Println("✅ 内嵌配置加载成功")
	return nil
}

// GetBybitConfig 获取Bybit配置
func GetBybitConfig() (string, string, string, string) {
	baseURL := BybitBaseURL
	wsURL := BybitWSURL

	if UseTestnet {
		baseURL = "https://api-testnet.bybit.com"
		wsURL = "wss://stream-testnet.bybit.com/v5/public/option"
	}

	return BybitAPIKey, BybitSecretKey, baseURL, wsURL
}

// GetGlobalDefaults 获取全局默认参数
func GetGlobalDefaults() *GlobalDefaults {
	return globalDefaults
}

// UpdateGlobalDefault 更新全局默认参数
func UpdateGlobalDefault(paramName string, value interface{}) error {
	switch paramName {
	case "expiry_threshold":
		if v, ok := value.(string); ok {
			globalDefaults.ExpiryThreshold = v
		}
	case "price_range":
		if v, ok := value.(float64); ok {
			globalDefaults.PriceRange = decimal.NewFromFloat(v)
		}
	case "max_position":
		if v, ok := value.(float64); ok {
			globalDefaults.MaxPosition = decimal.NewFromFloat(v)
		}
	case "order_size":
		if v, ok := value.(float64); ok {
			globalDefaults.OrderSize = decimal.NewFromFloat(v)
		}
	case "depth_threshold":
		if v, ok := value.(float64); ok {
			globalDefaults.DepthThreshold = decimal.NewFromFloat(v)
		}
	case "depth_levels":
		if v, ok := value.(int); ok {
			globalDefaults.DepthLevels = v
		}
	case "hedge_threshold":
		if v, ok := value.(float64); ok {
			globalDefaults.HedgeThreshold = decimal.NewFromFloat(v)
		}
	case "hedge_api_url":
		if v, ok := value.(string); ok {
			globalDefaults.HedgeAPIURL = v
		}
	}
	return nil
}

// StateCheckConfig 状态检查配置
type StateCheckConfig struct {
	NormalInterval     time.Duration `json:"normal_interval"`      // 正常检查间隔：30分钟
	HourBeforeInterval time.Duration `json:"hour_before_interval"` // 1小时内检查间隔：5分钟
	FinalInterval      time.Duration `json:"final_interval"`       // 15分钟内检查间隔：1分钟
	RetryInterval      time.Duration `json:"retry_interval"`       // 重试间隔：5秒
	MaxRetryCount      int           `json:"max_retry_count"`      // 最大重试次数：3次
	BatchSize          int           `json:"batch_size"`           // 批次大小：10个
	BatchInterval      time.Duration `json:"batch_interval"`       // 批次间隔：1秒
	APITimeout         time.Duration `json:"api_timeout"`          // API超时时间：10秒
}

// 默认状态检查配置
var defaultStateCheckConfig = &StateCheckConfig{
	NormalInterval:     30 * time.Minute,
	HourBeforeInterval: 5 * time.Minute,
	FinalInterval:      1 * time.Minute,
	RetryInterval:      5 * time.Second,
	MaxRetryCount:      3,
	BatchSize:          10,
	BatchInterval:      1 * time.Second,
	APITimeout:         10 * time.Second,
}

// GetStateCheckConfig 获取状态检查配置
func GetStateCheckConfig() *StateCheckConfig {
	return defaultStateCheckConfig
}
