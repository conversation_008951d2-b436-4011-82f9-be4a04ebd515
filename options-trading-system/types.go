package main

import (
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// GlobalDefaults 全局默认参数
type GlobalDefaults struct {
	ExpiryThreshold string          `json:"expiry_threshold"` // 监控行权时间阈值 (如 "24h")
	PriceRange      decimal.Decimal `json:"price_range"`      // 监控价格范围阈值 (如 0.1 表示 10%)
	MaxPosition     decimal.Decimal `json:"max_position"`     // 单个期权最高持仓量
	OrderSize       decimal.Decimal `json:"order_size"`       // 单次下单量
	DepthThreshold  decimal.Decimal `json:"depth_threshold"`  // 盘口深度检测阈值 (如 0.05 表示 5%)
	DepthLevels     int             `json:"depth_levels"`     // 盘口监控档数
	HedgeThreshold  decimal.Decimal `json:"hedge_threshold"`  // 动态对冲阈值 (如 0.0002 表示 0.02%)
	HedgeAPIURL     string          `json:"hedge_api_url"`    // 对冲系统API地址
}

// MonitorTask 监控任务
type MonitorTask struct {
	ID              string          `json:"id"`                // 任务ID
	OptionType      string          `json:"option_type"`       // 期权类型 ("call" 或 "put")
	ExpiryTimeRange string          `json:"expiry_time_range"` // 行权时间范围 (如 "5h" 或 "5h-10h")
	TimeValueTarget decimal.Decimal `json:"time_value_target"` // 目标时间价值阈值
	Status          string          `json:"status"`            // 任务状态 (running/stopped)
	CreatedAt       time.Time       `json:"created_at"`        // 创建时间
	UpdatedAt       time.Time       `json:"updated_at"`        // 最后更新时间
	StopChan        chan struct{}   `json:"-"`                 // 停止信号 (不序列化)
	mutex           sync.RWMutex    `json:"-"`                 // 读写锁 (不序列化)
}

// PositionTracker 持仓跟踪任务
type PositionTracker struct {
	ContractName    string          `json:"contract_name"`    // 期权合约名称 (如 "ETH-3JUN25-2500-C-USDT")
	OptionType      string          `json:"option_type"`      // 期权类型 ("call" 或 "put")
	StrikePrice     decimal.Decimal `json:"strike_price"`     // 行权价格
	ExpiryTime      time.Time       `json:"expiry_time"`      // 到期时间
	Symbol          string          `json:"symbol"`           // 标的资产 (如 "ETHUSDT")
	CurrentPosition decimal.Decimal `json:"current_position"` // 当前持仓量
	AveragePrice    decimal.Decimal `json:"average_price"`    // 持仓均价
	TotalCost       decimal.Decimal `json:"total_cost"`       // 总成本
	TotalFees       decimal.Decimal `json:"total_fees"`       // 累计手续费
	UnrealizedPnL   decimal.Decimal `json:"unrealized_pnl"`   // 未实现盈亏
	RealizedPnL     decimal.Decimal `json:"realized_pnl"`     // 已实现盈亏
	HedgeTaskID     string          `json:"hedge_task_id"`    // 绑定的对冲任务ID
	Status          string          `json:"status"`           // 状态 (active/closed/exercised)
	Trades          []TradeRecord   `json:"trades"`           // 交易记录
	Events          []PositionEvent `json:"events"`           // 事件记录
	CreatedAt       time.Time       `json:"created_at"`       // 创建时间
	UpdatedAt       time.Time       `json:"updated_at"`       // 最后更新时间
	mutex           sync.RWMutex    `json:"-"`                // 读写锁 (不序列化)
}

// TradeRecord 交易记录
type TradeRecord struct {
	TradeID    string          `json:"trade_id"`    // 交易ID
	OrderID    string          `json:"order_id"`    // 订单ID
	Side       string          `json:"side"`        // 交易方向 (sell/buy)
	Quantity   decimal.Decimal `json:"quantity"`    // 交易数量
	Price      decimal.Decimal `json:"price"`       // 成交价格
	Fee        decimal.Decimal `json:"fee"`         // 手续费
	Timestamp  time.Time       `json:"timestamp"`   // 成交时间
	UpdateType string          `json:"update_type"` // 更新类型 (sell_open/buy_close/exercise)
}

// PositionEvent 持仓事件
type PositionEvent struct {
	EventType   string    `json:"event_type"`  // 事件类型
	Description string    `json:"description"` // 事件描述
	Data        string    `json:"data"`        // 事件数据 (JSON格式)
	Timestamp   time.Time `json:"timestamp"`   // 事件时间
}

// OptionContract 期权合约信息
type OptionContract struct {
	Symbol          string          `json:"symbol"`        // 合约名称
	UnderlyingAsset string          `json:"underlying"`    // 标的资产
	OptionType      string          `json:"option_type"`   // 期权类型
	StrikePrice     decimal.Decimal `json:"strike_price"`  // 行权价格
	ExpiryTime      time.Time       `json:"expiry_time"`   // 到期时间
	Bid1Price       decimal.Decimal `json:"bid1_price"`    // 买一价
	Bid1Quantity    decimal.Decimal `json:"bid1_quantity"` // 买一量
	Ask1Price       decimal.Decimal `json:"ask1_price"`    // 卖一价
	Ask1Quantity    decimal.Decimal `json:"ask1_quantity"` // 卖一量
	LastPrice       decimal.Decimal `json:"last_price"`    // 最新价
	Volume24h       decimal.Decimal `json:"volume_24h"`    // 24小时成交量
	OpenInterest    decimal.Decimal `json:"open_interest"` // 持仓量
	UpdateTime      time.Time       `json:"update_time"`   // 更新时间
}

// MarketData 市场数据
type MarketData struct {
	Symbol    string          `json:"symbol"`    // 标的资产符号
	Price     decimal.Decimal `json:"price"`     // 当前价格
	Timestamp time.Time       `json:"timestamp"` // 时间戳
}

// HedgeTaskRequest 对冲任务请求
type HedgeTaskRequest struct {
	Direction      string          `json:"direction"`       // 对冲方向 (long/short)
	TargetPrice    decimal.Decimal `json:"target_price"`    // 目标价格
	StopRate       decimal.Decimal `json:"stop_rate"`       // 阈值
	Amount         decimal.Decimal `json:"amount"`          // 数量
	Symbol         string          `json:"symbol"`          // 交易对
	Source         string          `json:"source"`          // 来源 (options)
	OptionContract string          `json:"option_contract"` // 期权合约名称
}

// HedgeTaskResponse 对冲任务响应
type HedgeTaskResponse struct {
	Success bool   `json:"success"`
	TaskID  string `json:"task_id"`
	Message string `json:"message"`
}

// HedgeTaskUpdateRequest 对冲任务更新请求
type HedgeTaskUpdateRequest struct {
	Amount         decimal.Decimal `json:"amount"`          // 新的总数量
	UpdateType     string          `json:"update_type"`     // 更新类型
	Source         string          `json:"source"`          // 来源
	OptionContract string          `json:"option_contract"` // 期权合约名称
}

// HedgeTaskUpdateResponse 对冲任务更新响应
type HedgeTaskUpdateResponse struct {
	Success   bool            `json:"success"`
	TaskID    string          `json:"task_id"`
	OldAmount decimal.Decimal `json:"old_amount"`
	NewAmount decimal.Decimal `json:"new_amount"`
	Message   string          `json:"message"`
}

// HedgeTaskQueryResponse 对冲任务查询响应
type HedgeTaskQueryResponse struct {
	Success bool   `json:"success"`
	TaskID  string `json:"task_id"`
	Exists  bool   `json:"exists"`
}

// MonitorManager 监控任务管理器
type MonitorManager struct {
	tasks           map[string]*MonitorTask
	mutex           sync.RWMutex
	dataManager     *DataManager
	wsClient        *BybitWSClient
	contractManager *OptionContractManager
}

// PositionManager 持仓管理器
type PositionManager struct {
	positions   map[string]*PositionTracker
	mutex       sync.RWMutex
	dataManager *DataManager
}

// BybitClient Bybit客户端
type BybitClient struct {
	APIKey    string
	SecretKey string
	BaseURL   string
	WSBaseURL string
}

// DataManager 数据管理器
type DataManager struct {
	dataDir       string
	monitorsFile  string
	positionsFile string
	eventsFile    string
	backupDir     string
	mutex         sync.RWMutex
	autoSave      bool
	saveChannel   chan struct{}
	saveInterval  time.Duration
	backupEnabled bool
}

// 常量定义
const (
	// 任务状态
	StatusRunning = "running"
	StatusStopped = "stopped"

	// 期权类型
	OptionTypeCall = "call"
	OptionTypePut  = "put"

	// 持仓状态
	PositionStatusActive    = "active"
	PositionStatusClosed    = "closed"
	PositionStatusExercised = "exercised"

	// 交易方向
	TradeSideSell = "sell"
	TradeSideBuy  = "buy"

	// 更新类型
	UpdateTypeSellOpen = "sell_open" // 卖出开仓
	UpdateTypeBuyClose = "buy_close" // 买入平仓
	UpdateTypeExercise = "exercise"  // 期权行权

	// 事件类型
	EventTypeTradeExecuted    = "trade_executed"     // 交易执行
	EventTypePositionUpdated  = "position_updated"   // 持仓更新
	EventTypeHedgeTaskCreated = "hedge_task_created" // 对冲任务创建
	EventTypeHedgeTaskUpdated = "hedge_task_updated" // 对冲任务更新
	EventTypeOptionExercised  = "option_exercised"   // 期权行权
	EventTypePositionClosed   = "position_closed"    // 持仓关闭

	// 对冲方向
	HedgeDirectionLong  = "long"
	HedgeDirectionShort = "short"

	// 合约状态
	ContractStatusNormal = "normal" // 正常
	ContractStatusStale  = "stale"  // 数据陈旧但连接正常
	ContractStatusError  = "error"  // 连接异常
)

// OrderBookLevel 订单簿档位
type OrderBookLevel struct {
	Price    decimal.Decimal `json:"price"`
	Quantity decimal.Decimal `json:"quantity"`
}

// FullOrderBook 完整订单簿
type FullOrderBook struct {
	Symbol      string           `json:"symbol"`
	Bids        []OrderBookLevel `json:"bids"` // 买盘多档位
	Asks        []OrderBookLevel `json:"asks"` // 卖盘多档位
	LastUpdate  time.Time        `json:"last_update"`
	UpdateCount int64            `json:"update_count"`
	mutex       sync.RWMutex     `json:"-"`
}

// GetBestBid 获取最优买价
func (ob *FullOrderBook) GetBestBid() (decimal.Decimal, decimal.Decimal) {
	ob.mutex.RLock()
	defer ob.mutex.RUnlock()

	if len(ob.Bids) > 0 {
		return ob.Bids[0].Price, ob.Bids[0].Quantity
	}
	return decimal.Zero, decimal.Zero
}

// GetBestAsk 获取最优卖价
func (ob *FullOrderBook) GetBestAsk() (decimal.Decimal, decimal.Decimal) {
	ob.mutex.RLock()
	defer ob.mutex.RUnlock()

	if len(ob.Asks) > 0 {
		return ob.Asks[0].Price, ob.Asks[0].Quantity
	}
	return decimal.Zero, decimal.Zero
}

// UpdateSnapshot 更新完整快照
func (ob *FullOrderBook) UpdateSnapshot(bids, asks [][]string) {
	ob.mutex.Lock()
	defer ob.mutex.Unlock()

	// 清空现有数据
	ob.Bids = ob.Bids[:0]
	ob.Asks = ob.Asks[:0]

	// 更新买盘
	for _, bid := range bids {
		if len(bid) >= 2 {
			if price, err := decimal.NewFromString(bid[0]); err == nil {
				if quantity, err := decimal.NewFromString(bid[1]); err == nil {
					ob.Bids = append(ob.Bids, OrderBookLevel{
						Price:    price,
						Quantity: quantity,
					})
				}
			}
		}
	}

	// 更新卖盘
	for _, ask := range asks {
		if len(ask) >= 2 {
			if price, err := decimal.NewFromString(ask[0]); err == nil {
				if quantity, err := decimal.NewFromString(ask[1]); err == nil {
					ob.Asks = append(ob.Asks, OrderBookLevel{
						Price:    price,
						Quantity: quantity,
					})
				}
			}
		}
	}

	ob.LastUpdate = time.Now()
	ob.UpdateCount++
}

// UpdateDelta 增量更新
func (ob *FullOrderBook) UpdateDelta(bids, asks [][]string) {
	ob.mutex.Lock()
	defer ob.mutex.Unlock()

	// 更新买盘
	for _, bid := range bids {
		if len(bid) >= 2 {
			if price, err := decimal.NewFromString(bid[0]); err == nil {
				if quantity, err := decimal.NewFromString(bid[1]); err == nil {
					ob.updateLevel(&ob.Bids, price, quantity, true)
				}
			}
		}
	}

	// 更新卖盘
	for _, ask := range asks {
		if len(ask) >= 2 {
			if price, err := decimal.NewFromString(ask[0]); err == nil {
				if quantity, err := decimal.NewFromString(ask[1]); err == nil {
					ob.updateLevel(&ob.Asks, price, quantity, false)
				}
			}
		}
	}

	ob.LastUpdate = time.Now()
	ob.UpdateCount++
}

// updateLevel 更新指定档位
func (ob *FullOrderBook) updateLevel(levels *[]OrderBookLevel, price, quantity decimal.Decimal, isBid bool) {
	// 查找现有档位
	for i, level := range *levels {
		if level.Price.Equal(price) {
			if quantity.IsZero() {
				// 删除档位
				*levels = append((*levels)[:i], (*levels)[i+1:]...)
			} else {
				// 更新档位
				(*levels)[i].Quantity = quantity
			}
			return
		}
	}

	// 新增档位
	if !quantity.IsZero() {
		newLevel := OrderBookLevel{Price: price, Quantity: quantity}
		*levels = append(*levels, newLevel)

		// 重新排序
		if isBid {
			// 买盘按价格降序排列
			for i := len(*levels) - 1; i > 0; i-- {
				if (*levels)[i].Price.GreaterThan((*levels)[i-1].Price) {
					(*levels)[i], (*levels)[i-1] = (*levels)[i-1], (*levels)[i]
				} else {
					break
				}
			}
		} else {
			// 卖盘按价格升序排列
			for i := len(*levels) - 1; i > 0; i-- {
				if (*levels)[i].Price.LessThan((*levels)[i-1].Price) {
					(*levels)[i], (*levels)[i-1] = (*levels)[i-1], (*levels)[i]
				} else {
					break
				}
			}
		}
	}
}

// ContractStatus 合约状态
type ContractStatus struct {
	Symbol                string              `json:"symbol"`
	State                 OptionContractState `json:"state"`                    // 新增：合约状态
	LastStateCheck        time.Time           `json:"last_state_check"`         // 新增：最后状态检查时间
	NextCheckTime         time.Time           `json:"next_check_time"`          // 新增：下次检查时间
	CurrentCycleFailCount int                 `json:"current_cycle_fail_count"` // 新增：当前检查周期失败次数
	LastRetryTime         time.Time           `json:"last_retry_time"`          // 新增：最后重试时间
	MonitorStartTime      time.Time           `json:"monitor_start_time"`       // 新增：开始监控时间
	OrderBookValid        bool                `json:"orderbook_valid"`          // 现有：盘口数据有效性
	LastWSUpdate          time.Time           `json:"last_ws_update"`           // 现有：最后WebSocket更新时间
	LastRESTCheck         time.Time           `json:"last_rest_check"`          // 现有：最后REST检查时间
	RestCheckThreshold    time.Duration       `json:"rest_check_threshold"`     // 现有：REST检查阈值
	RestCheckActive       bool                `json:"rest_check_active"`        // 现有：是否正在REST验证
	WSConnected           bool                `json:"ws_connected"`             // 现有：WebSocket连接状态

	// 买一价变化检测相关字段
	LastBid1Price       decimal.Decimal `json:"last_bid1_price"`       // 最后记录的买一价
	IsFirstTimeData     bool            `json:"is_first_time_data"`    // 是否首次获得数据
	TradingCheckEnabled bool            `json:"trading_check_enabled"` // 交易检测是否启用
	LastBid1ChangeTime  time.Time       `json:"last_bid1_change_time"` // 最后买一价变化时间

	// 交易检测统计相关字段
	LastTradingCheckTime time.Time   `json:"last_trading_check_time"` // 最后交易检测时间
	TradingCheckHistory  []time.Time `json:"trading_check_history"`   // 交易检测历史记录（最近5分钟）
}

// TradingCheckEvent 交易检测事件
type TradingCheckEvent struct {
	Symbol      string          `json:"symbol"`        // 合约符号
	Bid1Price   decimal.Decimal `json:"bid1_price"`    // 买一价
	Timestamp   time.Time       `json:"timestamp"`     // 事件时间戳
	IsFirstTime bool            `json:"is_first_time"` // 是否首次数据
}

// OrderBookCache 订单簿缓存
type OrderBookCache struct {
	contracts map[string]*FullOrderBook // symbol -> 完整订单簿
	mutex     sync.RWMutex
}

// NewOrderBookCache 创建订单簿缓存
func NewOrderBookCache() *OrderBookCache {
	return &OrderBookCache{
		contracts: make(map[string]*FullOrderBook),
	}
}

// GetOrCreateOrderBook 获取或创建订单簿
func (cache *OrderBookCache) GetOrCreateOrderBook(symbol string) *FullOrderBook {
	cache.mutex.Lock()
	defer cache.mutex.Unlock()

	if ob, exists := cache.contracts[symbol]; exists {
		return ob
	}

	ob := &FullOrderBook{
		Symbol: symbol,
		Bids:   make([]OrderBookLevel, 0),
		Asks:   make([]OrderBookLevel, 0),
	}
	cache.contracts[symbol] = ob
	return ob
}

// GetOrderBook 获取订单簿
func (cache *OrderBookCache) GetOrderBook(symbol string) (*FullOrderBook, bool) {
	cache.mutex.RLock()
	defer cache.mutex.RUnlock()

	ob, exists := cache.contracts[symbol]
	return ob, exists
}

// GetBestBid 获取最优买价
func (cache *OrderBookCache) GetBestBid(symbol string) (decimal.Decimal, decimal.Decimal) {
	if ob, exists := cache.GetOrderBook(symbol); exists {
		return ob.GetBestBid()
	}
	return decimal.Zero, decimal.Zero
}

// GetBestAsk 获取最优卖价
func (cache *OrderBookCache) GetBestAsk(symbol string) (decimal.Decimal, decimal.Decimal) {
	if ob, exists := cache.GetOrderBook(symbol); exists {
		return ob.GetBestAsk()
	}
	return decimal.Zero, decimal.Zero
}

// OptionPositionData 期权持仓数据（从Bybit WebSocket获取）
type OptionPositionData struct {
	Symbol         string          `json:"symbol"`         // 期权合约名
	Size           decimal.Decimal `json:"size"`           // 仓位大小
	Side           string          `json:"side"`           // 持仓方向 (Buy/Sell)
	PositionValue  decimal.Decimal `json:"positionValue"`  // 仓位价值
	EntryPrice     decimal.Decimal `json:"entryPrice"`     // 入场价格
	MarkPrice      decimal.Decimal `json:"markPrice"`      // 标记价格
	LiqPrice       decimal.Decimal `json:"liqPrice"`       // 预估强平价
	UnrealisedPnl  decimal.Decimal `json:"unrealisedPnl"`  // 未结盈亏
	CumRealisedPnl decimal.Decimal `json:"cumRealisedPnl"` // 已结盈亏
	CreatedTime    string          `json:"createdTime"`    // 创建时间
	UpdatedTime    string          `json:"updatedTime"`    // 更新时间
	LastUpdate     time.Time       `json:"last_update"`    // 本地最后更新时间
}

// OptionPositionCache 期权持仓缓存
type OptionPositionCache struct {
	positions map[string]*OptionPositionData // symbol -> 持仓数据
	mutex     sync.RWMutex
}

// NewOptionPositionCache 创建新的期权持仓缓存
func NewOptionPositionCache() *OptionPositionCache {
	return &OptionPositionCache{
		positions: make(map[string]*OptionPositionData),
	}
}

// UpdatePosition 更新持仓数据
func (cache *OptionPositionCache) UpdatePosition(position *OptionPositionData) {
	cache.mutex.Lock()
	defer cache.mutex.Unlock()

	position.LastUpdate = time.Now()
	cache.positions[position.Symbol] = position
}

// GetPosition 获取持仓数据
func (cache *OptionPositionCache) GetPosition(symbol string) (*OptionPositionData, bool) {
	cache.mutex.RLock()
	defer cache.mutex.RUnlock()

	position, exists := cache.positions[symbol]
	return position, exists
}

// GetAllPositions 获取所有持仓数据
func (cache *OptionPositionCache) GetAllPositions() map[string]*OptionPositionData {
	cache.mutex.RLock()
	defer cache.mutex.RUnlock()

	result := make(map[string]*OptionPositionData)
	for symbol, position := range cache.positions {
		result[symbol] = position
	}
	return result
}

// RemovePosition 移除持仓数据
func (cache *OptionPositionCache) RemovePosition(symbol string) {
	cache.mutex.Lock()
	defer cache.mutex.Unlock()

	delete(cache.positions, symbol)
}
