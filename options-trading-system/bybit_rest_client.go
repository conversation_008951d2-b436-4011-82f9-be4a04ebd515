package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// BybitRESTClient REST API客户端
type BybitRESTClient struct {
	baseURL string
	client  *http.Client
}

// BybitRESTOrderBookResponse REST API订单簿响应
type BybitRESTOrderBookResponse struct {
	RetCode int    `json:"retCode"`
	RetMsg  string `json:"retMsg"`
	Result  struct {
		Symbol string     `json:"s"`
		Bids   [][]string `json:"b"`
		Asks   [][]string `json:"a"`
		U      int64      `json:"u"`
		Seq    int64      `json:"seq"`
	} `json:"result"`
	Time int64 `json:"time"`
}

// NewBybitRESTClient 创建新的REST客户端
func NewBybitRESTClient() *BybitRESTClient {
	_, _, baseURL, _ := GetBybitConfig()

	return &BybitRESTClient{
		baseURL: baseURL,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// generateSignature 生成Bybit API签名
func (client *BybitRESTClient) generateSignature(apiKey, secretKey, timestamp, queryString string) string {
	message := timestamp + apiKey + "5000" + queryString // 5000是recv_window
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(message))
	return hex.EncodeToString(h.Sum(nil))
}

// addAuthHeaders 添加认证头
func (client *BybitRESTClient) addAuthHeaders(req *http.Request, apiKey, secretKey, queryString string) {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	signature := client.generateSignature(apiKey, secretKey, timestamp, queryString)

	req.Header.Set("X-BAPI-API-KEY", apiKey)
	req.Header.Set("X-BAPI-TIMESTAMP", timestamp)
	req.Header.Set("X-BAPI-SIGN", signature)
	req.Header.Set("X-BAPI-RECV-WINDOW", "5000")
	req.Header.Set("Content-Type", "application/json")
}

// GetOrderBook 获取期权订单簿
func (client *BybitRESTClient) GetOrderBook(symbol string, depth int) (*FullOrderBook, error) {
	// 构造请求URL
	url := fmt.Sprintf("%s/v5/market/orderbook?category=option&symbol=%s&limit=%d",
		client.baseURL, symbol, depth)

	// 发送HTTP请求
	resp, err := client.client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var response BybitRESTOrderBookResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	// 检查API响应状态
	if response.RetCode != 0 {
		return nil, fmt.Errorf("API错误: %s", response.RetMsg)
	}

	// 创建订单簿对象
	orderBook := &FullOrderBook{
		Symbol:      symbol,
		Bids:        make([]OrderBookLevel, 0),
		Asks:        make([]OrderBookLevel, 0),
		LastUpdate:  time.Now(),
		UpdateCount: 1,
	}

	// 解析买盘数据
	for _, bid := range response.Result.Bids {
		if len(bid) >= 2 {
			if price, err := decimal.NewFromString(bid[0]); err == nil {
				if quantity, err := decimal.NewFromString(bid[1]); err == nil {
					orderBook.Bids = append(orderBook.Bids, OrderBookLevel{
						Price:    price,
						Quantity: quantity,
					})
				}
			}
		}
	}

	// 解析卖盘数据
	for _, ask := range response.Result.Asks {
		if len(ask) >= 2 {
			if price, err := decimal.NewFromString(ask[0]); err == nil {
				if quantity, err := decimal.NewFromString(ask[1]); err == nil {
					orderBook.Asks = append(orderBook.Asks, OrderBookLevel{
						Price:    price,
						Quantity: quantity,
					})
				}
			}
		}
	}

	return orderBook, nil
}

// CompareOrderBooks 比较两个订单簿是否一致
func (client *BybitRESTClient) CompareOrderBooks(cached, rest *FullOrderBook) bool {
	// 比较前3档买盘
	if !client.compareLevels(cached.Bids, rest.Bids, 3) {
		return false
	}

	// 比较前3档卖盘
	if !client.compareLevels(cached.Asks, rest.Asks, 3) {
		return false
	}

	return true
}

// compareLevels 比较指定档数的价格档位
func (client *BybitRESTClient) compareLevels(cached, rest []OrderBookLevel, levels int) bool {
	maxLevels := levels
	if len(cached) < maxLevels {
		maxLevels = len(cached)
	}
	if len(rest) < maxLevels {
		maxLevels = len(rest)
	}

	for i := 0; i < maxLevels; i++ {
		// 价格必须完全一致
		if !cached[i].Price.Equal(rest[i].Price) {
			return false
		}

		// 数量允许小幅差异（5%以内）
		if cached[i].Quantity.IsZero() && rest[i].Quantity.IsZero() {
			continue
		}

		if cached[i].Quantity.IsZero() || rest[i].Quantity.IsZero() {
			return false
		}

		diff := cached[i].Quantity.Sub(rest[i].Quantity).Abs()
		tolerance := cached[i].Quantity.Mul(decimal.NewFromFloat(0.05)) // 5%容差

		if diff.GreaterThan(tolerance) {
			return false
		}
	}

	return true
}

// InstrumentInfo 合约信息
type InstrumentInfo struct {
	Symbol       string `json:"symbol"`
	Status       string `json:"status"`
	BaseCoin     string `json:"baseCoin"`
	QuoteCoin    string `json:"quoteCoin"`
	OptionsType  string `json:"optionsType"`
	LaunchTime   string `json:"launchTime"`
	DeliveryTime string `json:"deliveryTime"`
}

// BybitInstrumentResponse 合约信息响应
type BybitInstrumentResponse struct {
	RetCode int    `json:"retCode"`
	RetMsg  string `json:"retMsg"`
	Result  struct {
		Category string           `json:"category"`
		List     []InstrumentInfo `json:"list"`
	} `json:"result"`
	Time int64 `json:"time"`
}

// BybitPositionResponse REST API持仓响应
type BybitPositionResponse struct {
	RetCode int    `json:"retCode"`
	RetMsg  string `json:"retMsg"`
	Result  struct {
		List []struct {
			Symbol         string `json:"symbol"`
			Size           string `json:"size"`
			Side           string `json:"side"`
			PositionValue  string `json:"positionValue"`
			AvgPrice       string `json:"avgPrice"` // REST API使用avgPrice作为入场价格
			MarkPrice      string `json:"markPrice"`
			LiqPrice       string `json:"liqPrice"`
			UnrealisedPnl  string `json:"unrealisedPnl"`
			CurRealisedPnl string `json:"curRealisedPnl"` // REST API使用curRealisedPnl作为已结盈亏
			CreatedTime    string `json:"createdTime"`
			UpdatedTime    string `json:"updatedTime"`
		} `json:"list"`
	} `json:"result"`
	Time int64 `json:"time"`
}

// GetInstrumentInfo 获取合约信息
func (client *BybitRESTClient) GetInstrumentInfo(category, symbol string) (*InstrumentInfo, error) {
	// 构造请求URL
	url := fmt.Sprintf("%s/v5/market/instruments-info?category=%s&symbol=%s",
		client.baseURL, category, symbol)

	// 发送HTTP请求
	resp, err := client.client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var response BybitInstrumentResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	// 检查API响应状态
	if response.RetCode != 0 {
		// 特殊处理合约不存在的情况
		if response.RetCode == 110023 {
			return nil, fmt.Errorf("110023: %s", response.RetMsg)
		}
		return nil, fmt.Errorf("API错误 %d: %s", response.RetCode, response.RetMsg)
	}

	// 检查是否有合约信息
	if len(response.Result.List) == 0 {
		return nil, fmt.Errorf("合约不存在: %s", symbol)
	}

	return &response.Result.List[0], nil
}

// GetPositions 获取期权持仓数据
func (client *BybitRESTClient) GetPositions(apiKey, secretKey string) (map[string]*OptionPositionData, error) {
	// 构造请求URL和查询参数
	queryString := "category=option"
	url := fmt.Sprintf("%s/v5/position/list?%s", client.baseURL, queryString)

	// 创建带认证的HTTP请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 添加认证头
	client.addAuthHeaders(req, apiKey, secretKey, queryString)

	//log.Printf("🔍 [REST API请求] URL: %s", url)
	//log.Printf("🔍 [REST API请求头] API-KEY: %s", req.Header.Get("X-BAPI-API-KEY"))
	//log.Printf("🔍 [REST API请求头] TIMESTAMP: %s", req.Header.Get("X-BAPI-TIMESTAMP"))

	// 发送HTTP请求
	resp, err := client.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	//log.Printf("🔍 [REST API响应] Status: %s", resp.Status)

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 调试：输出原始响应
	//log.Printf("🔍 [REST API原始响应] %s", string(body))

	// 解析JSON响应
	var response BybitPositionResponse
	if err := json.Unmarshal(body, &response); err != nil {
		log.Printf("❌ REST API JSON解析失败: %v", err)
		log.Printf("🔍 [解析失败的响应] %s", string(body))
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	// 检查API响应状态
	if response.RetCode != 0 {
		log.Printf("❌ REST API错误: RetCode=%d, RetMsg=%s", response.RetCode, response.RetMsg)
		return nil, fmt.Errorf("API错误: %s", response.RetMsg)
	}

	//log.Printf("✅ REST API解析成功，持仓数量: %d", len(response.Result.List))

	// 转换为OptionPositionData格式
	positions := make(map[string]*OptionPositionData)
	for _, pos := range response.Result.List {
		//log.Printf("🔍 [持仓数据] Symbol: %s, Size: %s, Side: %s", pos.Symbol, pos.Size, pos.Side)

		// 只处理期权合约 - 使用修复后的判断逻辑
		// 期权合约格式：ETH-4JUN25-2700-C-USDT
		isOption := strings.HasSuffix(pos.Symbol, "-USDT") &&
			(strings.Contains(pos.Symbol, "-C-") || strings.Contains(pos.Symbol, "-P-"))

		if !isOption {
			log.Printf("⚠️ 跳过非期权合约: %s", pos.Symbol)
			continue
		}

		//log.Printf("✅ 识别为期权合约: %s", pos.Symbol)

		optionPos := &OptionPositionData{
			Symbol:      pos.Symbol,
			Side:        pos.Side,
			CreatedTime: pos.CreatedTime,
			UpdatedTime: pos.UpdatedTime,
		}

		// 解析decimal字段（添加空值检查）
		if pos.Size != "" {
			if size, err := decimal.NewFromString(pos.Size); err == nil {
				optionPos.Size = size
			}
		} else {
			optionPos.Size = decimal.Zero
		}

		if pos.PositionValue != "" {
			if posValue, err := decimal.NewFromString(pos.PositionValue); err == nil {
				optionPos.PositionValue = posValue
			}
		} else {
			optionPos.PositionValue = decimal.Zero
		}

		// 修复：REST API使用avgPrice作为入场价格
		if pos.AvgPrice != "" {
			if avgPrice, err := decimal.NewFromString(pos.AvgPrice); err == nil {
				optionPos.EntryPrice = avgPrice
			}
		} else {
			optionPos.EntryPrice = decimal.Zero
		}

		if pos.MarkPrice != "" {
			if markPrice, err := decimal.NewFromString(pos.MarkPrice); err == nil {
				optionPos.MarkPrice = markPrice
			}
		} else {
			optionPos.MarkPrice = decimal.Zero
		}

		if pos.LiqPrice != "" {
			if liqPrice, err := decimal.NewFromString(pos.LiqPrice); err == nil {
				optionPos.LiqPrice = liqPrice
			}
		} else {
			optionPos.LiqPrice = decimal.Zero
		}

		if pos.UnrealisedPnl != "" {
			if unrealisedPnl, err := decimal.NewFromString(pos.UnrealisedPnl); err == nil {
				optionPos.UnrealisedPnl = unrealisedPnl
			}
		} else {
			optionPos.UnrealisedPnl = decimal.Zero
		}

		// 修复：REST API使用curRealisedPnl作为已结盈亏
		if pos.CurRealisedPnl != "" {
			if curRealisedPnl, err := decimal.NewFromString(pos.CurRealisedPnl); err == nil {
				optionPos.CumRealisedPnl = curRealisedPnl
			}
		} else {
			optionPos.CumRealisedPnl = decimal.Zero
		}

		positions[pos.Symbol] = optionPos
		//log.Printf("✅ 成功解析期权持仓: %s", pos.Symbol)
	}

	return positions, nil
}
