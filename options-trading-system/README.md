# 期权交易系统 (Options Trading System)

一个智能的期权交易系统，基于实时期权数据监控和时间价值分析自动执行期权交易，并与动态对冲系统联动实现风险管理。

## 📈 最新更新

### v2.3.0 - 期权持仓实时监控系统 (2025/01/02)

#### 🚀 重大更新：期权持仓实时监控
1. **WebSocket持仓订阅** ✅
   - 通过Bybit私有WebSocket实时订阅期权持仓数据
   - 支持期权合约名、仓位、价值、入场价格、标记价格等完整信息
   - 自动过滤期权合约，只显示相关持仓

2. **本地持仓缓存** ✅
   - 维护完整的期权持仓本地缓存
   - 实时更新持仓变化
   - 支持持仓数据的增删改查操作

3. **REST API数据对齐** ✅
   - 定期使用REST API验证WebSocket缓存数据
   - 自动同步数据不一致的持仓
   - 30秒间隔的数据完整性检查

4. **新增交互命令** ✅
   - `list_c` 命令：展示所有期权合约持仓状态
   - 表格化显示：合约名、方向、仓位、价值、入场价格、标记价格、未结盈亏、已结盈亏
   - 参考 `list_m` 命令的展示格式

#### 🔧 技术实现
1. **私有WebSocket认证**
   - HMAC-SHA256签名认证
   - 自动重连和心跳保持
   - 错误处理和异常恢复

2. **数据结构扩展**
   - `OptionPositionData`: 期权持仓数据结构
   - `OptionPositionCache`: 持仓缓存管理
   - 完整的持仓生命周期管理

3. **系统集成**
   - 与现有监控系统无缝集成
   - 独立的私有WebSocket连接
   - 不影响现有期权交易功能

### v2.2.0 - 期权状态管理系统重构 (2025/01/02)

#### 🚀 重大更新：期权合约状态管理
1. **全新状态管理系统** ✅
   - 新增6种期权状态：Trading、Delivering、Delivered、PreLaunch、NotExist、FetchFailed
   - 动态检查频率：正常30分钟、1小时内5分钟、15分钟内1分钟
   - 智能API调用频率控制，避免同一秒发出多个请求

2. **订阅前状态验证** ✅
   - 只订阅Trading状态的期权合约
   - 状态变化时自动取消非Trading合约订阅
   - 避免订阅已行权或不存在的期权

3. **周期性重试机制** ✅
   - 每个检查周期独立的失败计数
   - 连续3次失败才标记为获取失败
   - 5秒重试间隔，最大3次重试

4. **批量状态检查** ✅
   - 每批最多10个合约，批次间隔1秒
   - 基于合约名称hash的时间偏移
   - 异步处理，提高系统性能

5. **增强用户界面** ✅
   - list_m命令显示详细状态统计
   - 新增contract_state命令查看合约状态详情
   - 按时间价值排序的期权监控表格

#### 🔧 技术改进
1. **数据结构扩展**
   - ContractStatus新增状态相关字段
   - 支持监控开始时间、检查间隔等
   - 完整的状态生命周期管理

2. **配置管理优化**
   - embedded_config中新增StateCheckConfig
   - 可配置的检查间隔和重试参数
   - 默认配置适合生产环境使用

3. **日志优化**
   - 静默正常操作，只输出异常情况
   - 状态变化事件详细记录
   - 减少日志噪音，提高可读性

## 主要功能

- 🎯 实时期权数据监控和时间价值计算
- 📊 智能期权合约筛选和盘口深度分析
- ⚡ 自动期权交易执行和持仓管理
- 🔄 与动态对冲系统联动的风险管理
- 🎛️ 交互式命令行控制和参数配置
- 💾 完整的数据持久化和事件记录
- 📝 详细的交易统计和持仓跟踪
- 🔧 灵活的监控任务管理系统

## 系统架构

### 核心组件
- **监控任务系统**: 基于时间价值的期权交易监控
- **持仓跟踪系统**: 期权持仓的完整生命周期管理
- **对冲联动系统**: 与动态对冲程序的API交互
- **数据管理系统**: 实时数据订阅和本地持久化

### 交易流程
1. **数据订阅**: 通过Bybit WebSocket订阅期权实时数据
2. **合约筛选**: 根据时间和价格阈值筛选目标期权合约
3. **时间价值计算**: 实时计算期权的时间价值
4. **交易决策**: 基于多重条件判断是否执行交易
5. **自动下单**: 满足条件时自动执行期权卖出交易
6. **持仓跟踪**: 创建持仓跟踪任务记录交易详情
7. **对冲联动**: 自动为对冲系统创建相应的动态对冲任务
8. **持续监控**: 定期更新持仓状态并同步对冲任务

## 时间价值计算逻辑

系统采用标准的期权时间价值计算方法，结果以百分比形式表示：

### 计算公式
- **PUT期权**: `time_value = ((spot_price >= strike_price) ? bid1_price : bid1_price - (strike_price - spot_price)) / spot_price`
- **CALL期权**: `time_value = ((spot_price <= strike_price) ? bid1_price : bid1_price - (spot_price - strike_price)) / spot_price`

### 参数说明
- `spot_price`: 标的资产当前市价
- `strike_price`: 期权行权价格
- `bid1_price`: 期权买一价
- `time_value`: 计算得出的时间价值（百分比形式）

### 计算示例
假设ETH现货价格为2500 USDT，PUT期权行权价为2600 USDT，买一价为120 USDT：
```
PUT时间价值 = ((2500 >= 2600) ? 120 : 120 - (2600 - 2500)) / 2500
           = (120 - 100) / 2500
           = 20 / 2500
           = 0.008 (0.8%)
```

### 交易逻辑
当实时计算的时间价值大于监控任务设定的目标时间价值时，触发卖出期权的交易信号。
目标时间价值以百分比形式设置，如 0.012 表示 1.2%。

## 期权合约筛选机制

### 合约命名规则
Bybit期权合约命名格式：`ETHUSDT-3JUN25-2475-C`
- **标的资产**: ETHUSDT
- **到期日期**: 3JUN25 (2025年6月3日)
- **行权价格**: 2475
- **期权类型**: C(Call) / P(Put)

### 到期时间规则
所有期权在命名日期当天16:00行权，如：
- `ETHUSDT-3JUN25-2475-C` 在2025年6月3日16:00行权

### 动态筛选逻辑
系统根据监控参数动态筛选期权合约：

1. **获取现货价格**: 实时获取标的资产价格（如ETH = 2483 USDT）
2. **计算价格范围**: `spot_price × (1 ± price_range)`
   - 如price_range=0.1: 2483×0.9 到 2483×1.1 = 2234.7-2731.3
3. **计算到期时间**: 根据expiry_threshold筛选即将到期的期权
   - 如24h内到期：筛选24小时内16:00行权的期权
4. **生成监控列表**:
   ```
   ETHUSDT-3JUN25-2250-C
   ETHUSDT-3JUN25-2300-C
   ETHUSDT-3JUN25-2350-C
   ...
   ETHUSDT-3JUN25-2700-C
   ```

## 高频监控系统

### WebSocket实时监控
系统采用事件驱动的高频监控机制：

- **更新频率**: ~100ms，Bybit盘口有变化立即推送
- **触发机制**: 每次收到盘口更新立即检查交易条件
- **响应时间**: 盘口更新到交易条件检查 < 10ms
- **并发能力**: 支持同时监控100+期权合约

### 智能订阅管理
- **去重订阅**: 相同期权盘口只订阅一次，避免重复
- **动态管理**: 新增监控任务时智能检查订阅需求
- **资源优化**: 监控任务删除时自动清理无用订阅

### 交易执行机制
```
盘口更新 → 实时检查 → 满足条件 → 下单前二次确认 → 执行交易
                                    ↓
                                不满足 → 取消下单
```

### 防冲突设计
- **下单前确认**: 发送订单前再次检查盘口条件
- **状态跟踪**: 实时跟踪订单状态和成交情况
- **仓位同步**: 成交后立即更新持仓并重新评估交易条件

## 🔌 WebSocket 连接与数据缓存系统

### 📊 全新订单簿缓存架构

#### 核心设计理念
系统采用本地完整订单簿缓存机制，确保数据完整性和交易决策的准确性：

- **完整缓存**: 维护多档位完整订单簿，不仅仅是买一卖一价
- **智能监控**: 区分 WebSocket 连接异常和市场静止状态
- **数据验证**: REST API 定期验证 WebSocket 数据准确性
- **故障恢复**: 自动检测数据不一致并重新订阅

#### 数据处理流程
```
WebSocket 消息 → 解析类型 → 更新本地缓存 → 触发交易检查
     ↓              ↓           ↓            ↓
  Snapshot      Delta      完整订单簿      实时决策
   (完整)      (增量)      (25档深度)     (毫秒级)
```

### 🔍 智能监控机制

#### 三层监控体系
1. **5秒状态检查**: 区分连接异常和市场静止
   - 连接正常 + 无更新 = 市场静止 (继续使用缓存)
   - 连接异常 = 标记错误状态

2. **10秒数据验证**: REST API 验证缓存准确性
   - 单合约验证，不影响其他正常合约
   - 数据一致 = 继续使用缓存
   - 数据不一致 = 自动重新订阅

3. **实时状态更新**: 动态调整合约状态
   - 🟢 正常: 最近5秒有更新
   - 🟡 静止: 5秒-10分钟无更新，但连接正常
   - 🔴 异常: 连接断开或数据验证失败

### 关键技术问题与解决方案

#### 1. 统一合约格式 ✅
**解决方案**: 全系统统一使用 Bybit 格式，避免格式转换
- 统一格式: `ETH-4JUN25-2500-C-USDT`
- 消除转换错误和性能损耗
- 简化代码逻辑

#### 2. 完整订单簿缓存系统 ✅
**核心数据结构**:
```go
// 订单簿档位
type OrderBookLevel struct {
    Price    decimal.Decimal `json:"price"`
    Quantity decimal.Decimal `json:"quantity"`
}

// 完整订单簿
type FullOrderBook struct {
    Symbol      string            `json:"symbol"`
    Bids        []OrderBookLevel  `json:"bids"`        // 买盘多档位
    Asks        []OrderBookLevel  `json:"asks"`        // 卖盘多档位
    LastUpdate  time.Time         `json:"last_update"`
    UpdateCount int64             `json:"update_count"`
}

// 合约状态管理
type ContractStatus struct {
    Symbol          string    `json:"symbol"`
    LastWSUpdate    time.Time `json:"last_ws_update"`
    WSConnected     bool      `json:"ws_connected"`
    Status          string    `json:"status"`  // normal/stale/error
    RestCheckActive bool      `json:"rest_check_active"`
}
```

#### 3. 智能数据更新机制 ✅
**Snapshot vs Delta 处理**:
```go
func (client *BybitWSClient) handleOrderBookUpdate(message *BybitWSMessage) {
    symbol := orderBookData.Data.Symbol
    orderBook := client.orderBookCache.GetOrCreateOrderBook(symbol)

    // 根据消息类型更新订单簿
    if messageType == "snapshot" {
        orderBook.UpdateSnapshot(bids, asks)  // 完整替换
    } else if messageType == "delta" {
        orderBook.UpdateDelta(bids, asks)     // 增量更新
    }

    // 更新合约状态
    client.updateContractStatus(symbol)
}
```

#### 4. REST API 数据验证 ✅
**10秒验证机制**:
```go
func (monitor *ContractMonitor) verifyContractData(symbol string) {
    // 获取REST API数据
    restOrderBook, err := monitor.restClient.GetOrderBook(symbol, 25)
    if err != nil {
        return
    }

    // 获取缓存数据
    cachedOrderBook, exists := monitor.wsClient.orderBookCache.GetOrderBook(symbol)
    if !exists {
        return
    }

    // 比较数据一致性
    if monitor.restClient.CompareOrderBooks(cachedOrderBook, restOrderBook) {
        log.Printf("✅ 合约 %s 数据验证通过", symbol)
    } else {
        log.Printf("❌ 合约 %s 数据不一致，重新订阅", symbol)
        monitor.resubscribeContract(symbol)
    }
}
```

#### 3. WebSocket 数据结构解析 ⚠️
**问题**: Bybit 期权 WebSocket 返回的数据结构与文档不完全一致

**实际数据结构**:
```json
{
  "topic": "orderbook.25.ETH-4JUN25-2500-C-USDT",
  "type": "snapshot",
  "data": {
    "s": "ETH-4JUN25-2500-C-USDT",
    "b": [["117.8", "0.01"], ["117.7", "0.02"]],
    "a": [["118.0", "0.01"], ["118.1", "0.02"]],
    "u": 12345,
    "seq": 67890
  },
  "cts": 1701234567890
}
```

**解决方案**:
```go
type OrderBookData struct {
    Symbol   string     `json:"s"`
    Bids     [][]string `json:"b"`
    Asks     [][]string `json:"a"`
    UpdateID int64      `json:"u"`
    Sequence int64      `json:"seq"`
}

type WebSocketMessage struct {
    Topic string         `json:"topic"`
    Type  string         `json:"type"`
    Data  *OrderBookData `json:"data"`
    Ts    int64          `json:"cts"`
}
```

#### 4. 连接稳定性优化 ⚠️
**问题**: WebSocket 连接在高频数据更新时可能不稳定

**解决方案**:
```go
// 连接重试机制
func (ws *WebSocketClient) connectWithRetry() error {
    maxRetries := 5
    for i := 0; i < maxRetries; i++ {
        if err := ws.connect(); err == nil {
            return nil
        }
        time.Sleep(time.Duration(i+1) * time.Second)
    }
    return fmt.Errorf("连接失败，已重试 %d 次", maxRetries)
}

// 心跳保持
func (ws *WebSocketClient) startHeartbeat() {
    ticker := time.NewTicker(20 * time.Second)
    go func() {
        for range ticker.C {
            ws.conn.WriteMessage(websocket.PingMessage, []byte{})
        }
    }()
}
```

### 数据验证与错误处理

#### 数据完整性检查
```go
func (ws *WebSocketClient) validateOrderBookData(data *OrderBookData) error {
    if data.Symbol == "" {
        return fmt.Errorf("期权合约符号为空")
    }
    if len(data.Bids) == 0 && len(data.Asks) == 0 {
        return fmt.Errorf("订单簿数据为空")
    }
    return nil
}
```

#### 价格数据解析
```go
func parsePrice(priceStr string) (float64, error) {
    price, err := strconv.ParseFloat(priceStr, 64)
    if err != nil {
        return 0, fmt.Errorf("价格解析失败: %s", priceStr)
    }
    if price <= 0 {
        return 0, fmt.Errorf("无效价格: %f", price)
    }
    return price, nil
}
```

### 性能优化

#### 并发处理
```go
// 使用 goroutine 池处理 WebSocket 消息
type MessageProcessor struct {
    workerPool chan chan WebSocketMessage
    workers    []Worker
}

func (mp *MessageProcessor) processMessage(msg WebSocketMessage) {
    select {
    case worker := <-mp.workerPool:
        worker <- msg
    default:
        // 处理队列满的情况
        log.Warn("消息处理队列已满，丢弃消息")
    }
}
```

#### 内存管理
```go
// 定期清理过期的订单簿数据
func (ws *WebSocketClient) cleanupExpiredData() {
    ticker := time.NewTicker(5 * time.Minute)
    go func() {
        for range ticker.C {
            ws.mu.Lock()
            for symbol, lastUpdate := range ws.lastUpdateTime {
                if time.Since(lastUpdate) > 10*time.Minute {
                    delete(ws.orderBooks, symbol)
                    delete(ws.lastUpdateTime, symbol)
                }
            }
            ws.mu.Unlock()
        }
    }()
}
```

### 调试与监控

#### 连接状态监控
```go
func (ws *WebSocketClient) monitorConnection() {
    go func() {
        for {
            select {
            case <-ws.pingTicker.C:
                if err := ws.ping(); err != nil {
                    log.Error("WebSocket ping 失败: %v", err)
                    ws.reconnect()
                }
            case <-ws.done:
                return
            }
        }
    }()
}
```

#### 数据质量监控
```go
func (ws *WebSocketClient) logDataQuality() {
    ticker := time.NewTicker(1 * time.Minute)
    go func() {
        for range ticker.C {
            ws.mu.RLock()
            activeSymbols := len(ws.orderBooks)
            totalUpdates := ws.totalUpdates
            ws.mu.RUnlock()

            log.Info("数据质量报告: 活跃合约=%d, 总更新数=%d",
                activeSymbols, totalUpdates)
        }
    }()
}
```

## 系统架构

### 多线程设计
```
主线程: 监控任务管理和用户交互
  ↓
WebSocket线程: 盘口数据接收和分发
  ↓
检查线程池: 并行检查多个期权的交易条件
  ↓
交易线程: 执行下单和仓位管理
```

### 数据流架构
```
现货价格更新 → 重新计算监控合约 → 更新订阅列表
期权盘口更新 → 触发相关任务检查 → 满足条件则执行交易
交易成交确认 → 更新持仓数据 → 同步对冲任务 → 继续监控
```

## 依赖包安装

```bash
# 初始化模块并安装依赖
go mod init options-trading-system
go mod tidy

# 主要依赖包
go get github.com/adshao/go-binance/v2  # 用于参考WebSocket实现
go get github.com/shopspring/decimal
go get github.com/gorilla/websocket
go get github.com/gin-gonic/gin
go get github.com/atotto/clipboard
```

## 配置说明

系统使用内嵌配置，Bybit API密钥等配置已编译到程序中，无需外部配置文件。

### 默认全局参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `expiry_threshold` | 24h | 监控行权时间阈值 |
| `price_range` | 0.1 (10%) | 监控价格范围阈值 |
| `max_position` | 1.0 | 单个期权最高持仓量 |
| `order_size` | 0.1 | 单次下单量 |
| `depth_threshold` | 0.05 (5%) | 盘口深度检测阈值 |
| `depth_levels` | 5 | 盘口监控档数 |
| `hedge_threshold` | 0.0002 (0.02%) | 动态对冲阈值 |

## 使用方法

### 编译和运行

```bash
# 编译程序
go build -o /options-trading-system/options-system *.go && ./options-trading-system/options-system --test
(cd options-trading-system && go run . --test)
# 运行交互模式
./options-system --test

# 查看帮助
./options-system --help
```

## 交互命令

### 系统命令
```bash
help                           # 显示所有可用命令
quit / exit                    # 退出程序
```

### 全局参数设置
```bash
set expiry_threshold 12h       # 设置监控行权时间阈值
set price_range 0.15           # 设置监控价格范围阈值 (15%)
set max_position 2.0           # 设置单个期权最高持仓量
set order_size 0.1             # 设置单次下单量
set depth_threshold 0.05       # 设置盘口深度检测阈值 (5%)
set depth_levels 5             # 设置盘口监控档数
set hedge_threshold 0.0002     # 设置动态对冲阈值 (0.02%)
show defaults                  # 显示当前所有默认参数
```

### 监控任务管理
```bash
# 添加监控任务（默认自动启动）
add_m call 5h 0.012            # 添加CALL期权监控 (类型 时间范围 目标时间价值1.2%)
add_m put 3h-8h 0.008          # 添加PUT期权监控 (支持时间范围，目标0.8%)

# 任务管理（支持简化命令）
list_m                         # 列出所有监控任务
show_m <id>                    # 显示监控任务详情
stop_m <id>                    # 停止监控任务
start_m <id>                   # 启动监控任务
delete_m <id>                  # 删除监控任务

# 参数修改
modify_m <id> time_value_target 0.015    # 修改目标时间价值为1.5%
modify_m <id> expiry_time_range 6h       # 修改时间范围
```

### 持仓跟踪管理
```bash
list_p                         # 列出所有期权持仓
show_p <contract>              # 显示具体持仓详情和事件记录
delete_p <contract>            # 删除持仓跟踪任务

# 示例
show_p ETHUSDT-3JUN25-2500-C
```

### 任务ID格式
- **格式**: M1234567 (M + 7位数字)
- **示例**: M7083000, M1234567
- **生成**: 基于时间戳的唯一标识

### 数据管理
```bash
save                          # 手动保存数据
export                        # 导出详细报告到文件
```

### WebSocket 监控
```bash
ws_status / websocket         # 显示期权WebSocket连接状态表格
                             # 包含连接状态、最后更新时间、买一价等信息
```

#### WebSocket 状态说明
- **🟢 正常**: 最近5秒内有数据更新
- **⚠️ 异常**: 超过5秒无更新或连接问题
- **更新次数**: 显示该合约的数据更新计数
- **时间显示**: 距离最后更新的时间间隔

## 监控任务系统

### 任务参数
每个监控任务包含以下核心参数：
- **option_type**: 期权类型 ("call" 或 "put")
- **expiry_time_range**: 行权时间范围 (如 "5h" 或 "5h-10h")
- **time_value_target**: 目标时间价值阈值

### 交易触发条件
监控任务会持续检测期权合约，当以下条件同时满足时触发交易：

1. **时间条件**: 期权剩余到期时间符合设定范围
2. **价格条件**: 行权价格在监控范围内 `[spot_price*(1-price_range), spot_price*(1+price_range)]`
3. **深度条件**: 盘口深度满足要求 `(order_size/bid1_quantity) > depth_threshold`
4. **持仓条件**: 当前持仓量小于最大持仓限制
5. **价值条件**: 实时计算的时间价值大于目标时间价值

### 交易执行
满足条件时，系统会：
1. 以市价方式卖出期权
2. 下单数量为设定的 `order_size`
3. 记录详细的成交信息
4. 创建或更新持仓跟踪任务
5. 自动为对冲系统创建相应的动态对冲任务

## 持仓跟踪系统

### 自动创建
当期权交易成交后，系统会自动创建持仓跟踪任务：
- **任务命名**: 使用期权合约名称 (如 `ETHUSDT-2JUN25-2500-C`)
- **数据记录**: 完整的交易历史和持仓信息
- **事件跟踪**: 记录所有相关事件和状态变化

### 持仓信息
每个持仓跟踪任务维护以下信息：
- **基本信息**: 合约名称、期权类型、行权价格、到期时间
- **持仓数据**: 当前持仓量、持仓均价、总成本
- **盈亏统计**: 实时盈亏、盈亏比例、累计手续费
- **交易记录**: 每笔交易的详细信息
- **对冲绑定**: 关联的动态对冲任务ID

### 持续监控
系统会定期检查持仓状态：
- **持仓更新**: 通过Bybit API定期查询最新持仓
- **状态同步**: 发现变化时立即更新对冲任务
- **自动结束**: 持仓归零或期权行权时自动结束跟踪

## 与动态对冲系统联动

### API接口设计
期权系统通过RESTful API与动态对冲系统交互：

#### 1. 创建对冲任务
```http
POST /api/tasks
Content-Type: application/json

{
  "direction": "long",
  "target_price": 2500,
  "stop_rate": 0.0002,
  "amount": 0.1,
  "symbol": "ETHUSDT",
  "source": "options",
  "option_contract": "ETHUSDT-2JUN25-2500-C"
}
```

#### 2. 更新对冲任务数量
```http
PUT /api/tasks/{task_id}/amount
Content-Type: application/json

{
  "amount": 0.3,
  "update_type": "sell_open",  # 更新类型: sell_open, buy_close, exercise
  "source": "options",
  "option_contract": "ETHUSDT-2JUN25-2500-C"
}
```

#### 3. 查询绑定任务
```http
GET /api/tasks/by-option/{option_contract}
```

### 联动逻辑
1. **期权成交**: 自动创建或更新对冲任务
2. **持仓变化**: 实时同步对冲任务数量
3. **期权行权**: 将对冲任务数量更新为0
4. **来源标识**: 区分手动创建和期权程序创建的任务

## 数据持久化

### 存储结构
- **`data/monitors.json`**: 监控任务配置和状态
- **`data/positions.json`**: 持仓跟踪数据和交易记录
- **`data/events.json`**: 系统事件和操作日志
- **`data/backup/`**: 自动备份目录

### 自动保存
- **实时触发**: 交易成交、持仓变化时自动保存
- **定时保存**: 默认每5分钟自动保存一次
- **退出保存**: 程序正常退出时强制保存所有数据

## 技术特性

- **实时WebSocket**: 基于Bybit WebSocket获取实时期权数据
- **并发安全**: 支持多任务并发执行，线程安全设计
- **精确计算**: 使用decimal库确保价格和时间价值计算精度
- **错误处理**: 完善的错误处理和重连机制
- **API集成**: 与动态对冲系统的无缝集成
- **事件驱动**: 基于事件的系统架构，响应迅速
- **数据完整性**: 完整的数据持久化和恢复机制

## 🔍 故障排除

### 常见问题

#### 1. WebSocket 连接失败
**症状**:
- `❌ WebSocket连接失败`
- `连接超时或被拒绝`

**解决方案**:
1. **检查网络连接**
   ```bash
   # 测试网络连通性
   ping api.bybit.com
   curl -I https://api.bybit.com
   ```

2. **验证 API 密钥权限**
   - 确认 API 密钥具有期权交易权限
   - 检查 IP 白名单设置
   - 验证密钥是否过期

3. **确认 Bybit 服务状态**
   - 访问 Bybit 官方状态页面
   - 检查期权交易是否暂停

#### 2. 期权数据获取异常
**症状**:
- `❌ 期权合约格式错误`
- `订阅失败: orderbook.1.XXX`
- `💰 更新买一价: 0` (价格始终为0)

**解决方案**:
1. **检查期权合约格式转换**
   ```bash
   # 确认内部格式: ETHUSDT-4JUN25-2500-C
   # 确认 Bybit 格式: ETH-4JUN25-2500-C-USDT
   ```

2. **验证订单簿深度配置**
   ```go
   // 错误: orderbook.1.ETH-4JUN25-2500-C-USDT
   // 正确: orderbook.25.ETH-4JUN25-2500-C-USDT
   ```

3. **确认期权合约是否存在**
   - 检查到期日期是否正确
   - 验证行权价格是否在交易范围内
   - 确认交易时间 (期权在16:00行权)

#### 3. 数据结构解析错误
**症状**:
- `JSON解析失败`
- `数据字段缺失`

**解决方案**:
1. **使用正确的数据结构**
   ```go
   // 确保使用正确的字段名
   type OrderBookData struct {
       Symbol   string     `json:"s"`    // 不是 "symbol"
       Bids     [][]string `json:"b"`    // 不是 "bids"
       Asks     [][]string `json:"a"`    // 不是 "asks"
       UpdateID int64      `json:"u"`
       Sequence int64      `json:"seq"`
   }
   ```

2. **验证 WebSocket 消息格式**
   ```go
   // 时间戳字段是 "cts" 不是 "ts"
   type WebSocketMessage struct {
       Topic string         `json:"topic"`
       Type  string         `json:"type"`
       Data  *OrderBookData `json:"data"`
       Ts    int64          `json:"cts"`  // 注意字段名
   }
   ```

#### 4. 对冲系统连接失败
**症状**:
- `对冲系统API调用失败`
- `连接被拒绝`

**解决方案**:
1. **检查对冲系统是否运行**
   ```bash
   curl http://localhost:5879/api/status
   ```

2. **验证 API 地址配置**
   - 确认端口号正确
   - 检查防火墙设置

3. **确认网络连通性**
   ```bash
   telnet localhost 5879
   ```

### 调试模式

#### 启用详细日志
```bash
# 启用详细日志
go run . --test --verbose

# 启用 WebSocket 调试
export DEBUG_WEBSOCKET=true
go run . --test
```

#### 测试特定功能
```bash
# 测试 WebSocket 连接
go run . --test-websocket

# 验证期权合约格式转换
go run . --test-format-conversion

# 监控连接状态
go run . --monitor-connection
```

#### 检查系统状态
```bash
# 检查对冲系统状态
curl http://localhost:5879/api/status

# 查看期权系统日志
tail -f options-system.log
```

### WebSocket 调试技巧

#### 1. 查看原始 WebSocket 消息
```go
// 在 websocket.go 中添加调试输出
func (ws *WebSocketClient) handleMessage(message []byte) {
    if os.Getenv("DEBUG_WEBSOCKET") == "true" {
        fmt.Printf("🔍 原始消息: %s\n", string(message))
    }
    // ... 处理逻辑
}
```

#### 2. 验证订阅状态
```bash
# 在程序运行时检查订阅状态
list_m  # 查看监控任务
# 确认每个任务的期权合约都有对应的 WebSocket 订阅
```

#### 3. 监控数据更新频率
```go
// 添加数据更新统计
func (ws *WebSocketClient) logUpdateStats() {
    ticker := time.NewTicker(30 * time.Second)
    go func() {
        for range ticker.C {
            fmt.Printf("📊 数据更新统计: 活跃合约=%d, 更新次数=%d\n",
                len(ws.orderBooks), ws.updateCount)
        }
    }()
}
```

### 性能问题排查

#### 1. 内存使用过高
```bash
# 监控内存使用
go tool pprof http://localhost:6060/debug/pprof/heap
```

#### 2. CPU 使用率过高
```bash
# 监控 CPU 使用
go tool pprof http://localhost:6060/debug/pprof/profile
```

#### 3. WebSocket 连接数过多
```bash
# 检查连接数
netstat -an | grep :443 | wc -l
```

### 数据一致性检查

#### 1. 验证期权价格数据
```bash
# 在程序中添加价格验证
create_m ETH 4JUN25 C 10.0 1.2
list_m  # 查看是否显示真实价格数据
```

#### 2. 检查格式转换
```bash
# 手动测试格式转换
echo "ETHUSDT-4JUN25-2500-C" | 转换为 "ETH-4JUN25-2500-C-USDT"
```

#### 3. 验证订阅主题
```bash
# 确认订阅主题格式
# 正确: orderbook.25.ETH-4JUN25-2500-C-USDT
# 错误: orderbook.1.ETHUSDT-4JUN25-2500-C
```

## 注意事项

⚠️ **风险提示**：
- 期权交易具有高风险，可能导致全部本金损失
- 本系统仅用于教育和研究目的
- 实盘交易前请充分测试和验证策略
- 确保API密钥安全，不要在不安全的环境中运行
- 建议先使用小资金进行测试

💡 **使用建议**：
- 合理设置时间价值阈值，避免过于频繁的交易
- 定期检查持仓状态和对冲任务同步情况
- 监控期权到期时间，及时调整策略
- 使用详细的事件记录进行策略分析和优化

🔧 **WebSocket 连接建议**：
- 确保期权合约格式转换正确
- 使用期权专用的订单簿深度 (orderbook.25)
- 实现连接重试和心跳保持机制
- 定期清理过期的订单簿数据
- 监控数据更新频率和质量

## 版本更新记录

### v1.0.1 (2025-06-04) - WebSocket 连接优化

#### 🔧 关键问题修复
- **期权合约格式转换**: 修复 Bybit WebSocket 期权合约命名格式不一致问题
  - 内部格式: `ETHUSDT-4JUN25-2500-C`
  - Bybit 格式: `ETH-4JUN25-2500-C-USDT`
  - 实现双向格式转换函数

- **订单簿深度配置**: 修复期权订单簿深度配置错误
  - 从 `orderbook.1` 改为 `orderbook.25` (期权最小支持25档)
  - 解决订阅失败问题

- **WebSocket 数据结构**: 修复数据结构解析错误
  - 更正 JSON 字段映射 (`s`, `b`, `a`, `cts` 等)
  - 适配 Bybit 实际返回的数据格式

- **连接稳定性**: 增强 WebSocket 连接稳定性
  - 实现连接重试机制 (最多5次重试)
  - 添加心跳保持机制 (20秒间隔)
  - 自动重连和错误恢复

#### 📊 数据质量提升
- **实时数据验证**: 增加数据完整性检查和价格验证
- **性能优化**: 实现 goroutine 池处理 WebSocket 消息
- **内存管理**: 定期清理过期的订单簿数据
- **监控增强**: 添加连接状态和数据质量监控

#### 🔍 调试功能增强
- **详细故障排除**: 新增完整的 WebSocket 连接问题排查指南
- **调试模式**: 支持 WebSocket 原始消息调试
- **性能监控**: 添加内存、CPU 和连接数监控工具
- **数据一致性检查**: 提供格式转换和订阅状态验证工具

#### ✅ 验证结果
- **真实数据获取**: 成功获取 Bybit 期权实时盘口数据
- **价格更新**: 实时价格变化正常 (如 ETH-4JUN25-2500-C: 117.8 USDT)
- **监控任务**: 监控任务正常启动和运行
- **系统稳定性**: WebSocket 连接稳定，数据持续更新

### v1.0.0 (2025-05-31) - 初始版本

#### 🚀 核心功能
- **期权数据订阅**: 基于Bybit WebSocket的实时期权数据获取
- **时间价值计算**: 标准的PUT/CALL期权时间价值计算引擎
- **智能合约筛选**: 基于时间和价格阈值的期权合约筛选
- **自动交易执行**: 满足条件时自动执行期权卖出交易

#### 🎯 监控任务系统
- **灵活配置**: 支持单一时间点和时间范围的监控设置
- **多重条件**: 时间、价格、深度、持仓、价值的综合判断
- **实时监控**: 持续监控期权合约状态和市场变化
- **任务管理**: 完整的监控任务生命周期管理

#### 📊 持仓跟踪系统
- **自动创建**: 期权成交后自动创建持仓跟踪任务
- **完整记录**: 详细的交易历史和持仓信息维护
- **实时更新**: 定期同步Bybit持仓状态
- **生命周期**: 从开仓到平仓/行权的完整跟踪

#### 🔄 对冲系统联动
- **API集成**: 与动态对冲系统的RESTful API集成
- **智能绑定**: 期权合约与对冲任务的一对一绑定
- **实时同步**: 持仓变化时自动更新对冲任务数量
- **来源标识**: 区分手动和期权程序创建的对冲任务

#### 🎛️ 交互系统
- **命令行界面**: 完整的交互式命令行控制系统
- **参数配置**: 灵活的全局参数和任务参数设置
- **状态查询**: 详细的监控任务和持仓状态查询
- **数据管理**: 手动保存和导出功能

#### 💾 数据持久化
- **JSON存储**: 标准JSON格式的本地数据存储
- **自动备份**: 数据变更时自动备份机制
- **完整恢复**: 程序重启后完整恢复所有状态
- **事件记录**: 详细的系统事件和操作日志

#### 🔒 安全特性
- **内嵌配置**: API密钥等敏感信息内嵌编译
- **错误处理**: 完善的错误处理和异常恢复
- **并发安全**: 线程安全的多任务执行设计
- **精确计算**: decimal库确保金融计算精度
