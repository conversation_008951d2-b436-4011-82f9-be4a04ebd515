package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

var (
	// 命令行参数
	testMode = flag.Bool("test", false, "启动交互测试模式")
	help     = flag.Bool("help", false, "显示帮助信息")
)

func main() {
	// 解析命令行参数
	flag.Parse()

	// 显示帮助信息
	if *help {
		printUsage()
		return
	}

	// 显示启动信息
	printBanner()

	// 加载内嵌配置
	if err := LoadEmbeddedConfig(); err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化系统组件
	log.Println("🔧 初始化系统组件...")

	// 创建WebSocket客户端
	wsClient := NewBybitWSClient()

	// 创建私有WebSocket客户端（用于持仓数据）
	privateWSClient := NewBybitPrivateWSClient()

	// 创建期权合约管理器
	contractManager := NewOptionContractManager(wsClient)

	// 创建监控任务管理器
	monitorManager := NewMonitorManager(wsClient, contractManager)

	// 创建持仓管理器
	positionManager := NewPositionManager()

	// 创建对冲管理器
	hedgeManager := NewHedgeManager(positionManager)

	// 加载持久化数据
	log.Println("📄 加载持久化数据...")
	if err := monitorManager.LoadData(); err != nil {
		log.Printf("⚠️  加载监控任务数据失败: %v", err)
	}

	if err := positionManager.LoadData(); err != nil {
		log.Printf("⚠️  加载持仓数据失败: %v", err)
	}

	// 启动数据管理器
	monitorManager.StartDataManager(positionManager)

	// 初始化统一价格客户端
	log.Println("🚀 启动统一价格客户端...")
	if err := InitUnifiedPriceClient("ethusdt"); err != nil {
		log.Printf("❌ 启动统一价格客户端失败: %v", err)
		log.Println("⚠️  现货价格获取功能将不可用")
	} else {
		log.Println("✅ 统一价格客户端启动成功")
	}

	// 连接WebSocket
	log.Println("🔗 连接Bybit WebSocket...")
	if err := wsClient.Connect(); err != nil {
		log.Printf("❌ WebSocket连接失败: %v", err)
		log.Println("⚠️  期权数据订阅功能将不可用")
		log.Println("💡 监控任务将无法启动，请检查网络连接或API配置")
	} else {
		log.Println("✅ WebSocket连接成功")
		log.Println("📊 WebSocket已准备好接收期权盘口数据")

		// 不再订阅现货数据，因为已经使用币安API获取价格
		// 期权盘口数据将在监控任务启动时按需订阅
	}

	// 连接私有WebSocket（持仓数据）
	log.Println("🔗 连接Bybit私有WebSocket（持仓数据）...")
	if err := privateWSClient.Connect(); err != nil {
		log.Printf("❌ 私有WebSocket连接失败: %v", err)
		log.Println("⚠️  期权持仓数据订阅功能将不可用")
	} else {
		log.Println("✅ 私有WebSocket连接成功")
		log.Println("📊 私有WebSocket已准备好接收期权持仓数据")
	}

	// 测试对冲系统连接
	log.Println("🔗 测试对冲系统连接...")
	if err := hedgeManager.TestConnection(); err != nil {
		log.Printf("⚠️  对冲系统连接测试失败: %v", err)
		log.Println("   请确保对冲系统正在运行并且API地址正确")
	} else {
		log.Println("✅ 对冲系统连接正常")
	}

	// 设置信号处理
	setupSignalHandler(monitorManager, positionManager, contractManager, wsClient)

	if *testMode {
		// 启动交互测试模式
		log.Println("🎛️  启动交互测试模式...")
		interactiveManager := NewInteractiveManager(monitorManager, positionManager, hedgeManager, privateWSClient)
		interactiveManager.StartInteractiveMode()
	} else {
		// 启动正常模式
		log.Println("🚀 启动期权交易系统...")
		startNormalMode(monitorManager, positionManager, hedgeManager)
	}
}

// printBanner 打印启动横幅
func printBanner() {
	fmt.Println(`
╔══════════════════════════════════════════════════════════════╗
║                    期权交易系统 v1.0.0                        ║
║                  Options Trading System                     ║
║                                                              ║
║  🎯 智能期权监控  📊 实时持仓跟踪  🔄 动态对冲联动            ║
║                                                              ║
║  基于Bybit API的期权交易自动化系统                           ║
╚══════════════════════════════════════════════════════════════╝
`)
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Println(`
期权交易系统 - 使用说明

用法:
  ./options-system [选项]

选项:
  --test      启动交互测试模式，可以通过命令行控制系统
  --help      显示此帮助信息

模式说明:
  正常模式:   启动完整的期权交易系统，自动执行监控和交易
  测试模式:   交互式命令行模式，用于测试和配置系统参数

示例:
  ./options-system                # 启动正常模式
  ./options-system --test         # 启动测试模式
  ./options-system --help         # 显示帮助

配置:
  系统使用内嵌配置，API密钥等配置已编译到程序中
  可以通过交互命令动态调整运行参数

注意事项:
  - 确保Bybit API密钥已正确配置
  - 确保对冲系统正在运行并可访问
  - 建议先使用测试模式熟悉系统操作
  - 实盘交易前请充分测试策略

更多信息请参考 README.md 文档
`)
}

// startNormalMode 启动正常模式
func startNormalMode(monitorManager *MonitorManager, positionManager *PositionManager, hedgeManager *HedgeManager) {
	log.Println("🎯 期权交易系统已启动")

	// TODO: 在这里实现正常模式的逻辑
	// 1. 启动WebSocket连接订阅期权数据
	// 2. 启动定期持仓检查任务
	// 3. 启动监控任务执行器

	// 显示系统状态
	showSystemStatus(monitorManager, positionManager)

	// 启动定期状态报告
	startStatusReporter(monitorManager, positionManager)

	// 保持程序运行
	select {}
}

// showSystemStatus 显示系统状态
func showSystemStatus(monitorManager *MonitorManager, positionManager *PositionManager) {
	monitors := monitorManager.GetAllMonitors()
	positions := positionManager.GetAllPositions()

	log.Printf("📊 系统状态: %d个监控任务, %d个持仓", len(monitors), len(positions))

	// 显示监控任务状态
	runningTasks := 0
	for _, task := range monitors {
		if task.Status == StatusRunning {
			runningTasks++
		}
	}
	log.Printf("🎯 监控任务: %d个运行中, %d个已停止", runningTasks, len(monitors)-runningTasks)

	// 显示持仓状态
	activePositions := 0
	for _, position := range positions {
		if position.Status == PositionStatusActive {
			activePositions++
		}
	}
	log.Printf("📈 持仓状态: %d个活跃, %d个已关闭", activePositions, len(positions)-activePositions)
}

// startStatusReporter 启动状态报告器
func startStatusReporter(monitorManager *MonitorManager, positionManager *PositionManager) {
	go func() {
		ticker := time.NewTicker(5 * time.Minute) // 每5分钟报告一次状态
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				showSystemStatus(monitorManager, positionManager)
			}
		}
	}()
}

// setupSignalHandler 设置信号处理
func setupSignalHandler(monitorManager *MonitorManager, positionManager *PositionManager, contractManager *OptionContractManager, wsClient *BybitWSClient) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("\n🛑 收到退出信号，正在安全关闭系统...")

		// 停止统一价格客户端
		log.Println("🔌 停止统一价格客户端...")
		StopUnifiedPriceClient()

		// 停止期权合约管理器和价格客户端
		log.Println("🔌 停止期权合约管理器...")
		contractManager.Stop()

		// 断开WebSocket连接
		log.Println("🔌 断开WebSocket连接...")
		wsClient.Disconnect()

		// 停止所有运行中的监控任务（不修改状态，保持running状态以便重启后恢复）
		monitors := monitorManager.GetAllMonitors()
		stoppedCount := 0
		for _, task := range monitors {
			if task.Status == StatusRunning {
				// 直接关闭停止通道，不调用StopMonitor（避免修改状态）
				select {
				case <-task.StopChan:
					// 通道已经关闭
				default:
					close(task.StopChan)
				}
				stoppedCount++
			}
		}
		if stoppedCount > 0 {
			log.Printf("✅ 已停止 %d 个监控任务协程（保持running状态）", stoppedCount)
		}

		// 保存所有数据
		log.Println("💾 保存数据...")
		if err := monitorManager.SaveData(positionManager); err != nil {
			log.Printf("❌ 保存数据失败: %v", err)
		} else {
			log.Println("✅ 数据保存完成")
		}

		log.Println("👋 系统已安全关闭")
		os.Exit(0)
	}()
}

// 初始化日志格式
func init() {
	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
	log.SetPrefix("")
}
