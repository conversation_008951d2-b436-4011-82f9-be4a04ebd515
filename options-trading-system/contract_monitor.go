package main

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// OptionContractState 期权合约状态枚举
type OptionContractState int

const (
	StateTrading     OptionContractState = iota // 正常交易
	StateDelivering                             // 交割中
	StateDelivered                              // 已行权
	StatePreLaunch                              // 预上市
	StateNotExist                               // 不存在
	StateFetchFailed                            // 获取失败
)

// StateToString 状态转换为字符串
func StateToString(state OptionContractState) string {
	switch state {
	case StateTrading:
		return "Trading"
	case StateDelivering:
		return "Delivering"
	case StateDelivered:
		return "Delivered"
	case StatePreLaunch:
		return "PreLaunch"
	case StateNotExist:
		return "NotExist"
	case StateFetchFailed:
		return "FetchFailed"
	default:
		return "Unknown"
	}
}

// ContractMonitor 合约监控器
type ContractMonitor struct {
	wsClient   *BybitWSClient
	restClient *BybitRESTClient
	statusMap  map[string]*ContractStatus
	mutex      sync.RWMutex
	ticker5s   *time.Ticker
	ticker10s  *time.Ticker
	stopChan   chan struct{}
}

// NewContractMonitor 创建新的合约监控器
func NewContractMonitor(wsClient *BybitWSClient) *ContractMonitor {
	return &ContractMonitor{
		wsClient:   wsClient,
		restClient: NewBybitRESTClient(),
		statusMap:  make(map[string]*ContractStatus),
		ticker5s:   time.NewTicker(5 * time.Second),
		ticker10s:  time.NewTicker(10 * time.Second),
		stopChan:   make(chan struct{}),
	}
}

// Start 启动监控
func (monitor *ContractMonitor) Start() {
	go monitor.checkRestThreshold()
	go monitor.startStateChecker()
}

// Stop 停止监控
func (monitor *ContractMonitor) Stop() {
	close(monitor.stopChan)
	monitor.ticker5s.Stop()
	monitor.ticker10s.Stop()
}

// AddContract 添加合约到监控列表
func (monitor *ContractMonitor) AddContract(symbol string) {
	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	if _, exists := monitor.statusMap[symbol]; !exists {
		now := time.Now()
		monitor.statusMap[symbol] = &ContractStatus{
			Symbol:                symbol,
			State:                 StateTrading, // 初始化为Trading状态
			LastStateCheck:        now,
			NextCheckTime:         now.Add(2 * time.Minute), // 2分钟后下次检查
			CurrentCycleFailCount: 0,
			MonitorStartTime:      now,
			OrderBookValid:        false, // 初始化为false，等待数据
			LastWSUpdate:          now,
			RestCheckThreshold:    10 * time.Second, // 默认10秒
			WSConnected:           true,

			// 买一价变化检测相关字段初始化
			LastBid1Price:       decimal.Zero,
			IsFirstTimeData:     true,        // 初始化为首次数据
			TradingCheckEnabled: true,        // 默认启用交易检测
			LastBid1ChangeTime:  time.Time{}, // 零值时间

			// 交易检测统计相关字段初始化
			LastTradingCheckTime: time.Time{},          // 零值时间
			TradingCheckHistory:  make([]time.Time, 0), // 空的历史记录
		}

		// 立即进行第一次状态检查（在订阅前）
		go monitor.checkContractStateImmediate(symbol)
	}
}

// UpdateContractStatus 更新合约状态（由WebSocket客户端调用）
func (monitor *ContractMonitor) UpdateContractStatus(symbol string, hasData bool, wsConnected bool) {
	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	if status, exists := monitor.statusMap[symbol]; exists {
		// 首次获得完整数据后设置有效性
		if !status.OrderBookValid && hasData {
			status.OrderBookValid = true
			log.Printf("✅ 合约 %s 盘口数据初始化完成", symbol)
		}
		status.LastWSUpdate = time.Now()
		status.WSConnected = wsConnected
	} else {
		// 初始化合约状态
		now := time.Now()
		monitor.statusMap[symbol] = &ContractStatus{
			Symbol:                symbol,
			State:                 StateTrading, // 初始化为Trading状态
			LastStateCheck:        now,
			NextCheckTime:         now.Add(2 * time.Minute), // 2分钟后首次检查
			CurrentCycleFailCount: 0,
			MonitorStartTime:      now,
			OrderBookValid:        hasData,
			LastWSUpdate:          now,
			RestCheckThreshold:    10 * time.Second, // 默认10秒
			WSConnected:           wsConnected,

			// 买一价变化检测相关字段初始化
			LastBid1Price:       decimal.Zero,
			IsFirstTimeData:     true,        // 初始化为首次数据
			TradingCheckEnabled: true,        // 默认启用交易检测
			LastBid1ChangeTime:  time.Time{}, // 零值时间

			// 交易检测统计相关字段初始化
			LastTradingCheckTime: time.Time{},          // 零值时间
			TradingCheckHistory:  make([]time.Time, 0), // 空的历史记录
		}
		if hasData {
			log.Printf("✅ 合约 %s 盘口数据初始化完成", symbol)
		}
	}
}

// checkRestThreshold 基于RT阈值的检查
func (monitor *ContractMonitor) checkRestThreshold() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-monitor.stopChan:
			return
		case <-ticker.C:
			monitor.performRestThresholdCheck()
		}
	}
}

// performRestThresholdCheck 执行RT阈值检查
func (monitor *ContractMonitor) performRestThresholdCheck() {
	monitor.mutex.RLock()
	var contractsToCheck []string
	now := time.Now()

	for symbol, status := range monitor.statusMap {
		if !status.OrderBookValid {
			continue // 跳过无效的合约
		}

		timeSince := now.Sub(status.LastWSUpdate)
		if timeSince > status.RestCheckThreshold && !status.RestCheckActive {
			contractsToCheck = append(contractsToCheck, symbol)
		}

		// 更新WebSocket连接状态
		status.WSConnected = monitor.wsClient.isConnected
	}
	monitor.mutex.RUnlock()

	// 异步验证需要检查的合约
	for _, symbol := range contractsToCheck {
		go monitor.verifyContractData(symbol)
	}
}

// checkWSConnection 检查WebSocket连接状态
func (monitor *ContractMonitor) checkWSConnection() bool {
	return monitor.wsClient.isConnected
}

// 注意：已删除旧的5秒和10秒检查逻辑，统一使用RT阈值检查

// verifyContractData 验证单个合约数据
func (monitor *ContractMonitor) verifyContractData(symbol string) {
	// 标记正在检查
	monitor.mutex.Lock()
	if status, exists := monitor.statusMap[symbol]; exists {
		if status.RestCheckActive {
			monitor.mutex.Unlock()
			return // 已经在检查中
		}
		status.RestCheckActive = true
		status.LastRESTCheck = time.Now()
	}
	monitor.mutex.Unlock()

	defer func() {
		// 检查完成，清除标记
		monitor.mutex.Lock()
		if status, exists := monitor.statusMap[symbol]; exists {
			status.RestCheckActive = false
		}
		monitor.mutex.Unlock()
	}()

	// 获取REST API数据
	restOrderBook, err := monitor.restClient.GetOrderBook(symbol, 25)
	if err != nil {
		log.Printf("⚠️  获取 %s REST订单簿失败: %v", symbol, err)
		return
	}

	// 获取缓存数据
	cachedOrderBook, exists := monitor.wsClient.orderBookCache.GetOrderBook(symbol)
	if !exists {
		log.Printf("⚠️  合约 %s 缓存数据不存在", symbol)
		return
	}

	// 比较数据
	if monitor.restClient.CompareOrderBooks(cachedOrderBook, restOrderBook) {
		// 数据一致，更新最后检查时间和WebSocket更新时间
		monitor.mutex.Lock()
		if status, exists := monitor.statusMap[symbol]; exists {
			status.LastRESTCheck = time.Now()
			// 更新LastWSUpdate，表示数据仍然有效
			status.LastWSUpdate = time.Now()
		}
		monitor.mutex.Unlock()
		// 静默正常操作：数据验证通过不输出日志（修改方案第六阶段）
	} else {
		// 数据不一致，标记为无效
		monitor.mutex.Lock()
		if status, exists := monitor.statusMap[symbol]; exists {
			status.OrderBookValid = false
		}
		monitor.mutex.Unlock()
		log.Printf("❌ 合约 %s 数据不一致，标记为无效", symbol)
		monitor.resubscribeContract(symbol)
	}
}

// resubscribeContract 重新订阅合约
func (monitor *ContractMonitor) resubscribeContract(symbol string) {
	// 取消当前订阅
	monitor.unsubscribeContract(symbol)

	// 等待一小段时间
	time.Sleep(1 * time.Second)

	// 重新订阅
	if err := monitor.wsClient.SubscribeOptionOrderBook(symbol); err != nil {
		log.Printf("❌ 重新订阅 %s 失败: %v", symbol, err)

		// 保持无效状态
		monitor.mutex.Lock()
		if status, exists := monitor.statusMap[symbol]; exists {
			status.OrderBookValid = false
		}
		monitor.mutex.Unlock()
	} else {
		log.Printf("✅ 成功重新订阅: %s", symbol)

		// 重置状态，等待新数据到达后设置为有效
		monitor.mutex.Lock()
		if status, exists := monitor.statusMap[symbol]; exists {
			status.OrderBookValid = false // 等待新数据
			status.LastWSUpdate = time.Now()
		}
		monitor.mutex.Unlock()
	}
}

// unsubscribeContract 取消订阅合约
func (monitor *ContractMonitor) unsubscribeContract(symbol string) {
	if monitor.wsClient != nil {
		// 构造取消订阅的topic
		topic := fmt.Sprintf("orderbook.25.%s", symbol)

		// 通过WebSocket客户端发送取消订阅
		// 注意：Bybit可能不支持单独取消订阅，这里记录日志
		log.Printf("🚫 正在取消订阅: %s (topic: %s)", symbol, topic)

		// TODO: 实现实际的取消订阅逻辑
		// 目前Bybit WebSocket可能需要重连来取消订阅
	}
}

// GetContractStatus 获取合约状态
func (monitor *ContractMonitor) GetContractStatus(symbol string) (*ContractStatus, bool) {
	monitor.mutex.RLock()
	defer monitor.mutex.RUnlock()

	status, exists := monitor.statusMap[symbol]
	return status, exists
}

// GetAllContractStatus 获取所有合约状态
func (monitor *ContractMonitor) GetAllContractStatus() map[string]*ContractStatus {
	monitor.mutex.RLock()
	defer monitor.mutex.RUnlock()

	result := make(map[string]*ContractStatus)
	for symbol, status := range monitor.statusMap {
		result[symbol] = status
	}

	return result
}

// SetRestCheckThreshold 设置所有合约的REST检查阈值
func (monitor *ContractMonitor) SetRestCheckThreshold(duration time.Duration) int {
	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	count := 0
	for _, status := range monitor.statusMap {
		status.RestCheckThreshold = duration
		count++
	}

	return count
}

// startStateChecker 启动状态检查器
func (monitor *ContractMonitor) startStateChecker() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟检查一次是否有合约需要检查
	defer ticker.Stop()

	for {
		select {
		case <-monitor.stopChan:
			return
		case <-ticker.C:
			monitor.checkDueContracts()
		}
	}
}

// checkDueContracts 检查到期需要检查的合约
func (monitor *ContractMonitor) checkDueContracts() {
	now := time.Now()
	var contractsToCheck []string

	monitor.mutex.RLock()
	for symbol, status := range monitor.statusMap {
		if now.After(status.NextCheckTime) {
			contractsToCheck = append(contractsToCheck, symbol)
		}
	}
	monitor.mutex.RUnlock()

	// 批量检查状态
	config := GetStateCheckConfig()
	for i := 0; i < len(contractsToCheck); i += config.BatchSize {
		end := i + config.BatchSize
		if end > len(contractsToCheck) {
			end = len(contractsToCheck)
		}

		// 为每个批次启动goroutine
		batch := contractsToCheck[i:end]
		go monitor.checkContractStateBatch(batch)

		// 批次间隔
		if i+config.BatchSize < len(contractsToCheck) {
			time.Sleep(config.BatchInterval)
		}
	}
}

// checkContractStateBatch 批量检查合约状态
func (monitor *ContractMonitor) checkContractStateBatch(symbols []string) {
	for _, symbol := range symbols {
		// 为每个合约添加时间偏移，避免同一秒发出多个请求
		offset := monitor.calculateTimeOffset(symbol)
		time.Sleep(offset)

		go monitor.checkContractState(symbol)
	}
}

// calculateTimeOffset 计算时间偏移量
func (monitor *ContractMonitor) calculateTimeOffset(symbol string) time.Duration {
	// 基于合约名称hash值计算偏移量（0-59秒）
	hash := 0
	for _, c := range symbol {
		hash = hash*31 + int(c)
	}
	offset := hash % 60
	return time.Duration(offset) * time.Second
}

// checkContractState 检查单个合约状态
func (monitor *ContractMonitor) checkContractState(symbol string) {
	// 重置当前周期失败计数
	monitor.mutex.Lock()
	status, exists := monitor.statusMap[symbol]
	if !exists {
		monitor.mutex.Unlock()
		return
	}
	status.CurrentCycleFailCount = 0 // 每个检查周期开始时重置
	monitor.mutex.Unlock()

	// 获取合约状态
	state, err := monitor.getContractStateFromAPI(symbol)

	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	if err != nil {
		status.CurrentCycleFailCount++
		if status.CurrentCycleFailCount >= GetStateCheckConfig().MaxRetryCount {
			oldState := status.State
			status.State = StateFetchFailed
			if oldState != StateFetchFailed {
				log.Printf("📊 合约 %s 状态变化: %s -> %s", symbol, StateToString(oldState), StateToString(StateFetchFailed))
				monitor.handleStateChange(symbol, oldState, StateFetchFailed)
			}
		} else {
			// 重试
			status.LastRetryTime = time.Now()
			go func() {
				time.Sleep(GetStateCheckConfig().RetryInterval)
				monitor.checkContractState(symbol)
			}()
			return
		}
	} else {
		status.CurrentCycleFailCount = 0
		oldState := status.State
		status.State = state

		// 状态变化处理：只在状态变化时输出日志
		if oldState != state {
			log.Printf("📊 合约 %s 状态变化: %s -> %s", symbol, StateToString(oldState), StateToString(state))
			monitor.handleStateChange(symbol, oldState, state)
		}
		// 静默正常操作：状态检查成功且状态未变化时不输出日志（修改方案第六阶段）
	}

	// 更新检查时间
	status.LastStateCheck = time.Now()
	status.NextCheckTime = time.Now().Add(monitor.calculateCheckInterval(symbol))
}

// calculateCheckInterval 计算检查间隔
func (monitor *ContractMonitor) calculateCheckInterval(symbol string) time.Duration {
	// 解析到期时间
	expiryTime := monitor.parseExpiryFromSymbol(symbol)
	timeToExpiry := expiryTime.Sub(time.Now())

	config := GetStateCheckConfig()
	if timeToExpiry <= 15*time.Minute {
		return config.FinalInterval // 15分钟内：1分钟
	} else if timeToExpiry <= 1*time.Hour {
		return config.HourBeforeInterval // 1小时内：5分钟
	} else {
		return config.NormalInterval // 正常：30分钟
	}
}

// parseExpiryFromSymbol 从合约名称解析到期时间
func (monitor *ContractMonitor) parseExpiryFromSymbol(symbol string) time.Time {
	// 解析格式：ETH-3JUN25-2500-C-USDT
	parts := strings.Split(symbol, "-")
	if len(parts) < 2 {
		return time.Now().Add(24 * time.Hour) // 默认24小时后
	}

	dateStr := parts[1] // 如 "3JUN25"

	// 解析日期部分
	if len(dateStr) < 6 {
		return time.Now().Add(24 * time.Hour) // 默认24小时后
	}

	// 提取日、月、年
	dayStr := dateStr[:len(dateStr)-5]               // "3"
	monthStr := dateStr[len(dayStr) : len(dayStr)+3] // "JUN"
	yearStr := dateStr[len(dayStr)+3:]               // "25"

	// 解析日期
	day, err := strconv.Atoi(dayStr)
	if err != nil {
		log.Printf("⚠️  解析日期失败 %s: %v", symbol, err)
		return time.Now().Add(24 * time.Hour)
	}

	// 月份映射
	monthMap := map[string]time.Month{
		"JAN": time.January, "FEB": time.February, "MAR": time.March,
		"APR": time.April, "MAY": time.May, "JUN": time.June,
		"JUL": time.July, "AUG": time.August, "SEP": time.September,
		"OCT": time.October, "NOV": time.November, "DEC": time.December,
	}

	month, exists := monthMap[monthStr]
	if !exists {
		log.Printf("⚠️  无效月份 %s in %s", monthStr, symbol)
		return time.Now().Add(24 * time.Hour)
	}

	// 解析年份 (25 -> 2025)
	year, err := strconv.Atoi(yearStr)
	if err != nil {
		log.Printf("⚠️  解析年份失败 %s: %v", symbol, err)
		return time.Now().Add(24 * time.Hour)
	}
	year += 2000 // 25 -> 2025

	// 构造到期时间 (假设是当天的16:00 UTC，这是期权常见的到期时间)
	expiryTime := time.Date(year, month, day, 16, 0, 0, 0, time.UTC)

	// 如果时间已过，返回当前时间+1小时（避免立即触发检查）
	if expiryTime.Before(time.Now()) {
		return time.Now().Add(1 * time.Hour)
	}

	return expiryTime
}

// getContractStateFromAPI 从API获取合约状态
func (monitor *ContractMonitor) getContractStateFromAPI(symbol string) (OptionContractState, error) {
	// 调用REST API获取合约信息
	instrumentInfo, err := monitor.restClient.GetInstrumentInfo("option", symbol)
	if err != nil {
		// 检查是否是合约不存在的错误
		if strings.Contains(err.Error(), "110023") || strings.Contains(err.Error(), "not available for trades") {
			return StateNotExist, nil
		}
		return StateFetchFailed, err
	}

	// 根据API返回的状态映射到内部状态
	switch instrumentInfo.Status {
	case "Trading":
		return StateTrading, nil
	case "Delivering":
		return StateDelivering, nil
	case "Closed":
		return StateDelivered, nil
	case "PreLaunch":
		return StatePreLaunch, nil
	default:
		return StateFetchFailed, fmt.Errorf("未知状态: %s", instrumentInfo.Status)
	}
}

// handleStateChange 处理状态变化
func (monitor *ContractMonitor) handleStateChange(symbol string, oldState, newState OptionContractState) {
	// 状态变化日志
	log.Printf("📊 合约 %s 状态变化: %s -> %s", symbol, StateToString(oldState), StateToString(newState))

	// 处理状态变化的具体逻辑
	switch newState {
	case StateTrading:
		// 变为Trading状态 - 不自动订阅，需要重新评估是否符合监控条件
		log.Printf("ℹ️  合约 %s 变为Trading状态，需要重新评估监控条件", symbol)

	case StateDelivering:
		// 进入交割状态 - 立即取消订阅
		if oldState == StateTrading {
			log.Printf("⚠️  合约 %s 进入交割状态，正在取消订阅...", symbol)
			monitor.unsubscribeContract(symbol)
		}

	case StateDelivered:
		// 已行权 - 立即取消订阅并清理监控
		if oldState == StateTrading || oldState == StateDelivering {
			log.Printf("✅ 合约 %s 已行权，正在取消订阅并清理监控...", symbol)
			monitor.unsubscribeContract(symbol)
			monitor.removeFromMonitoring(symbol)
		}

	case StatePreLaunch:
		// 预上市状态 - 取消订阅但保留监控
		if oldState == StateTrading {
			log.Printf("🕐 合约 %s 变为预上市状态，取消订阅但保留监控", symbol)
			monitor.unsubscribeContract(symbol)
		}

	case StateNotExist:
		// 合约不存在 - 完全移除
		log.Printf("❌ 合约 %s 不存在，完全移除监控", symbol)
		monitor.unsubscribeContract(symbol)
		monitor.removeFromMonitoring(symbol)

	case StateFetchFailed:
		// 获取失败 - 保持监控，等待恢复
		log.Printf("⚠️  合约 %s 状态获取失败，保持监控等待恢复", symbol)
	}
}

// removeFromMonitoring 从监控列表中移除合约
func (monitor *ContractMonitor) removeFromMonitoring(symbol string) {
	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	if _, exists := monitor.statusMap[symbol]; exists {
		delete(monitor.statusMap, symbol)
		log.Printf("🗑️  已从监控列表移除: %s", symbol)
	}
}

// checkContractStateImmediate 立即检查合约状态（用于新添加的合约）
func (monitor *ContractMonitor) checkContractStateImmediate(symbol string) {
	// 获取合约状态
	state, err := monitor.getContractStateFromAPI(symbol)

	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	status, exists := monitor.statusMap[symbol]
	if !exists {
		return
	}

	if err != nil {
		status.CurrentCycleFailCount++
		if status.CurrentCycleFailCount >= GetStateCheckConfig().MaxRetryCount {
			oldState := status.State
			status.State = StateFetchFailed
			if oldState != StateFetchFailed {
				log.Printf("📊 合约 %s 状态变化: %s -> %s", symbol, StateToString(oldState), StateToString(StateFetchFailed))
				monitor.handleStateChange(symbol, oldState, StateFetchFailed)
			}
		}
	} else {
		status.CurrentCycleFailCount = 0
		oldState := status.State
		status.State = state

		// 只在状态变化时输出日志
		if oldState != state {
			log.Printf("📊 合约 %s 状态变化: %s -> %s", symbol, StateToString(oldState), StateToString(state))
			monitor.handleStateChange(symbol, oldState, state)
		}
	}

	// 更新检查时间
	status.LastStateCheck = time.Now()
	status.NextCheckTime = time.Now().Add(monitor.calculateCheckInterval(symbol))
}

// UpdateBid1PriceInfo 更新买一价信息
func (monitor *ContractMonitor) UpdateBid1PriceInfo(symbol string, bid1Price decimal.Decimal, isFirstTime bool) {
	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	if status, exists := monitor.statusMap[symbol]; exists {
		status.LastBid1Price = bid1Price
		status.LastBid1ChangeTime = time.Now()

		if isFirstTime {
			status.IsFirstTimeData = false // 标记已不再是首次数据
		}
	}
}
