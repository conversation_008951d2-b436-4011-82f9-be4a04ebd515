package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// UnifiedPriceClient 统一价格客户端
type UnifiedPriceClient struct {
	binanceWS *BinanceWSClient
	mutex     sync.RWMutex
}

// PricePurpose 价格用途类型
type PricePurpose string

const (
	PurposeTimeValue    PricePurpose = "time_value"    // 时间价值计算
	PurposeMonitorRange PricePurpose = "monitor_range" // 监控范围计算
)

// 注意：OptionType 常量已在 types.go 中定义，这里不重复声明

// NewUnifiedPriceClient 创建统一价格客户端
func NewUnifiedPriceClient(symbol string) *UnifiedPriceClient {
	client := &UnifiedPriceClient{
		binanceWS: NewBinanceWSClient(symbol),
	}

	return client
}

// Start 启动价格客户端
func (upc *UnifiedPriceClient) Start() error {
	log.Println("🚀 启动统一价格客户端...")

	if err := upc.binanceWS.Connect(); err != nil {
		return fmt.Errorf("启动币安WebSocket失败: %v", err)
	}

	log.Println("✅ 统一价格客户端启动成功")
	return nil
}

// Stop 停止价格客户端
func (upc *UnifiedPriceClient) Stop() {
	log.Println("🛑 停止统一价格客户端...")

	if upc.binanceWS != nil {
		upc.binanceWS.Disconnect()
	}

	log.Println("✅ 统一价格客户端已停止")
}

// GetSpotPrice 获取现货价格（统一接口）
func (upc *UnifiedPriceClient) GetSpotPrice(purpose PricePurpose, optionType ...string) (decimal.Decimal, bool) {
	upc.mutex.RLock()
	defer upc.mutex.RUnlock()

	switch purpose {
	case PurposeTimeValue:
		// 时间价值计算需要指定期权类型
		if len(optionType) == 0 {
			log.Printf("❌ 时间价值计算需要指定期权类型")
			return decimal.Zero, false
		}

		switch optionType[0] {
		case OptionTypeCall:
			// CALL期权使用卖一价
			return upc.binanceWS.GetAskPrice()
		case OptionTypePut:
			// PUT期权使用买一价
			return upc.binanceWS.GetBidPrice()
		default:
			log.Printf("❌ 未知期权类型: %s", optionType[0])
			return decimal.Zero, false
		}

	case PurposeMonitorRange:
		// 监控范围计算使用中间价
		return upc.binanceWS.GetMidPrice()

	default:
		log.Printf("❌ 未知价格用途: %s", purpose)
		return decimal.Zero, false
	}
}

// GetBidPrice 获取买一价（兼容接口）
func (upc *UnifiedPriceClient) GetBidPrice() (decimal.Decimal, bool) {
	return upc.binanceWS.GetBidPrice()
}

// GetAskPrice 获取卖一价（兼容接口）
func (upc *UnifiedPriceClient) GetAskPrice() (decimal.Decimal, bool) {
	return upc.binanceWS.GetAskPrice()
}

// GetMidPrice 获取中间价（兼容接口）
func (upc *UnifiedPriceClient) GetMidPrice() (decimal.Decimal, bool) {
	return upc.binanceWS.GetMidPrice()
}

// IsConnected 检查连接状态
func (upc *UnifiedPriceClient) IsConnected() bool {
	if upc.binanceWS == nil {
		return false
	}
	return upc.binanceWS.IsConnected()
}

// GetConnectionStatus 获取连接状态信息
func (upc *UnifiedPriceClient) GetConnectionStatus() map[string]interface{} {
	status := make(map[string]interface{})

	if upc.binanceWS == nil {
		status["connected"] = false
		status["error"] = "WebSocket客户端未初始化"
		return status
	}

	status["connected"] = upc.binanceWS.IsConnected()
	status["last_update"] = upc.binanceWS.GetLastUpdate()

	if bid, exists := upc.binanceWS.GetBidPrice(); exists {
		status["bid_price"] = bid.String()
	} else {
		status["bid_price"] = "无数据"
	}

	if ask, exists := upc.binanceWS.GetAskPrice(); exists {
		status["ask_price"] = ask.String()
	} else {
		status["ask_price"] = "无数据"
	}

	if mid, exists := upc.binanceWS.GetMidPrice(); exists {
		status["mid_price"] = mid.String()
	} else {
		status["mid_price"] = "无数据"
	}

	return status
}

// IsHealthy 检查价格客户端是否健康
func (upc *UnifiedPriceClient) IsHealthy() bool {
	if upc.binanceWS == nil {
		return false
	}

	// 检查连接状态
	if !upc.binanceWS.IsConnected() {
		return false
	}

	// 检查数据是否新鲜（30秒内有更新）
	lastUpdate := upc.binanceWS.GetLastUpdate()
	if lastUpdate.IsZero() {
		return false
	}

	return time.Since(lastUpdate) < 30*time.Second
}

// GetPriceAge 获取价格数据的年龄
func (upc *UnifiedPriceClient) GetPriceAge() time.Duration {
	if upc.binanceWS == nil {
		return time.Duration(0)
	}

	lastUpdate := upc.binanceWS.GetLastUpdate()
	if lastUpdate.IsZero() {
		return time.Duration(0)
	}

	return time.Since(lastUpdate)
}

// GetDetailedStatus 获取详细状态信息
func (upc *UnifiedPriceClient) GetDetailedStatus() map[string]interface{} {
	status := upc.GetConnectionStatus()

	// 添加健康状态
	status["healthy"] = upc.IsHealthy()

	// 添加数据年龄
	age := upc.GetPriceAge()
	if age > 0 {
		status["data_age"] = age.String()
	} else {
		status["data_age"] = "无数据"
	}

	// 添加符号信息
	if upc.binanceWS != nil {
		status["symbol"] = upc.binanceWS.symbol
	}

	return status
}

// 全局统一价格客户端实例
var globalUnifiedPriceClient *UnifiedPriceClient

// InitUnifiedPriceClient 初始化全局统一价格客户端
func InitUnifiedPriceClient(symbol string) error {
	if globalUnifiedPriceClient != nil {
		globalUnifiedPriceClient.Stop()
	}

	globalUnifiedPriceClient = NewUnifiedPriceClient(symbol)
	return globalUnifiedPriceClient.Start()
}

// GetGlobalUnifiedPriceClient 获取全局统一价格客户端
func GetGlobalUnifiedPriceClient() *UnifiedPriceClient {
	return globalUnifiedPriceClient
}

// StopUnifiedPriceClient 停止全局统一价格客户端
func StopUnifiedPriceClient() {
	if globalUnifiedPriceClient != nil {
		globalUnifiedPriceClient.Stop()
		globalUnifiedPriceClient = nil
	}
}

// GetUnifiedSpotPrice 获取统一现货价格（全局函数）
func GetUnifiedSpotPrice(purpose PricePurpose, optionType ...string) (decimal.Decimal, bool) {
	if globalUnifiedPriceClient == nil {
		log.Printf("❌ 统一价格客户端未初始化")
		return decimal.Zero, false
	}

	return globalUnifiedPriceClient.GetSpotPrice(purpose, optionType...)
}

// 便捷函数

// GetSpotPriceForTimeValue 获取时间价值计算用的现货价格
func GetSpotPriceForTimeValue(optionType string) (decimal.Decimal, bool) {
	return GetUnifiedSpotPrice(PurposeTimeValue, optionType)
}

// GetSpotPriceForMonitorRange 获取监控范围计算用的现货价格
func GetSpotPriceForMonitorRange() (decimal.Decimal, bool) {
	return GetUnifiedSpotPrice(PurposeMonitorRange)
}

// 全局健康检查和状态函数

// IsUnifiedPriceClientHealthy 检查全局统一价格客户端是否健康
func IsUnifiedPriceClientHealthy() bool {
	if globalUnifiedPriceClient == nil {
		return false
	}
	return globalUnifiedPriceClient.IsHealthy()
}

// GetUnifiedPriceClientStatus 获取全局统一价格客户端状态
func GetUnifiedPriceClientStatus() map[string]interface{} {
	if globalUnifiedPriceClient == nil {
		return map[string]interface{}{
			"initialized": false,
			"error":       "统一价格客户端未初始化",
		}
	}

	status := globalUnifiedPriceClient.GetDetailedStatus()
	status["initialized"] = true
	return status
}

// GetUnifiedPriceClientAge 获取全局统一价格客户端数据年龄
func GetUnifiedPriceClientAge() time.Duration {
	if globalUnifiedPriceClient == nil {
		return time.Duration(0)
	}
	return globalUnifiedPriceClient.GetPriceAge()
}
