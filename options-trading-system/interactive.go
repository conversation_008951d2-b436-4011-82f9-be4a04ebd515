package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// InteractiveManager 交互管理器
type InteractiveManager struct {
	monitorManager  *MonitorManager
	positionManager *PositionManager
	hedgeManager    *HedgeManager
	privateWSClient *BybitPrivateWSClient // 新增：私有WebSocket客户端
}

// NewInteractiveManager 创建新的交互管理器
func NewInteractiveManager(monitorManager *MonitorManager, positionManager *PositionManager, hedgeManager *HedgeManager, privateWSClient *BybitPrivateWSClient) *InteractiveManager {
	return &InteractiveManager{
		monitorManager:  monitorManager,
		positionManager: positionManager,
		hedgeManager:    hedgeManager,
		privateWSClient: privateWSClient,
	}
}

// StartInteractiveMode 启动交互模式
func (im *InteractiveManager) StartInteractiveMode() {
	log.Println("🎛️  进入交互模式，输入 'help' 查看可用命令")

	scanner := bufio.NewScanner(os.Stdin)
	for {
		fmt.Print("options> ")
		if !scanner.Scan() {
			break
		}

		command := strings.TrimSpace(scanner.Text())
		if command == "" {
			continue
		}

		im.handleCommand(command)
	}
}

// handleCommand 处理命令
func (im *InteractiveManager) handleCommand(command string) {
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return
	}

	switch strings.ToLower(parts[0]) {
	case "help":
		im.printHelp()
	case "quit", "exit":
		im.handleExit()
	case "set":
		im.handleSetCommand(parts[1:])
	case "show":
		im.handleShowCommand(parts[1:])
	case "add_m", "add_monitor":
		im.handleAddMonitorCommand(parts[1:])
	case "list_m", "list_monitors":
		im.handleListMonitorsCommand()
	case "show_m", "show_monitor":
		im.handleShowMonitorCommand(parts[1:])
	case "start_m", "start_monitor":
		im.handleStartMonitorCommand(parts[1:])
	case "stop_m", "stop_monitor":
		im.handleStopMonitorCommand(parts[1:])
	case "delete_m", "delete_monitor":
		im.handleDeleteMonitorCommand(parts[1:])
	case "modify_m", "modify_monitor":
		im.handleModifyMonitorCommand(parts[1:])
	case "startall_m", "startall_monitors":
		im.handleStartAllMonitorsCommand()
	case "stopall_m", "stopall_monitors":
		im.handleStopAllMonitorsCommand()
	case "list_p", "list_positions":
		im.handleListPositionsCommand()
	case "list_c", "list_contracts":
		im.handleListContractsCommand()
	case "show_p", "show_position":
		im.handleShowPositionCommand(parts[1:])
	case "delete_p", "delete_position":
		im.handleDeletePositionCommand(parts[1:])
	case "save":
		im.handleSaveCommand()
	case "export":
		im.handleExportCommand()
	case "test_tv", "test_timevalue":
		im.handleTestTimeValueCommand(parts[1:])
	case "ws_status", "websocket":
		im.handleWebSocketStatusCommand()
	case "set_rest_threshold":
		im.handleSetRestThresholdCommand(parts[1:])
	case "contract_state":
		im.handleContractStateCommand(parts[1:])
	default:
		log.Printf("❌ 未知命令: %s，输入 'help' 查看帮助", parts[0])
	}
}

// printHelp 打印帮助信息
func (im *InteractiveManager) printHelp() {
	fmt.Println(`
📋 期权交易系统 - 可用命令:

🔧 系统命令:
  help                           显示此帮助信息
  quit / exit                    退出程序

⚙️  全局参数设置:
  set expiry_threshold <时间>     设置监控行权时间阈值 (如: 12h)
  set price_range <百分比>        设置监控价格范围阈值 (如: 0.15)
  set max_position <数量>         设置单个期权最高持仓量
  set order_size <数量>           设置单次下单量
  set depth_threshold <百分比>    设置盘口深度检测阈值
  set depth_levels <档数>         设置盘口监控档数
  set hedge_threshold <百分比>    设置动态对冲阈值
  set hedge_api_url <URL>         设置对冲系统API地址
  show defaults                   显示当前所有默认参数

🎯 监控任务管理:
  add_m <类型> <时间范围> <目标价值>  添加监控任务
    示例: add_m call 5h 0.012 (1.2%)
    示例: add_m put 3h-8h 0.008 (0.8%)
  list_m                          列出所有监控任务（详细信息）
  show_m <ID>                     显示监控任务详情
  start_m <ID>                    启动监控任务
  stop_m <ID>                     停止监控任务
  startall_m                      启动所有监控任务
  stopall_m                       停止所有监控任务
  delete_m <ID>                   删除监控任务
  modify_m <ID> <参数> <值>        修改监控任务参数
    参数: time_value_target, expiry_time_range, option_type

📊 持仓跟踪管理:
  list_p                          列出所有期权持仓
  list_c                          列出所有期权合约持仓状态（实时）
  show_p <合约名>                 显示具体持仓详情和事件记录
  delete_p <合约名>               删除持仓跟踪任务

💾 数据管理:
  save                           手动保存数据
  export                         导出详细报告到文件
  test_tv <类型> <现货价> <行权价>  测试时间价值计算
    示例: test_tv call 2500 2600

🔌 WebSocket 监控:
  ws_status / websocket          显示期权WebSocket连接状态表格
  set_rest_threshold <时间>      设置REST检查时间阈值
    示例: set_rest_threshold 30s, set_rest_threshold 1m
  contract_state [合约名]        查看合约状态详情
    示例: contract_state, contract_state ETH-4JUN25-2500-C-USDT

💡 使用提示:
  - 期权类型: call 或 put
  - 时间范围: 5h (单一时间) 或 5h-10h (时间范围)
  - 合约名称格式: ETH-3JUN25-2500-C-USDT (Bybit格式)
  - REST阈值: 支持秒(s)、分钟(m)、小时(h)单位
`)
}

// handleExit 处理退出命令
func (im *InteractiveManager) handleExit() {
	log.Println("正在保存数据并退出...")

	// 保存数据
	if err := im.monitorManager.SaveData(im.positionManager); err != nil {
		log.Printf("❌ 保存数据失败: %v", err)
	}

	log.Println("👋 再见!")
	os.Exit(0)
}

// handleSetCommand 处理设置命令
func (im *InteractiveManager) handleSetCommand(args []string) {
	if len(args) < 2 {
		log.Println("❌ 设置命令格式: set <参数名> <值>")
		return
	}

	paramName := args[0]
	valueStr := args[1]

	switch paramName {
	case "expiry_threshold":
		if err := UpdateGlobalDefault(paramName, valueStr); err != nil {
			log.Printf("❌ 设置失败: %v", err)
		} else {
			log.Printf("✅ %s 已设置为: %s", paramName, valueStr)
		}
	case "hedge_api_url":
		if err := UpdateGlobalDefault(paramName, valueStr); err != nil {
			log.Printf("❌ 设置失败: %v", err)
		} else {
			log.Printf("✅ %s 已设置为: %s", paramName, valueStr)
		}
	case "depth_levels":
		if value, err := strconv.Atoi(valueStr); err != nil {
			log.Printf("❌ 无效的整数值: %s", valueStr)
		} else {
			if err := UpdateGlobalDefault(paramName, value); err != nil {
				log.Printf("❌ 设置失败: %v", err)
			} else {
				log.Printf("✅ %s 已设置为: %d", paramName, value)
			}
		}
	default:
		// 其他参数作为浮点数处理
		if value, err := strconv.ParseFloat(valueStr, 64); err != nil {
			log.Printf("❌ 无效的数值: %s", valueStr)
		} else {
			if err := UpdateGlobalDefault(paramName, value); err != nil {
				log.Printf("❌ 设置失败: %v", err)
			} else {
				log.Printf("✅ %s 已设置为: %f", paramName, value)
			}
		}
	}
}

// handleShowCommand 处理显示命令
func (im *InteractiveManager) handleShowCommand(args []string) {
	if len(args) == 0 || args[0] != "defaults" {
		log.Println("❌ 显示命令格式: show defaults")
		return
	}

	defaults := GetGlobalDefaults()
	fmt.Println("\n📋 当前全局默认参数:")
	fmt.Printf("  监控行权时间阈值:     %s\n", defaults.ExpiryThreshold)
	fmt.Printf("  监控价格范围阈值:     %s (%.1f%%)\n", defaults.PriceRange.String(), defaults.PriceRange.Mul(decimal.NewFromInt(100)).InexactFloat64())
	fmt.Printf("  单个期权最高持仓量:   %s\n", defaults.MaxPosition.String())
	fmt.Printf("  单次下单量:           %s\n", defaults.OrderSize.String())
	fmt.Printf("  盘口深度检测阈值:     %s (%.1f%%)\n", defaults.DepthThreshold.String(), defaults.DepthThreshold.Mul(decimal.NewFromInt(100)).InexactFloat64())
	fmt.Printf("  盘口监控档数:         %d\n", defaults.DepthLevels)
	fmt.Printf("  动态对冲阈值:         %s (%.3f%%)\n", defaults.HedgeThreshold.String(), defaults.HedgeThreshold.Mul(decimal.NewFromInt(100)).InexactFloat64())
	fmt.Printf("  对冲系统API地址:      %s\n", defaults.HedgeAPIURL)
	fmt.Println()
}

// handleAddMonitorCommand 处理添加监控任务命令
func (im *InteractiveManager) handleAddMonitorCommand(args []string) {
	if len(args) < 3 {
		log.Println("❌ 添加监控任务格式: add_monitor <类型> <时间范围> <目标时间价值>")
		log.Println("   示例: add_monitor call 5h 0.05")
		log.Println("   示例: add_monitor put 3h-8h 0.03")
		return
	}

	optionType := strings.ToLower(args[0])
	expiryTimeRange := args[1]
	timeValueTargetStr := args[2]

	// 解析目标时间价值
	timeValueTarget, err := decimal.NewFromString(timeValueTargetStr)
	if err != nil {
		log.Printf("❌ 无效的目标时间价值: %s", timeValueTargetStr)
		return
	}

	// 添加监控任务
	task, err := im.monitorManager.AddMonitor(optionType, expiryTimeRange, timeValueTarget)
	if err != nil {
		log.Printf("❌ 添加监控任务失败: %v", err)
		return
	}

	log.Printf("✅ 监控任务已添加: %s", task.ID)
}

// handleListMonitorsCommand 处理列出监控任务命令
func (im *InteractiveManager) handleListMonitorsCommand() {
	monitors := im.monitorManager.GetAllMonitors()
	if len(monitors) == 0 {
		log.Println("📭 没有监控任务")
		return
	}

	log.Printf("📊 监控任务列表 (%d个任务):", len(monitors))
	log.Println("")

	for _, task := range monitors {
		im.printDetailedMonitorStatus(task)
		log.Println("")
	}
}

// handleShowMonitorCommand 处理显示监控任务详情命令
func (im *InteractiveManager) handleShowMonitorCommand(args []string) {
	if len(args) < 1 {
		log.Println("❌ 显示监控任务格式: show_monitor <任务ID>")
		return
	}

	taskID := args[0]
	task, err := im.monitorManager.GetMonitor(taskID)
	if err != nil {
		log.Printf("❌ %v", err)
		return
	}

	fmt.Printf("\n📋 监控任务详情: %s\n", task.ID)
	fmt.Printf("  期权类型:         %s\n", strings.ToUpper(task.OptionType))
	fmt.Printf("  时间范围:         %s\n", task.ExpiryTimeRange)
	fmt.Printf("  目标时间价值:     %s\n", task.TimeValueTarget.String())
	fmt.Printf("  状态:             %s\n", task.Status)
	fmt.Printf("  创建时间:         %s\n", task.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  最后更新:         %s\n", task.UpdatedAt.Format("2006-01-02 15:04:05"))
	fmt.Println()
}

// handleStartMonitorCommand 处理启动监控任务命令
func (im *InteractiveManager) handleStartMonitorCommand(args []string) {
	if len(args) < 1 {
		log.Println("❌ 启动监控任务格式: start_monitor <任务ID>")
		return
	}

	taskID := args[0]
	if err := im.monitorManager.StartMonitor(taskID); err != nil {
		log.Printf("❌ %v", err)
	}
}

// handleStopMonitorCommand 处理停止监控任务命令
func (im *InteractiveManager) handleStopMonitorCommand(args []string) {
	if len(args) < 1 {
		log.Println("❌ 停止监控任务格式: stop_monitor <任务ID>")
		return
	}

	taskID := args[0]
	if err := im.monitorManager.StopMonitor(taskID); err != nil {
		log.Printf("❌ %v", err)
	}
}

// handleStartAllMonitorsCommand 处理启动所有监控任务命令
func (im *InteractiveManager) handleStartAllMonitorsCommand() {
	monitors := im.monitorManager.GetAllMonitors()
	if len(monitors) == 0 {
		log.Println("📭 没有监控任务")
		return
	}

	startedCount := 0
	for taskID, task := range monitors {
		if task.Status == StatusStopped {
			if err := im.monitorManager.StartMonitor(taskID); err != nil {
				log.Printf("⚠️  启动任务 %s 失败: %v", taskID, err)
			} else {
				startedCount++
			}
		}
	}

	if startedCount > 0 {
		log.Printf("✅ 成功启动 %d 个监控任务", startedCount)
	} else {
		log.Println("ℹ️  所有监控任务都已在运行")
	}
}

// handleStopAllMonitorsCommand 处理停止所有监控任务命令
func (im *InteractiveManager) handleStopAllMonitorsCommand() {
	monitors := im.monitorManager.GetAllMonitors()
	if len(monitors) == 0 {
		log.Println("📭 没有监控任务")
		return
	}

	stoppedCount := 0
	for taskID, task := range monitors {
		if task.Status == StatusRunning {
			if err := im.monitorManager.StopMonitor(taskID); err != nil {
				log.Printf("⚠️  停止任务 %s 失败: %v", taskID, err)
			} else {
				stoppedCount++
			}
		}
	}

	if stoppedCount > 0 {
		log.Printf("✅ 成功停止 %d 个监控任务", stoppedCount)
	} else {
		log.Println("ℹ️  所有监控任务都已停止")
	}
}

// handleDeleteMonitorCommand 处理删除监控任务命令
func (im *InteractiveManager) handleDeleteMonitorCommand(args []string) {
	if len(args) < 1 {
		log.Println("❌ 删除监控任务格式: delete_monitor <任务ID>")
		return
	}

	taskID := args[0]
	if err := im.monitorManager.DeleteMonitor(taskID); err != nil {
		log.Printf("❌ %v", err)
	}
}

// handleModifyMonitorCommand 处理修改监控任务命令
func (im *InteractiveManager) handleModifyMonitorCommand(args []string) {
	if len(args) < 3 {
		log.Println("❌ 修改监控任务格式: modify_monitor <任务ID> <参数名> <值>")
		log.Println("   可修改参数: time_value_target, expiry_time_range, option_type")
		return
	}

	taskID := args[0]
	paramName := args[1]
	valueStr := args[2]

	var value interface{}
	var err error

	switch paramName {
	case "time_value_target":
		value, err = decimal.NewFromString(valueStr)
	case "expiry_time_range":
		value = valueStr
	case "option_type":
		value = strings.ToLower(valueStr)
	default:
		log.Printf("❌ 未知参数: %s", paramName)
		return
	}

	if err != nil {
		log.Printf("❌ 无效的参数值: %s", valueStr)
		return
	}

	if err := im.monitorManager.ModifyMonitor(taskID, paramName, value); err != nil {
		log.Printf("❌ %v", err)
	}
}

// handleListPositionsCommand 处理列出持仓命令
func (im *InteractiveManager) handleListPositionsCommand() {
	positions := im.positionManager.GetAllPositions()

	if len(positions) == 0 {
		log.Println("📊 当前没有持仓")
		return
	}

	fmt.Println("\n📊 持仓列表:")
	fmt.Printf("%-25s %-6s %-10s %-12s %-12s %-10s %-15s\n", "合约名称", "类型", "持仓量", "均价", "未实现盈亏", "状态", "对冲任务ID")
	fmt.Println(strings.Repeat("-", 100))

	for _, position := range positions {
		hedgeTaskID := position.HedgeTaskID
		if hedgeTaskID == "" {
			hedgeTaskID = "未绑定"
		}

		fmt.Printf("%-25s %-6s %-10s %-12s %-12s %-10s %-15s\n",
			position.ContractName,
			strings.ToUpper(position.OptionType),
			position.CurrentPosition.String(),
			position.AveragePrice.String(),
			position.UnrealizedPnL.String(),
			position.Status,
			hedgeTaskID)
	}
	fmt.Println()
}

// handleListContractsCommand 处理列出期权合约持仓命令
func (im *InteractiveManager) handleListContractsCommand() {
	if im.privateWSClient == nil {
		log.Println("❌ 私有WebSocket客户端未初始化")
		return
	}

	positions := im.privateWSClient.GetAllPositions()

	if len(positions) == 0 {
		log.Println("📊 当前没有期权合约持仓")
		return
	}

	fmt.Println("\n📊 期权合约持仓列表:")
	fmt.Printf("%-30s %-6s %-12s %-15s %-12s %-12s %-15s %-15s\n",
		"合约名称", "方向", "仓位", "价值", "入场价格", "标记价格", "未结盈亏", "已结盈亏")
	fmt.Println(strings.Repeat("-", 140))

	for _, position := range positions {
		// 格式化显示数据
		sizeStr := position.Size.String()
		if position.Size.IsZero() {
			sizeStr = "0"
		}

		valueStr := position.PositionValue.String()
		if position.PositionValue.IsZero() {
			valueStr = "0"
		}

		entryPriceStr := position.EntryPrice.String()
		if position.EntryPrice.IsZero() {
			entryPriceStr = "0"
		}

		markPriceStr := position.MarkPrice.String()
		if position.MarkPrice.IsZero() {
			markPriceStr = "0"
		}

		unrealizedPnlStr := position.UnrealisedPnl.String()
		if position.UnrealisedPnl.IsZero() {
			unrealizedPnlStr = "0"
		}

		realizedPnlStr := position.CumRealisedPnl.String()
		if position.CumRealisedPnl.IsZero() {
			realizedPnlStr = "0"
		}

		fmt.Printf("%-30s %-6s %-12s %-15s %-12s %-12s %-15s %-15s\n",
			position.Symbol,
			position.Side,
			sizeStr,
			valueStr,
			entryPriceStr,
			markPriceStr,
			unrealizedPnlStr,
			realizedPnlStr)
	}

	fmt.Printf("\n📈 总计: %d 个期权合约持仓\n", len(positions))
	fmt.Println()
}

// handleShowPositionCommand 处理显示持仓详情命令
func (im *InteractiveManager) handleShowPositionCommand(args []string) {
	if len(args) < 1 {
		log.Println("❌ 显示持仓格式: show_position <合约名称>")
		return
	}

	contractName := args[0]
	position, err := im.positionManager.GetPosition(contractName)
	if err != nil {
		log.Printf("❌ %v", err)
		return
	}

	fmt.Printf("\n📊 持仓详情: %s\n", position.ContractName)
	fmt.Printf("  期权类型:         %s\n", strings.ToUpper(position.OptionType))
	fmt.Printf("  行权价格:         %s\n", position.StrikePrice.String())
	fmt.Printf("  到期时间:         %s\n", position.ExpiryTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("  标的资产:         %s\n", position.Symbol)
	fmt.Printf("  当前持仓量:       %s\n", position.CurrentPosition.String())
	fmt.Printf("  持仓均价:         %s\n", position.AveragePrice.String())
	fmt.Printf("  总成本:           %s\n", position.TotalCost.String())
	fmt.Printf("  累计手续费:       %s\n", position.TotalFees.String())
	fmt.Printf("  未实现盈亏:       %s\n", position.UnrealizedPnL.String())
	fmt.Printf("  已实现盈亏:       %s\n", position.RealizedPnL.String())
	fmt.Printf("  对冲任务ID:       %s\n", position.HedgeTaskID)
	fmt.Printf("  状态:             %s\n", position.Status)
	fmt.Printf("  创建时间:         %s\n", position.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  最后更新:         %s\n", position.UpdatedAt.Format("2006-01-02 15:04:05"))

	// 显示交易记录
	if len(position.Trades) > 0 {
		fmt.Println("\n  📈 交易记录:")
		for i, trade := range position.Trades {
			fmt.Printf("    %d. %s %s %s @ %s (手续费: %s) [%s]\n",
				i+1, trade.Timestamp.Format("01-02 15:04"), strings.ToUpper(trade.Side),
				trade.Quantity.String(), trade.Price.String(), trade.Fee.String(), trade.TradeID)
		}
	}

	// 显示事件记录
	if len(position.Events) > 0 {
		fmt.Println("\n  📝 事件记录:")
		for i, event := range position.Events {
			fmt.Printf("    %d. %s %s - %s\n",
				i+1, event.Timestamp.Format("01-02 15:04"), event.EventType, event.Description)
		}
	}
	fmt.Println()
}

// handleDeletePositionCommand 处理删除持仓命令
func (im *InteractiveManager) handleDeletePositionCommand(args []string) {
	if len(args) < 1 {
		log.Println("❌ 删除持仓格式: delete_position <合约名称>")
		return
	}

	contractName := args[0]
	if err := im.positionManager.DeletePosition(contractName); err != nil {
		log.Printf("❌ %v", err)
	}
}

// handleSaveCommand 处理保存命令
func (im *InteractiveManager) handleSaveCommand() {
	if err := im.monitorManager.SaveData(im.positionManager); err != nil {
		log.Printf("❌ 保存数据失败: %v", err)
	} else {
		log.Println("✅ 数据保存成功")
	}
}

// handleExportCommand 处理导出命令
func (im *InteractiveManager) handleExportCommand() {
	// 创建导出目录
	exportDir := "export"
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		log.Printf("❌ 创建导出目录失败: %v", err)
		return
	}

	// 生成导出文件名
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("options_report_%s.txt", timestamp)
	filepath := fmt.Sprintf("%s/%s", exportDir, filename)

	// 生成报告内容
	content := im.generateReport()

	// 写入文件
	if err := os.WriteFile(filepath, []byte(content), 0644); err != nil {
		log.Printf("❌ 导出报告失败: %v", err)
		return
	}

	log.Printf("✅ 报告已导出到: %s", filepath)
}

// handleTestTimeValueCommand 处理测试时间价值计算命令
func (im *InteractiveManager) handleTestTimeValueCommand(args []string) {
	if len(args) < 3 {
		log.Println("❌ 测试时间价值格式: test_tv <期权类型> <现货价格> <行权价格>")
		log.Println("   示例: test_tv call 2500 2600")
		log.Println("   示例: test_tv put 2500 2400")
		return
	}

	optionType := strings.ToLower(args[0])
	if optionType != "call" && optionType != "put" {
		log.Println("❌ 期权类型必须是 call 或 put")
		return
	}

	spotPrice, err := decimal.NewFromString(args[1])
	if err != nil {
		log.Printf("❌ 无效的现货价格: %s", args[1])
		return
	}

	strikePrice, err := decimal.NewFromString(args[2])
	if err != nil {
		log.Printf("❌ 无效的行权价格: %s", args[2])
		return
	}

	log.Printf("🧪 测试时间价值计算:")
	log.Printf("   期权类型: %s", strings.ToUpper(optionType))
	log.Printf("   现货价格: %s", spotPrice.String())
	log.Printf("   行权价格: %s", strikePrice.String())
	log.Println("")

	// 调用真实的时间价值计算函数
	timeValuePercent, err := im.monitorManager.contractManager.calculateRealTimeValue("TEST-CONTRACT", spotPrice, strikePrice, optionType)
	if err != nil {
		log.Printf("❌ 无法计算时间价值: %v", err)
		log.Println("💡 提示: 这是测试命令，需要真实的期权盘口数据才能计算")
		return
	}

	log.Printf("🎯 最终结果: 时间价值百分比 = %.4f%%", timeValuePercent.Mul(decimal.NewFromInt(100)).InexactFloat64())
}

// generateReport 生成报告内容
func (im *InteractiveManager) generateReport() string {
	var report strings.Builder

	report.WriteString("期权交易系统详细报告\n")
	report.WriteString("===================\n\n")
	report.WriteString(fmt.Sprintf("生成时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	// 全局参数
	defaults := GetGlobalDefaults()
	report.WriteString("全局参数配置:\n")
	report.WriteString(fmt.Sprintf("  监控行权时间阈值: %s\n", defaults.ExpiryThreshold))
	report.WriteString(fmt.Sprintf("  监控价格范围阈值: %s\n", defaults.PriceRange.String()))
	report.WriteString(fmt.Sprintf("  单个期权最高持仓量: %s\n", defaults.MaxPosition.String()))
	report.WriteString(fmt.Sprintf("  单次下单量: %s\n", defaults.OrderSize.String()))
	report.WriteString(fmt.Sprintf("  盘口深度检测阈值: %s\n", defaults.DepthThreshold.String()))
	report.WriteString(fmt.Sprintf("  盘口监控档数: %d\n", defaults.DepthLevels))
	report.WriteString(fmt.Sprintf("  动态对冲阈值: %s\n", defaults.HedgeThreshold.String()))
	report.WriteString(fmt.Sprintf("  对冲系统API地址: %s\n\n", defaults.HedgeAPIURL))

	// 监控任务
	monitors := im.monitorManager.GetAllMonitors()
	report.WriteString(fmt.Sprintf("监控任务 (%d个):\n", len(monitors)))
	for _, task := range monitors {
		report.WriteString(fmt.Sprintf("  %s: %s %s (目标: %s) [%s]\n",
			task.ID, strings.ToUpper(task.OptionType), task.ExpiryTimeRange,
			task.TimeValueTarget.String(), task.Status))
	}
	report.WriteString("\n")

	// 持仓信息
	positions := im.positionManager.GetAllPositions()
	report.WriteString(fmt.Sprintf("持仓信息 (%d个):\n", len(positions)))
	for _, position := range positions {
		report.WriteString(fmt.Sprintf("  %s:\n", position.ContractName))
		report.WriteString(fmt.Sprintf("    类型: %s, 持仓: %s, 均价: %s\n",
			strings.ToUpper(position.OptionType), position.CurrentPosition.String(), position.AveragePrice.String()))
		report.WriteString(fmt.Sprintf("    盈亏: 未实现 %s, 已实现 %s\n",
			position.UnrealizedPnL.String(), position.RealizedPnL.String()))
		report.WriteString(fmt.Sprintf("    对冲任务: %s, 状态: %s\n",
			position.HedgeTaskID, position.Status))
		report.WriteString(fmt.Sprintf("    交易次数: %d, 事件数: %d\n\n",
			len(position.Trades), len(position.Events)))
	}

	return report.String()
}

// printDetailedMonitorStatus 显示监控任务详情状态（修改方案增强版）
func (im *InteractiveManager) printDetailedMonitorStatus(task *MonitorTask) {
	// 获取期权合约信息
	optionContracts, err := im.monitorManager.contractManager.GetEligibleContracts(task)
	if err != nil {
		log.Printf("⚠️  获取期权合约失败: %v", err)
		return
	}

	// 获取现货价格
	spotPrice, exists := im.monitorManager.contractManager.getSpotPrice("ETHUSDT")
	if !exists {
		spotPrice = decimal.NewFromFloat(0)
	}

	// 计算运行时长
	var duration string
	var startTime string
	if task.Status == StatusRunning {
		duration = formatDuration(time.Since(task.UpdatedAt))
		startTime = task.UpdatedAt.Format("15:04")
	} else {
		duration = "已停止"
		startTime = task.UpdatedAt.Format("15:04")
	}

	// 获取价格范围
	defaults := GetGlobalDefaults()
	priceRange := defaults.PriceRange

	// 解析行权价范围
	minStrike, maxStrike := im.getStrikePriceRange(optionContracts)

	// 计算时间价值和满足条件的合约
	timeValues, _, _, _ := im.calculateTimeValues(task, optionContracts, spotPrice)

	// 输出基本信息头部
	log.Printf("🎯 任务 %s 筛选期权合约: 现货价格=%.2f, 范围=[%d-%d], 合约数=%d",
		task.ID, spotPrice.InexactFloat64(), minStrike, maxStrike, len(optionContracts))
	log.Println("")

	// 输出详细监控信息
	optionTypeDesc := "CALL期权卖出"
	if task.OptionType == OptionTypePut {
		optionTypeDesc = "PUT期权卖出"
	}

	log.Printf("【期权监控】%s (%s)", task.ID, optionTypeDesc)
	log.Printf("├─ 基本信息: ETHUSDT | 到期: %s | 阈值: %.2f%% | 价格范围: ±%.0f%%",
		task.ExpiryTimeRange, task.TimeValueTarget.Mul(decimal.NewFromInt(100)).InexactFloat64(), priceRange.Mul(decimal.NewFromInt(100)).InexactFloat64())
	log.Printf("├─ 运行状态: %s | 开始: %s | 持续: %s",
		task.Status, startTime, duration)
	log.Printf("├─ 现货价格: %.2f | 监控范围: [%d-%d] | 合约数: %d个",
		spotPrice.InexactFloat64(), minStrike, maxStrike, len(optionContracts))

	// 获取合约状态统计
	stateStats := im.getContractStateStatistics(optionContracts)
	log.Printf("└─ 状态统计: Trading: %d个 | 交割中: %d个 | 已行权: %d个 | 其他: %d个",
		stateStats["Trading"], stateStats["Delivering"], stateStats["Delivered"], stateStats["Other"])
	log.Println("")

	// 显示期权详情表格
	if len(timeValues) > 0 {
		log.Println("       期权合约        时间价值 买1价  最后获取   最后检查       状态 WS 监控时长")
		log.Println("──────────────────────────────────────────────────────────────────────────────")

		// 按照修改方案排序：Trading状态在前，按时间价值降序排列
		tradingContracts := make([]TimeValueInfo, 0)
		otherContracts := make([]TimeValueInfo, 0)

		for _, tv := range timeValues {
			contractName := ""
			for _, contract := range optionContracts {
				if im.parseStrikePriceFromContract(contract) == tv.Strike {
					contractName = contract
					break
				}
			}
			if contractName == "" {
				contractName = fmt.Sprintf("ETH-未知-%d-C-USDT", tv.Strike)
			}

			state, wsConnected, lastUpdate, monitorDuration := im.getContractStatusInfo(contractName)
			lastTradingCheck := im.monitorManager.formatLastTradingCheck(contractName)

			tvInfo := TimeValueInfo{
				Strike:            tv.Strike,
				TimeValue:         tv.TimeValue,
				Bid1Price:         tv.Bid1Price,
				CalculationDetail: fmt.Sprintf("%s|%s|%s|%s|%s", StateToString(state), formatWSStatus(wsConnected), formatTimeSince(lastUpdate), lastTradingCheck, formatDuration(monitorDuration)),
			}

			if state == StateTrading {
				tradingContracts = append(tradingContracts, tvInfo)
			} else {
				otherContracts = append(otherContracts, tvInfo)
			}
		}

		// 排序：Trading状态按时间价值降序，其他状态按合约名
		for i := 0; i < len(tradingContracts)-1; i++ {
			for j := i + 1; j < len(tradingContracts); j++ {
				if tradingContracts[i].TimeValue.LessThan(tradingContracts[j].TimeValue) {
					tradingContracts[i], tradingContracts[j] = tradingContracts[j], tradingContracts[i]
				}
			}
		}

		// 显示Trading状态的合约
		for _, tv := range tradingContracts {
			contractName := ""
			for _, contract := range optionContracts {
				if im.parseStrikePriceFromContract(contract) == tv.Strike {
					contractName = contract
					break
				}
			}
			if contractName == "" {
				contractName = fmt.Sprintf("ETH-未知-%d-C-USDT", tv.Strike)
			}

			parts := strings.Split(tv.CalculationDetail, "|")
			if len(parts) == 5 {
				log.Printf("%-12s %-9s %-6s %-9s %-12s %-3s %-1s %-3s",
					contractName,
					fmt.Sprintf("%.2f%%", tv.TimeValue.Mul(decimal.NewFromInt(100)).InexactFloat64()),
					fmt.Sprintf("%.1f", tv.Bid1Price.InexactFloat64()),
					parts[2], // 最后获取
					parts[3], // 最后检查
					parts[0], // 状态
					parts[1], // WS连接
					parts[4]) // 监控时长
			}
		}

		// 显示其他状态的合约
		for _, tv := range otherContracts {
			contractName := ""
			for _, contract := range optionContracts {
				if im.parseStrikePriceFromContract(contract) == tv.Strike {
					contractName = contract
					break
				}
			}
			if contractName == "" {
				contractName = fmt.Sprintf("ETH-未知-%d-C-USDT", tv.Strike)
			}

			parts := strings.Split(tv.CalculationDetail, "|")
			if len(parts) == 5 {
				log.Printf("%-33s %-11s %-9s %-11s %-15s %-9s %-9s %-8s",
					contractName,
					"0.00%",
					fmt.Sprintf("%.1f", tv.Bid1Price.InexactFloat64()),
					parts[2], // 最后获取
					parts[3], // 最后检查
					parts[0], // 状态
					parts[1], // WS连接
					parts[4]) // 监控时长
			}
		}

		log.Println("")
		log.Printf("📊 统计: 总计 %d个期权 | Trading: %d个 | 其他状态: %d个",
			len(timeValues), len(tradingContracts), len(otherContracts))
	} else {
		log.Println("📊 当前没有符合条件的期权合约")
	}
}

// getContractStateStatistics 获取合约状态统计
func (im *InteractiveManager) getContractStateStatistics(contracts []string) map[string]int {
	stats := map[string]int{
		"Trading":    0,
		"Delivering": 0,
		"Delivered":  0,
		"Other":      0,
	}

	if im.monitorManager.wsClient == nil || im.monitorManager.wsClient.contractMonitor == nil {
		return stats
	}

	monitor := im.monitorManager.wsClient.contractMonitor
	for _, contract := range contracts {
		if status, exists := monitor.GetContractStatus(contract); exists {
			switch status.State {
			case StateTrading:
				stats["Trading"]++
			case StateDelivering:
				stats["Delivering"]++
			case StateDelivered:
				stats["Delivered"]++
			default:
				stats["Other"]++
			}
		} else {
			stats["Other"]++
		}
	}

	return stats
}

// getContractStatusInfo 获取合约状态信息
func (im *InteractiveManager) getContractStatusInfo(contractName string) (OptionContractState, bool, time.Time, time.Duration) {
	if im.monitorManager.wsClient == nil || im.monitorManager.wsClient.contractMonitor == nil {
		return StateFetchFailed, false, time.Now(), 0
	}

	monitor := im.monitorManager.wsClient.contractMonitor
	if status, exists := monitor.GetContractStatus(contractName); exists {
		monitorDuration := time.Since(status.MonitorStartTime)
		return status.State, status.WSConnected, status.LastWSUpdate, monitorDuration
	}

	return StateFetchFailed, false, time.Now(), 0
}

// formatWSStatus 格式化WebSocket连接状态
func formatWSStatus(connected bool) string {
	if connected {
		return "✅"
	}
	return "❌"
}

// TimeValueInfo 时间价值信息
type TimeValueInfo struct {
	Strike            int
	TimeValue         decimal.Decimal
	Bid1Price         decimal.Decimal
	CalculationDetail string
}

// calculateTimeValues 计算时间价值
func (im *InteractiveManager) calculateTimeValues(task *MonitorTask, contracts []string, spotPrice decimal.Decimal) ([]TimeValueInfo, int, decimal.Decimal, int) {
	var timeValues []TimeValueInfo
	satisfiedCount := 0
	maxTimeValue := decimal.Zero
	maxStrike := 0

	for _, contract := range contracts {
		strike := im.parseStrikePriceFromContract(contract)
		strikePrice := decimal.NewFromInt(int64(strike))

		// 尝试从WebSocket获取真实数据
		timeValue, bid1Price, calculationDetail, err := im.calculateRealTimeValue(contract, spotPrice, strikePrice, task.OptionType)
		if err != nil {
			log.Printf("❌ 无法获取期权 %s 的真实数据: %v", contract, err)
			// 不使用模拟数据，直接跳过
			continue
		}

		timeValues = append(timeValues, TimeValueInfo{
			Strike:            strike,
			TimeValue:         timeValue,
			Bid1Price:         bid1Price,
			CalculationDetail: calculationDetail,
		})

		if timeValue.GreaterThan(task.TimeValueTarget) {
			satisfiedCount++
		}

		if timeValue.GreaterThan(maxTimeValue) {
			maxTimeValue = timeValue
			maxStrike = strike
		}
	}

	return timeValues, satisfiedCount, maxTimeValue, maxStrike
}

// calculateRealTimeValue 计算真实时间价值（修改方案增强版）
func (im *InteractiveManager) calculateRealTimeValue(contract string, spotPrice, strikePrice decimal.Decimal, optionType string) (decimal.Decimal, decimal.Decimal, string, error) {
	// 第一步：验证合约状态（盘口数据判断逻辑优化）
	if im.monitorManager.wsClient != nil && im.monitorManager.wsClient.contractMonitor != nil {
		if status, exists := im.monitorManager.wsClient.contractMonitor.GetContractStatus(contract); exists {
			// 前提条件：合约状态必须为StateTrading
			if status.State != StateTrading {
				return decimal.Zero, decimal.Zero, "", fmt.Errorf("合约 %s 状态为 %s，不执行时间价值计算", contract, StateToString(status.State))
			}

			// 验证标准：盘口数据有效性
			if !status.OrderBookValid {
				return decimal.Zero, decimal.Zero, "", fmt.Errorf("合约 %s 盘口数据无效", contract)
			}
		} else {
			return decimal.Zero, decimal.Zero, "", fmt.Errorf("合约 %s 状态未知，无法执行计算", contract)
		}
	}

	// 第二步：从WebSocket客户端获取期权最优买价
	bid1Price, err := im.monitorManager.wsClient.GetBestBidPrice(contract)
	if err != nil {
		return decimal.Zero, decimal.Zero, "", fmt.Errorf("获取期权 %s 买一价失败: %v", contract, err)
	}

	// 第三步：买一价处理（特殊情况：买一价为0时仍认为数据有效）
	calculationDetail := ""
	if bid1Price.IsZero() {
		// 无买盘：买一价按0计算时间价值
		calculationDetail = "无买盘(买一价=0)"
		log.Printf("ℹ️  期权 %s 无买盘，买一价为0", contract)
	}

	// 第四步：计算时间价值百分比（按照公式）
	var timeValue decimal.Decimal

	if optionType == OptionTypePut {
		// PUT期权: time_value = ((spot_price >= strike_price) ? bid1_price : bid1_price - (strike_price - spot_price)) / spot_price
		if spotPrice.GreaterThanOrEqual(strikePrice) {
			timeValue = bid1Price
			if calculationDetail == "" {
				calculationDetail = fmt.Sprintf("PUT价外: %.2f/%.2f", bid1Price.InexactFloat64(), spotPrice.InexactFloat64())
			}
		} else {
			timeValue = bid1Price.Sub(strikePrice.Sub(spotPrice))
			if calculationDetail == "" {
				calculationDetail = fmt.Sprintf("PUT价内: (%.2f-(%.0f-%.2f))/%.2f",
					bid1Price.InexactFloat64(), strikePrice.InexactFloat64(), spotPrice.InexactFloat64(), spotPrice.InexactFloat64())
			}
		}
	} else {
		// CALL期权: time_value = ((spot_price <= strike_price) ? bid1_price : bid1_price - (spot_price - strike_price)) / spot_price
		if spotPrice.LessThanOrEqual(strikePrice) {
			timeValue = bid1Price
			if calculationDetail == "" {
				calculationDetail = fmt.Sprintf("CALL价外: %.2f/%.2f", bid1Price.InexactFloat64(), spotPrice.InexactFloat64())
			}
		} else {
			timeValue = bid1Price.Sub(spotPrice.Sub(strikePrice))
			if calculationDetail == "" {
				calculationDetail = fmt.Sprintf("CALL价内: (%.2f-(%.2f-%.0f))/%.2f",
					bid1Price.InexactFloat64(), spotPrice.InexactFloat64(), strikePrice.InexactFloat64(), spotPrice.InexactFloat64())
			}
		}
	}

	// 第五步：转换为百分比
	var timeValuePercent decimal.Decimal
	if spotPrice.IsZero() {
		return decimal.Zero, decimal.Zero, "", fmt.Errorf("现货价格为零")
	}

	timeValuePercent = timeValue.Div(spotPrice)

	return timeValuePercent, bid1Price, calculationDetail, nil
}

// getStrikePriceRange 获取行权价范围
func (im *InteractiveManager) getStrikePriceRange(contracts []string) (int, int) {
	if len(contracts) == 0 {
		return 0, 0
	}

	minStrike := 999999
	maxStrike := 0

	for _, contract := range contracts {
		strike := im.parseStrikePriceFromContract(contract)
		if strike < minStrike {
			minStrike = strike
		}
		if strike > maxStrike {
			maxStrike = strike
		}
	}

	return minStrike, maxStrike
}

// parseStrikePriceFromContract 从合约名称解析行权价
func (im *InteractiveManager) parseStrikePriceFromContract(contract string) int {
	// 解析格式: ETHUSDT-3JUN25-2500-C
	parts := strings.Split(contract, "-")
	if len(parts) >= 3 {
		if strike, err := strconv.Atoi(parts[2]); err == nil {
			return strike
		}
	}
	return 0
}

// formatDuration 格式化时长
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0f秒", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.0f分钟", d.Minutes())
	} else {
		hours := int(d.Hours())
		minutes := int(d.Minutes()) % 60
		if minutes == 0 {
			return fmt.Sprintf("%d小时", hours)
		}
		return fmt.Sprintf("%d小时%d分钟", hours, minutes)
	}
}

// handleWebSocketStatusCommand 处理WebSocket状态查看命令
func (im *InteractiveManager) handleWebSocketStatusCommand() {
	// 获取WebSocket客户端
	wsClient := im.monitorManager.contractManager.wsClient
	if wsClient == nil {
		log.Println("❌ WebSocket客户端未初始化")
		return
	}

	// 获取状态数据
	statusList := wsClient.GetWebSocketStatusTable()
	if len(statusList) == 0 {
		log.Println("📊 当前没有订阅的期权合约")
		return
	}

	// 打印表格头
	fmt.Println("\n🔌 期权WebSocket连接状态:")
	fmt.Printf("%-35s %-8s %-12s %-15s %-12s\n", "期权合约", "连接状态", "最后更新", "买一价", "更新次数")
	fmt.Println(strings.Repeat("-", 85))

	// 打印每个合约的状态
	for _, status := range statusList {
		var statusIcon string
		if status.Connected {
			statusIcon = "✅ 正常"
		} else {
			statusIcon = "⚠️ 异常"
		}

		bid1Price := status.Bid1Price
		if bid1Price == "0" {
			bid1Price = "无数据"
		}

		fmt.Printf("%-35s %-8s %-12s %-15s %-12d\n",
			status.Symbol,
			statusIcon,
			status.TimeSince,
			bid1Price,
			status.UpdateCount)
	}

	// 统计信息
	connectedCount := 0
	for _, status := range statusList {
		if status.Connected {
			connectedCount++
		}
	}

	fmt.Printf("\n📊 统计: 总计 %d 个合约，正常 %d 个，异常 %d 个\n\n",
		len(statusList), connectedCount, len(statusList)-connectedCount)
}

// handleSetRestThresholdCommand 处理设置REST检查阈值命令
func (im *InteractiveManager) handleSetRestThresholdCommand(args []string) {
	if len(args) != 1 {
		log.Println("❌ 用法: set_rest_threshold <时间>")
		log.Println("   示例: set_rest_threshold 30s")
		log.Println("   示例: set_rest_threshold 1m")
		return
	}

	// 解析时间
	duration, err := time.ParseDuration(args[0])
	if err != nil {
		log.Printf("❌ 无效的时间格式: %s", args[0])
		log.Println("   支持格式: 30s, 1m, 2h")
		return
	}

	if duration < 10*time.Second {
		log.Println("❌ REST检查阈值不能小于10秒")
		return
	}

	// 更新所有合约的REST检查阈值
	wsClient := im.monitorManager.contractManager.wsClient
	if wsClient == nil {
		log.Println("❌ WebSocket客户端未初始化")
		return
	}

	// 从合约监控器更新阈值
	count := wsClient.contractMonitor.SetRestCheckThreshold(duration)
	log.Printf("✅ 已更新 %d 个合约的REST检查阈值为: %s", count, duration)
}

// handleContractStateCommand 处理合约状态查看命令
func (im *InteractiveManager) handleContractStateCommand(args []string) {
	wsClient := im.monitorManager.contractManager.wsClient
	if wsClient == nil {
		log.Println("❌ WebSocket客户端未初始化")
		return
	}

	if len(args) == 0 {
		// 显示所有合约状态
		im.showAllContractStates()
	} else {
		// 显示指定合约状态
		im.showContractState(args[0])
	}
}

// showAllContractStates 显示所有合约状态
func (im *InteractiveManager) showAllContractStates() {
	statusMap := im.monitorManager.contractManager.wsClient.contractMonitor.GetAllContractStatus()

	if len(statusMap) == 0 {
		log.Println("📊 当前没有监控的合约")
		return
	}

	fmt.Println("\n📊 所有合约状态:")
	fmt.Println("─────────────────────────────────────────────────────────────────────────")
	fmt.Printf("%-35s %-12s %-15s %-15s %-8s %-8s\n",
		"合约名称", "状态", "最后检查", "下次检查", "失败次数", "盘口有效")
	fmt.Println("─────────────────────────────────────────────────────────────────────────")

	for symbol, status := range statusMap {
		lastCheck := formatTimeSince(status.LastStateCheck)
		nextCheck := formatTimeUntil(status.NextCheckTime)
		failCount := fmt.Sprintf("%d/3", status.CurrentCycleFailCount)
		valid := "否"
		if status.OrderBookValid {
			valid = "是"
		}

		fmt.Printf("%-35s %-12s %-15s %-15s %-8s %-8s\n",
			symbol, StateToString(status.State), lastCheck, nextCheck, failCount, valid)
	}
	fmt.Println("─────────────────────────────────────────────────────────────────────────")
	fmt.Printf("📊 统计: 总计 %d 个合约\n\n", len(statusMap))
}

// showContractState 显示指定合约状态
func (im *InteractiveManager) showContractState(symbol string) {
	status, exists := im.monitorManager.contractManager.wsClient.contractMonitor.GetContractStatus(symbol)
	if !exists {
		log.Printf("❌ 合约 %s 不在监控列表中", symbol)
		return
	}

	fmt.Println("\n合约状态详情:")
	fmt.Println("─────────────────────────────────────────────────────────────────────────")
	fmt.Printf("合约名称: %s\n", status.Symbol)
	fmt.Printf("当前状态: %s\n", StateToString(status.State))
	fmt.Printf("最后检查: %s (%s)\n", formatTimeSince(status.LastStateCheck), status.LastStateCheck.Format("2006/01/02 15:04:05"))
	fmt.Printf("下次检查: %s (%s)\n", formatTimeUntil(status.NextCheckTime), status.NextCheckTime.Format("2006/01/02 15:04:05"))

	// 计算检查间隔
	interval := status.NextCheckTime.Sub(status.LastStateCheck)
	fmt.Printf("检查间隔: %s\n", interval)

	fmt.Printf("失败次数: %d/3 (当前周期)\n", status.CurrentCycleFailCount)

	// 计算监控时长
	monitorDuration := time.Since(status.MonitorStartTime)
	fmt.Printf("监控时长: %s\n", formatDuration(monitorDuration))

	valid := "否"
	if status.OrderBookValid {
		valid = "是"
	}
	fmt.Printf("盘口有效: %s\n", valid)

	wsStatus := "异常"
	if status.WSConnected {
		wsStatus = "正常"
	}
	fmt.Printf("WS连接: %s\n", wsStatus)

	// 尝试获取买一价和时间价值
	if bid1Price, err := im.monitorManager.contractManager.wsClient.GetBestBidPrice(symbol); err == nil {
		fmt.Printf("买一价: %s USDT\n", bid1Price.String())

		// 尝试计算时间价值
		if spotPrice, exists := im.monitorManager.contractManager.wsClient.GetSpotPrice("ETHUSDT"); exists {
			// 简化的时间价值计算（这里可以调用具体的计算方法）
			fmt.Printf("现货价格: %s USDT\n", spotPrice.String())
		}
	} else {
		fmt.Printf("买一价: 无数据\n")
		fmt.Printf("时间价值: 无法计算\n")
	}

	fmt.Println("─────────────────────────────────────────────────────────────────────────")
	fmt.Println()
}

// formatTimeSince 格式化时间差（多久之前）
func formatTimeSince(t time.Time) string {
	if t.IsZero() {
		return "从未"
	}

	duration := time.Since(t)
	if duration < time.Minute {
		return fmt.Sprintf("%ds ago", int(duration.Seconds()))
	} else if duration < time.Hour {
		return fmt.Sprintf("%dm ago", int(duration.Minutes()))
	} else {
		return fmt.Sprintf("%dh ago", int(duration.Hours()))
	}
}

// formatTimeUntil 格式化时间差（多久之后）
func formatTimeUntil(t time.Time) string {
	if t.IsZero() {
		return "未设置"
	}

	duration := time.Until(t)
	if duration < 0 {
		return "已过期"
	}

	if duration < time.Minute {
		return fmt.Sprintf("%ds后", int(duration.Seconds()))
	} else if duration < time.Hour {
		return fmt.Sprintf("%dm后", int(duration.Minutes()))
	} else {
		return fmt.Sprintf("%dh后", int(duration.Hours()))
	}
}
