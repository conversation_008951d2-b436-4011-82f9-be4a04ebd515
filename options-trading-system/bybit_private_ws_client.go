package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
)

// BybitPrivateWSClient Bybit私有WebSocket客户端（用于持仓数据）
type BybitPrivateWSClient struct {
	conn              *websocket.Conn
	apiKey            string
	secretKey         string
	wsURL             string
	isConnected       bool
	mutex             sync.RWMutex
	stopChan          chan struct{}
	positionCache     *OptionPositionCache
	reconnectInterval time.Duration
	restClient        *BybitRESTClient
	lastRestCheck     time.Time
	restCheckInterval time.Duration
}

// BybitPrivateWSMessage 私有WebSocket消息结构
type BybitPrivateWSMessage struct {
	Op      string      `json:"op"`      // 操作类型 (auth, subscribe, etc.)
	Topic   string      `json:"topic"`   // 主题 (position, etc.)
	Type    string      `json:"type"`    // 消息类型 (snapshot, delta, etc.)
	Data    interface{} `json:"data"`    // 数据内容
	Success bool        `json:"success"` // 操作是否成功
	RetMsg  string      `json:"ret_msg"` // 返回消息
	ConnId  string      `json:"conn_id"` // 连接ID
	Args    []string    `json:"args"`    // 参数列表 (用于pong消息等)
	Ts      int64       `json:"ts"`      // 时间戳
}

// BybitPositionWSData WebSocket持仓数据结构（根据官方文档）
type BybitPositionWSData struct {
	Id           string `json:"id"`           // 消息ID
	Topic        string `json:"topic"`        // 主题名
	CreationTime int64  `json:"creationTime"` // 消息创建时间
	Data         []struct {
		Category               string `json:"category"`               // 产品类型
		Symbol                 string `json:"symbol"`                 // 合约名称
		Side                   string `json:"side"`                   // 持仓方向 Buy,Sell
		Size                   string `json:"size"`                   // 当前仓位的合约数量
		PositionIdx            int    `json:"positionIdx"`            // 仓位标识
		TradeMode              int    `json:"tradeMode"`              // 交易模式
		PositionValue          string `json:"positionValue"`          // 仓位价值
		RiskId                 int    `json:"riskId"`                 // 风险限额ID
		RiskLimitValue         string `json:"riskLimitValue"`         // 风险限额值
		EntryPrice             string `json:"entryPrice"`             // 入场价
		MarkPrice              string `json:"markPrice"`              // 标记价
		Leverage               string `json:"leverage"`               // 杠杆
		PositionBalance        string `json:"positionBalance"`        // 仓位保证金
		AutoAddMargin          int    `json:"autoAddMargin"`          // 是否自动追加保证金
		PositionIM             string `json:"positionIM"`             // 仓位起始保证金
		PositionMM             string `json:"positionMM"`             // 仓位维持保证金
		LiqPrice               string `json:"liqPrice"`               // 强平价格
		BustPrice              string `json:"bustPrice"`              // 破产价格
		TakeProfit             string `json:"takeProfit"`             // 止盈价格
		StopLoss               string `json:"stopLoss"`               // 止损价格
		TrailingStop           string `json:"trailingStop"`           // 追踪止损
		UnrealisedPnl          string `json:"unrealisedPnl"`          // 未结盈亏
		SessionAvgPrice        string `json:"sessionAvgPrice"`        // USDC合约平均持仓价格
		Delta                  string `json:"delta"`                  // Delta
		Gamma                  string `json:"gamma"`                  // Gamma
		Vega                   string `json:"vega"`                   // Vega
		Theta                  string `json:"theta"`                  // Theta
		CurRealisedPnl         string `json:"curRealisedPnl"`         // 当前持仓的已结盈亏
		CumRealisedPnl         string `json:"cumRealisedPnl"`         // 累计已结盈亏
		PositionStatus         string `json:"positionStatus"`         // 仓位状态
		AdlRankIndicator       int    `json:"adlRankIndicator"`       // 自动减仓灯
		CreatedTime            string `json:"createdTime"`            // 仓位创建时间戳
		UpdatedTime            string `json:"updatedTime"`            // 仓位数据更新时间戳
		Seq                    int64  `json:"seq"`                    // 序列号
		IsReduceOnly           bool   `json:"isReduceOnly"`           // 是否仅减仓
		MmrSysUpdatedTime      string `json:"mmrSysUpdatedTime"`      // MMR系统更新时间
		LeverageSysUpdatedTime string `json:"leverageSysUpdatedTime"` // 杠杆系统更新时间
		TpslMode               string `json:"tpslMode"`               // 止盈止损模式
	} `json:"data"`
}

// NewBybitPrivateWSClient 创建新的Bybit私有WebSocket客户端
func NewBybitPrivateWSClient() *BybitPrivateWSClient {
	apiKey, secretKey, _, _ := GetBybitConfig()

	// 私有WebSocket使用不同的URL
	wsURL := "wss://stream.bybit.com/v5/private"
	if UseTestnet {
		wsURL = "wss://stream-testnet.bybit.com/v5/private"
	}

	client := &BybitPrivateWSClient{
		apiKey:            apiKey,
		secretKey:         secretKey,
		wsURL:             wsURL,
		isConnected:       false,
		stopChan:          make(chan struct{}),
		positionCache:     NewOptionPositionCache(),
		reconnectInterval: 5 * time.Second,
		restClient:        NewBybitRESTClient(),
		restCheckInterval: 1 * time.Second, // 1秒检查一次
	}

	return client
}

// Connect 连接到私有WebSocket
func (client *BybitPrivateWSClient) Connect() error {
	log.Println("🔗 连接Bybit私有WebSocket（持仓数据）...")

	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	conn, _, err := dialer.Dial(client.wsURL, http.Header{})
	if err != nil {
		return fmt.Errorf("私有WebSocket连接失败: %v", err)
	}

	client.conn = conn
	client.isConnected = true

	log.Println("✅ 私有WebSocket连接成功")

	// 进行认证
	if err := client.authenticate(); err != nil {
		client.Disconnect()
		return fmt.Errorf("WebSocket认证失败: %v", err)
	}

	// 启动消息处理协程
	go client.handleMessages()

	// 启动心跳协程
	go client.heartbeat()

	// 启动REST API验证协程
	go client.startRestVerification()

	return nil
}

// authenticate 进行WebSocket认证
func (client *BybitPrivateWSClient) authenticate() error {
	expires := time.Now().UnixMilli() + 10000 // 10秒后过期
	val := fmt.Sprintf("GET/realtime%d", expires)

	h := hmac.New(sha256.New, []byte(client.secretKey))
	h.Write([]byte(val))
	signature := hex.EncodeToString(h.Sum(nil))

	authMessage := map[string]interface{}{
		"op": "auth",
		"args": []interface{}{
			client.apiKey,
			expires,
			signature,
		},
	}

	if err := client.conn.WriteJSON(authMessage); err != nil {
		return fmt.Errorf("发送认证消息失败: %v", err)
	}

	log.Println("🔐 已发送WebSocket认证请求")
	return nil
}

// SubscribePositions 订阅期权持仓数据
func (client *BybitPrivateWSClient) SubscribePositions() error {
	if !client.isConnected || client.conn == nil {
		return fmt.Errorf("WebSocket未连接，无法订阅持仓数据")
	}

	// 使用专门的期权持仓订阅主题
	message := map[string]interface{}{
		"op":   "subscribe",
		"args": []string{"position"},
	}

	log.Printf("🔍 [订阅请求] 发送期权持仓订阅消息: %+v", message)

	if err := client.conn.WriteJSON(message); err != nil {
		log.Printf("❌ 订阅期权持仓数据失败: %v", err)
		return fmt.Errorf("订阅期权持仓数据失败: %v", err)
	}

	log.Println("📊 已发送期权持仓数据订阅请求 (position)")
	return nil
}

// Disconnect 断开WebSocket连接
func (client *BybitPrivateWSClient) Disconnect() {
	if client.conn != nil {
		client.isConnected = false
		close(client.stopChan)
		client.conn.Close()
		log.Println("🔌 私有WebSocket连接已断开")
	}
}

// handleMessages 处理WebSocket消息
func (client *BybitPrivateWSClient) handleMessages() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("❌ 私有WebSocket消息处理异常: %v", r)
		}
	}()

	for {
		select {
		case <-client.stopChan:
			return
		default:
			// 读取原始消息
			_, messageBytes, err := client.conn.ReadMessage()
			if err != nil {
				if client.isConnected {
					log.Printf("⚠️ 私有WebSocket读取消息失败: %v", err)
					client.reconnect()
				}
				return
			}

			// 调试：输出原始消息
			log.Printf("🔍 [私有WS原始消息] %s", string(messageBytes))

			// 解析消息
			var message BybitPrivateWSMessage
			if err := json.Unmarshal(messageBytes, &message); err != nil {
				log.Printf("❌ 私有WebSocket消息解析失败: %v", err)
				log.Printf("🔍 [解析失败的消息] %s", string(messageBytes))
				continue
			}

			client.processMessage(&message)
		}
	}
}

// processMessage 处理具体消息
func (client *BybitPrivateWSClient) processMessage(message *BybitPrivateWSMessage) {
	log.Printf("🔍 [消息处理] Topic: '%s', Type: '%s', Op: '%s'", message.Topic, message.Type, message.Op)

	// 处理系统消息（认证、订阅响应等）
	if message.Topic == "" {
		switch message.Op {
		case "auth":
			client.handleAuthResponse(message)
			return
		case "subscribe":
			client.handleSubscriptionResponse(message)
			return
		case "pong":
			// 心跳响应，正常忽略
			log.Printf("💓 收到心跳响应")
			return
		default:
			// 尝试从Data字段解析系统消息
			if rawData, ok := message.Data.(map[string]interface{}); ok {
				if op, exists := rawData["op"]; exists {
					log.Printf("🔍 [系统消息] Op: %v", op)
					if op == "auth" {
						client.handleAuthResponse(message)
						return
					}
				}
			}

			// 检查是否是旧格式的认证或订阅响应
			if message.Type == "AUTH_RESP" || strings.Contains(fmt.Sprintf("%v", message.Data), "auth") {
				client.handleAuthResponse(message)
				return
			} else if message.Type == "COMMAND_RESP" {
				client.handleSubscriptionResponse(message)
				return
			} else {
				log.Printf("🔍 [未知系统消息] 完整消息: %+v", message)
				return
			}
		}
	}

	// 处理有topic的消息
	switch {
	case message.Topic == "position" || strings.HasPrefix(message.Topic, "position."):
		log.Printf("📊 [持仓消息] 开始处理持仓更新，主题: %s", message.Topic)
		client.handlePositionUpdate(message)
	default:
		log.Printf("🔍 [未知主题] Topic: %s, 完整消息: %+v", message.Topic, message)
	}
}

// handleAuthResponse 处理认证响应
func (client *BybitPrivateWSClient) handleAuthResponse(message *BybitPrivateWSMessage) {
	log.Printf("🔍 [认证响应] Op: %s, Success: %t, RetMsg: %s", message.Op, message.Success, message.RetMsg)

	// 检查新格式的认证响应（直接在消息根级别）
	if message.Op == "auth" {
		if message.Success {
			log.Println("✅ WebSocket认证成功")
			// 认证成功后订阅持仓数据
			if err := client.SubscribePositions(); err != nil {
				log.Printf("❌ 订阅持仓数据失败: %v", err)
			}
		} else {
			log.Printf("❌ WebSocket认证失败: %s", message.RetMsg)
		}
		return
	}

	// 兼容旧格式的认证响应（在Data字段中）
	if dataMap, ok := message.Data.(map[string]interface{}); ok {
		log.Printf("🔍 [认证响应] 解析后数据: %+v", dataMap)

		if success, exists := dataMap["success"]; exists {
			if success.(bool) {
				log.Println("✅ WebSocket认证成功")
				// 认证成功后订阅持仓数据
				if err := client.SubscribePositions(); err != nil {
					log.Printf("❌ 订阅持仓数据失败: %v", err)
				}
			} else {
				log.Println("❌ WebSocket认证失败")
				if msg, exists := dataMap["ret_msg"]; exists {
					log.Printf("❌ 认证失败原因: %v", msg)
				}
			}
		}
	} else {
		log.Printf("❌ 认证响应数据格式错误: %T", message.Data)
	}
}

// handleSubscriptionResponse 处理订阅响应
func (client *BybitPrivateWSClient) handleSubscriptionResponse(message *BybitPrivateWSMessage) {
	log.Printf("🔍 [订阅响应] Op: %s, Success: %t, RetMsg: %s", message.Op, message.Success, message.RetMsg)

	// 检查新格式的订阅响应（直接在消息根级别）
	if message.Op == "subscribe" {
		if message.Success {
			log.Println("✅ 持仓数据订阅成功")
			// 订阅成功后立即使用REST API初始化持仓数据
			go client.initializePositionsFromREST()
		} else {
			log.Printf("❌ 持仓数据订阅失败: %s", message.RetMsg)
		}
		return
	}

	// 兼容旧格式的订阅响应（在Data字段中）
	if dataMap, ok := message.Data.(map[string]interface{}); ok {
		log.Printf("🔍 [订阅响应] 解析后数据: %+v", dataMap)

		if success, exists := dataMap["success"]; exists {
			if success.(bool) {
				log.Println("✅ 持仓数据订阅成功")
				// 订阅成功后立即使用REST API初始化持仓数据
				go client.initializePositionsFromREST()
			} else {
				log.Println("❌ 持仓数据订阅失败")
				if msg, exists := dataMap["ret_msg"]; exists {
					log.Printf("❌ 订阅失败原因: %v", msg)
				}
			}
		}
	} else {
		log.Printf("❌ 订阅响应数据格式错误: %T", message.Data)
	}
}

// handlePositionUpdate 处理持仓更新
func (client *BybitPrivateWSClient) handlePositionUpdate(message *BybitPrivateWSMessage) {
	log.Printf("🔍 [持仓更新] Topic: %s, Type: %s", message.Topic, message.Type)
	log.Printf("🔍 [持仓更新] 原始数据: %+v", message.Data)

	// 直接解析Data字段为持仓数据数组
	dataBytes, err := json.Marshal(message.Data)
	if err != nil {
		log.Printf("❌ 序列化持仓数据失败: %v", err)
		return
	}

	log.Printf("🔍 [持仓更新] 数据内容: %s", string(dataBytes))

	// 解析为持仓数据数组
	var positions []struct {
		Category               string `json:"category"`
		Symbol                 string `json:"symbol"`
		Side                   string `json:"side"`
		Size                   string `json:"size"`
		PositionIdx            int    `json:"positionIdx"`
		TradeMode              int    `json:"tradeMode"`
		PositionValue          string `json:"positionValue"`
		RiskId                 int    `json:"riskId"`
		RiskLimitValue         string `json:"riskLimitValue"`
		EntryPrice             string `json:"entryPrice"`
		MarkPrice              string `json:"markPrice"`
		Leverage               string `json:"leverage"`
		PositionBalance        string `json:"positionBalance"`
		AutoAddMargin          int    `json:"autoAddMargin"`
		PositionIM             string `json:"positionIM"`
		PositionMM             string `json:"positionMM"`
		LiqPrice               string `json:"liqPrice"`
		BustPrice              string `json:"bustPrice"`
		TakeProfit             string `json:"takeProfit"`
		StopLoss               string `json:"stopLoss"`
		TrailingStop           string `json:"trailingStop"`
		UnrealisedPnl          string `json:"unrealisedPnl"`
		SessionAvgPrice        string `json:"sessionAvgPrice"`
		Delta                  string `json:"delta"`
		Gamma                  string `json:"gamma"`
		Vega                   string `json:"vega"`
		Theta                  string `json:"theta"`
		CurRealisedPnl         string `json:"curRealisedPnl"`
		CumRealisedPnl         string `json:"cumRealisedPnl"`
		PositionStatus         string `json:"positionStatus"`
		AdlRankIndicator       int    `json:"adlRankIndicator"`
		CreatedTime            string `json:"createdTime"`
		UpdatedTime            string `json:"updatedTime"`
		Seq                    int64  `json:"seq"`
		IsReduceOnly           bool   `json:"isReduceOnly"`
		MmrSysUpdatedTime      string `json:"mmrSysUpdatedTime"`
		LeverageSysUpdatedTime string `json:"leverageSysUpdatedTime"`
		TpslMode               string `json:"tpslMode"`
	}

	if err := json.Unmarshal(dataBytes, &positions); err != nil {
		log.Printf("❌ 解析持仓数据失败: %v", err)
		log.Printf("🔍 [解析失败的数据] %s", string(dataBytes))
		return
	}

	log.Printf("🔍 [持仓更新] 解析成功，持仓数量: %d", len(positions))

	// 处理每个持仓数据
	for i, pos := range positions {
		log.Printf("🔍 [持仓%d] Symbol: %s, Category: %s, Size: %s, Side: %s",
			i+1, pos.Symbol, pos.Category, pos.Size, pos.Side)

		// 检查是否为期权合约
		if pos.Category != "option" {
			log.Printf("⚠️ 跳过非期权合约: %s (category: %s)", pos.Symbol, pos.Category)
			continue
		}

		// 进一步验证期权合约格式
		if !isOptionContract(pos.Symbol) {
			log.Printf("⚠️ 期权合约格式验证失败: %s", pos.Symbol)
			continue
		}

		// 转换数据类型
		optionPos := &OptionPositionData{
			Symbol:      pos.Symbol,
			Side:        pos.Side,
			CreatedTime: pos.CreatedTime,
			UpdatedTime: pos.UpdatedTime,
		}

		// 解析decimal字段（添加空值检查）
		if pos.Size != "" {
			if size, err := decimal.NewFromString(pos.Size); err == nil {
				optionPos.Size = size
			} else {
				log.Printf("⚠️ 解析Size失败: %s, error: %v", pos.Size, err)
			}
		} else {
			optionPos.Size = decimal.Zero
		}

		if pos.PositionValue != "" {
			if posValue, err := decimal.NewFromString(pos.PositionValue); err == nil {
				optionPos.PositionValue = posValue
			} else {
				log.Printf("⚠️ 解析PositionValue失败: %s, error: %v", pos.PositionValue, err)
			}
		} else {
			optionPos.PositionValue = decimal.Zero
		}

		if pos.EntryPrice != "" {
			if entryPrice, err := decimal.NewFromString(pos.EntryPrice); err == nil {
				optionPos.EntryPrice = entryPrice
			} else {
				log.Printf("⚠️ 解析EntryPrice失败: %s, error: %v", pos.EntryPrice, err)
			}
		} else {
			optionPos.EntryPrice = decimal.Zero
		}

		if pos.MarkPrice != "" {
			if markPrice, err := decimal.NewFromString(pos.MarkPrice); err == nil {
				optionPos.MarkPrice = markPrice
			} else {
				log.Printf("⚠️ 解析MarkPrice失败: %s, error: %v", pos.MarkPrice, err)
			}
		} else {
			optionPos.MarkPrice = decimal.Zero
		}

		if pos.LiqPrice != "" {
			if liqPrice, err := decimal.NewFromString(pos.LiqPrice); err == nil {
				optionPos.LiqPrice = liqPrice
			} else {
				log.Printf("⚠️ 解析LiqPrice失败: %s, error: %v", pos.LiqPrice, err)
			}
		} else {
			optionPos.LiqPrice = decimal.Zero // 空值设为0
		}

		if pos.UnrealisedPnl != "" {
			if unrealisedPnl, err := decimal.NewFromString(pos.UnrealisedPnl); err == nil {
				optionPos.UnrealisedPnl = unrealisedPnl
			} else {
				log.Printf("⚠️ 解析UnrealisedPnl失败: %s, error: %v", pos.UnrealisedPnl, err)
			}
		} else {
			optionPos.UnrealisedPnl = decimal.Zero
		}

		if pos.CumRealisedPnl != "" {
			if cumRealisedPnl, err := decimal.NewFromString(pos.CumRealisedPnl); err == nil {
				optionPos.CumRealisedPnl = cumRealisedPnl
			} else {
				log.Printf("⚠️ 解析CumRealisedPnl失败: %s, error: %v", pos.CumRealisedPnl, err)
			}
		} else {
			optionPos.CumRealisedPnl = decimal.Zero // 空值设为0
		}

		// 更新缓存
		client.positionCache.UpdatePosition(optionPos)

		log.Printf("📊 期权持仓更新成功: %s, 仓位: %s, 价值: %s, 未结盈亏: %s",
			optionPos.Symbol,
			optionPos.Size.String(),
			optionPos.PositionValue.String(),
			optionPos.UnrealisedPnl.String())
	}
}

// isOptionContract 判断是否为期权合约
func isOptionContract(symbol string) bool {
	// 期权合约格式：ETH-4JUN25-2700-C-USDT
	// 检查是否以-USDT结尾
	if !strings.HasSuffix(symbol, "-USDT") {
		return false
	}

	// 检查是否包含期权类型标识 -C- 或 -P-
	if strings.Contains(symbol, "-C-") || strings.Contains(symbol, "-P-") {
		return true
	}

	return false
}

// heartbeat 心跳机制
func (client *BybitPrivateWSClient) heartbeat() {
	ticker := time.NewTicker(20 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-client.stopChan:
			return
		case <-ticker.C:
			if client.isConnected && client.conn != nil {
				message := map[string]interface{}{
					"op": "ping",
				}
				if err := client.conn.WriteJSON(message); err != nil {
					log.Printf("⚠️ 私有WebSocket心跳发送失败: %v", err)
					client.isConnected = false
				}
			}
		}
	}
}

// reconnect 重连机制
func (client *BybitPrivateWSClient) reconnect() {
	client.isConnected = false

	for {
		log.Printf("🔄 尝试重连私有WebSocket...")

		if err := client.Connect(); err != nil {
			log.Printf("⚠️ 私有WebSocket重连失败: %v, %v后重试", err, client.reconnectInterval)
			time.Sleep(client.reconnectInterval)
			continue
		}

		log.Println("✅ 私有WebSocket重连成功")
		break
	}
}

// startRestVerification 启动REST API验证
func (client *BybitPrivateWSClient) startRestVerification() {
	ticker := time.NewTicker(client.restCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-client.stopChan:
			return
		case <-ticker.C:
			client.verifyPositionsWithREST()
		}
	}
}

// verifyPositionsWithREST 使用REST API验证持仓数据
func (client *BybitPrivateWSClient) verifyPositionsWithREST() {
	// 获取REST API持仓数据
	restPositions, err := client.getPositionsFromREST()
	if err != nil {
		log.Printf("⚠️ REST API获取持仓失败: %v", err)
		return
	}

	// 比较WebSocket缓存和REST API数据
	wsPositions := client.positionCache.GetAllPositions()

	// 检查数据一致性
	inconsistentCount := 0
	for symbol, restPos := range restPositions {
		if wsPos, exists := wsPositions[symbol]; exists {
			if !client.comparePositions(wsPos, restPos) {
				inconsistentCount++
				log.Printf("⚠️ 持仓数据不一致: %s", symbol)
			}
		} else {
			// WebSocket中没有但REST API中有的持仓
			client.positionCache.UpdatePosition(restPos)
			log.Printf("📊 从REST API同步新持仓: %s", symbol)
		}
	}

	// 移除WebSocket中有但REST API中没有的持仓
	for symbol := range wsPositions {
		if _, exists := restPositions[symbol]; !exists {
			client.positionCache.RemovePosition(symbol)
			log.Printf("🗑️ 移除已平仓合约: %s", symbol)
		}
	}

	if inconsistentCount == 0 {
		//log.Printf("✅ 持仓数据验证通过 (%d个合约)", len(restPositions))
	} else {
		log.Printf("⚠️ 发现 %d 个持仓数据不一致", inconsistentCount)
	}

	client.lastRestCheck = time.Now()
}

// getPositionsFromREST 从REST API获取持仓数据
func (client *BybitPrivateWSClient) getPositionsFromREST() (map[string]*OptionPositionData, error) {
	// 使用REST客户端获取持仓数据
	return client.restClient.GetPositions(client.apiKey, client.secretKey)
}

// comparePositions 比较两个持仓数据是否一致（只比较关键字段：持仓量和合约名）
func (client *BybitPrivateWSClient) comparePositions(ws, rest *OptionPositionData) bool {
	// 只比较持仓量和合约名，价格字段允许差异
	if !ws.Size.Equal(rest.Size) {
		log.Printf("🔍 [持仓验证] 持仓量不一致: WS=%s, REST=%s", ws.Size.String(), rest.Size.String())
		return false
	}
	if ws.Symbol != rest.Symbol {
		log.Printf("🔍 [持仓验证] 合约名不一致: WS=%s, REST=%s", ws.Symbol, rest.Symbol)
		return false
	}
	return true
}

// GetPositionCache 获取持仓缓存
func (client *BybitPrivateWSClient) GetPositionCache() *OptionPositionCache {
	return client.positionCache
}

// GetAllPositions 获取所有持仓数据
func (client *BybitPrivateWSClient) GetAllPositions() map[string]*OptionPositionData {
	return client.positionCache.GetAllPositions()
}

// GetPosition 获取指定合约的持仓数据
func (client *BybitPrivateWSClient) GetPosition(symbol string) (*OptionPositionData, bool) {
	return client.positionCache.GetPosition(symbol)
}

// initializePositionsFromREST 使用REST API初始化持仓数据
func (client *BybitPrivateWSClient) initializePositionsFromREST() {
	log.Println("🔄 正在从REST API初始化持仓数据...")

	// 获取REST API持仓数据
	restPositions, err := client.getPositionsFromREST()
	if err != nil {
		log.Printf("⚠️ 初始化持仓数据失败: %v", err)
		return
	}

	// 逐个更新持仓缓存
	for symbol, position := range restPositions {
		client.positionCache.UpdatePosition(position)
		log.Printf("📊 初始化持仓: %s, 仓位: %s", symbol, position.Size.String())
	}

	log.Printf("✅ 从REST API初始化持仓数据完成 (%d个合约)", len(restPositions))
}
