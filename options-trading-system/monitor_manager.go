package main

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// NewMonitorManager 创建新的监控任务管理器
func NewMonitorManager(wsClient *BybitWSClient, contractManager *OptionContractManager) *MonitorManager {
	mm := &MonitorManager{
		tasks:           make(map[string]*MonitorTask),
		wsClient:        wsClient,
		contractManager: contractManager,
	}

	// 初始化数据管理器
	mm.dataManager = NewDataManager()

	// 启动交易检测事件处理器
	go mm.startTradingCheckProcessor()

	return mm
}

// AddMonitor 添加监控任务
func (mm *MonitorManager) AddMonitor(optionType, expiryTimeRange string, timeValueTarget decimal.Decimal) (*MonitorTask, error) {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	// 验证参数
	if err := mm.validateMonitorRequest(optionType, expiryTimeRange, timeValueTarget); err != nil {
		return nil, err
	}

	// 生成任务ID (M + 7位数字)
	taskID := fmt.Sprintf("M%07d", time.Now().UnixNano()%10000000)

	// 创建任务（默认停止状态，需要手动启动）
	task := &MonitorTask{
		ID:              taskID,
		OptionType:      optionType,
		ExpiryTimeRange: expiryTimeRange,
		TimeValueTarget: timeValueTarget,
		Status:          StatusStopped, // 默认停止状态
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		StopChan:        make(chan struct{}),
	}

	// 存储任务
	mm.tasks[taskID] = task

	// 触发数据保存
	mm.dataManager.TriggerSave()

	log.Printf("✅ 监控任务创建成功: %s (%s, %s, 目标时间价值: %s)",
		taskID, optionType, expiryTimeRange, timeValueTarget.String())
	log.Printf("💡 使用 'start_m %s' 命令启动监控", taskID)

	return task, nil
}

// StartMonitor 启动监控任务
func (mm *MonitorManager) StartMonitor(taskID string) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	task, exists := mm.tasks[taskID]
	if !exists {
		return fmt.Errorf("监控任务不存在: %s", taskID)
	}

	if task.Status == StatusRunning {
		return fmt.Errorf("监控任务已在运行: %s", taskID)
	}

	// 0. 检查WebSocket连接状态
	if mm.wsClient == nil || !mm.wsClient.isConnected {
		return fmt.Errorf("WebSocket未连接，无法启动监控任务。请检查网络连接")
	}

	// 1. 获取需要的期权合约列表
	optionContracts, err := mm.contractManager.GetEligibleContracts(task)
	if err != nil {
		return fmt.Errorf("获取期权合约失败: %v", err)
	}

	log.Printf("🔍 任务 %s 需要监控 %d 个期权合约", taskID, len(optionContracts))

	// 2. 订阅期权盘口数据
	mm.contractManager.SubscribeContracts(optionContracts)

	// 3. 等待盘口数据到达并验证
	log.Printf("⏳ 等待期权盘口数据...")
	if err := mm.waitForMarketData(optionContracts, 30*time.Second); err != nil {
		return fmt.Errorf("等待盘口数据失败: %v", err)
	}

	log.Printf("✅ 盘口数据准备就绪，启动监控任务: %s", taskID)

	// 4. 启动监控任务
	task.StopChan = make(chan struct{})
	task.Status = StatusRunning
	task.UpdatedAt = time.Now()

	// 启动监控协程
	go mm.runMonitorTask(task)

	// 触发数据保存
	mm.dataManager.TriggerSave()

	log.Printf("🎯 监控任务开始运行: %s", taskID)
	return nil
}

// waitForMarketData 等待盘口数据到达并验证
func (mm *MonitorManager) waitForMarketData(contracts []string, timeout time.Duration) error {
	startTime := time.Now()
	checkInterval := 1 * time.Second

	log.Printf("📊 开始验证 %d 个期权合约的状态...", len(contracts))

	// 第一步：过滤掉不存在的合约，只保留有效合约
	var validContracts []string
	var invalidContracts []string

	// 等待一小段时间让合约状态检查完成
	time.Sleep(2 * time.Second)

	for _, contract := range contracts {
		if status, exists := mm.wsClient.contractMonitor.GetContractStatus(contract); exists {
			if status.State == StateNotExist {
				invalidContracts = append(invalidContracts, contract)
				log.Printf("🚫 跳过不存在的合约: %s", contract)
			} else {
				validContracts = append(validContracts, contract)
			}
		} else {
			// 如果状态未知，暂时保留在有效列表中，后续再检查
			validContracts = append(validContracts, contract)
		}
	}

	if len(invalidContracts) > 0 {
		log.Printf("⚠️  已过滤 %d 个不存在的合约: %v", len(invalidContracts), invalidContracts)
	}

	if len(validContracts) == 0 {
		return fmt.Errorf("没有有效的期权合约可以监控")
	}

	log.Printf("📊 等待 %d 个有效期权合约的盘口数据...", len(validContracts))

	// 第二步：等待有效合约的盘口数据
	for {
		// 检查是否超时
		if time.Since(startTime) > timeout {
			return fmt.Errorf("等待盘口数据超时 (%v)", timeout)
		}

		// 检查有效合约的数据状态
		validCount := 0
		var missingContracts []string

		for _, contract := range validContracts {
			if mm.isContractDataValid(contract) {
				validCount++
			} else {
				// 再次检查合约状态，如果变成不存在则从列表中移除
				if status, exists := mm.wsClient.contractMonitor.GetContractStatus(contract); exists {
					if status.State == StateNotExist {
						log.Printf("🚫 合约 %s 状态变为不存在，从等待列表中移除", contract)
						continue // 跳过这个合约，不计入缺失列表
					}
				}
				missingContracts = append(missingContracts, contract)
			}
		}

		// 重新计算有效合约数量（排除运行时发现的不存在合约）
		actualValidContracts := len(validContracts)
		for _, contract := range validContracts {
			if status, exists := mm.wsClient.contractMonitor.GetContractStatus(contract); exists {
				if status.State == StateNotExist {
					actualValidContracts--
				}
			}
		}

		// 如果所有有效合约都有数据，返回成功
		if validCount == actualValidContracts && actualValidContracts > 0 {
			log.Printf("✅ 所有有效期权盘口数据已就绪 (%d/%d)", validCount, actualValidContracts)
			return nil
		}

		// 如果有效合约数量太少，返回错误
		if actualValidContracts == 0 {
			return fmt.Errorf("所有期权合约都不存在或无效")
		}

		// 显示进度
		if len(missingContracts) <= 5 {
			log.Printf("⏳ 等待盘口数据 (%d/%d): %v", validCount, actualValidContracts, missingContracts)
		} else {
			log.Printf("⏳ 等待盘口数据 (%d/%d): %d个合约数据缺失", validCount, actualValidContracts, len(missingContracts))
		}

		// 等待一段时间后重新检查
		time.Sleep(checkInterval)
	}
}

// isContractDataValid 检查合约数据是否有效
func (mm *MonitorManager) isContractDataValid(contract string) bool {
	// 检查合约状态和盘口有效性
	if status, exists := mm.wsClient.contractMonitor.GetContractStatus(contract); exists {
		// 只有Trading状态且盘口有效的合约才认为数据有效
		return status.State == StateTrading && status.OrderBookValid
	}
	return false
}

// StopMonitor 停止监控任务
func (mm *MonitorManager) StopMonitor(taskID string) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	task, exists := mm.tasks[taskID]
	if !exists {
		return fmt.Errorf("监控任务不存在: %s", taskID)
	}

	if task.Status == StatusStopped {
		return fmt.Errorf("监控任务已停止: %s", taskID)
	}

	// 1. 获取该任务的期权合约列表
	optionContracts, err := mm.contractManager.GetEligibleContracts(task)
	if err == nil {
		// 2. 减少订阅计数（如果没有其他任务需要，会自动取消订阅）
		mm.contractManager.UnsubscribeContracts(optionContracts)
		log.Printf("📊 任务 %s 取消订阅 %d 个期权合约", taskID, len(optionContracts))
	}

	// 3. 发送停止信号（安全关闭）
	select {
	case <-task.StopChan:
		// 通道已经关闭
	default:
		close(task.StopChan)
	}
	task.Status = StatusStopped
	task.UpdatedAt = time.Now()

	// 触发数据保存
	mm.dataManager.TriggerSave()

	log.Printf("✅ 监控任务已停止: %s", taskID)
	return nil
}

// DeleteMonitor 删除监控任务
func (mm *MonitorManager) DeleteMonitor(taskID string) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	task, exists := mm.tasks[taskID]
	if !exists {
		return fmt.Errorf("监控任务不存在: %s", taskID)
	}

	// 如果任务正在运行，先停止它（安全关闭）
	if task.Status == StatusRunning {
		select {
		case <-task.StopChan:
			// 通道已经关闭
		default:
			close(task.StopChan)
		}
	}

	// 从任务列表中删除
	delete(mm.tasks, taskID)

	// 触发数据保存
	mm.dataManager.TriggerSave()

	log.Printf("🗑️  监控任务已删除: %s", taskID)
	return nil
}

// GetMonitor 获取监控任务
func (mm *MonitorManager) GetMonitor(taskID string) (*MonitorTask, error) {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	task, exists := mm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("监控任务不存在: %s", taskID)
	}

	return task, nil
}

// GetAllMonitors 获取所有监控任务
func (mm *MonitorManager) GetAllMonitors() map[string]*MonitorTask {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	// 创建副本避免并发问题
	result := make(map[string]*MonitorTask)
	for id, task := range mm.tasks {
		result[id] = task
	}

	return result
}

// ModifyMonitor 修改监控任务参数
func (mm *MonitorManager) ModifyMonitor(taskID, paramName string, value interface{}) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	task, exists := mm.tasks[taskID]
	if !exists {
		return fmt.Errorf("监控任务不存在: %s", taskID)
	}

	// 修改参数
	switch paramName {
	case "time_value_target":
		if v, ok := value.(decimal.Decimal); ok {
			task.TimeValueTarget = v
		} else if v, ok := value.(float64); ok {
			task.TimeValueTarget = decimal.NewFromFloat(v)
		} else {
			return fmt.Errorf("无效的时间价值目标值")
		}
	case "expiry_time_range":
		if v, ok := value.(string); ok {
			if err := mm.validateExpiryTimeRange(v); err != nil {
				return err
			}
			task.ExpiryTimeRange = v
		} else {
			return fmt.Errorf("无效的时间范围值")
		}
	case "option_type":
		if v, ok := value.(string); ok {
			if v != OptionTypeCall && v != OptionTypePut {
				return fmt.Errorf("无效的期权类型: %s", v)
			}
			task.OptionType = v
		} else {
			return fmt.Errorf("无效的期权类型值")
		}
	default:
		return fmt.Errorf("未知参数: %s", paramName)
	}

	task.UpdatedAt = time.Now()

	// 触发数据保存
	mm.dataManager.TriggerSave()

	log.Printf("✅ 监控任务参数已修改: %s, %s = %v", taskID, paramName, value)
	return nil
}

// validateMonitorRequest 验证监控任务请求参数
func (mm *MonitorManager) validateMonitorRequest(optionType, expiryTimeRange string, timeValueTarget decimal.Decimal) error {
	// 验证期权类型
	if optionType != OptionTypeCall && optionType != OptionTypePut {
		return fmt.Errorf("无效的期权类型: %s", optionType)
	}

	// 验证时间范围
	if err := mm.validateExpiryTimeRange(expiryTimeRange); err != nil {
		return err
	}

	// 验证时间价值目标
	if timeValueTarget.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("时间价值目标必须大于0: %s", timeValueTarget.String())
	}

	return nil
}

// validateExpiryTimeRange 验证时间范围格式
func (mm *MonitorManager) validateExpiryTimeRange(expiryTimeRange string) error {
	// 支持格式: "5h" 或 "5h-10h"
	if strings.Contains(expiryTimeRange, "-") {
		// 范围格式
		parts := strings.Split(expiryTimeRange, "-")
		if len(parts) != 2 {
			return fmt.Errorf("无效的时间范围格式: %s", expiryTimeRange)
		}

		for _, part := range parts {
			if err := mm.validateSingleTimeValue(strings.TrimSpace(part)); err != nil {
				return err
			}
		}
	} else {
		// 单一时间格式
		if err := mm.validateSingleTimeValue(expiryTimeRange); err != nil {
			return err
		}
	}

	return nil
}

// validateSingleTimeValue 验证单个时间值
func (mm *MonitorManager) validateSingleTimeValue(timeValue string) error {
	if !strings.HasSuffix(timeValue, "h") {
		return fmt.Errorf("时间值必须以'h'结尾: %s", timeValue)
	}

	hourStr := strings.TrimSuffix(timeValue, "h")
	hours, err := strconv.Atoi(hourStr)
	if err != nil {
		return fmt.Errorf("无效的小时数: %s", hourStr)
	}

	if hours <= 0 || hours > 8760 { // 最大一年
		return fmt.Errorf("小时数必须在1-8760之间: %d", hours)
	}

	return nil
}

// runMonitorTask 运行监控任务
func (mm *MonitorManager) runMonitorTask(task *MonitorTask) {
	log.Printf("🎯 监控任务开始运行: %s", task.ID)

	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-task.StopChan:
			log.Printf("🛑 监控任务已停止: %s", task.ID)
			return
		case <-ticker.C:
			// 执行监控逻辑
			mm.executeMonitorLogic(task)
		}
	}
}

// executeMonitorLogic 执行监控逻辑
func (mm *MonitorManager) executeMonitorLogic(task *MonitorTask) {
	// 1. 获取符合条件的期权合约
	optionContracts, err := mm.contractManager.GetEligibleContracts(task)
	if err != nil {
		log.Printf("⚠️  获取期权合约失败: %s, %v", task.ID, err)
		return
	}

	// 2. 验证所有合约都有有效的盘口数据
	validContracts := 0
	for _, contract := range optionContracts {
		if mm.isContractDataValid(contract) {
			validContracts++
		}
	}

	// 如果没有有效数据，跳过本次检测
	if validContracts == 0 {
		log.Printf("⚠️  任务 %s: 没有有效的盘口数据，跳过检测", task.ID)
		return
	}

	// 如果部分数据缺失，记录警告但继续检测
	if validContracts < len(optionContracts) {
		log.Printf("⚠️  任务 %s: 部分盘口数据缺失 (%d/%d)", task.ID, validContracts, len(optionContracts))
	}

	// 3. 计算时间价值并检查交易条件（只检查有有效数据的合约）
	satisfiedCount := 0
	for _, contract := range optionContracts {
		// 只检查有有效数据的合约
		if !mm.isContractDataValid(contract) {
			continue
		}

		if mm.contractManager.CheckTradingCondition(task.ID, contract, task) {
			satisfiedCount++
			// 执行交易逻辑：卖出期权
			mm.executeOptionTrade(task, contract)
		}
	}

	// 只有满足条件时才输出日志
	if satisfiedCount > 0 {
		log.Printf("💰 任务 %s: %d 个合约满足交易条件，准备执行交易", task.ID, satisfiedCount)
	}
}

// LoadData 加载持久化数据
func (mm *MonitorManager) LoadData() error {
	// 加载监控任务数据
	monitors, err := mm.dataManager.LoadMonitors()
	if err != nil {
		return fmt.Errorf("加载监控任务数据失败: %v", err)
	}

	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	// 恢复监控任务数据
	mm.tasks = monitors

	// 恢复任务的运行时状态
	runningCount := 0
	for _, task := range monitors {
		// 重新初始化不能序列化的字段
		task.StopChan = make(chan struct{})

		// 如果任务之前是运行状态，自动重启
		if task.Status == StatusRunning {
			go mm.runMonitorTask(task)
			runningCount++
		}
	}

	log.Printf("✅ 监控任务数据恢复完成: %d个任务 (%d个自动启动)", len(monitors), runningCount)
	return nil
}

// StartDataManager 启动数据管理器
func (mm *MonitorManager) StartDataManager(positionManager *PositionManager) {
	mm.dataManager.StartAutoSave(mm, positionManager)
}

// SaveData 手动保存数据
func (mm *MonitorManager) SaveData(positionManager *PositionManager) error {
	// 获取所有监控任务
	monitors := mm.GetAllMonitors()

	// 获取所有持仓
	positions := positionManager.GetAllPositions()

	// 提取事件数据
	events := make(map[string][]PositionEvent)
	for contractName, position := range positions {
		position.mutex.RLock()
		events[contractName] = make([]PositionEvent, len(position.Events))
		copy(events[contractName], position.Events)
		position.mutex.RUnlock()
	}

	// 保存监控任务数据
	if err := mm.dataManager.SaveMonitors(monitors); err != nil {
		return fmt.Errorf("保存监控任务数据失败: %v", err)
	}

	// 保存持仓数据
	if err := mm.dataManager.SavePositions(positions); err != nil {
		return fmt.Errorf("保存持仓数据失败: %v", err)
	}

	// 保存事件数据
	if err := mm.dataManager.SaveEvents(events); err != nil {
		return fmt.Errorf("保存事件数据失败: %v", err)
	}

	log.Println("💾 数据手动保存完成")
	return nil
}

// executeOptionTrade 执行期权交易
func (mm *MonitorManager) executeOptionTrade(task *MonitorTask, contract string) {
	log.Printf("💰 任务 %s: 准备卖出期权 %s", task.ID, contract)

	// 获取全局默认参数
	defaults := GetGlobalDefaults()
	orderSize := defaults.OrderSize

	// 获取期权最优买价作为卖出价格
	bid1Price, err := mm.wsClient.GetBestBidPrice(contract)
	if err != nil {
		log.Printf("❌ 任务 %s: 获取期权 %s 买一价失败: %v", task.ID, contract, err)
		return
	}

	if bid1Price.IsZero() {
		log.Printf("❌ 任务 %s: 期权 %s 买一价为零，无法卖出", task.ID, contract)
		return
	}

	// 记录交易意图
	log.Printf("📋 任务 %s: 期权交易详情", task.ID)
	log.Printf("   合约: %s", contract)
	log.Printf("   方向: 卖出 (SELL)")
	log.Printf("   数量: %s", orderSize.String())
	log.Printf("   价格: %s USDT (买一价)", bid1Price.String())

	// TODO: 这里应该调用Bybit期权交易API
	// 由于期权交易API比较复杂，暂时只记录交易意图
	log.Printf("⚠️  注意: 期权交易API尚未实现，仅记录交易信号")
	log.Printf("🎯 交易信号: 卖出 %s 数量 %s @ %s USDT", contract, orderSize.String(), bid1Price.String())

	// 创建模拟交易记录（实际实现时应该替换为真实API调用）
	mm.recordTradeSignal(task, contract, "SELL", orderSize, bid1Price)
}

// recordTradeSignal 记录交易信号
func (mm *MonitorManager) recordTradeSignal(task *MonitorTask, contract, side string, quantity, price decimal.Decimal) {
	log.Printf("📝 记录交易信号: 任务=%s, 合约=%s, 方向=%s, 数量=%s, 价格=%s",
		task.ID, contract, side, quantity.String(), price.String())

	// TODO: 这里可以添加交易信号的持久化存储
	// 例如保存到数据库或文件中，用于后续分析
}

// startTradingCheckProcessor 启动交易检测事件处理器
func (mm *MonitorManager) startTradingCheckProcessor() {
	if mm.wsClient == nil {
		return
	}

	tradingCheckChan := mm.wsClient.GetTradingCheckChan()

	for {
		select {
		case event := <-tradingCheckChan:
			// 立即处理交易检测事件
			mm.processTradingCheckEvent(event)
		}
	}
}

// processTradingCheckEvent 处理交易检测事件
func (mm *MonitorManager) processTradingCheckEvent(event TradingCheckEvent) {
	// 快速检测（基于缓存，无API调用）
	mm.fastTradingCheck(event.Symbol, event.Bid1Price, event.IsFirstTime)
}

// fastTradingCheck 快速交易检测（基于缓存）
func (mm *MonitorManager) fastTradingCheck(symbol string, bid1Price decimal.Decimal, isFirstTime bool) {
	// 获取缓存的合约状态（无API调用）
	status, exists := mm.wsClient.contractMonitor.GetContractStatus(symbol)
	if !exists {
		return // 合约不在监控列表
	}

	// 快速状态验证
	if status.State != StateTrading {
		return // 非Trading状态，跳过检测
	}

	if !status.OrderBookValid {
		return // 盘口数据无效，跳过检测
	}

	if !status.TradingCheckEnabled {
		return // 交易检测未启用，跳过检测
	}

	// 记录交易检测统计信息
	mm.recordTradingCheck(symbol)

	// 执行交易条件检测
	mm.checkTradingConditionsWithCache(symbol, bid1Price, status, isFirstTime)
}

// checkTradingConditionsWithCache 基于缓存检查交易条件
func (mm *MonitorManager) checkTradingConditionsWithCache(symbol string, bid1Price decimal.Decimal, status *ContractStatus, isFirstTime bool) {
	// 快速计算时间价值（使用统一价格客户端）
	timeValue, err := mm.calculateTimeValueWithUnifiedPrice(symbol, bid1Price)
	if err != nil {
		log.Printf("🔍 [快速检测调试] 合约 %s: 时间价值计算失败: %v", symbol, err)
		return // 计算失败
	}

	// 检查是否有监控任务需要这个合约
	mm.checkAllTasksForContract(symbol, timeValue, bid1Price, isFirstTime)
}

// calculateTimeValueFast 快速计算时间价值
func (mm *MonitorManager) calculateTimeValueFast(symbol string, spotPrice, bid1Price decimal.Decimal) (decimal.Decimal, error) {
	// 解析行权价
	strikePrice := mm.parseStrikePriceFromContract(symbol)
	if strikePrice == 0 {
		return decimal.Zero, fmt.Errorf("无法解析行权价: %s", symbol)
	}

	// 解析期权类型
	optionType := mm.parseOptionTypeFromContract(symbol)
	if optionType == "" {
		return decimal.Zero, fmt.Errorf("无法解析期权类型: %s", symbol)
	}

	// 计算时间价值百分比（按照公式）
	var timeValue decimal.Decimal
	strikePriceDecimal := decimal.NewFromInt(int64(strikePrice))

	if optionType == OptionTypePut {
		// PUT期权: time_value = ((spot_price >= strike_price) ? bid1_price : bid1_price - (strike_price - spot_price)) / spot_price
		if spotPrice.GreaterThanOrEqual(strikePriceDecimal) {
			timeValue = bid1Price
		} else {
			timeValue = bid1Price.Sub(strikePriceDecimal.Sub(spotPrice))
		}
	} else {
		// CALL期权: time_value = ((spot_price <= strike_price) ? bid1_price : bid1_price - (spot_price - strike_price)) / spot_price
		if spotPrice.LessThanOrEqual(strikePriceDecimal) {
			timeValue = bid1Price
		} else {
			timeValue = bid1Price.Sub(spotPrice.Sub(strikePriceDecimal))
		}
	}

	// 转换为百分比
	if spotPrice.IsZero() {
		return decimal.Zero, fmt.Errorf("现货价格为零")
	}

	timeValuePercent := timeValue.Div(spotPrice)
	return timeValuePercent, nil
}

// checkAllTasksForContract 检查所有任务是否需要这个合约
func (mm *MonitorManager) checkAllTasksForContract(symbol string, timeValue, bid1Price decimal.Decimal, isFirstTime bool) {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	for _, task := range mm.tasks {
		if task.Status != StatusRunning {
			continue // 只检查运行中的任务
		}

		// 检查这个合约是否属于当前任务
		if mm.contractBelongsToTask(symbol, task) {
			// 检查是否满足交易条件
			if mm.meetsTradeConditions(symbol, timeValue, task) {
				// 满足条件，执行交易流程
				mm.executeTradingFlow(task, symbol, bid1Price, timeValue, isFirstTime)
			}
		}
	}
}

// parseStrikePriceFromContract 从合约名称解析行权价
func (mm *MonitorManager) parseStrikePriceFromContract(symbol string) int {
	// 解析格式：ETH-4JUN25-2500-C-USDT
	parts := strings.Split(symbol, "-")
	if len(parts) < 4 {
		return 0
	}

	strikeStr := parts[2] // 2500
	strike, err := strconv.Atoi(strikeStr)
	if err != nil {
		return 0
	}

	return strike
}

// parseOptionTypeFromContract 从合约名称解析期权类型
func (mm *MonitorManager) parseOptionTypeFromContract(symbol string) string {
	// 解析格式：ETH-4JUN25-2500-C-USDT
	parts := strings.Split(symbol, "-")
	if len(parts) < 4 {
		return ""
	}

	typeStr := parts[3] // C 或 P
	if typeStr == "C" {
		return OptionTypeCall
	} else if typeStr == "P" {
		return OptionTypePut
	}

	return ""
}

// contractBelongsToTask 检查合约是否属于指定任务
func (mm *MonitorManager) contractBelongsToTask(symbol string, task *MonitorTask) bool {
	// 获取任务的期权合约列表
	optionContracts, err := mm.contractManager.GetEligibleContracts(task)
	if err != nil {
		return false
	}

	// 检查合约是否在列表中
	for _, contract := range optionContracts {
		if contract == symbol {
			return true
		}
	}

	return false
}

// meetsTradeConditions 检查是否满足交易条件
func (mm *MonitorManager) meetsTradeConditions(symbol string, timeValue decimal.Decimal, task *MonitorTask) bool {
	// 检查时间价值是否达到阈值
	return timeValue.GreaterThanOrEqual(task.TimeValueTarget)
}

// executeTradingFlow 执行交易流程
func (mm *MonitorManager) executeTradingFlow(task *MonitorTask, symbol string, bid1Price, timeValue decimal.Decimal, isFirstTime bool) {
	// 记录交易检测事件
	log.Printf("🎯 [快速检测] 任务 %s: 合约 %s 满足交易条件", task.ID, symbol)
	log.Printf("   买一价: %s, 时间价值: %.2f%%, 阈值: %.2f%%",
		bid1Price.String(),
		timeValue.Mul(decimal.NewFromInt(100)).InexactFloat64(),
		task.TimeValueTarget.Mul(decimal.NewFromInt(100)).InexactFloat64())

	if isFirstTime {
		log.Printf("   [首次数据] 合约 %s 首次获得买一价数据", symbol)
	}

	// 调用现有的交易执行逻辑
	mm.executeOptionTrade(task, symbol)
}

// recordTradingCheck 记录交易检测统计信息
func (mm *MonitorManager) recordTradingCheck(symbol string) {
	now := time.Now()

	// 更新合约状态中的交易检测统计信息
	if status, exists := mm.wsClient.contractMonitor.GetContractStatus(symbol); exists {
		// 更新最后检测时间
		status.LastTradingCheckTime = now

		// 添加到历史记录
		status.TradingCheckHistory = append(status.TradingCheckHistory, now)

		// 清理5分钟前的历史记录
		mm.cleanupTradingCheckHistory(status)
	}
}

// cleanupTradingCheckHistory 清理5分钟前的交易检测历史记录
func (mm *MonitorManager) cleanupTradingCheckHistory(status *ContractStatus) {
	fiveMinutesAgo := time.Now().Add(-5 * time.Minute)

	// 过滤出5分钟内的记录
	var recentHistory []time.Time
	for _, checkTime := range status.TradingCheckHistory {
		if checkTime.After(fiveMinutesAgo) {
			recentHistory = append(recentHistory, checkTime)
		}
	}

	// 更新历史记录
	status.TradingCheckHistory = recentHistory
}

// getTradingCheckCount 获取最近5分钟内的交易检测次数
func (mm *MonitorManager) getTradingCheckCount(symbol string) int {
	if status, exists := mm.wsClient.contractMonitor.GetContractStatus(symbol); exists {
		// 先清理过期记录
		mm.cleanupTradingCheckHistory(status)
		return len(status.TradingCheckHistory)
	}
	return 0
}

// formatLastTradingCheck 格式化最后交易检测信息
func (mm *MonitorManager) formatLastTradingCheck(symbol string) string {
	status, exists := mm.wsClient.contractMonitor.GetContractStatus(symbol)
	if !exists {
		return "-(0/5m)"
	}

	// 获取最后检测时间
	lastCheckTime := status.LastTradingCheckTime
	var timeStr string
	if lastCheckTime.IsZero() {
		timeStr = "never"
	} else {
		duration := time.Since(lastCheckTime)
		if duration < time.Minute {
			timeStr = fmt.Sprintf("%ds ago", int(duration.Seconds()))
		} else if duration < time.Hour {
			timeStr = fmt.Sprintf("%dm ago", int(duration.Minutes()))
		} else {
			timeStr = fmt.Sprintf("%dh ago", int(duration.Hours()))
		}
	}

	// 获取5分钟内检测次数
	count := mm.getTradingCheckCount(symbol)

	return fmt.Sprintf("%s(%d/5m)", timeStr, count)
}

// calculateTimeValueWithUnifiedPrice 使用统一价格客户端计算时间价值
func (mm *MonitorManager) calculateTimeValueWithUnifiedPrice(symbol string, bid1Price decimal.Decimal) (decimal.Decimal, error) {
	// 解析行权价
	strikePrice := mm.parseStrikePriceFromContract(symbol)
	if strikePrice == 0 {
		return decimal.Zero, fmt.Errorf("无法解析行权价: %s", symbol)
	}

	// 解析期权类型
	optionTypeStr := mm.parseOptionTypeFromContract(symbol)
	if optionTypeStr == "" {
		return decimal.Zero, fmt.Errorf("无法解析期权类型: %s", symbol)
	}

	// 根据期权类型获取对应的现货价格
	var spotPrice decimal.Decimal
	var exists bool

	if optionTypeStr == OptionTypeCall {
		// CALL期权使用卖一价
		spotPrice, exists = GetSpotPriceForTimeValue(OptionTypeCall)
	} else {
		// PUT期权使用买一价
		spotPrice, exists = GetSpotPriceForTimeValue(OptionTypePut)
	}

	if !exists {
		return decimal.Zero, fmt.Errorf("无法获取现货价格")
	}

	// 计算时间价值百分比（按照公式）
	var timeValue decimal.Decimal
	strikePriceDecimal := decimal.NewFromInt(int64(strikePrice))

	if optionTypeStr == OptionTypePut {
		// PUT期权: time_value = ((spot_price >= strike_price) ? bid1_price : bid1_price - (strike_price - spot_price)) / spot_price
		if spotPrice.GreaterThanOrEqual(strikePriceDecimal) {
			timeValue = bid1Price
		} else {
			timeValue = bid1Price.Sub(strikePriceDecimal.Sub(spotPrice))
		}
	} else {
		// CALL期权: time_value = ((spot_price <= strike_price) ? bid1_price : bid1_price - (spot_price - strike_price)) / spot_price
		if spotPrice.LessThanOrEqual(strikePriceDecimal) {
			timeValue = bid1Price
		} else {
			timeValue = bid1Price.Sub(spotPrice.Sub(strikePriceDecimal))
		}
	}

	// 转换为百分比
	if spotPrice.IsZero() {
		return decimal.Zero, fmt.Errorf("现货价格为零")
	}

	timeValuePercent := timeValue.Div(spotPrice)
	return timeValuePercent, nil
}
