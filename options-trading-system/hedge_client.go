package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// HedgeClient 对冲系统API客户端
type HedgeClient struct {
	baseURL    string
	httpClient *http.Client
}

// NewHedgeClient 创建新的对冲客户端
func NewHedgeClient(baseURL string) *HedgeClient {
	return &HedgeClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CreateHedgeTask 创建对冲任务
func (hc *HedgeClient) CreateHedgeTask(contractName string, optionType string, strikePrice, quantity decimal.Decimal) (*HedgeTaskResponse, error) {
	// 确定对冲方向
	direction := HedgeDirectionLong
	if optionType == OptionTypePut {
		direction = HedgeDirectionShort
	}

	// 获取全局默认参数
	defaults := GetGlobalDefaults()

	// 构建请求
	request := HedgeTaskRequest{
		Direction:      direction,
		TargetPrice:    strikePrice,
		StopRate:       defaults.HedgeThreshold,
		Amount:         quantity,
		Symbol:         extractSymbolFromContract(contractName),
		Source:         "options",
		OptionContract: contractName,
	}

	// 发送请求
	url := fmt.Sprintf("%s/api/tasks", hc.baseURL)
	response, err := hc.sendRequest("POST", url, request)
	if err != nil {
		return nil, fmt.Errorf("创建对冲任务请求失败: %v", err)
	}

	var hedgeResponse HedgeTaskResponse
	if err := json.Unmarshal(response, &hedgeResponse); err != nil {
		return nil, fmt.Errorf("解析对冲任务响应失败: %v", err)
	}

	if !hedgeResponse.Success {
		return nil, fmt.Errorf("创建对冲任务失败: %s", hedgeResponse.Message)
	}

	log.Printf("✅ 对冲任务创建成功: %s -> %s (%s, 数量: %s)",
		contractName, hedgeResponse.TaskID, direction, quantity.String())

	return &hedgeResponse, nil
}

// UpdateHedgeTaskAmount 更新对冲任务数量
func (hc *HedgeClient) UpdateHedgeTaskAmount(taskID, contractName string, newAmount decimal.Decimal, updateType string) (*HedgeTaskUpdateResponse, error) {
	// 构建请求
	request := HedgeTaskUpdateRequest{
		Amount:         newAmount,
		UpdateType:     updateType,
		Source:         "options",
		OptionContract: contractName,
	}

	// 发送请求
	url := fmt.Sprintf("%s/api/tasks/%s/amount", hc.baseURL, taskID)
	response, err := hc.sendRequest("PUT", url, request)
	if err != nil {
		return nil, fmt.Errorf("更新对冲任务请求失败: %v", err)
	}

	var updateResponse HedgeTaskUpdateResponse
	if err := json.Unmarshal(response, &updateResponse); err != nil {
		return nil, fmt.Errorf("解析对冲任务更新响应失败: %v", err)
	}

	if !updateResponse.Success {
		return nil, fmt.Errorf("更新对冲任务失败: %s", updateResponse.Message)
	}

	log.Printf("✅ 对冲任务数量已更新: %s, %s -> %s (%s)",
		taskID, updateResponse.OldAmount.String(), updateResponse.NewAmount.String(), updateType)

	return &updateResponse, nil
}

// QueryHedgeTask 查询对冲任务
func (hc *HedgeClient) QueryHedgeTask(contractName string) (*HedgeTaskQueryResponse, error) {
	// 发送请求
	url := fmt.Sprintf("%s/api/tasks/by-option/%s", hc.baseURL, contractName)
	response, err := hc.sendRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("查询对冲任务请求失败: %v", err)
	}

	var queryResponse HedgeTaskQueryResponse
	if err := json.Unmarshal(response, &queryResponse); err != nil {
		return nil, fmt.Errorf("解析对冲任务查询响应失败: %v", err)
	}

	if !queryResponse.Success {
		return nil, fmt.Errorf("查询对冲任务失败")
	}

	return &queryResponse, nil
}

// sendRequest 发送HTTP请求
func (hc *HedgeClient) sendRequest(method, url string, payload interface{}) ([]byte, error) {
	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("序列化请求数据失败: %v", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	// 创建请求
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Options-Trading-System/1.0")

	// 发送请求
	resp, err := hc.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP请求失败: %d %s, 响应: %s", resp.StatusCode, resp.Status, string(responseBody))
	}

	return responseBody, nil
}

// extractSymbolFromContract 从期权合约名称提取标的资产符号
func extractSymbolFromContract(contractName string) string {
	// 期权合约格式: ETHUSDT-2JUN25-2500-C
	// 提取 ETHUSDT 部分
	parts := strings.Split(contractName, "-")
	if len(parts) > 0 {
		return parts[0]
	}
	return contractName
}

// HedgeManager 对冲管理器
type HedgeManager struct {
	client          *HedgeClient
	positionManager *PositionManager
}

// NewHedgeManager 创建新的对冲管理器
func NewHedgeManager(positionManager *PositionManager) *HedgeManager {
	defaults := GetGlobalDefaults()
	client := NewHedgeClient(defaults.HedgeAPIURL)

	return &HedgeManager{
		client:          client,
		positionManager: positionManager,
	}
}

// HandlePositionChange 处理持仓变化
func (hm *HedgeManager) HandlePositionChange(contractName string, position *PositionTracker, updateType string) error {
	// 检查是否已有绑定的对冲任务
	if position.HedgeTaskID == "" {
		// 创建新的对冲任务
		return hm.createNewHedgeTask(contractName, position)
	} else {
		// 更新现有对冲任务
		return hm.updateExistingHedgeTask(contractName, position, updateType)
	}
}

// createNewHedgeTask 创建新的对冲任务
func (hm *HedgeManager) createNewHedgeTask(contractName string, position *PositionTracker) error {
	// 创建对冲任务
	response, err := hm.client.CreateHedgeTask(
		contractName,
		position.OptionType,
		position.StrikePrice,
		position.CurrentPosition,
	)
	if err != nil {
		return fmt.Errorf("创建对冲任务失败: %v", err)
	}

	// 绑定对冲任务ID
	if err := hm.positionManager.SetHedgeTaskID(contractName, response.TaskID); err != nil {
		log.Printf("⚠️  绑定对冲任务ID失败: %v", err)
	}

	return nil
}

// updateExistingHedgeTask 更新现有对冲任务
func (hm *HedgeManager) updateExistingHedgeTask(contractName string, position *PositionTracker, updateType string) error {
	// 更新对冲任务数量
	_, err := hm.client.UpdateHedgeTaskAmount(
		position.HedgeTaskID,
		contractName,
		position.CurrentPosition,
		updateType,
	)
	if err != nil {
		return fmt.Errorf("更新对冲任务失败: %v", err)
	}

	// 添加对冲任务更新事件
	position.mutex.Lock()
	position.Events = append(position.Events, PositionEvent{
		EventType:   EventTypeHedgeTaskUpdated,
		Description: fmt.Sprintf("对冲任务已更新: %s (类型: %s, 数量: %s)", position.HedgeTaskID, updateType, position.CurrentPosition.String()),
		Data:        fmt.Sprintf(`{"hedge_task_id":"%s","update_type":"%s","amount":"%s"}`, position.HedgeTaskID, updateType, position.CurrentPosition.String()),
		Timestamp:   time.Now(),
	})
	position.mutex.Unlock()

	return nil
}

// HandleOptionExercise 处理期权行权
func (hm *HedgeManager) HandleOptionExercise(contractName string, position *PositionTracker) error {
	if position.HedgeTaskID == "" {
		return nil // 没有绑定的对冲任务
	}

	// 将对冲任务数量设为0
	_, err := hm.client.UpdateHedgeTaskAmount(
		position.HedgeTaskID,
		contractName,
		decimal.Zero,
		UpdateTypeExercise,
	)
	if err != nil {
		return fmt.Errorf("处理期权行权失败: %v", err)
	}

	log.Printf("✅ 期权行权处理完成: %s, 对冲任务: %s", contractName, position.HedgeTaskID)
	return nil
}

// TestConnection 测试与对冲系统的连接
func (hm *HedgeManager) TestConnection() error {
	// 尝试查询一个不存在的期权合约来测试连接
	_, err := hm.client.QueryHedgeTask("TEST-CONNECTION")
	if err != nil {
		// 如果是因为合约不存在而失败，说明连接正常
		if strings.Contains(err.Error(), "不存在") || strings.Contains(err.Error(), "not found") {
			return nil
		}
		return fmt.Errorf("对冲系统连接测试失败: %v", err)
	}
	return nil
}
