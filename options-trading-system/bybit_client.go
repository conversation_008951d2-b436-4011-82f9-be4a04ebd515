package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
)

// BybitWSClient Bybit WebSocket客户端
type BybitWSClient struct {
	conn              *websocket.Conn
	apiKey            string
	secretKey         string
	baseURL           string
	wsURL             string
	subscriptions     map[string]bool            // 已订阅的频道
	orderBookCache    *OrderBookCache            // 订单簿缓存
	contractMonitor   *ContractMonitor           // 合约监控器
	spotPrices        map[string]decimal.Decimal // 现货价格
	mutex             sync.RWMutex
	reconnectInterval time.Duration
	isConnected       bool
	stopChan          chan struct{}

	// 交易检测事件通道
	tradingCheckChan chan TradingCheckEvent

	// 持仓数据更新通道
	positionUpdateChan chan map[string]interface{}
}

// BybitWSMessage WebSocket消息结构
type BybitWSMessage struct {
	Topic string      `json:"topic"`
	Type  string      `json:"type"`
	Data  interface{} `json:"data"`
	Ts    int64       `json:"ts"`
}

// BybitOrderBookData 订单簿数据（与测试程序一致的格式）
type BybitOrderBookData struct {
	Topic string `json:"topic"`
	Type  string `json:"type"` // snapshot 或 delta
	TS    int64  `json:"ts"`
	Data  struct {
		Symbol string     `json:"s"`
		Bids   [][]string `json:"b"` // [价格, 数量]
		Asks   [][]string `json:"a"` // [价格, 数量]
		U      int64      `json:"u"` // 更新ID
		Seq    int64      `json:"seq"`
	} `json:"data"`
	CTS int64 `json:"cts,omitempty"`
}

// BybitTickerData 行情数据
type BybitTickerData struct {
	Symbol      string `json:"symbol"`
	LastPrice   string `json:"lastPrice"`
	Volume24h   string `json:"volume24h"`
	Turnover24h string `json:"turnover24h"`
}

// NewBybitWSClient 创建新的Bybit WebSocket客户端
func NewBybitWSClient() *BybitWSClient {
	apiKey, secretKey, baseURL, wsURL := GetBybitConfig()

	client := &BybitWSClient{
		apiKey:             apiKey,
		secretKey:          secretKey,
		baseURL:            baseURL,
		wsURL:              wsURL,
		subscriptions:      make(map[string]bool),
		orderBookCache:     NewOrderBookCache(),
		spotPrices:         make(map[string]decimal.Decimal),
		reconnectInterval:  5 * time.Second,
		isConnected:        false,
		stopChan:           make(chan struct{}),
		tradingCheckChan:   make(chan TradingCheckEvent, 1000),     // 缓冲1000个事件
		positionUpdateChan: make(chan map[string]interface{}, 100), // 缓冲100个持仓更新
	}

	// 初始化合约监控器
	client.contractMonitor = NewContractMonitor(client)

	return client
}

// Connect 连接到WebSocket
func (client *BybitWSClient) Connect() error {
	log.Println("🔗 连接Bybit WebSocket...")

	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	conn, _, err := dialer.Dial(client.wsURL, http.Header{})
	if err != nil {
		return fmt.Errorf("WebSocket连接失败: %v", err)
	}

	client.conn = conn
	client.isConnected = true

	log.Println("✅ WebSocket连接成功")

	// 启动消息处理协程
	go client.handleMessages()

	// 启动心跳协程
	go client.heartbeat()

	// 启动合约监控器
	client.contractMonitor.Start()

	return nil
}

// Disconnect 断开WebSocket连接
func (client *BybitWSClient) Disconnect() {
	if client.conn != nil {
		client.isConnected = false
		close(client.stopChan)

		// 停止合约监控器
		client.contractMonitor.Stop()

		client.conn.Close()
		log.Println("🔌 WebSocket连接已断开")
	}
}

// SubscribeOptionOrderBook 订阅期权订单簿
func (client *BybitWSClient) SubscribeOptionOrderBook(symbol string) error {
	// 检查WebSocket连接状态
	if !client.isConnected || client.conn == nil {
		return fmt.Errorf("WebSocket未连接，无法订阅: %s", symbol)
	}

	// 订阅前状态验证：立即检查合约状态（修改方案第三阶段）
	if client.contractMonitor != nil && client.contractMonitor.restClient != nil {
		state, err := client.contractMonitor.getContractStateFromAPI(symbol)
		if err != nil {
			log.Printf("⚠️ 无法验证合约 %s 状态: %v", symbol, err)
			// 不阻止订阅，但记录警告
		} else if state != StateTrading {
			return fmt.Errorf("合约 %s 状态为 %s，不允许订阅", symbol, StateToString(state))
		}
		// 静默正常操作：状态验证通过时不输出日志（修改方案第六阶段）
	}

	// 直接使用 Bybit 格式，无需转换
	// 使用25档深度（期权最小支持25档）
	topic := fmt.Sprintf("orderbook.25.%s", symbol)

	client.mutex.Lock()
	defer client.mutex.Unlock()

	if client.subscriptions[topic] {
		return nil // 已经订阅
	}

	message := map[string]interface{}{
		"op":   "subscribe",
		"args": []string{topic},
	}

	if err := client.conn.WriteJSON(message); err != nil {
		return fmt.Errorf("订阅失败: %v", err)
	}

	client.subscriptions[topic] = true
	// 静默订阅成功，只在错误时输出（修改方案第六阶段）

	return nil
}

// 注意：现在全系统统一使用 Bybit 格式 (ETH-3JUN25-2500-C-USDT)，不再需要格式转换

// SubscribeSpotTicker 订阅现货行情
func (client *BybitWSClient) SubscribeSpotTicker(symbol string) error {
	// 检查WebSocket连接状态
	if !client.isConnected || client.conn == nil {
		return fmt.Errorf("WebSocket未连接，无法订阅: %s", symbol)
	}

	topic := fmt.Sprintf("tickers.%s", symbol)

	client.mutex.Lock()
	defer client.mutex.Unlock()

	if client.subscriptions[topic] {
		return nil // 已经订阅
	}

	message := map[string]interface{}{
		"op":   "subscribe",
		"args": []string{topic},
	}

	if err := client.conn.WriteJSON(message); err != nil {
		return fmt.Errorf("订阅失败: %v", err)
	}

	client.subscriptions[topic] = true
	log.Printf("📈 已订阅现货行情: %s", symbol)

	return nil
}

// handleMessages 处理WebSocket消息
func (client *BybitWSClient) handleMessages() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("❌ WebSocket消息处理异常: %v", r)
		}
	}()

	for {
		select {
		case <-client.stopChan:
			return
		default:
			var message BybitWSMessage
			if err := client.conn.ReadJSON(&message); err != nil {
				if client.isConnected {
					log.Printf("⚠️  WebSocket读取消息失败: %v", err)
					client.reconnect()
				}
				return
			}

			// 静默处理消息，不输出调试日志
			client.processMessage(&message)
		}
	}
}

// processMessage 处理具体消息
func (client *BybitWSClient) processMessage(message *BybitWSMessage) {
	switch {
	case strings.HasPrefix(message.Topic, "orderbook"):
		client.handleOrderBookUpdate(message)
	case strings.HasPrefix(message.Topic, "tickers"):
		client.handleTickerUpdate(message)
	case message.Topic == "position":
		client.handlePositionUpdate(message)
	case message.Type == "pong":
		// 心跳响应，静默处理
	case message.Type == "COMMAND_RESP":
		// 订阅响应，处理订阅结果
		client.handleSubscriptionResponse(message)
	case message.Type == "" && message.Data == nil:
		// 空消息，忽略
	default:
		// 未知消息类型，只在调试模式下输出
		if strings.ToLower(strings.TrimSpace(fmt.Sprintf("%v", message.Data))) != "null" {
			log.Printf("❓ 未知消息类型: Topic=%s, Type=%s", message.Topic, message.Type)
		}
	}
}

// handleSubscriptionResponse 处理订阅响应
func (client *BybitWSClient) handleSubscriptionResponse(message *BybitWSMessage) {
	if message.Data == nil {
		return
	}

	// 解析订阅响应数据
	dataMap, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	// 只输出失败的订阅
	if failTopics, exists := dataMap["failTopics"]; exists {
		if topics, ok := failTopics.([]interface{}); ok {
			for _, topic := range topics {
				if topicStr, ok := topic.(string); ok {
					log.Printf("❌ 订阅失败: %s", topicStr)
				}
			}
		}
	}
}

// handleOrderBookUpdate 处理订单簿更新
func (client *BybitWSClient) handleOrderBookUpdate(message *BybitWSMessage) {
	// 直接解析整个消息为订单簿数据
	dataBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("❌ 序列化消息数据失败: %v", err)
		return
	}

	var orderBookData BybitOrderBookData
	if err := json.Unmarshal(dataBytes, &orderBookData); err != nil {
		log.Printf("❌ 解析订单簿数据失败: %v", err)
		return
	}

	// 获取数据字段
	symbol := orderBookData.Data.Symbol
	bids := orderBookData.Data.Bids
	asks := orderBookData.Data.Asks
	messageType := orderBookData.Type

	// 获取或创建订单簿
	orderBook := client.orderBookCache.GetOrCreateOrderBook(symbol)

	// 根据消息类型更新订单簿
	if messageType == "snapshot" {
		orderBook.UpdateSnapshot(bids, asks)
	} else if messageType == "delta" {
		orderBook.UpdateDelta(bids, asks)
	}

	// 通知合约监控器更新状态
	hasData := messageType == "snapshot" || len(bids) > 0 || len(asks) > 0
	client.contractMonitor.UpdateContractStatus(symbol, hasData, client.isConnected)

	// 买一价变化检测和交易检测触发
	client.detectBid1PriceChangeAndTrigger(symbol, orderBook)
}

// handleTickerUpdate 处理行情更新
func (client *BybitWSClient) handleTickerUpdate(message *BybitWSMessage) {
	// 解析行情数据
	dataBytes, err := json.Marshal(message.Data)
	if err != nil {
		return
	}

	var tickerData BybitTickerData
	if err := json.Unmarshal(dataBytes, &tickerData); err != nil {
		return
	}

	client.mutex.Lock()
	defer client.mutex.Unlock()

	// 更新现货价格
	if price, err := decimal.NewFromString(tickerData.LastPrice); err == nil {
		client.spotPrices[tickerData.Symbol] = price
	}
}

// handlePositionUpdate 处理持仓更新
func (client *BybitWSClient) handlePositionUpdate(message *BybitWSMessage) {
	// 解析持仓数据
	dataBytes, err := json.Marshal(message.Data)
	if err != nil {
		log.Printf("❌ 序列化持仓数据失败: %v", err)
		return
	}

	var positionData []map[string]interface{}
	if err := json.Unmarshal(dataBytes, &positionData); err != nil {
		log.Printf("❌ 解析持仓数据失败: %v", err)
		return
	}

	// 处理每个持仓数据
	for _, position := range positionData {
		// 只处理期权持仓
		if symbol, ok := position["symbol"].(string); ok {
			isOption := strings.HasSuffix(symbol, "-USDT") &&
				(strings.Contains(symbol, "-C-") || strings.Contains(symbol, "-P-"))

			if isOption {
				// 发送到持仓更新通道
				select {
				case client.positionUpdateChan <- position:
					// 发送成功
				default:
					// 通道满，跳过这次更新
					log.Printf("⚠️ 持仓更新通道满，跳过 %s 更新", symbol)
				}
			}
		}
	}
}

// heartbeat 心跳机制
func (client *BybitWSClient) heartbeat() {
	ticker := time.NewTicker(20 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-client.stopChan:
			return
		case <-ticker.C:
			if client.isConnected && client.conn != nil {
				message := map[string]interface{}{
					"op": "ping",
				}
				if err := client.conn.WriteJSON(message); err != nil {
					log.Printf("⚠️  心跳发送失败: %v", err)
					// 心跳失败，标记连接断开
					client.isConnected = false
				}
			}
		}
	}
}

// reconnect 重连机制
func (client *BybitWSClient) reconnect() {
	client.isConnected = false

	for {
		log.Printf("🔄 尝试重连WebSocket...")

		if err := client.Connect(); err != nil {
			log.Printf("⚠️  重连失败: %v, %v后重试", err, client.reconnectInterval)
			time.Sleep(client.reconnectInterval)
			continue
		}

		// 重新订阅所有频道
		client.mutex.RLock()
		subscriptions := make([]string, 0, len(client.subscriptions))
		for topic := range client.subscriptions {
			subscriptions = append(subscriptions, topic)
		}
		client.mutex.RUnlock()

		// 清空订阅状态，重新订阅
		client.mutex.Lock()
		client.subscriptions = make(map[string]bool)
		client.mutex.Unlock()

		for _, topic := range subscriptions {
			if strings.HasPrefix(topic, "orderbook") {
				symbol := strings.TrimPrefix(topic, "orderbook.25.")
				client.SubscribeOptionOrderBook(symbol)
			} else if strings.HasPrefix(topic, "tickers") {
				symbol := strings.TrimPrefix(topic, "tickers.")
				client.SubscribeSpotTicker(symbol)
			}
		}

		log.Println("✅ WebSocket重连成功")
		break
	}
}

// GetOptionContract 获取期权合约数据 (从订单簿缓存获取)
func (client *BybitWSClient) GetOptionContract(symbol string) (*FullOrderBook, bool) {
	return client.orderBookCache.GetOrderBook(symbol)
}

// GetSpotPrice 获取现货价格
func (client *BybitWSClient) GetSpotPrice(symbol string) (decimal.Decimal, bool) {
	client.mutex.RLock()
	defer client.mutex.RUnlock()

	price, exists := client.spotPrices[symbol]
	return price, exists
}

// AddOptionContract 添加期权合约到监控列表
func (client *BybitWSClient) AddOptionContract(symbol string) {
	// 直接添加到合约监控器
	client.contractMonitor.AddContract(symbol)
}

// GetSubscribedOptions 获取已订阅的期权列表
func (client *BybitWSClient) GetSubscribedOptions() []string {
	// 从合约监控器获取
	statusMap := client.contractMonitor.GetAllContractStatus()

	var options []string
	for symbol := range statusMap {
		options = append(options, symbol)
	}

	return options
}

// GetSubscriptionStatus 获取订阅状态
func (client *BybitWSClient) GetSubscriptionStatus() map[string]bool {
	client.mutex.RLock()
	defer client.mutex.RUnlock()

	status := make(map[string]bool)
	for topic, subscribed := range client.subscriptions {
		status[topic] = subscribed
	}

	return status
}

// WebSocketStatus WebSocket状态信息
type WebSocketStatus struct {
	Symbol      string    `json:"symbol"`
	Connected   bool      `json:"connected"`
	LastUpdate  time.Time `json:"last_update"`
	Bid1Price   string    `json:"bid1_price"`
	UpdateCount int       `json:"update_count"`
	TimeSince   string    `json:"time_since"`
}

// GetWebSocketStatusTable 获取WebSocket状态表格数据
func (client *BybitWSClient) GetWebSocketStatusTable() []WebSocketStatus {
	// 从合约监控器获取状态
	statusMap := client.contractMonitor.GetAllContractStatus()

	var statusList []WebSocketStatus
	now := time.Now()

	for symbol, contractStatus := range statusMap {
		timeSince := now.Sub(contractStatus.LastWSUpdate)
		var timeSinceStr string

		if timeSince < time.Minute {
			timeSinceStr = fmt.Sprintf("%ds ago", int(timeSince.Seconds()))
		} else if timeSince < time.Hour {
			timeSinceStr = fmt.Sprintf("%dm ago", int(timeSince.Minutes()))
		} else {
			timeSinceStr = fmt.Sprintf("%dh ago", int(timeSince.Hours()))
		}

		// 从订单簿缓存获取买一价
		bid1Price := "无数据"
		updateCount := 0
		if orderBook, exists := client.orderBookCache.GetOrderBook(symbol); exists {
			if bestBid, _ := orderBook.GetBestBid(); !bestBid.IsZero() {
				bid1Price = bestBid.String()
			}
			updateCount = int(orderBook.UpdateCount)
		}

		// 根据新的逻辑判断连接状态：使用盘口有效性属性B
		connected := contractStatus.OrderBookValid

		status := WebSocketStatus{
			Symbol:      symbol,
			Connected:   connected,
			LastUpdate:  contractStatus.LastWSUpdate,
			Bid1Price:   bid1Price,
			UpdateCount: updateCount,
			TimeSince:   timeSinceStr,
		}

		statusList = append(statusList, status)
	}

	return statusList
}

// GetBestBidPrice 获取期权最优买价
func (client *BybitWSClient) GetBestBidPrice(symbol string) (decimal.Decimal, error) {
	if orderBook, exists := client.orderBookCache.GetOrderBook(symbol); exists {
		bestBid, _ := orderBook.GetBestBid()
		// 按照修改方案要求：买一价为零时仍认为数据有效（表示无买盘）
		return bestBid, nil
	}
	return decimal.Zero, fmt.Errorf("期权合约数据不存在: %s", symbol)
}

// GetBestAskPrice 获取期权最优卖价
func (client *BybitWSClient) GetBestAskPrice(symbol string) (decimal.Decimal, error) {
	if orderBook, exists := client.orderBookCache.GetOrderBook(symbol); exists {
		if bestAsk, _ := orderBook.GetBestAsk(); !bestAsk.IsZero() {
			return bestAsk, nil
		}
		return decimal.Zero, fmt.Errorf("期权 %s 卖一价为零", symbol)
	}
	return decimal.Zero, fmt.Errorf("期权合约数据不存在: %s", symbol)
}

// detectBid1PriceChangeAndTrigger 检测买一价变化并触发交易检测
func (client *BybitWSClient) detectBid1PriceChangeAndTrigger(symbol string, orderBook *FullOrderBook) {
	// 获取当前买一价
	newBid1Price, _ := orderBook.GetBestBid()

	// 获取合约状态
	status, exists := client.contractMonitor.GetContractStatus(symbol)
	if !exists {
		return // 合约不在监控列表
	}

	// 检测变化条件
	isChanged := false
	isFirstTime := status.IsFirstTimeData

	if isFirstTime {
		// 首次获得数据
		isChanged = true
	} else if !newBid1Price.Equal(status.LastBid1Price) {
		// 买一价发生变化
		isChanged = true
	}

	if isChanged {
		// 更新合约状态中的买一价信息
		client.contractMonitor.UpdateBid1PriceInfo(symbol, newBid1Price, isFirstTime)

		// 触发交易检测事件
		client.triggerTradingCheck(symbol, newBid1Price, isFirstTime)
	}
}

// triggerTradingCheck 触发交易检测
func (client *BybitWSClient) triggerTradingCheck(symbol string, bid1Price decimal.Decimal, isFirstTime bool) {
	event := TradingCheckEvent{
		Symbol:      symbol,
		Bid1Price:   bid1Price,
		Timestamp:   time.Now(),
		IsFirstTime: isFirstTime,
	}

	// 非阻塞发送事件
	select {
	case client.tradingCheckChan <- event:
		// 事件发送成功，静默处理
	default:
		// 通道满，记录警告但不阻塞
		log.Printf("⚠️ 交易检测通道满，跳过 %s 买一价变化事件", symbol)
	}
}

// GetTradingCheckChan 获取交易检测事件通道（供MonitorManager使用）
func (client *BybitWSClient) GetTradingCheckChan() <-chan TradingCheckEvent {
	return client.tradingCheckChan
}

// SubscribePosition 订阅持仓数据
func (client *BybitWSClient) SubscribePosition() error {
	// 检查WebSocket连接状态
	if !client.isConnected || client.conn == nil {
		return fmt.Errorf("WebSocket未连接，无法订阅持仓数据")
	}

	topic := "position"

	client.mutex.Lock()
	defer client.mutex.Unlock()

	if client.subscriptions[topic] {
		return nil // 已经订阅
	}

	message := map[string]interface{}{
		"op":   "subscribe",
		"args": []string{topic},
	}

	if err := client.conn.WriteJSON(message); err != nil {
		return fmt.Errorf("订阅持仓数据失败: %v", err)
	}

	client.subscriptions[topic] = true
	log.Printf("📊 已订阅持仓数据")

	return nil
}

// GetPositionUpdateChannel 获取持仓更新通道
func (client *BybitWSClient) GetPositionUpdateChannel() <-chan map[string]interface{} {
	return client.positionUpdateChan
}
