package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// NewPositionManager 创建新的持仓管理器
func NewPositionManager() *PositionManager {
	pm := &PositionManager{
		positions: make(map[string]*PositionTracker),
	}

	// 初始化数据管理器
	pm.dataManager = NewDataManager()

	return pm
}

// CreatePosition 创建持仓跟踪任务
func (pm *PositionManager) CreatePosition(contractName string, trade *TradeRecord) (*PositionTracker, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 解析期权合约信息
	optionInfo, err := pm.parseOptionContract(contractName)
	if err != nil {
		return nil, fmt.Errorf("解析期权合约失败: %v", err)
	}

	// 检查是否已存在持仓
	if position, exists := pm.positions[contractName]; exists {
		// 更新现有持仓
		return pm.updateExistingPosition(position, trade)
	}

	// 创建新持仓
	position := &PositionTracker{
		ContractName:    contractName,
		OptionType:      optionInfo.OptionType,
		StrikePrice:     optionInfo.StrikePrice,
		ExpiryTime:      optionInfo.ExpiryTime,
		Symbol:          optionInfo.Symbol,
		CurrentPosition: trade.Quantity,
		AveragePrice:    trade.Price,
		TotalCost:       trade.Price.Mul(trade.Quantity),
		TotalFees:       trade.Fee,
		UnrealizedPnL:   decimal.Zero,
		RealizedPnL:     decimal.Zero,
		HedgeTaskID:     "", // 稍后设置
		Status:          PositionStatusActive,
		Trades:          []TradeRecord{*trade},
		Events:          []PositionEvent{},
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 添加创建事件
	position.Events = append(position.Events, PositionEvent{
		EventType:   EventTypeTradeExecuted,
		Description: fmt.Sprintf("期权交易执行: %s %s", trade.Side, trade.Quantity.String()),
		Data:        fmt.Sprintf(`{"trade_id":"%s","price":"%s","quantity":"%s"}`, trade.TradeID, trade.Price.String(), trade.Quantity.String()),
		Timestamp:   time.Now(),
	})

	// 存储持仓
	pm.positions[contractName] = position

	// 触发数据保存
	pm.dataManager.TriggerSave()

	log.Printf("✅ 持仓跟踪任务创建成功: %s (数量: %s, 价格: %s)",
		contractName, trade.Quantity.String(), trade.Price.String())

	return position, nil
}

// UpdatePosition 更新持仓
func (pm *PositionManager) UpdatePosition(contractName string, trade *TradeRecord) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	position, exists := pm.positions[contractName]
	if !exists {
		return fmt.Errorf("持仓不存在: %s", contractName)
	}

	_, err := pm.updateExistingPosition(position, trade)
	return err
}

// updateExistingPosition 更新现有持仓
func (pm *PositionManager) updateExistingPosition(position *PositionTracker, trade *TradeRecord) (*PositionTracker, error) {
	position.mutex.Lock()
	defer position.mutex.Unlock()

	// 添加交易记录
	position.Trades = append(position.Trades, *trade)

	// 根据交易方向更新持仓
	if trade.Side == TradeSideSell {
		// 卖出增加持仓
		oldPosition := position.CurrentPosition

		position.CurrentPosition = position.CurrentPosition.Add(trade.Quantity)
		position.TotalCost = position.TotalCost.Add(trade.Price.Mul(trade.Quantity))
		position.TotalFees = position.TotalFees.Add(trade.Fee)

		// 重新计算平均价格
		if position.CurrentPosition.GreaterThan(decimal.Zero) {
			position.AveragePrice = position.TotalCost.Div(position.CurrentPosition)
		}

		log.Printf("📈 持仓增加: %s, 数量: %s -> %s, 均价: %s",
			position.ContractName,
			oldPosition.String(),
			position.CurrentPosition.String(),
			position.AveragePrice.String())

	} else if trade.Side == TradeSideBuy {
		// 买入减少持仓
		oldPosition := position.CurrentPosition
		position.CurrentPosition = position.CurrentPosition.Sub(trade.Quantity)
		position.TotalFees = position.TotalFees.Add(trade.Fee)

		// 计算已实现盈亏
		realizedPnL := trade.Quantity.Mul(position.AveragePrice.Sub(trade.Price))
		position.RealizedPnL = position.RealizedPnL.Add(realizedPnL)

		log.Printf("📉 持仓减少: %s, 数量: %s -> %s, 已实现盈亏: %s",
			position.ContractName,
			oldPosition.String(),
			position.CurrentPosition.String(),
			realizedPnL.String())

		// 如果持仓归零，标记为已关闭
		if position.CurrentPosition.LessThanOrEqual(decimal.Zero) {
			position.Status = PositionStatusClosed
			log.Printf("🔒 持仓已关闭: %s", position.ContractName)
		}
	}

	// 添加交易事件
	position.Events = append(position.Events, PositionEvent{
		EventType:   EventTypeTradeExecuted,
		Description: fmt.Sprintf("期权交易执行: %s %s", trade.Side, trade.Quantity.String()),
		Data:        fmt.Sprintf(`{"trade_id":"%s","price":"%s","quantity":"%s","update_type":"%s"}`, trade.TradeID, trade.Price.String(), trade.Quantity.String(), trade.UpdateType),
		Timestamp:   time.Now(),
	})

	position.UpdatedAt = time.Now()

	// 触发数据保存
	pm.dataManager.TriggerSave()

	return position, nil
}

// GetPosition 获取持仓
func (pm *PositionManager) GetPosition(contractName string) (*PositionTracker, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	position, exists := pm.positions[contractName]
	if !exists {
		return nil, fmt.Errorf("持仓不存在: %s", contractName)
	}

	return position, nil
}

// GetAllPositions 获取所有持仓
func (pm *PositionManager) GetAllPositions() map[string]*PositionTracker {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 创建副本避免并发问题
	result := make(map[string]*PositionTracker)
	for contractName, position := range pm.positions {
		result[contractName] = position
	}

	return result
}

// DeletePosition 删除持仓跟踪任务
func (pm *PositionManager) DeletePosition(contractName string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	position, exists := pm.positions[contractName]
	if !exists {
		return fmt.Errorf("持仓不存在: %s", contractName)
	}

	// 添加删除事件
	position.Events = append(position.Events, PositionEvent{
		EventType:   EventTypePositionClosed,
		Description: "持仓跟踪任务已删除",
		Data:        `{"action":"manual_delete"}`,
		Timestamp:   time.Now(),
	})

	// 从持仓列表中删除
	delete(pm.positions, contractName)

	// 触发数据保存
	pm.dataManager.TriggerSave()

	log.Printf("🗑️  持仓跟踪任务已删除: %s", contractName)
	return nil
}

// SetHedgeTaskID 设置对冲任务ID
func (pm *PositionManager) SetHedgeTaskID(contractName, hedgeTaskID string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	position, exists := pm.positions[contractName]
	if !exists {
		return fmt.Errorf("持仓不存在: %s", contractName)
	}

	position.mutex.Lock()
	defer position.mutex.Unlock()

	position.HedgeTaskID = hedgeTaskID
	position.UpdatedAt = time.Now()

	// 添加对冲任务绑定事件
	position.Events = append(position.Events, PositionEvent{
		EventType:   EventTypeHedgeTaskCreated,
		Description: fmt.Sprintf("对冲任务已绑定: %s", hedgeTaskID),
		Data:        fmt.Sprintf(`{"hedge_task_id":"%s"}`, hedgeTaskID),
		Timestamp:   time.Now(),
	})

	// 触发数据保存
	pm.dataManager.TriggerSave()

	log.Printf("🔗 对冲任务已绑定: %s -> %s", contractName, hedgeTaskID)
	return nil
}

// UpdatePositionFromAPI 从API更新持仓信息
func (pm *PositionManager) UpdatePositionFromAPI(contractName string, newPosition decimal.Decimal, isExercised bool) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	position, exists := pm.positions[contractName]
	if !exists {
		return fmt.Errorf("持仓不存在: %s", contractName)
	}

	position.mutex.Lock()
	defer position.mutex.Unlock()

	oldPosition := position.CurrentPosition
	position.CurrentPosition = newPosition
	position.UpdatedAt = time.Now()

	var eventType, description string
	if isExercised {
		position.Status = PositionStatusExercised
		eventType = EventTypeOptionExercised
		description = "期权已行权"
	} else {
		eventType = EventTypePositionUpdated
		description = fmt.Sprintf("持仓数量更新: %s -> %s", oldPosition.String(), newPosition.String())
	}

	// 添加更新事件
	position.Events = append(position.Events, PositionEvent{
		EventType:   eventType,
		Description: description,
		Data:        fmt.Sprintf(`{"old_position":"%s","new_position":"%s","is_exercised":%t}`, oldPosition.String(), newPosition.String(), isExercised),
		Timestamp:   time.Now(),
	})

	// 如果持仓归零或已行权，标记为结束
	if newPosition.LessThanOrEqual(decimal.Zero) || isExercised {
		if position.Status == PositionStatusActive {
			position.Status = PositionStatusClosed
		}
		log.Printf("🔒 持仓已结束: %s (原因: %s)", contractName, description)
	}

	// 触发数据保存
	pm.dataManager.TriggerSave()

	log.Printf("🔄 持仓已更新: %s, %s", contractName, description)
	return nil
}

// parseOptionContract 解析期权合约信息
func (pm *PositionManager) parseOptionContract(contractName string) (*OptionContract, error) {
	// 期权合约格式: ETHUSDT-2JUN25-2500-C
	parts := strings.Split(contractName, "-")
	if len(parts) != 4 {
		return nil, fmt.Errorf("无效的期权合约格式: %s", contractName)
	}

	symbol := parts[0]
	dateStr := parts[1]
	priceStr := parts[2]
	typeStr := parts[3]

	// 解析行权价格
	strikePrice, err := decimal.NewFromString(priceStr)
	if err != nil {
		return nil, fmt.Errorf("无效的行权价格: %s", priceStr)
	}

	// 解析期权类型
	var optionType string
	switch strings.ToUpper(typeStr) {
	case "C":
		optionType = OptionTypeCall
	case "P":
		optionType = OptionTypePut
	default:
		return nil, fmt.Errorf("无效的期权类型: %s", typeStr)
	}

	// 解析到期时间 (简化处理，实际应该根据交易所规则解析)
	expiryTime, err := time.Parse("2JAN06", dateStr)
	if err != nil {
		return nil, fmt.Errorf("无效的到期时间格式: %s", dateStr)
	}

	return &OptionContract{
		Symbol:          contractName,
		UnderlyingAsset: symbol,
		OptionType:      optionType,
		StrikePrice:     strikePrice,
		ExpiryTime:      expiryTime,
	}, nil
}

// GetActivePositions 获取活跃持仓
func (pm *PositionManager) GetActivePositions() map[string]*PositionTracker {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	result := make(map[string]*PositionTracker)
	for contractName, position := range pm.positions {
		if position.Status == PositionStatusActive && position.CurrentPosition.GreaterThan(decimal.Zero) {
			result[contractName] = position
		}
	}

	return result
}

// CalculateUnrealizedPnL 计算未实现盈亏
func (pm *PositionManager) CalculateUnrealizedPnL(contractName string, currentPrice decimal.Decimal) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	position, exists := pm.positions[contractName]
	if !exists {
		return fmt.Errorf("持仓不存在: %s", contractName)
	}

	position.mutex.Lock()
	defer position.mutex.Unlock()

	// 计算未实现盈亏 (卖出期权的盈亏计算)
	position.UnrealizedPnL = position.CurrentPosition.Mul(position.AveragePrice.Sub(currentPrice))
	position.UpdatedAt = time.Now()

	return nil
}

// LoadData 加载持久化数据
func (pm *PositionManager) LoadData() error {
	// 加载持仓数据
	positions, err := pm.dataManager.LoadPositions()
	if err != nil {
		return fmt.Errorf("加载持仓数据失败: %v", err)
	}

	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 恢复持仓数据
	pm.positions = positions

	log.Printf("✅ 持仓数据恢复完成: %d个持仓", len(positions))
	return nil
}
