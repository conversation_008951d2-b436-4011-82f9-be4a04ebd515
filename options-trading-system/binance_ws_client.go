package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
)

// BinanceWSClient 币安WebSocket客户端
type BinanceWSClient struct {
	wsURL       string
	conn        *websocket.Conn
	isConnected bool
	mutex       sync.RWMutex
	stopChan    chan struct{}

	// 盘口数据缓存
	bidPrice   decimal.Decimal // 买一价
	askPrice   decimal.Decimal // 卖一价
	lastUpdate time.Time       // 最后更新时间
	symbol     string          // 交易对
}

// BinanceDepthData 币安盘口数据结构（直接格式）
type BinanceDepthData struct {
	EventType     string     `json:"e"`  // 事件类型
	EventTime     int64      `json:"E"`  // 事件时间
	TransactTime  int64      `json:"T"`  // 交易时间
	Symbol        string     `json:"s"`  // 交易对
	FirstUpdateID int64      `json:"U"`  // 第一个更新ID
	FinalUpdateID int64      `json:"u"`  // 最后一个更新ID
	PrevUpdateID  int64      `json:"pu"` // 前一个更新ID
	Bids          [][]string `json:"b"`  // 买盘
	Asks          [][]string `json:"a"`  // 卖盘
}

// NewBinanceWSClient 创建币安WebSocket客户端
func NewBinanceWSClient(symbol string) *BinanceWSClient {
	return &BinanceWSClient{
		wsURL:       "wss://fstream.binance.com/ws/",
		symbol:      symbol,
		isConnected: false,
		stopChan:    make(chan struct{}),
		bidPrice:    decimal.Zero,
		askPrice:    decimal.Zero,
	}
}

// Connect 连接WebSocket
func (client *BinanceWSClient) Connect() error {
	// 构建WebSocket URL
	streamName := fmt.Sprintf("%s@depth5@100ms", client.symbol)
	wsURL := client.wsURL + streamName

	log.Printf("🔗 连接币安永续合约WebSocket: %s", client.symbol)

	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	conn, _, err := dialer.Dial(wsURL, http.Header{})
	if err != nil {
		return fmt.Errorf("币安WebSocket连接失败: %v", err)
	}

	client.conn = conn
	client.isConnected = true

	log.Printf("✅ 币安WebSocket连接成功: %s", client.symbol)

	// 启动消息处理协程
	go client.handleMessages()

	// 启动心跳协程
	go client.heartbeat()

	return nil
}

// Disconnect 断开连接
func (client *BinanceWSClient) Disconnect() {
	if client.conn != nil {
		client.isConnected = false
		close(client.stopChan)
		client.conn.Close()
		log.Printf("🔌 币安WebSocket连接已断开: %s", client.symbol)
	}
}

// handleMessages 处理WebSocket消息
func (client *BinanceWSClient) handleMessages() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("❌ 币安WebSocket消息处理异常: %v", r)
		}
	}()

	for {
		select {
		case <-client.stopChan:
			return
		default:
			// 先读取原始消息
			_, rawMessage, err := client.conn.ReadMessage()
			if err != nil {
				if client.isConnected {
					log.Printf("⚠️  币安WebSocket读取消息失败: %v", err)
					client.reconnect()
				}
				return
			}

			// 调试：打印原始消息（前200字符）
			rawStr := string(rawMessage)
			if len(rawStr) > 200 {
				rawStr = rawStr[:200] + "..."
			}
			//log.Printf("🔍 [币安WebSocket调试] 原始消息: %s", rawStr)

			// 解析JSON消息
			var message BinanceDepthData
			if err := json.Unmarshal(rawMessage, &message); err != nil {
				log.Printf("🔍 [币安WebSocket调试] JSON解析失败: %v", err)
				continue
			}

			// 处理盘口数据
			client.processDepthData(&message)
		}
	}
}

// processDepthData 处理盘口数据
func (client *BinanceWSClient) processDepthData(data *BinanceDepthData) {
	// 添加调试日志
	//log.Printf("🔍 [币安WebSocket调试] 接收到数据: EventType=%s, Symbol=%s, Bids数量=%d, Asks数量=%d",
	//	data.EventType, data.Symbol, len(data.Bids), len(data.Asks))

	if data.EventType != "depthUpdate" {
		log.Printf("🔍 [币安WebSocket调试] 跳过非depthUpdate事件: %s", data.EventType)
		return
	}

	client.mutex.Lock()
	defer client.mutex.Unlock()

	// 更新买一价
	if len(data.Bids) > 0 && len(data.Bids[0]) >= 2 {
		if bidPrice, err := decimal.NewFromString(data.Bids[0][0]); err == nil {
			client.bidPrice = bidPrice
			//log.Printf("🔍 [币安WebSocket调试] 更新买一价: %s", bidPrice.String())
		} else {
			log.Printf("🔍 [币安WebSocket调试] 买一价解析失败: %v", err)
		}
	}

	// 更新卖一价
	if len(data.Asks) > 0 && len(data.Asks[0]) >= 2 {
		if askPrice, err := decimal.NewFromString(data.Asks[0][0]); err == nil {
			client.askPrice = askPrice
			//log.Printf("🔍 [币安WebSocket调试] 更新卖一价: %s", askPrice.String())
		} else {
			log.Printf("🔍 [币安WebSocket调试] 卖一价解析失败: %v", err)
		}
	}

	// 更新时间戳
	client.lastUpdate = time.Now()
	//log.Printf("🔍 [币安WebSocket调试] 数据更新完成，买一价=%s, 卖一价=%s",
	//	client.bidPrice.String(), client.askPrice.String())
}

// heartbeat 心跳机制
func (client *BinanceWSClient) heartbeat() {
	ticker := time.NewTicker(20 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-client.stopChan:
			return
		case <-ticker.C:
			if client.isConnected && client.conn != nil {
				// 币安WebSocket不需要主动发送心跳，只需要检查连接状态
				if err := client.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
					log.Printf("⚠️  币安WebSocket心跳失败: %v", err)
					client.isConnected = false
				}
			}
		}
	}
}

// reconnect 重连机制
func (client *BinanceWSClient) reconnect() {
	client.isConnected = false

	for {
		log.Printf("🔄 尝试重连币安WebSocket...")

		if err := client.Connect(); err != nil {
			log.Printf("⚠️  币安WebSocket重连失败: %v, 5秒后重试", err)
			time.Sleep(5 * time.Second)
			continue
		}

		log.Println("✅ 币安WebSocket重连成功")
		break
	}
}

// GetBidPrice 获取买一价
func (client *BinanceWSClient) GetBidPrice() (decimal.Decimal, bool) {
	client.mutex.RLock()
	defer client.mutex.RUnlock()

	// 检查数据是否有效（30秒内更新）
	if time.Since(client.lastUpdate) > 30*time.Second {
		return decimal.Zero, false
	}

	return client.bidPrice, !client.bidPrice.IsZero()
}

// GetAskPrice 获取卖一价
func (client *BinanceWSClient) GetAskPrice() (decimal.Decimal, bool) {
	client.mutex.RLock()
	defer client.mutex.RUnlock()

	// 检查数据是否有效（30秒内更新）
	if time.Since(client.lastUpdate) > 30*time.Second {
		return decimal.Zero, false
	}

	return client.askPrice, !client.askPrice.IsZero()
}

// GetMidPrice 获取中间价
func (client *BinanceWSClient) GetMidPrice() (decimal.Decimal, bool) {
	client.mutex.RLock()
	defer client.mutex.RUnlock()

	// 检查数据是否有效（30秒内更新）
	if time.Since(client.lastUpdate) > 30*time.Second {
		return decimal.Zero, false
	}

	if client.bidPrice.IsZero() || client.askPrice.IsZero() {
		return decimal.Zero, false
	}

	midPrice := client.bidPrice.Add(client.askPrice).Div(decimal.NewFromInt(2))
	return midPrice, true
}

// IsConnected 检查连接状态
func (client *BinanceWSClient) IsConnected() bool {
	client.mutex.RLock()
	defer client.mutex.RUnlock()
	return client.isConnected
}

// GetLastUpdate 获取最后更新时间
func (client *BinanceWSClient) GetLastUpdate() time.Time {
	client.mutex.RLock()
	defer client.mutex.RUnlock()
	return client.lastUpdate
}
