期权状态管理完整修改方案（修正版）
=====================================

修改目标：
解决期权合约状态管理问题，确保只监控有效的Trading状态期权，优化系统性能和用户体验。

第一阶段：数据结构改造
====================

1.1 新增期权合约状态枚举
type OptionContractState int

const (
    StateTrading     OptionContractState = iota // 正常交易
    StateDelivering                             // 交割中
    StateDelivered                              // 已行权
    StatePreLaunch                              // 预上市
    StateNotExist                               // 不存在
    StateFetchFailed                            // 获取失败
)

1.2 扩展ContractStatus结构
type ContractStatus struct {
    Symbol              string
    State               OptionContractState  // 新增：合约状态
    LastStateCheck      time.Time           // 新增：最后状态检查时间
    NextCheckTime       time.Time           // 新增：下次检查时间
    CurrentCycleFailCount int               // 新增：当前检查周期失败次数
    LastRetryTime       time.Time           // 新增：最后重试时间
    MonitorStartTime    time.Time           // 新增：开始监控时间
    OrderBookValid      bool                // 现有：盘口数据有效性
    LastWSUpdate        time.Time           // 现有：最后WebSocket更新时间
    RestCheckThreshold  time.Duration       // 现有：REST检查阈值
    WSConnected         bool                // 现有：WebSocket连接状态
}

1.3 API状态映射关系
API返回 "Trading" -> StateTrading (正常交易)
API返回 "Delivering" -> StateDelivering (交割中)
API返回 "Closed" -> StateDelivered (已行权)
API返回 "PreLaunch" -> StatePreLaunch (预上市)
API返回 retCode: 110023 -> StateNotExist (不存在)
其他API错误 -> StateFetchFailed (获取失败)

第二阶段：状态检查机制
====================

2.1 动态检查频率策略
距离行权时间 <= 15分钟：每1分钟检查一次
距离行权时间 <= 1小时：每5分钟检查一次
正常情况：每30分钟检查一次

2.2 API调用频率控制
问题：避免同一秒发出多个API请求
方案：为每个合约分配检查时间偏移量
实现：
- 基于合约名称hash值计算偏移量（0-59秒）
- 确保不同合约的检查时间分散在不同秒数
- 例：ETH-3JUN25-2500-C-USDT在第15秒检查，ETH-3JUN25-2600-C-USDT在第42秒检查

2.3 分批检查策略
批次大小：每批最多10个合约
批次间隔：每批之间间隔1秒
异步处理：每个批次使用独立goroutine处理

2.4 错误重试机制（修正版）
周期性重置：每个检查周期开始时，CurrentCycleFailCount重置为0
重试策略：单次检查失败后，每5秒重试一次
失败阈值：当前检查周期内连续3次失败才标记为StateFetchFailed
重试限制：每个检查周期最多重试3次
示例流程：
  20:00 检查周期开始，CurrentCycleFailCount = 0
  20:00:00 第1次检查失败，CurrentCycleFailCount = 1
  20:00:05 第2次重试失败，CurrentCycleFailCount = 2  
  20:00:10 第3次重试成功，状态更新为Trading，CurrentCycleFailCount = 0
  20:30 下个检查周期开始，CurrentCycleFailCount重置为0

第三阶段：订阅管理改造
====================

3.1 订阅前状态验证
流程：
1. 调用REST API检查合约状态
2. 只有StateTrading状态才允许订阅
3. 非Trading状态直接拒绝订阅并记录日志
4. 订阅成功后添加到状态监控列表，记录MonitorStartTime

3.2 状态变化处理
监控逻辑：定期检查所有已订阅合约的状态
状态变化响应：
- StateTrading -> 其他状态：立即取消订阅，从监控列表移除
- 其他状态 -> StateTrading：不自动订阅（需要重新评估是否符合监控条件）

3.3 订阅生命周期管理
原则：永远只订阅StateTrading状态的期权合约
清理机制：定期清理非Trading状态的合约订阅

第四阶段：盘口数据判断逻辑优化
============================

4.1 数据有效性判断
前提条件：合约状态必须为StateTrading
验证标准：WebSocket缓存盘口数据与REST API盘口数据一致
特殊情况：买一价为0时仍认为数据有效（表示无买盘）

4.2 时间价值计算逻辑
计算条件：
1. 合约状态为StateTrading
2. 盘口数据有效（OrderBookValid = true）
买一价处理：
- 有买盘：使用实际买一价
- 无买盘：买一价按0计算时间价值

4.3 监控任务执行逻辑
执行条件：只对StateTrading状态的期权执行时间价值计算和下单检测
停止条件：状态变为非Trading时立即停止计算和检测

第五阶段：用户界面增强
====================

5.1 list_m命令增强（修正版）

5.1.1 监控任务基本信息展示
🎯 任务 M5687000 筛选期权合约: 现货价格=2614.43, 范围=[2352.99-2875.87], 合约数=12

【期权监控】M5687000 (CALL期权卖出)
├─ 基本信息: ETHUSDT | 到期: 24h | 阈值: 1.80% | 价格范围: ±10%
├─ 运行状态: running | 开始: 18:51 | 持续: 8秒
├─ 现货价格: 2614.43 | 监控范围: [2350-2600] | 合约数: 12个
└─ 状态统计: Trading: 10个 | 交割中: 1个 | 已行权: 1个 | 其他: 0个

5.1.2 期权详情表格
期权合约                          时间价值    买1价     最后获取    状态      WS连接    监控时长
─────────────────────────────────────────────────────────────────────────────────────────
ETH-3JUN25-2500-C-USDT           2.45%      109.2     2s ago     Trading   ✅       5分钟
ETH-3JUN25-2550-C-USDT           1.89%      59.2      1s ago     Trading   ✅       5分钟
ETH-3JUN25-2600-C-USDT           1.23%      8.8       30s ago    Trading   ✅       5分钟
ETH-3JUN25-2650-C-USDT           0.67%      0.6       45s ago    Trading   ✅       5分钟
ETH-3JUN25-2700-C-USDT           0.12%      0.0       1m ago     Trading   ✅       5分钟
ETH-4JUN25-2500-C-USDT           2.78%      113.3     3s ago     Trading   ✅       5分钟
ETH-4JUN25-2550-C-USDT           2.34%      70.4      2s ago     Trading   ✅       5分钟
ETH-4JUN25-2600-C-USDT           1.67%      40.8      1s ago     Trading   ✅       5分钟
ETH-3JUN25-2450-C-USDT           0.00%      159.2     2m ago     Delivered ❌       5分钟
ETH-3JUN25-2750-C-USDT           0.00%      0.0       3m ago     Delivering⚠️       5分钟

📊 统计: 总计 10个期权 | Trading: 8个 | 其他状态: 2个

5.1.3 排序规则
主排序：按时间价值从大到小排序
次排序：相同时间价值按期权名称排序
状态分组：Trading状态在前，其他状态在后

5.2 新增contract_state命令
功能：查看指定合约或所有合约的状态详情
用法：
  contract_state                           # 显示所有合约状态
  contract_state ETH-3JUN25-2500-C-USDT  # 显示指定合约状态

显示内容：
合约状态详情:
─────────────────────────────────────────────────────────────────────────
合约名称: ETH-3JUN25-2500-C-USDT
当前状态: Trading
最后检查: 2分钟前 (2025/06/03 20:28:15)
下次检查: 28分钟后 (2025/06/03 21:00:15)
检查间隔: 30分钟
失败次数: 0/3 (当前周期)
监控时长: 1小时15分钟
盘口有效: 是
WS连接: 正常
买一价: 109.2 USDT
时间价值: 2.45%
─────────────────────────────────────────────────────────────────────────

第六阶段：日志优化
================

6.1 静默正常操作
不输出日志的情况：
- REST验证通过（数据一致）
- 状态检查成功且状态未变化
- 正常的盘口数据更新

6.2 重要事件日志
必须输出的情况：
- 合约状态变化
- 订阅/取消订阅操作
- REST验证失败
- API调用失败
- 系统错误

6.3 日志级别分类
INFO: 状态变化、订阅管理
WARN: 验证失败、重试操作
ERROR: API错误、系统异常
DEBUG: 详细调试信息（可配置开关）

第七阶段：性能优化
================

7.1 缓存策略
状态缓存：缓存合约状态检查结果
缓存时效：根据检查频率设置缓存过期时间
缓存更新：状态变化时立即更新缓存

7.2 异步处理
状态检查：异步执行，不阻塞主流程
订阅管理：异步处理订阅/取消订阅操作
日志记录：异步写入日志文件

7.3 资源管理
连接池：复用HTTP连接
内存管理：定期清理过期数据
goroutine管理：控制并发数量，避免资源泄露

第八阶段：错误处理和容错机制
==========================

8.1 网络异常处理
超时重试：API调用超时时自动重试
降级策略：连续失败时降低检查频率
恢复机制：网络恢复后自动恢复正常检查频率

8.2 数据一致性保证
状态同步：确保WebSocket客户端和状态管理器的数据一致
事务性操作：状态变化和订阅管理的原子性操作
数据校验：定期校验数据完整性

8.3 系统监控
健康检查：定期检查各组件运行状态
性能监控：监控API调用延迟和成功率
告警机制：异常情况及时告警

第九阶段：配置管理（修正版）
==========================

9.1 embedded_config配置项
type StateCheckConfig struct {
    NormalInterval      time.Duration `json:"normal_interval"`       // 正常检查间隔：30分钟
    HourBeforeInterval  time.Duration `json:"hour_before_interval"`  // 1小时内检查间隔：5分钟
    FinalInterval       time.Duration `json:"final_interval"`        // 15分钟内检查间隔：1分钟
    RetryInterval       time.Duration `json:"retry_interval"`        // 重试间隔：5秒
    MaxRetryCount       int           `json:"max_retry_count"`        // 最大重试次数：3次
    BatchSize           int           `json:"batch_size"`             // 批次大小：10个
    BatchInterval       time.Duration `json:"batch_interval"`        // 批次间隔：1秒
    APITimeout          time.Duration `json:"api_timeout"`           // API超时时间：10秒
}

// 默认配置
var DefaultStateCheckConfig = StateCheckConfig{
    NormalInterval:     30 * time.Minute,
    HourBeforeInterval: 5 * time.Minute,
    FinalInterval:      1 * time.Minute,
    RetryInterval:      5 * time.Second,
    MaxRetryCount:      3,
    BatchSize:          10,
    BatchInterval:      1 * time.Second,
    APITimeout:         10 * time.Second,
}

9.2 配置集成
配置位置：添加到现有的embedded_config.go文件中
配置加载：程序启动时从配置文件加载
配置验证：启动时验证配置参数的合理性

第十阶段：测试和验证
==================

10.1 单元测试
状态转换逻辑测试
API调用频率控制测试
错误重试机制测试
周期性失败计数重置测试

10.2 集成测试
完整流程测试
异常场景测试
性能压力测试
长时间运行稳定性测试

10.3 验证标准
功能正确性：所有原问题得到解决
性能指标：API调用频率符合限制
稳定性：长时间运行无内存泄露
用户体验：界面友好，日志清晰

实施优先级和时间安排
==================

高优先级（立即实施）
1. 数据结构改造
2. 状态检查机制
3. 订阅管理改造

中优先级（第二阶段）
4. 盘口数据判断逻辑优化
5. 用户界面增强
6. 日志优化

低优先级（第三阶段）
7. 性能优化
8. 错误处理和容错机制
9. 配置管理
10. 测试和验证

预期效果
========

问题解决
✅ 完全解决等待盘口数据显示缺失问题
✅ 完全解决时间计算精度问题
✅ 完全解决盘口有效但更新时间久的问题
✅ 避免订阅无效期权合约
✅ 提供清晰的期权状态监控界面

系统改进
🚀 提高系统稳定性和可靠性
🚀 优化资源使用效率
🚀 增强用户体验和监控能力
🚀 提供完善的状态管理和诊断功能

这个修正版方案澄清了错误重试机制的周期性重置逻辑，完善了list_m命令的展示格式，
并将配置管理集成到embedded_config中，形成了更加完整和准确的实施指南。
