#!/usr/bin/env python3
"""
验证API密钥和私钥是否匹配
"""

import base64
import aiohttp
import asyncio
import time
from cryptography.hazmat.primitives.asymmetric import ed25519
from cryptography.hazmat.primitives import serialization
from ed25519_config import ED25519_PRIVATE_KEY_PEM

async def verify_key_pair():
    """验证API密钥和私钥是否匹配"""
    
    # API密钥
    api_key = "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso"
    
    # 加载Ed25519私钥
    ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
        serialization.load_pem_private_key(
            ED25519_PRIVATE_KEY_PEM.encode('utf-8'),
            password=None
        ).private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
    )
    
    # 获取公钥
    public_key = ed25519_private_key.public_key()
    public_key_bytes = public_key.public_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PublicFormat.Raw
    )
    
    print(f"API密钥: {api_key}")
    print(f"私钥 (Hex): {ed25519_private_key.private_bytes(encoding=serialization.Encoding.Raw, format=serialization.PrivateFormat.Raw, encryption_algorithm=serialization.NoEncryption()).hex()}")
    print(f"公钥 (Hex): {public_key_bytes.hex()}")
    print(f"公钥 (Base64): {base64.b64encode(public_key_bytes).decode('utf-8')}")
    
    # 测试1: 简单的账户信息查询
    print("\n" + "="*60)
    print("测试1: 账户信息查询（无参数签名）")
    print("="*60)
    
    timestamp = int(time.time() * 1000)
    
    # 构建查询字符串
    query_string = f"timestamp={timestamp}"
    
    # 生成签名
    signature_bytes = ed25519_private_key.sign(query_string.encode('utf-8'))
    signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
    
    print(f"查询字符串: {query_string}")
    print(f"签名: {signature_base64}")
    
    # 发送请求
    url = f"https://fapi.binance.com/fapi/v2/account?{query_string}&signature={signature_base64}"
    headers = {
        'X-MBX-APIKEY': api_key
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                result = await response.json()
                print(f"响应状态: {response.status}")
                print(f"响应内容: {result}")
                
                if response.status == 200:
                    print("✅ 账户信息查询成功 - API密钥和私钥匹配！")
                    return True
                else:
                    print("❌ 账户信息查询失败")
                    if result.get('code') == -2015:
                        print("   错误: Invalid API-key - API密钥可能不是Ed25519类型")
                    elif result.get('code') == -1022:
                        print("   错误: Signature invalid - 私钥与API密钥不匹配")
                    return False
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

async def test_different_endpoints():
    """测试不同的API端点"""
    
    api_key = "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso"
    
    # 加载Ed25519私钥
    ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
        serialization.load_pem_private_key(
            ED25519_PRIVATE_KEY_PEM.encode('utf-8'),
            password=None
        ).private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
    )
    
    # 测试不同的端点
    endpoints = [
        ("账户信息", "GET", "https://fapi.binance.com/fapi/v2/account"),
        ("持仓信息", "GET", "https://fapi.binance.com/fapi/v2/positionRisk"),
        ("余额信息", "GET", "https://fapi.binance.com/fapi/v2/balance"),
    ]
    
    for name, method, base_url in endpoints:
        print(f"\n测试: {name}")
        print("-" * 40)
        
        timestamp = int(time.time() * 1000)
        query_string = f"timestamp={timestamp}"
        
        # 生成签名
        signature_bytes = ed25519_private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        url = f"{base_url}?{query_string}&signature={signature_base64}"
        headers = {'X-MBX-APIKEY': api_key}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    result = await response.json()
                    
                    if response.status == 200:
                        print(f"✅ {name} 成功")
                    else:
                        print(f"❌ {name} 失败: {result}")
                        
        except Exception as e:
            print(f"❌ {name} 异常: {e}")

async def main():
    print("🔍 验证API密钥和私钥匹配性")
    print("="*60)
    
    # 基本验证
    success = await verify_key_pair()
    
    if success:
        print("\n🎉 密钥对验证成功！")
        print("继续测试其他端点...")
        await test_different_endpoints()
    else:
        print("\n❌ 密钥对验证失败")
        print("可能的原因:")
        print("1. API密钥不是Ed25519类型")
        print("2. 私钥与API密钥不匹配")
        print("3. API权限不足")

if __name__ == "__main__":
    asyncio.run(main())
