package main

import (
	"log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
)

// WebSocket连接状态
type WSConnectionState string

const (
	WSStateDisconnected WSConnectionState = "disconnected"
	WSStateConnecting   WSConnectionState = "connecting"
	WSStateConnected    WSConnectionState = "connected"
	WSStateReconnecting WSConnectionState = "reconnecting"
	WSStateClosed       WSConnectionState = "closed"
)

// 订单状态
type OrderState string

const (
	OrderStateIdle      OrderState = "idle"
	OrderStatePlacing   OrderState = "placing"
	OrderStateSubmitted OrderState = "submitted" // 新增：已提交到交易所，等待确认
	OrderStateActive    OrderState = "active"
	OrderStateModifying OrderState = "modifying"
	OrderStateCompleted OrderState = "completed"
	OrderStateFailed    OrderState = "failed"
)

// WebSocket连接管理器
type WSConnection struct {
	conn           *websocket.Conn
	url            string
	state          WSConnectionState
	connectedAt    time.Time
	lastPing       time.Time
	lastPong       time.Time
	reconnectCount int
	maxReconnects  int
	mutex          sync.RWMutex
	stopChan       chan struct{}
	reconnectChan  chan struct{}
}

// 初始化配置
type InitializationConfig struct {
	MaxRetries            int           `json:"max_retries"`            // 最大重试次数
	RetryInterval         time.Duration `json:"retry_interval"`         // 重试间隔
	ConnectionTimeout     time.Duration `json:"connection_timeout"`     // 连接超时
	AuthenticationTimeout time.Duration `json:"authentication_timeout"` // 认证超时
	ListenKeyTimeout      time.Duration `json:"listen_key_timeout"`     // listenKey创建超时
	EnableAsyncInit       bool          `json:"enable_async_init"`      // 启用异步初始化
	EnableRollback        bool          `json:"enable_rollback"`        // 启用失败回滚
}

// 初始化步骤
type InitStep int

const (
	InitStepTradingWebSocket  InitStep = iota // 交易WebSocket连接
	InitStepAuthentication                    // 会话认证
	InitStepListenKey                         // listenKey创建
	InitStepUserDataWebSocket                 // 用户数据WebSocket连接
	InitStepComplete                          // 初始化完成
)

// 初始化状态
type InitStatus int

const (
	InitStatusPending    InitStatus = iota // 等待中
	InitStatusInProgress                   // 进行中
	InitStatusSuccess                      // 成功
	InitStatusFailed                       // 失败
	InitStatusRolledBack                   // 已回滚
)

// 初始化结果
type InitResult struct {
	Status      InitStatus        `json:"status"`
	Step        InitStep          `json:"step"`
	Error       error             `json:"error,omitempty"`
	StartTime   time.Time         `json:"start_time"`
	EndTime     time.Time         `json:"end_time,omitempty"`
	Duration    time.Duration     `json:"duration,omitempty"`
	RetryCount  int               `json:"retry_count"`
	StepResults map[InitStep]bool `json:"step_results"`
}

// 智能订单配置
type SmartOrderConfig struct {
	OrderTimeout         time.Duration        `json:"order_timeout"`          // 订单超时时间
	MaxRetries           int                  `json:"max_retries"`            // 最大重试次数
	ModifyThreshold      float64              `json:"modify_threshold"`       // 修改阈值
	MaxModifyCount       int                  `json:"max_modify_count"`       // 最大修改次数
	ModifyInterval       time.Duration        `json:"modify_interval"`        // 最小修改间隔
	ReconnectInterval    time.Duration        `json:"reconnect_interval"`     // 重连间隔
	ListenKeyRenewal     time.Duration        `json:"listen_key_renewal"`     // listenKey续期间隔
	MaxReconnectAttempts int                  `json:"max_reconnect_attempts"` // 最大重连次数
	PingInterval         time.Duration        `json:"ping_interval"`          // ping间隔
	PongInterval         time.Duration        `json:"pong_interval"`          // pong间隔
	HealthCheckInterval  time.Duration        `json:"health_check_interval"`  // 健康检查间隔
	SilenceTimeout       time.Duration        `json:"silence_timeout"`        // 静默超时
	ChannelBufferSize    int                  `json:"channel_buffer_size"`    // 通道缓冲区大小
	InitConfig           InitializationConfig `json:"init_config"`            // 初始化配置
}

// 默认智能订单配置
func DefaultSmartOrderConfig() SmartOrderConfig {
	return SmartOrderConfig{
		OrderTimeout:         3 * time.Second,
		MaxRetries:           3,
		ModifyThreshold:      0.000001, // 0.01%
		MaxModifyCount:       200,
		ModifyInterval:       10 * time.Millisecond,
		ReconnectInterval:    1 * time.Second,   // 默认1秒重连间隔
		ListenKeyRenewal:     20 * time.Minute,  // 默认20分钟续期
		MaxReconnectAttempts: 10,                // 默认最大重连10次
		PingInterval:         20 * time.Second,  // 默认20秒ping间隔
		PongInterval:         30 * time.Second,  // 默认30秒pong间隔
		HealthCheckInterval:  5 * time.Second,   // 默认5秒健康检查
		SilenceTimeout:       120 * time.Second, // 默认2分钟静默超时
		ChannelBufferSize:    1000,              // 默认1000缓冲区大小
		InitConfig: InitializationConfig{
			MaxRetries:            3,                // 默认最大重试3次
			RetryInterval:         2 * time.Second,  // 默认重试间隔2秒
			ConnectionTimeout:     10 * time.Second, // 默认连接超时10秒
			AuthenticationTimeout: 5 * time.Second,  // 默认认证超时5秒
			ListenKeyTimeout:      5 * time.Second,  // 默认listenKey创建超时5秒
			EnableAsyncInit:       true,             // 默认启用异步初始化
			EnableRollback:        true,             // 默认启用失败回滚
		},
	}
}

// 从嵌入式配置创建智能订单配置
func SmartOrderConfigFromEmbedded() SmartOrderConfig {
	// 从嵌入式配置获取WebSocket参数
	embeddedConfig := GetEmbeddedConfig()

	log.Printf("🔧 [SmartOrderConfigFromEmbedded] 嵌入式配置读取:")
	log.Printf("   - MaxRetries: %d", embeddedConfig.Initialization.MaxRetries)
	log.Printf("   - RetryIntervalMs: %d", embeddedConfig.Initialization.RetryIntervalMs)
	log.Printf("   - EnableAsyncInit: %t", embeddedConfig.Initialization.EnableAsyncInit)
	log.Printf("   - EnableRollback: %t", embeddedConfig.Initialization.EnableRollback)

	config := SmartOrderConfig{
		OrderTimeout:         3 * time.Second,
		MaxRetries:           3,
		ModifyThreshold:      0.000001, // 0.01%
		MaxModifyCount:       200,
		ModifyInterval:       10 * time.Millisecond,
		ReconnectInterval:    time.Duration(embeddedConfig.WebSocket.ReconnectIntervalMs) * time.Millisecond,
		ListenKeyRenewal:     time.Duration(embeddedConfig.WebSocket.ListenKeyRenewalMs) * time.Millisecond,
		MaxReconnectAttempts: embeddedConfig.WebSocket.MaxReconnectAttempts,
		PingInterval:         time.Duration(embeddedConfig.WebSocket.PingIntervalMs) * time.Millisecond,
		PongInterval:         time.Duration(embeddedConfig.WebSocket.PongIntervalMs) * time.Millisecond,
		HealthCheckInterval:  time.Duration(embeddedConfig.WebSocket.HealthCheckMs) * time.Millisecond,
		SilenceTimeout:       time.Duration(embeddedConfig.WebSocket.SilenceTimeoutMs) * time.Millisecond,
		ChannelBufferSize:    embeddedConfig.WebSocket.ChannelBufferSize,
		InitConfig: InitializationConfig{
			MaxRetries:            embeddedConfig.Initialization.MaxRetries,
			RetryInterval:         time.Duration(embeddedConfig.Initialization.RetryIntervalMs) * time.Millisecond,
			ConnectionTimeout:     time.Duration(embeddedConfig.Initialization.ConnectionTimeoutMs) * time.Millisecond,
			AuthenticationTimeout: time.Duration(embeddedConfig.Initialization.AuthenticationTimeoutMs) * time.Millisecond,
			ListenKeyTimeout:      time.Duration(embeddedConfig.Initialization.ListenKeyTimeoutMs) * time.Millisecond,
			EnableAsyncInit:       embeddedConfig.Initialization.EnableAsyncInit,
			EnableRollback:        embeddedConfig.Initialization.EnableRollback,
		},
	}

	log.Printf("🔧 [SmartOrderConfigFromEmbedded] 生成的配置:")
	log.Printf("   - InitConfig.MaxRetries: %d", config.InitConfig.MaxRetries)
	log.Printf("   - InitConfig.RetryInterval: %v", config.InitConfig.RetryInterval)
	log.Printf("   - InitConfig.EnableAsyncInit: %t", config.InitConfig.EnableAsyncInit)
	log.Printf("   - InitConfig.EnableRollback: %t", config.InitConfig.EnableRollback)

	return config
}

// 待处理请求
type PendingRequest struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Timestamp     time.Time              `json:"timestamp"`
	Data          map[string]interface{} `json:"data"`
	RetryCount    int                    `json:"retry_count"`
	ClientOrderID string                 `json:"client_order_id,omitempty"`
}

// 订单跟踪器
type OrderTracker struct {
	ClientOrderID  string          `json:"client_order_id"`
	OrderID        int64           `json:"order_id,omitempty"`
	State          OrderState      `json:"state"`
	Side           string          `json:"side"`
	Quantity       decimal.Decimal `json:"quantity"`
	Price          decimal.Decimal `json:"price,omitempty"`
	RequestTime    time.Time       `json:"request_time"`
	ConfirmTime    time.Time       `json:"confirm_time,omitempty"`
	ModifyCount    int             `json:"modify_count"`
	LastModifyTime time.Time       `json:"last_modify_time,omitempty"`
	CompletedTime  time.Time       `json:"completed_time,omitempty"`
	RetryCount     int             `json:"retry_count"`
}

// WebSocket API请求
type WSRequest struct {
	ID     string                 `json:"id"`
	Method string                 `json:"method"`
	Params map[string]interface{} `json:"params"`
}

// WebSocket API响应
type WSResponse struct {
	ID         string                 `json:"id"`
	Status     int                    `json:"status"`
	Result     map[string]interface{} `json:"result,omitempty"`
	Error      *WSError               `json:"error,omitempty"`
	RateLimits []RateLimit            `json:"rateLimits,omitempty"`
}

// WebSocket错误
type WSError struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// 速率限制
type RateLimit struct {
	RateLimitType string `json:"rateLimitType"`
	Interval      string `json:"interval"`
	IntervalNum   int    `json:"intervalNum"`
	Limit         int    `json:"limit"`
	Count         int    `json:"count"`
}

// 用户数据流事件
type UserDataEvent struct {
	EventType string                 `json:"e"`
	EventTime int64                  `json:"E"`
	Data      map[string]interface{} `json:",inline"`
}

// 订单更新事件
type OrderUpdateEvent struct {
	EventType       string          `json:"e"`  // ORDER_TRADE_UPDATE
	EventTime       int64           `json:"E"`  // 事件时间
	TransactionTime int64           `json:"T"`  // 撮合时间
	Symbol          string          `json:"s"`  // 交易对
	ClientOrderID   string          `json:"c"`  // 客户端订单ID
	Side            string          `json:"S"`  // 订单方向
	OrderType       string          `json:"o"`  // 订单类型
	TimeInForce     string          `json:"f"`  // 有效方式
	OrigQty         decimal.Decimal `json:"q"`  // 订单原始数量
	Price           decimal.Decimal `json:"p"`  // 订单价格
	AvgPrice        decimal.Decimal `json:"ap"` // 订单平均价格
	StopPrice       decimal.Decimal `json:"sp"` // 条件订单触发价格
	ExecutionType   string          `json:"x"`  // 本次事件的具体执行类型
	OrderStatus     string          `json:"X"`  // 订单的当前状态
	OrderID         int64           `json:"i"`  // 订单ID
	LastFilledQty   decimal.Decimal `json:"l"`  // 订单末次成交数量
	AccumulatedQty  decimal.Decimal `json:"z"`  // 订单累计已成交数量
	LastFilledPrice decimal.Decimal `json:"L"`  // 订单末次成交价格
	CommissionAsset string          `json:"N"`  // 手续费资产类别
	Commission      decimal.Decimal `json:"n"`  // 手续费数量
	OrderTradeTime  int64           `json:"OT"` // 成交时间
	TradeID         int64           `json:"t"`  // 成交ID
	BidsNotional    decimal.Decimal `json:"b"`  // 买单净值
	AsksNotional    decimal.Decimal `json:"a"`  // 卖单净值
	IsMaker         bool            `json:"m"`  // 该成交是作为挂单成交吗？
	IsReduceOnly    bool            `json:"R"`  // 是否为只减仓单
	WorkingType     string          `json:"wt"` // 触发价类型
	OrigOrderType   string          `json:"ot"` // 原始订单类型
	PositionSide    string          `json:"ps"` // 持仓方向
	CloseAll        bool            `json:"cp"` // 是否为触发平仓单
	ActivationPrice decimal.Decimal `json:"AP"` // 跟踪止损激活价格
	CallbackRate    decimal.Decimal `json:"cr"` // 跟踪止损回调比例
	RealizedPnl     decimal.Decimal `json:"rp"` // 该交易实现盈亏
}

// listenKey管理器
type ListenKeyManager struct {
	listenKey   string
	createdAt   time.Time
	lastRenewal time.Time
	mutex       sync.RWMutex
}

// 市场数据订阅者
type MarketDataSubscriber struct {
	TaskID  string
	Channel chan *OrderBookData
}

// WebSocket事件类型
type WSEventType string

const (
	WSEventTradingResponse WSEventType = "trading_response"
	WSEventUserData        WSEventType = "user_data"
	WSEventOrderBook       WSEventType = "order_book"
	WSEventTimeout         WSEventType = "timeout"
	WSEventError           WSEventType = "error"
	WSEventReconnect       WSEventType = "reconnect"
)

// WebSocket事件
type WSEvent struct {
	Type      WSEventType            `json:"type"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
	Source    string                 `json:"source"` // 事件来源（trading/userdata/orderbook）
}

// 智能订单统计
type SmartOrderStats struct {
	TotalOrders      int64           `json:"total_orders"`
	SuccessfulOrders int64           `json:"successful_orders"`
	FailedOrders     int64           `json:"failed_orders"`
	TotalModifies    int64           `json:"total_modifies"`
	AvgResponseTime  time.Duration   `json:"avg_response_time"`
	AvgModifyCount   float64         `json:"avg_modify_count"`
	SlippageSaved    decimal.Decimal `json:"slippage_saved"`
	FeeSaved         decimal.Decimal `json:"fee_saved"`
	LastUpdateTime   time.Time       `json:"last_update_time"`
}

// 连接质量监控
type ConnectionQuality struct {
	ConnectedAt        time.Time     `json:"connected_at"`
	LastMessageTime    time.Time     `json:"last_message_time"`
	LastPingTime       time.Time     `json:"last_ping_time"`
	LastPongTime       time.Time     `json:"last_pong_time"`
	MessageCount       int64         `json:"message_count"`
	ReconnectCount     int           `json:"reconnect_count"`
	AvgLatency         time.Duration `json:"avg_latency"`
	MaxSilenceDuration time.Duration `json:"max_silence_duration"`
	IsHealthy          bool          `json:"is_healthy"`
	mutex              sync.RWMutex
}

// 更新连接质量统计
func (cq *ConnectionQuality) UpdateMessageReceived() {
	cq.mutex.Lock()
	defer cq.mutex.Unlock()

	now := time.Now()
	cq.LastMessageTime = now
	cq.MessageCount++

	// 计算静默时间
	if !cq.ConnectedAt.IsZero() {
		silenceDuration := now.Sub(cq.LastMessageTime)
		if silenceDuration > cq.MaxSilenceDuration {
			cq.MaxSilenceDuration = silenceDuration
		}
	}

	// 更新健康状态
	cq.IsHealthy = time.Since(cq.LastMessageTime) < 60*time.Second
}

// 更新ping统计
func (cq *ConnectionQuality) UpdatePingSent() {
	cq.mutex.Lock()
	defer cq.mutex.Unlock()
	cq.LastPingTime = time.Now()
}

// 更新pong统计
func (cq *ConnectionQuality) UpdatePongReceived() {
	cq.mutex.Lock()
	defer cq.mutex.Unlock()
	now := time.Now()
	cq.LastPongTime = now

	// 计算延迟
	if !cq.LastPingTime.IsZero() {
		latency := now.Sub(cq.LastPingTime)
		if cq.AvgLatency == 0 {
			cq.AvgLatency = latency
		} else {
			// 简单的移动平均
			cq.AvgLatency = (cq.AvgLatency + latency) / 2
		}
	}
}

// 更新重连统计
func (cq *ConnectionQuality) UpdateReconnect() {
	cq.mutex.Lock()
	defer cq.mutex.Unlock()
	cq.ReconnectCount++
	cq.IsHealthy = false
}

// 获取连接质量信息
func (cq *ConnectionQuality) GetQuality() (bool, time.Duration, int64) {
	cq.mutex.RLock()
	defer cq.mutex.RUnlock()
	return cq.IsHealthy, time.Since(cq.LastMessageTime), cq.MessageCount
}
