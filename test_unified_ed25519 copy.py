#!/usr/bin/env python3
"""
统一Ed25519方案测试程序
验证REST API和WebSocket API都使用同一个Ed25519密钥
"""

import asyncio
import websockets
import json
import time
import base64
import logging
import aiohttp
from cryptography.hazmat.primitives.asymmetric import ed25519
from cryptography.hazmat.primitives import serialization
from config import BINANCE_CONFIG
from ed25519_config import ED25519_PRIVATE_KEY_PEM

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedEd25519Tester:
    def __init__(self):
        # 统一使用Ed25519密钥
        self.api_key = BINANCE_CONFIG.get("ws_api_key", "your_ed25519_api_key_here")
        
        # 加载Ed25519私钥
        self.ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
            serialization.load_pem_private_key(
                ED25519_PRIVATE_KEY_PEM.encode('utf-8'),
                password=None
            ).private_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PrivateFormat.Raw,
                encryption_algorithm=serialization.NoEncryption()
            )
        )

        # 调试：显示私钥信息
        private_key_hex = self.ed25519_private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        ).hex()
        logger.info(f"🔍 Python程序使用的私钥 (Hex): {private_key_hex}")
        
        # 端点配置
        self.rest_base = "https://fapi.binance.com"
        self.ws_api = "wss://ws-fapi.binance.com/ws-fapi/v1"
        
        self.request_id = 0
    
    def generate_ed25519_signature_rest(self, params: dict) -> str:
        """生成Ed25519签名（REST API格式 - 不排序）"""
        # 构建查询字符串（不排序，保持原始顺序）
        query_parts = []
        for key, value in params.items():
            if key != "signature":
                query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 生成Ed25519签名
        signature_bytes = self.ed25519_private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        logger.info(f"🔍 REST签名字符串: {query_string}")
        logger.info(f"🔍 REST Ed25519签名: {signature_base64}")
        
        return signature_base64
    
    def generate_ed25519_signature_websocket(self, params: dict) -> str:
        """生成Ed25519签名（WebSocket API格式 - 排序）"""
        # 排除signature参数并排序
        filtered_params = {k: v for k, v in params.items() if k != 'signature'}
        sorted_params = sorted(filtered_params.items())
        
        # 构建查询字符串
        query_parts = []
        for key, value in sorted_params:
            query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 生成Ed25519签名
        signature_bytes = self.ed25519_private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        logger.debug(f"WebSocket签名字符串: {query_string}")
        logger.debug(f"WebSocket Ed25519签名: {signature_base64}")
        
        return signature_base64
    
    async def test_rest_api_ed25519(self):
        """测试REST API使用Ed25519签名"""
        logger.info("🔍 测试REST API使用Ed25519签名...")
        
        if self.api_key == "your_ed25519_api_key_here":
            logger.error("❌ 请先配置Ed25519 API密钥")
            return False
        
        timestamp = int(time.time() * 1000)
        
        # REST API参数（保持原始顺序）
        from collections import OrderedDict
        params = OrderedDict([
            ("symbol", "ETHUSDT"),
            ("side", "BUY"),
            ("type", "LIMIT"),
            ("quantity", "0.01"),
            ("price", "2400.00"),
            ("timeInForce", "GTC"),
            ("newClientOrderId", f"rest_ed25519_test_{timestamp}"),
            ("timestamp", timestamp),
            ("test", "true")  # 测试模式
        ])
        
        # 生成Ed25519签名（REST格式）
        signature = self.generate_ed25519_signature_rest(params)
        params["signature"] = signature
        
        # 发送REST API请求
        url = f"{self.rest_base}/fapi/v1/order"
        headers = {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=params) as response:
                    result = await response.json()
                    
                    logger.info(f"📥 REST API Ed25519响应: {result}")
                    
                    if response.status == 200:
                        logger.info("✅ REST API Ed25519签名成功")
                        return True
                    else:
                        logger.error(f"❌ REST API Ed25519签名失败: {result}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ REST API Ed25519测试异常: {e}")
            return False
    
    async def test_websocket_session_ed25519(self):
        """测试WebSocket会话使用Ed25519签名"""
        logger.info("🔍 测试WebSocket会话使用Ed25519签名...")
        
        if self.api_key == "your_ed25519_api_key_here":
            logger.error("❌ 请先配置Ed25519 API密钥")
            return False, None
        
        try:
            ws = await websockets.connect(self.ws_api)
            logger.info("✅ WebSocket连接成功")
            
            # 构建登录请求
            timestamp = int(time.time() * 1000)
            login_params = {
                "apiKey": self.api_key,
                "timestamp": timestamp
            }
            
            # 生成Ed25519签名（WebSocket格式）
            signature = self.generate_ed25519_signature_websocket(login_params)
            login_params["signature"] = signature
            
            login_request = {
                "id": f"unified_login_{timestamp}",
                "method": "session.logon",
                "params": login_params
            }
            
            logger.info(f"📤 发送WebSocket登录请求（统一Ed25519签名）")
            await ws.send(json.dumps(login_request))
            
            # 等待登录响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)
            
            logger.info(f"📥 WebSocket登录响应: {data}")
            
            if data.get('status') == 200:
                logger.info("✅ WebSocket Ed25519会话登录成功")
                return True, ws
            else:
                error = data.get('error', {})
                logger.error(f"❌ WebSocket Ed25519会话登录失败: {error}")
                await ws.close()
                return False, None
                
        except Exception as e:
            logger.error(f"❌ WebSocket Ed25519登录测试异常: {e}")
            return False, None
    
    async def test_websocket_authenticated_order_ed25519(self, ws):
        """测试已认证WebSocket下单（统一Ed25519）"""
        logger.info("🔍 测试已认证WebSocket下单（统一Ed25519）...")
        
        try:
            timestamp = int(time.time() * 1000)
            
            # 构建下单请求（已认证会话，添加timestamp）
            order_params = {
                "symbol": "ETHUSDT",
                "side": "BUY",
                "type": "LIMIT",
                "quantity": "0.01",
                # "price": "2500.00",
                "priceMatch": "QUEUE_20",
                "timeInForce": "GTX",
                "newClientOrderId": f"ws_ed25519_test_{timestamp}",
                "timestamp": timestamp,  # 添加timestamp
                "test": True  # 测试模式
            }
            
            order_request = {
                "id": f"unified_order_{timestamp}",
                "method": "order.place",
                "params": order_params
            }
            
            logger.info(f"📤 发送WebSocket下单请求（已认证，无需签名）")
            await ws.send(json.dumps(order_request))
            
            # 等待下单响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)
            
            logger.info(f"📥 WebSocket下单响应: {data}")
            
            if data.get('status') == 200:
                logger.info("✅ WebSocket已认证下单成功（统一Ed25519）")
                return True
            else:
                error = data.get('error', {})
                logger.error(f"❌ WebSocket已认证下单失败: {error}")
                return False
                
        except Exception as e:
            logger.error(f"❌ WebSocket下单测试异常: {e}")
            return False
    
    async def run_unified_test(self):
        """运行统一Ed25519方案测试"""
        logger.info("🚀 开始统一Ed25519方案综合测试")
        logger.info("验证REST API和WebSocket API使用同一个Ed25519密钥")
        logger.info("=" * 80)
        
        results = {}
        
        # 测试1: REST API Ed25519签名
        logger.info("\n📋 测试1: REST API使用Ed25519签名")
        rest_success = await self.test_rest_api_ed25519()
        results['rest_api_ed25519'] = rest_success
        
        # 测试2: WebSocket会话Ed25519登录
        logger.info("\n📋 测试2: WebSocket会话使用Ed25519签名")
        ws_login_success, authenticated_ws = await self.test_websocket_session_ed25519()
        results['websocket_ed25519_login'] = ws_login_success
        
        # 测试3: 已认证WebSocket下单
        if ws_login_success and authenticated_ws:
            logger.info("\n📋 测试3: 已认证WebSocket下单")
            ws_order_success = await self.test_websocket_authenticated_order_ed25519(authenticated_ws)
            results['websocket_authenticated_order'] = ws_order_success
            
            # 关闭WebSocket连接
            await authenticated_ws.close()
        else:
            results['websocket_authenticated_order'] = False
        
        # 输出测试结果
        logger.info("\n" + "=" * 80)
        logger.info("📊 统一Ed25519方案测试结果:")
        logger.info("=" * 80)
        
        test_descriptions = {
            'rest_api_ed25519': 'REST API (Ed25519)',
            'websocket_ed25519_login': 'WebSocket登录 (Ed25519)',
            'websocket_authenticated_order': 'WebSocket下单 (已认证)'
        }
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            description = test_descriptions.get(test_name, test_name)
            logger.info(f"{description:30} : {status}")
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        logger.info("=" * 80)
        logger.info(f"总测试数: {total_tests}, 通过: {passed_tests}, 失败: {total_tests - passed_tests}")
        
        # 分析结果
        if passed_tests == total_tests:
            logger.info("🎉 统一Ed25519方案完全成功!")
            logger.info("💡 Go程序可以使用统一架构:")
            logger.info("   - 单一Ed25519 API密钥")
            logger.info("   - REST API和WebSocket API共用")
            logger.info("   - 保持现有程序逻辑")
            logger.info("   - 最佳性能和安全性")
            return True
        else:
            logger.info("❌ 统一Ed25519方案需要配置")
            
            if not results.get('rest_api_ed25519'):
                logger.info("   - REST API Ed25519签名需要调试")
            
            if not results.get('websocket_ed25519_login'):
                logger.info("   - WebSocket Ed25519登录需要调试")
            
            return False

async def main():
    """主函数"""
    print("🔧 统一Ed25519方案测试程序")
    print("验证REST API和WebSocket API使用同一个Ed25519密钥")
    print("=" * 70)
    
    tester = UnifiedEd25519Tester()
    success = await tester.run_unified_test()
    
    if success:
        print("\n🎯 Go程序实施方案:")
        print("1. 使用单一Ed25519密钥")
        print("2. REST API: Ed25519签名（不排序参数）")
        print("3. WebSocket API: Ed25519签名（排序参数）")
        print("4. 保持现有智能订单逻辑")
        print("5. 修复side参数格式（long→BUY）")
    else:
        print("\n📋 需要完成的配置:")
        print("1. 确保Ed25519 API密钥正确配置")
        print("2. 检查API权限设置")
        print("3. 重新运行测试")

if __name__ == "__main__":
    try:
        import aiohttp
        import websockets
        from cryptography.hazmat.primitives.asymmetric import ed25519
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install aiohttp websockets cryptography")
        exit(1)
    
    asyncio.run(main())
