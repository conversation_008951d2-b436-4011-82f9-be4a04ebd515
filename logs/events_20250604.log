2025/06/04 00:56:20 main.go:109: 启动本地测试模式...
2025/06/04 00:56:20 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 00:56:20 main.go:356: 
2025/06/04 00:56:20 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/04 00:56:20 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 00:56:20 main.go:360:       示例: add long 3500 0.0005
2025/06/04 00:56:20 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 00:56:20 main.go:362: 
2025/06/04 00:56:20 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/04 00:56:20 main.go:364:    list                                     - 列出所有任务
2025/06/04 00:56:20 main.go:365: 
2025/06/04 00:56:20 main.go:366: ⚙️ 参数配置:
2025/06/04 00:56:20 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/04 00:56:20 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 00:56:20 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/04 00:56:20 main.go:370: 
2025/06/04 00:56:20 main.go:371: 📊 状态控制:
2025/06/04 00:56:20 main.go:372:    status                                   - 查看状态输出设置
2025/06/04 00:56:20 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 00:56:20 main.go:374:    status off                               - 停止状态输出
2025/06/04 00:56:20 main.go:375: 
2025/06/04 00:56:20 main.go:376: 📋 数据导出:
2025/06/04 00:56:20 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/04 00:56:20 main.go:378: 
2025/06/04 00:56:20 main.go:379: 💾 数据管理:
2025/06/04 00:56:20 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/04 00:56:20 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 00:56:20 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 00:56:20 main.go:383: 
2025/06/04 00:56:20 main.go:384: 📋 事件查看:
2025/06/04 00:56:20 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 00:56:20 main.go:386: 
2025/06/04 00:56:20 main.go:387: 🎛️  批量控制:
2025/06/04 00:56:20 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/04 00:56:20 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/04 00:56:20 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 00:56:20 main.go:391: 
2025/06/04 00:56:20 main.go:392: 🗑️  任务管理:
2025/06/04 00:56:20 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 00:56:20 main.go:394: 
2025/06/04 00:56:20 main.go:395: 🔧 其他:
2025/06/04 00:56:20 main.go:396:    help                                     - 显示此帮助信息
2025/06/04 00:56:20 main.go:397:    quit | exit                              - 退出程序
2025/06/04 00:56:20 main.go:398: ========================
2025/06/04 00:56:20 main.go:399: 
2025/06/04 00:56:22 main.go:661: 📋 当前没有运行的任务
2025/06/04 00:57:23 main.go:109: 启动本地测试模式...
2025/06/04 00:57:23 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 00:57:23 main.go:356: 
2025/06/04 00:57:23 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/04 00:57:23 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 00:57:23 main.go:360:       示例: add long 3500 0.0005
2025/06/04 00:57:23 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 00:57:23 main.go:362: 
2025/06/04 00:57:23 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/04 00:57:23 main.go:364:    list                                     - 列出所有任务
2025/06/04 00:57:23 main.go:365: 
2025/06/04 00:57:23 main.go:366: ⚙️ 参数配置:
2025/06/04 00:57:23 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/04 00:57:23 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 00:57:23 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/04 00:57:23 main.go:370: 
2025/06/04 00:57:23 main.go:371: 📊 状态控制:
2025/06/04 00:57:23 main.go:372:    status                                   - 查看状态输出设置
2025/06/04 00:57:23 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 00:57:23 main.go:374:    status off                               - 停止状态输出
2025/06/04 00:57:23 main.go:375: 
2025/06/04 00:57:23 main.go:376: 📋 数据导出:
2025/06/04 00:57:23 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/04 00:57:23 main.go:378: 
2025/06/04 00:57:23 main.go:379: 💾 数据管理:
2025/06/04 00:57:23 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/04 00:57:23 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 00:57:23 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 00:57:23 main.go:383: 
2025/06/04 00:57:23 main.go:384: 📋 事件查看:
2025/06/04 00:57:23 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 00:57:23 main.go:386: 
2025/06/04 00:57:23 main.go:387: 🎛️  批量控制:
2025/06/04 00:57:23 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/04 00:57:23 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/04 00:57:23 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 00:57:23 main.go:391: 
2025/06/04 00:57:23 main.go:392: 🗑️  任务管理:
2025/06/04 00:57:23 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 00:57:23 main.go:394: 
2025/06/04 00:57:23 main.go:395: 🔧 其他:
2025/06/04 00:57:23 main.go:396:    help                                     - 显示此帮助信息
2025/06/04 00:57:23 main.go:397:    quit | exit                              - 退出程序
2025/06/04 00:57:23 main.go:398: ========================
2025/06/04 00:57:23 main.go:399: 
2025/06/04 00:57:35 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/04 00:57:47 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/04 04:22:05 main.go:109: 启动本地测试模式...
2025/06/04 04:22:05 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 04:22:05 main.go:356: 
2025/06/04 04:22:05 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/04 04:22:05 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 04:22:05 main.go:360:       示例: add long 3500 0.0005
2025/06/04 04:22:05 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 04:22:05 main.go:362: 
2025/06/04 04:22:05 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/04 04:22:05 main.go:364:    list                                     - 列出所有任务
2025/06/04 04:22:05 main.go:365: 
2025/06/04 04:22:05 main.go:366: ⚙️ 参数配置:
2025/06/04 04:22:05 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/04 04:22:05 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 04:22:05 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/04 04:22:05 main.go:370: 
2025/06/04 04:22:05 main.go:371: 📊 状态控制:
2025/06/04 04:22:05 main.go:372:    status                                   - 查看状态输出设置
2025/06/04 04:22:05 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 04:22:05 main.go:374:    status off                               - 停止状态输出
2025/06/04 04:22:05 main.go:375: 
2025/06/04 04:22:05 main.go:376: 📋 数据导出:
2025/06/04 04:22:05 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/04 04:22:05 main.go:378: 
2025/06/04 04:22:05 main.go:379: 💾 数据管理:
2025/06/04 04:22:05 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/04 04:22:05 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 04:22:05 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 04:22:05 main.go:383: 
2025/06/04 04:22:05 main.go:384: 📋 事件查看:
2025/06/04 04:22:05 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 04:22:05 main.go:386: 
2025/06/04 04:22:05 main.go:387: 🎛️  批量控制:
2025/06/04 04:22:05 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/04 04:22:05 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/04 04:22:05 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 04:22:05 main.go:391: 
2025/06/04 04:22:05 main.go:392: 🗑️  任务管理:
2025/06/04 04:22:05 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 04:22:05 main.go:394: 
2025/06/04 04:22:05 main.go:395: 🔧 其他:
2025/06/04 04:22:05 main.go:396:    help                                     - 显示此帮助信息
2025/06/04 04:22:05 main.go:397:    quit | exit                              - 退出程序
2025/06/04 04:22:05 main.go:398: ========================
2025/06/04 04:22:05 main.go:399: 
2025/06/04 04:23:26 main.go:109: 启动本地测试模式...
2025/06/04 04:23:26 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 04:23:26 main.go:356: 
2025/06/04 04:23:26 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/04 04:23:26 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 04:23:26 main.go:360:       示例: add long 3500 0.0005
2025/06/04 04:23:26 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 04:23:26 main.go:362: 
2025/06/04 04:23:26 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/04 04:23:26 main.go:364:    list                                     - 列出所有任务
2025/06/04 04:23:26 main.go:365: 
2025/06/04 04:23:26 main.go:366: ⚙️ 参数配置:
2025/06/04 04:23:26 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/04 04:23:26 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 04:23:26 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/04 04:23:26 main.go:370: 
2025/06/04 04:23:26 main.go:371: 📊 状态控制:
2025/06/04 04:23:26 main.go:372:    status                                   - 查看状态输出设置
2025/06/04 04:23:26 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 04:23:26 main.go:374:    status off                               - 停止状态输出
2025/06/04 04:23:26 main.go:375: 
2025/06/04 04:23:26 main.go:376: 📋 数据导出:
2025/06/04 04:23:26 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/04 04:23:26 main.go:378: 
2025/06/04 04:23:26 main.go:379: 💾 数据管理:
2025/06/04 04:23:26 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/04 04:23:26 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 04:23:26 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 04:23:26 main.go:383: 
2025/06/04 04:23:26 main.go:384: 📋 事件查看:
2025/06/04 04:23:26 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 04:23:26 main.go:386: 
2025/06/04 04:23:26 main.go:387: 🎛️  批量控制:
2025/06/04 04:23:26 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/04 04:23:26 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/04 04:23:26 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 04:23:26 main.go:391: 
2025/06/04 04:23:26 main.go:392: 🗑️  任务管理:
2025/06/04 04:23:26 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 04:23:26 main.go:394: 
2025/06/04 04:23:26 main.go:395: 🔧 其他:
2025/06/04 04:23:26 main.go:396:    help                                     - 显示此帮助信息
2025/06/04 04:23:26 main.go:397:    quit | exit                              - 退出程序
2025/06/04 04:23:26 main.go:398: ========================
2025/06/04 04:23:26 main.go:399: 
2025/06/04 04:24:01 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/04 04:24:07 main.go:109: 启动本地测试模式...
2025/06/04 04:24:07 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 04:24:07 main.go:356: 
2025/06/04 04:24:07 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/04 04:24:07 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 04:24:07 main.go:360:       示例: add long 3500 0.0005
2025/06/04 04:24:07 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 04:24:07 main.go:362: 
2025/06/04 04:24:07 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/04 04:24:07 main.go:364:    list                                     - 列出所有任务
2025/06/04 04:24:07 main.go:365: 
2025/06/04 04:24:07 main.go:366: ⚙️ 参数配置:
2025/06/04 04:24:07 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/04 04:24:07 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 04:24:07 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/04 04:24:07 main.go:370: 
2025/06/04 04:24:07 main.go:371: 📊 状态控制:
2025/06/04 04:24:07 main.go:372:    status                                   - 查看状态输出设置
2025/06/04 04:24:07 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 04:24:07 main.go:374:    status off                               - 停止状态输出
2025/06/04 04:24:07 main.go:375: 
2025/06/04 04:24:07 main.go:376: 📋 数据导出:
2025/06/04 04:24:07 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/04 04:24:07 main.go:378: 
2025/06/04 04:24:07 main.go:379: 💾 数据管理:
2025/06/04 04:24:07 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/04 04:24:07 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 04:24:07 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 04:24:07 main.go:383: 
2025/06/04 04:24:07 main.go:384: 📋 事件查看:
2025/06/04 04:24:07 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 04:24:07 main.go:386: 
2025/06/04 04:24:07 main.go:387: 🎛️  批量控制:
2025/06/04 04:24:07 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/04 04:24:07 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/04 04:24:07 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 04:24:07 main.go:391: 
2025/06/04 04:24:07 main.go:392: 🗑️  任务管理:
2025/06/04 04:24:07 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 04:24:07 main.go:394: 
2025/06/04 04:24:07 main.go:395: 🔧 其他:
2025/06/04 04:24:07 main.go:396:    help                                     - 显示此帮助信息
2025/06/04 04:24:07 main.go:397:    quit | exit                              - 退出程序
2025/06/04 04:24:07 main.go:398: ========================
2025/06/04 04:24:07 main.go:399: 
2025/06/04 04:24:11 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/04 06:23:40 main.go:109: 启动本地测试模式...
2025/06/04 06:23:40 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 06:23:40 main.go:356: 
2025/06/04 06:23:40 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/04 06:23:40 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 06:23:40 main.go:360:       示例: add long 3500 0.0005
2025/06/04 06:23:40 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 06:23:40 main.go:362: 
2025/06/04 06:23:40 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/04 06:23:40 main.go:364:    list                                     - 列出所有任务
2025/06/04 06:23:40 main.go:365: 
2025/06/04 06:23:40 main.go:366: ⚙️ 参数配置:
2025/06/04 06:23:40 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/04 06:23:40 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 06:23:40 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/04 06:23:40 main.go:370: 
2025/06/04 06:23:40 main.go:371: 📊 状态控制:
2025/06/04 06:23:40 main.go:372:    status                                   - 查看状态输出设置
2025/06/04 06:23:40 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 06:23:40 main.go:374:    status off                               - 停止状态输出
2025/06/04 06:23:40 main.go:375: 
2025/06/04 06:23:40 main.go:376: 📋 数据导出:
2025/06/04 06:23:40 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/04 06:23:40 main.go:378: 
2025/06/04 06:23:40 main.go:379: 💾 数据管理:
2025/06/04 06:23:40 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/04 06:23:40 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 06:23:40 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 06:23:40 main.go:383: 
2025/06/04 06:23:40 main.go:384: 📋 事件查看:
2025/06/04 06:23:40 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 06:23:40 main.go:386: 
2025/06/04 06:23:40 main.go:387: 🎛️  批量控制:
2025/06/04 06:23:40 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/04 06:23:40 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/04 06:23:40 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 06:23:40 main.go:391: 
2025/06/04 06:23:40 main.go:392: 🗑️  任务管理:
2025/06/04 06:23:40 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 06:23:40 main.go:394: 
2025/06/04 06:23:40 main.go:395: 🔧 其他:
2025/06/04 06:23:40 main.go:396:    help                                     - 显示此帮助信息
2025/06/04 06:23:40 main.go:397:    quit | exit                              - 退出程序
2025/06/04 06:23:40 main.go:398: ========================
2025/06/04 06:23:40 main.go:399: 
2025/06/04 06:23:44 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/04 14:16:20 main.go:109: 启动本地测试模式...
2025/06/04 14:16:20 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 14:16:20 main.go:356: 
2025/06/04 14:16:20 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/04 14:16:20 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 14:16:20 main.go:360:       示例: add long 3500 0.0005
2025/06/04 14:16:20 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 14:16:20 main.go:362: 
2025/06/04 14:16:20 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/04 14:16:20 main.go:364:    list                                     - 列出所有任务
2025/06/04 14:16:20 main.go:365: 
2025/06/04 14:16:20 main.go:366: ⚙️ 参数配置:
2025/06/04 14:16:20 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/04 14:16:20 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 14:16:20 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/04 14:16:20 main.go:370: 
2025/06/04 14:16:20 main.go:371: 📊 状态控制:
2025/06/04 14:16:20 main.go:372:    status                                   - 查看状态输出设置
2025/06/04 14:16:20 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 14:16:20 main.go:374:    status off                               - 停止状态输出
2025/06/04 14:16:20 main.go:375: 
2025/06/04 14:16:20 main.go:376: 📋 数据导出:
2025/06/04 14:16:20 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/04 14:16:20 main.go:378: 
2025/06/04 14:16:20 main.go:379: 💾 数据管理:
2025/06/04 14:16:20 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/04 14:16:20 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 14:16:20 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 14:16:20 main.go:383: 
2025/06/04 14:16:20 main.go:384: 📋 事件查看:
2025/06/04 14:16:20 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 14:16:20 main.go:386: 
2025/06/04 14:16:20 main.go:387: 🎛️  批量控制:
2025/06/04 14:16:20 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/04 14:16:20 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/04 14:16:20 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 14:16:20 main.go:391: 
2025/06/04 14:16:20 main.go:392: 🗑️  任务管理:
2025/06/04 14:16:20 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 14:16:20 main.go:394: 
2025/06/04 14:16:20 main.go:395: 🔧 其他:
2025/06/04 14:16:20 main.go:396:    help                                     - 显示此帮助信息
2025/06/04 14:16:20 main.go:397:    quit | exit                              - 退出程序
2025/06/04 14:16:20 main.go:398: ========================
2025/06/04 14:16:20 main.go:399: 
2025/06/04 14:16:25 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/04 15:46:39 main.go:105: 启动本地测试模式...
2025/06/04 15:46:39 main.go:106: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/04 15:46:39 main.go:384: 
2025/06/04 15:46:39 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 15:46:39 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 15:46:39 main.go:388:       示例: add long 3500 0.0005
2025/06/04 15:46:39 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 15:46:39 main.go:390: 
2025/06/04 15:46:39 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 15:46:39 main.go:392:    list                                     - 列出所有任务
2025/06/04 15:46:39 main.go:393: 
2025/06/04 15:46:39 main.go:394: ⚙️ 参数配置:
2025/06/04 15:46:39 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 15:46:39 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 15:46:39 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 15:46:39 main.go:398: 
2025/06/04 15:46:39 main.go:399: 📊 状态控制:
2025/06/04 15:46:39 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 15:46:39 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 15:46:39 main.go:402:    status off                               - 停止状态输出
2025/06/04 15:46:39 main.go:403: 
2025/06/04 15:46:39 main.go:404: 📋 数据导出:
2025/06/04 15:46:39 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 15:46:39 main.go:406: 
2025/06/04 15:46:39 main.go:407: 💾 数据管理:
2025/06/04 15:46:39 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 15:46:39 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 15:46:39 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 15:46:39 main.go:411: 
2025/06/04 15:46:39 main.go:412: 📋 事件查看:
2025/06/04 15:46:39 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 15:46:39 main.go:414: 
2025/06/04 15:46:39 main.go:415: 🎛️  批量控制:
2025/06/04 15:46:39 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 15:46:39 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 15:46:39 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 15:46:39 main.go:419: 
2025/06/04 15:46:39 main.go:420: 🗑️  任务管理:
2025/06/04 15:46:39 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 15:46:39 main.go:422: 
2025/06/04 15:46:39 main.go:423: 🔧 其他:
2025/06/04 15:46:39 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 15:46:39 main.go:425:    quit | exit                              - 退出程序
2025/06/04 15:46:39 main.go:426: ========================
2025/06/04 15:46:39 main.go:427: 
2025/06/04 15:46:48 main.go:126: 收到停止信号，正在停止所有任务...
2025/06/04 15:46:58 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 15:46:58 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 15:46:58 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 15:46:58 main.go:190: API接口:
2025/06/04 15:46:58 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 15:46:58 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 15:46:58 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 15:46:58 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 15:46:58 main.go:195: 期权系统API接口:
2025/06/04 15:46:58 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 15:46:58 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 15:46:58 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 15:46:58 main.go:384: 
2025/06/04 15:46:58 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 15:46:58 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 15:46:58 main.go:388:       示例: add long 3500 0.0005
2025/06/04 15:46:58 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 15:46:58 main.go:390: 
2025/06/04 15:46:58 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 15:46:58 main.go:392:    list                                     - 列出所有任务
2025/06/04 15:46:58 main.go:393: 
2025/06/04 15:46:58 main.go:394: ⚙️ 参数配置:
2025/06/04 15:46:58 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 15:46:58 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 15:46:58 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 15:46:58 main.go:398: 
2025/06/04 15:46:58 api.go:35: API服务器启动，端口: 5879
2025/06/04 15:46:58 main.go:399: 📊 状态控制:
2025/06/04 15:46:58 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 15:46:58 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 15:46:58 main.go:402:    status off                               - 停止状态输出
2025/06/04 15:46:58 main.go:403: 
2025/06/04 15:46:58 main.go:404: 📋 数据导出:
2025/06/04 15:46:58 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 15:46:58 main.go:406: 
2025/06/04 15:46:58 main.go:407: 💾 数据管理:
2025/06/04 15:46:58 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 15:46:58 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 15:46:58 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 15:46:58 main.go:411: 
2025/06/04 15:46:58 main.go:412: 📋 事件查看:
2025/06/04 15:46:58 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 15:46:58 main.go:414: 
2025/06/04 15:46:58 main.go:415: 🎛️  批量控制:
2025/06/04 15:46:58 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 15:46:58 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 15:46:58 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 15:46:58 main.go:419: 
2025/06/04 15:46:58 main.go:420: 🗑️  任务管理:
2025/06/04 15:46:58 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 15:46:58 main.go:422: 
2025/06/04 15:46:58 main.go:423: 🔧 其他:
2025/06/04 15:46:58 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 15:46:58 main.go:425:    quit | exit                              - 退出程序
2025/06/04 15:46:58 main.go:426: ========================
2025/06/04 15:46:58 main.go:427: 
2025/06/04 15:47:19 main.go:689: 📋 当前没有运行的任务
2025/06/04 16:32:49 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 16:32:49 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 17:06:14 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 17:06:14 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 17:11:14 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 17:11:14 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 17:16:14 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 17:16:14 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 17:21:14 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 17:21:14 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 17:26:14 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 17:26:14 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 17:26:18 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 17:49:19 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 17:49:19 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 17:49:19 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 17:49:19 main.go:190: API接口:
2025/06/04 17:49:19 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 17:49:19 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 17:49:19 api.go:35: API服务器启动，端口: 5879
2025/06/04 17:49:19 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 17:49:19 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 17:49:19 main.go:195: 期权系统API接口:
2025/06/04 17:49:19 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 17:49:19 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 17:49:19 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 17:49:19 main.go:384: 
2025/06/04 17:49:19 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 17:49:19 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 17:49:19 main.go:388:       示例: add long 3500 0.0005
2025/06/04 17:49:19 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 17:49:19 main.go:390: 
2025/06/04 17:49:19 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 17:49:19 main.go:392:    list                                     - 列出所有任务
2025/06/04 17:49:19 main.go:393: 
2025/06/04 17:49:19 main.go:394: ⚙️ 参数配置:
2025/06/04 17:49:19 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 17:49:19 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 17:49:19 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 17:49:19 main.go:398: 
2025/06/04 17:49:19 main.go:399: 📊 状态控制:
2025/06/04 17:49:19 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 17:49:19 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 17:49:19 main.go:402:    status off                               - 停止状态输出
2025/06/04 17:49:19 main.go:403: 
2025/06/04 17:49:19 main.go:404: 📋 数据导出:
2025/06/04 17:49:19 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 17:49:19 main.go:406: 
2025/06/04 17:49:19 main.go:407: 💾 数据管理:
2025/06/04 17:49:19 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 17:49:19 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 17:49:19 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 17:49:19 main.go:411: 
2025/06/04 17:49:19 main.go:412: 📋 事件查看:
2025/06/04 17:49:19 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 17:49:19 main.go:414: 
2025/06/04 17:49:19 main.go:415: 🎛️  批量控制:
2025/06/04 17:49:19 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 17:49:19 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 17:49:19 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 17:49:19 main.go:419: 
2025/06/04 17:49:19 main.go:420: 🗑️  任务管理:
2025/06/04 17:49:19 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 17:49:19 main.go:422: 
2025/06/04 17:49:19 main.go:423: 🔧 其他:
2025/06/04 17:49:19 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 17:49:19 main.go:425:    quit | exit                              - 退出程序
2025/06/04 17:49:19 main.go:426: ========================
2025/06/04 17:49:19 main.go:427: 
2025/06/04 17:54:19 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 17:54:19 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 17:59:19 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 17:59:19 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 18:19:24 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 18:19:24 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 18:40:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 18:40:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 18:45:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 18:45:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 18:50:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 18:50:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 18:55:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 18:55:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:00:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:00:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:05:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:05:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:10:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:10:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:15:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:15:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:20:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:20:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:25:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:25:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:30:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:30:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 19:35:32 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 19:35:32 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 20:23:24 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 20:23:24 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 21:52:21 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 21:52:21 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:04:21 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 22:04:21 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:09:21 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 22:09:21 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:11:51 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 22:14:21 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 22:14:21 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:27:56 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 22:27:56 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 22:27:56 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 22:27:56 main.go:190: API接口:
2025/06/04 22:27:56 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 22:27:56 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 22:27:56 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 22:27:56 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 22:27:56 main.go:195: 期权系统API接口:
2025/06/04 22:27:56 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 22:27:56 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 22:27:56 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 22:27:56 api.go:35: API服务器启动，端口: 5879
2025/06/04 22:27:56 main.go:384: 
2025/06/04 22:27:56 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 22:27:56 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 22:27:56 main.go:388:       示例: add long 3500 0.0005
2025/06/04 22:27:56 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 22:27:56 main.go:390: 
2025/06/04 22:27:56 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 22:27:56 main.go:392:    list                                     - 列出所有任务
2025/06/04 22:27:56 main.go:393: 
2025/06/04 22:27:56 main.go:394: ⚙️ 参数配置:
2025/06/04 22:27:56 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 22:27:56 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 22:27:56 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 22:27:56 main.go:398: 
2025/06/04 22:27:56 main.go:399: 📊 状态控制:
2025/06/04 22:27:56 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 22:27:56 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 22:27:56 main.go:402:    status off                               - 停止状态输出
2025/06/04 22:27:56 main.go:403: 
2025/06/04 22:27:56 main.go:404: 📋 数据导出:
2025/06/04 22:27:56 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 22:27:56 main.go:406: 
2025/06/04 22:27:56 main.go:407: 💾 数据管理:
2025/06/04 22:27:56 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 22:27:56 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 22:27:56 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 22:27:56 main.go:411: 
2025/06/04 22:27:56 main.go:412: 📋 事件查看:
2025/06/04 22:27:56 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 22:27:56 main.go:414: 
2025/06/04 22:27:56 main.go:415: 🎛️  批量控制:
2025/06/04 22:27:56 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 22:27:56 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 22:27:56 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 22:27:56 main.go:419: 
2025/06/04 22:27:56 main.go:420: 🗑️  任务管理:
2025/06/04 22:27:56 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 22:27:56 main.go:422: 
2025/06/04 22:27:56 main.go:423: 🔧 其他:
2025/06/04 22:27:56 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 22:27:56 main.go:425:    quit | exit                              - 退出程序
2025/06/04 22:27:56 main.go:426: ========================
2025/06/04 22:27:56 main.go:427: 
2025/06/04 22:32:56 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 22:32:56 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:37:37 状态变化:  -> running | 原因: 任务创建
2025/06/04 22:37:37 hedge_task.go:45: 启动对冲任务: 9952000
2025/06/04 22:37:37 manager.go:84: 创建对冲任务: 9952000, 交易对: ETHUSDC, 方向: long, 目标价: 5000
2025/06/04 22:37:37 api.go:86: API: 创建任务成功 - 9952000
2025/06/04 22:37:37 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/04 22:37:37 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:37:37 状态变化:  -> running | 原因: 任务创建
2025/06/04 22:37:37 manager.go:84: 创建对冲任务: 2440000, 交易对: ETHUSDC, 方向: short, 目标价: 2000
2025/06/04 22:37:37 api.go:86: API: 创建任务成功 - 2440000
2025/06/04 22:37:37 hedge_task.go:45: 启动对冲任务: 2440000
2025/06/04 22:37:37 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/04 22:37:37 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:37:41 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:37:49 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:37:56 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/04 22:37:56 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:38:10 main.go:521: ❌ 未知命令: list_m，输入 'help' 查看帮助
2025/06/04 22:38:12 main.go:808: 9952000 运行   L      5000 0.200% 无仓位              0  22:38   0m   0.00     0.00   0.00 否       
2025/06/04 22:38:12 main.go:808: 2440000 运行   S      2000 0.300% 无仓位              0  22:38   0m   0.00     0.00   0.00 否       
2025/06/04 22:38:12 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 22:38:12 manager.go:131: 停止对冲任务: 9952000
2025/06/04 22:38:12 hedge_task.go:60: 任务 9952000: 收到停止信号
2025/06/04 22:38:12 hedge_task.go:470: 任务 9952000: 无仓位，直接停止
2025/06/04 22:38:12 hedge_task.go:62: 对冲任务结束: 9952000
2025/06/04 22:38:12 api.go:147: API: 停止任务成功 - 9952000
2025/06/04 22:38:12 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/04 22:38:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:38:12 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 22:38:12 hedge_task.go:60: 任务 2440000: 收到停止信号
2025/06/04 22:38:12 hedge_task.go:470: 任务 2440000: 无仓位，直接停止
2025/06/04 22:38:12 manager.go:131: 停止对冲任务: 2440000
2025/06/04 22:38:12 hedge_task.go:62: 对冲任务结束: 2440000
2025/06/04 22:38:12 api.go:147: API: 停止任务成功 - 2440000
2025/06/04 22:38:12 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/04 22:38:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:38:12 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp **********:49937->***********:443: use of closed network connection, 5秒后重试...
2025/06/04 22:38:47 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 22:38:47 main.go:220: 停止任务 9952000 失败: 任务已停止: 9952000
2025/06/04 22:38:47 main.go:220: 停止任务 2440000 失败: 任务已停止: 2440000
2025/06/04 22:38:51 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 22:38:51 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 22:38:51 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 22:38:51 main.go:190: API接口:
2025/06/04 22:38:51 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 22:38:51 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 22:38:51 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 22:38:51 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 22:38:51 main.go:195: 期权系统API接口:
2025/06/04 22:38:51 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 22:38:51 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 22:38:51 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 22:38:51 main.go:384: 
2025/06/04 22:38:51 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 22:38:51 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 22:38:51 main.go:388:       示例: add long 3500 0.0005
2025/06/04 22:38:51 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 22:38:51 main.go:390: 
2025/06/04 22:38:51 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 22:38:51 main.go:392:    list                                     - 列出所有任务
2025/06/04 22:38:51 main.go:393: 
2025/06/04 22:38:51 main.go:394: ⚙️ 参数配置:
2025/06/04 22:38:51 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 22:38:51 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 22:38:51 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 22:38:51 main.go:398: 
2025/06/04 22:38:51 main.go:399: 📊 状态控制:
2025/06/04 22:38:51 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 22:38:51 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 22:38:51 main.go:402:    status off                               - 停止状态输出
2025/06/04 22:38:51 main.go:403: 
2025/06/04 22:38:51 main.go:404: 📋 数据导出:
2025/06/04 22:38:51 api.go:35: API服务器启动，端口: 5879
2025/06/04 22:38:51 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 22:38:51 main.go:406: 
2025/06/04 22:38:51 main.go:407: 💾 数据管理:
2025/06/04 22:38:51 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 22:38:51 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 22:38:51 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 22:38:51 main.go:411: 
2025/06/04 22:38:51 main.go:412: 📋 事件查看:
2025/06/04 22:38:51 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 22:38:51 main.go:414: 
2025/06/04 22:38:51 main.go:415: 🎛️  批量控制:
2025/06/04 22:38:51 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 22:38:51 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 22:38:51 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 22:38:51 main.go:419: 
2025/06/04 22:38:51 main.go:420: 🗑️  任务管理:
2025/06/04 22:38:51 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 22:38:51 main.go:422: 
2025/06/04 22:38:51 main.go:423: 🔧 其他:
2025/06/04 22:38:51 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 22:38:51 main.go:425:    quit | exit                              - 退出程序
2025/06/04 22:38:51 main.go:426: ========================
2025/06/04 22:38:51 main.go:427: 
2025/06/04 22:38:54 main.go:808: 9952000 停止   L      5000 0.200% 无仓位              0  22:38   0m   0.00     0.00   0.00 否       
2025/06/04 22:38:54 main.go:808: 2440000 停止   S      2000 0.300% 无仓位              0  22:38   0m   0.00     0.00   0.00 否       
2025/06/04 22:39:01 状态变化:  -> running | 原因: 任务创建
2025/06/04 22:39:01 manager.go:84: 创建对冲任务: 6495000, 交易对: ETHUSDC, 方向: long, 目标价: 5000
2025/06/04 22:39:01 api.go:86: API: 创建任务成功 - 6495000
2025/06/04 22:39:01 hedge_task.go:45: 启动对冲任务: 6495000
2025/06/04 22:39:01 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/04 22:39:01 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:39:01 状态变化:  -> running | 原因: 任务创建
2025/06/04 22:39:01 manager.go:84: 创建对冲任务: 9201000, 交易对: ETHUSDC, 方向: short, 目标价: 2000
2025/06/04 22:39:01 api.go:86: API: 创建任务成功 - 9201000
2025/06/04 22:39:01 hedge_task.go:45: 启动对冲任务: 9201000
2025/06/04 22:39:01 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 22:39:01 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:39:36 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 22:39:36 manager.go:131: 停止对冲任务: 6495000
2025/06/04 22:39:36 api.go:147: API: 停止任务成功 - 6495000
2025/06/04 22:39:36 hedge_task.go:60: 任务 6495000: 收到停止信号
2025/06/04 22:39:36 hedge_task.go:470: 任务 6495000: 无仓位，直接停止
2025/06/04 22:39:36 hedge_task.go:62: 对冲任务结束: 6495000
2025/06/04 22:39:36 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 22:39:36 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:39:36 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 22:39:36 manager.go:131: 停止对冲任务: 9201000
2025/06/04 22:39:36 api.go:147: API: 停止任务成功 - 9201000
2025/06/04 22:39:36 hedge_task.go:60: 任务 9201000: 收到停止信号
2025/06/04 22:39:36 hedge_task.go:470: 任务 9201000: 无仓位，直接停止
2025/06/04 22:39:36 hedge_task.go:62: 对冲任务结束: 9201000
2025/06/04 22:39:36 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 22:39:36 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:39:39 main.go:521: ❌ 未知命令: lilist，输入 'help' 查看帮助
2025/06/04 22:39:41 main.go:808: 9201000 停止   S      2000 0.300% 无仓位              0  22:39   0m   0.00     0.00   0.00 否       
2025/06/04 22:39:41 main.go:808: 2440000 停止   S      2000 0.300% 无仓位              0  22:39   0m   0.00     0.00   0.00 否       
2025/06/04 22:39:41 main.go:808: 9952000 停止   L      5000 0.200% 无仓位              0  22:39   0m   0.00     0.00   0.00 否       
2025/06/04 22:39:41 main.go:808: 6495000 停止   L      5000 0.200% 无仓位              0  22:39   0m   0.00     0.00   0.00 否       
2025/06/04 22:40:17 状态变化:  -> running | 原因: 任务创建
2025/06/04 22:40:17 manager.go:84: 创建对冲任务: 8916000, 交易对: ETHUSDC, 方向: long, 目标价: 5000
2025/06/04 22:40:17 api.go:86: API: 创建任务成功 - 8916000
2025/06/04 22:40:17 hedge_task.go:45: 启动对冲任务: 8916000
2025/06/04 22:40:17 data_manager.go:184: 💾 任务数据已保存 (5个任务)
2025/06/04 22:40:17 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:40:17 状态变化:  -> running | 原因: 任务创建
2025/06/04 22:40:17 manager.go:84: 创建对冲任务: 2809000, 交易对: ETHUSDC, 方向: short, 目标价: 2000
2025/06/04 22:40:17 hedge_task.go:45: 启动对冲任务: 2809000
2025/06/04 22:40:17 api.go:86: API: 创建任务成功 - 2809000
2025/06/04 22:40:17 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:40:17 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:40:25 main.go:808: 9952000 停止   L      5000 0.200% 无仓位              0  22:40   0m   0.00     0.00   0.00 否       
2025/06/04 22:40:25 main.go:808: 6495000 停止   L      5000 0.200% 无仓位              0  22:40   0m   0.00     0.00   0.00 否       
2025/06/04 22:40:25 main.go:808: 9201000 停止   S      2000 0.300% 无仓位              0  22:40   0m   0.00     0.00   0.00 否       
2025/06/04 22:40:25 main.go:808: 8916000 运行   L      5000 0.200% 无仓位              0  22:40   0m   0.00     0.00   0.00 否       
2025/06/04 22:40:25 main.go:808: 2809000 运行   S      2000 0.300% 无仓位              0  22:40   0m   0.00     0.00   0.00 否       
2025/06/04 22:40:25 main.go:808: 2440000 停止   S      2000 0.300% 无仓位              0  22:40   0m   0.00     0.00   0.00 否       
2025/06/04 22:40:52 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 22:40:52 manager.go:131: 停止对冲任务: 8916000
2025/06/04 22:40:52 api.go:147: API: 停止任务成功 - 8916000
2025/06/04 22:40:52 hedge_task.go:60: 任务 8916000: 收到停止信号
2025/06/04 22:40:52 hedge_task.go:470: 任务 8916000: 无仓位，直接停止
2025/06/04 22:40:52 hedge_task.go:62: 对冲任务结束: 8916000
2025/06/04 22:40:52 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:40:52 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:40:52 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 22:40:52 manager.go:131: 停止对冲任务: 2809000
2025/06/04 22:40:52 hedge_task.go:60: 任务 2809000: 收到停止信号
2025/06/04 22:40:52 hedge_task.go:470: 任务 2809000: 无仓位，直接停止
2025/06/04 22:40:52 hedge_task.go:62: 对冲任务结束: 2809000
2025/06/04 22:40:52 api.go:147: API: 停止任务成功 - 2809000
2025/06/04 22:40:52 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:40:52 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:40:52 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp **********:50568->***********:443: use of closed network connection, 5秒后重试...
2025/06/04 22:42:25 main.go:808: 2440000 停止   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:25 main.go:808: 9952000 停止   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:25 main.go:808: 6495000 停止   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:25 main.go:808: 9201000 停止   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:25 main.go:808: 8916000 停止   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:25 main.go:808: 2809000 停止   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:32 main.go:808: 6495000 停止   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:32 main.go:808: 9201000 停止   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:32 main.go:808: 8916000 停止   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:32 main.go:808: 2809000 停止   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:32 main.go:808: 2440000 停止   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:32 main.go:808: 9952000 停止   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:37 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:42:37 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:42:37 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:42:37 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:42:37 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:42:37 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:42:37 hedge_task.go:45: 启动对冲任务: 9952000
2025/06/04 22:42:37 hedge_task.go:45: 启动对冲任务: 2809000
2025/06/04 22:42:37 hedge_task.go:45: 启动对冲任务: 2440000
2025/06/04 22:42:37 hedge_task.go:45: 启动对冲任务: 6495000
2025/06/04 22:42:37 hedge_task.go:45: 启动对冲任务: 9201000
2025/06/04 22:42:37 hedge_task.go:45: 启动对冲任务: 8916000
2025/06/04 22:42:37 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:42:37 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:42:41 main.go:521: ❌ 未知命令: list_m，输入 'help' 查看帮助
2025/06/04 22:42:43 main.go:808: 9952000 运行   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:43 main.go:808: 6495000 运行   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:43 main.go:808: 9201000 运行   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:43 main.go:808: 8916000 运行   L      5000 0.200% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:43 main.go:808: 2809000 运行   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:43 main.go:808: 2440000 运行   S      2000 0.300% 无仓位              0  22:42   0m   0.00     0.00   0.00 否       
2025/06/04 22:42:57 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 22:42:57 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 22:42:57 manager.go:131: 停止对冲任务: 2440000
2025/06/04 22:42:57 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 22:42:57 manager.go:131: 停止对冲任务: 9952000
2025/06/04 22:42:57 hedge_task.go:60: 任务 2440000: 收到停止信号
2025/06/04 22:42:57 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 22:42:57 hedge_task.go:470: 任务 2440000: 无仓位，直接停止
2025/06/04 22:42:57 hedge_task.go:62: 对冲任务结束: 2440000
2025/06/04 22:42:57 hedge_task.go:60: 任务 6495000: 收到停止信号
2025/06/04 22:42:57 hedge_task.go:470: 任务 6495000: 无仓位，直接停止
2025/06/04 22:42:57 hedge_task.go:62: 对冲任务结束: 6495000
2025/06/04 22:42:57 manager.go:131: 停止对冲任务: 6495000
2025/06/04 22:42:57 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 22:42:57 hedge_task.go:60: 任务 9952000: 收到停止信号
2025/06/04 22:42:57 hedge_task.go:470: 任务 9952000: 无仓位，直接停止
2025/06/04 22:42:57 manager.go:131: 停止对冲任务: 9201000
2025/06/04 22:42:57 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 22:42:57 manager.go:131: 停止对冲任务: 8916000
2025/06/04 22:42:57 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 22:42:57 manager.go:131: 停止对冲任务: 2809000
2025/06/04 22:42:57 hedge_task.go:60: 任务 2809000: 收到停止信号
2025/06/04 22:42:57 hedge_task.go:470: 任务 2809000: 无仓位，直接停止
2025/06/04 22:42:57 hedge_task.go:62: 对冲任务结束: 2809000
2025/06/04 22:42:57 hedge_task.go:60: 任务 9201000: 收到停止信号
2025/06/04 22:42:57 hedge_task.go:470: 任务 9201000: 无仓位，直接停止
2025/06/04 22:42:57 hedge_task.go:62: 对冲任务结束: 9201000
2025/06/04 22:42:57 hedge_task.go:62: 对冲任务结束: 9952000
2025/06/04 22:42:57 hedge_task.go:60: 任务 8916000: 收到停止信号
2025/06/04 22:42:57 hedge_task.go:470: 任务 8916000: 无仓位，直接停止
2025/06/04 22:42:57 hedge_task.go:62: 对冲任务结束: 8916000
2025/06/04 22:42:57 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:42:57 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:42:57 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:42:57 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:42:57 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:42:57 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:42:57 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:42:57 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:42:57 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:42:57 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:42:57 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:42:57 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:42:57 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp **********:51783->***********:443: use of closed network connection, 5秒后重试...
2025/06/04 22:43:03 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 22:43:03 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 22:43:03 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 22:43:03 main.go:190: API接口:
2025/06/04 22:43:03 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 22:43:03 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 22:43:03 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 22:43:03 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 22:43:03 main.go:195: 期权系统API接口:
2025/06/04 22:43:03 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 22:43:03 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 22:43:03 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 22:43:03 main.go:384: 
2025/06/04 22:43:03 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 22:43:03 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 22:43:03 api.go:35: API服务器启动，端口: 5879
2025/06/04 22:43:03 main.go:388:       示例: add long 3500 0.0005
2025/06/04 22:43:03 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 22:43:03 main.go:390: 
2025/06/04 22:43:03 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 22:43:03 main.go:392:    list                                     - 列出所有任务
2025/06/04 22:43:03 main.go:393: 
2025/06/04 22:43:03 main.go:394: ⚙️ 参数配置:
2025/06/04 22:43:03 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 22:43:03 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 22:43:03 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 22:43:03 main.go:398: 
2025/06/04 22:43:03 main.go:399: 📊 状态控制:
2025/06/04 22:43:03 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 22:43:03 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 22:43:03 main.go:402:    status off                               - 停止状态输出
2025/06/04 22:43:03 main.go:403: 
2025/06/04 22:43:03 main.go:404: 📋 数据导出:
2025/06/04 22:43:03 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 22:43:03 main.go:406: 
2025/06/04 22:43:03 main.go:407: 💾 数据管理:
2025/06/04 22:43:03 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 22:43:03 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 22:43:03 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 22:43:03 main.go:411: 
2025/06/04 22:43:03 main.go:412: 📋 事件查看:
2025/06/04 22:43:03 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 22:43:03 main.go:414: 
2025/06/04 22:43:03 main.go:415: 🎛️  批量控制:
2025/06/04 22:43:03 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 22:43:03 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 22:43:03 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 22:43:03 main.go:419: 
2025/06/04 22:43:03 main.go:420: 🗑️  任务管理:
2025/06/04 22:43:03 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 22:43:03 main.go:422: 
2025/06/04 22:43:03 main.go:423: 🔧 其他:
2025/06/04 22:43:03 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 22:43:03 main.go:425:    quit | exit                              - 退出程序
2025/06/04 22:43:03 main.go:426: ========================
2025/06/04 22:43:03 main.go:427: 
2025/06/04 22:43:07 main.go:521: ❌ 未知命令: starall，输入 'help' 查看帮助
2025/06/04 22:43:09 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:43:09 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:43:09 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:43:09 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:43:09 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:43:09 状态变化: stopped -> running | 原因: 批量启动
2025/06/04 22:43:09 hedge_task.go:45: 启动对冲任务: 6495000
2025/06/04 22:43:09 hedge_task.go:45: 启动对冲任务: 9201000
2025/06/04 22:43:09 hedge_task.go:45: 启动对冲任务: 9952000
2025/06/04 22:43:09 hedge_task.go:45: 启动对冲任务: 8916000
2025/06/04 22:43:09 hedge_task.go:45: 启动对冲任务: 2809000
2025/06/04 22:43:09 hedge_task.go:45: 启动对冲任务: 2440000
2025/06/04 22:43:09 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:43:09 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:43:15 main.go:808: 6495000 运行   L      5000 0.200% 无仓位              0  22:43   0m   0.00     0.00   0.00 否       
2025/06/04 22:43:15 main.go:808: 8916000 运行   L      5000 0.200% 无仓位              0  22:43   0m   0.00     0.00   0.00 否       
2025/06/04 22:43:15 main.go:808: 9201000 运行   S      2000 0.300% 无仓位              0  22:43   0m   0.00     0.00   0.00 否       
2025/06/04 22:43:15 main.go:808: 9952000 运行   L      5000 0.200% 无仓位              0  22:43   0m   0.00     0.00   0.00 否       
2025/06/04 22:43:15 main.go:808: 2440000 运行   S      2000 0.300% 无仓位              0  22:43   0m   0.00     0.00   0.00 否       
2025/06/04 22:43:15 main.go:808: 2809000 运行   S      2000 0.300% 无仓位              0  22:43   0m   0.00     0.00   0.00 否       
2025/06/04 22:43:18 main.go:384: 
2025/06/04 22:43:18 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 22:43:18 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 22:43:18 main.go:388:       示例: add long 3500 0.0005
2025/06/04 22:43:18 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 22:43:18 main.go:390: 
2025/06/04 22:43:18 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 22:43:18 main.go:392:    list                                     - 列出所有任务
2025/06/04 22:43:18 main.go:393: 
2025/06/04 22:43:18 main.go:394: ⚙️ 参数配置:
2025/06/04 22:43:18 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 22:43:18 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 22:43:18 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 22:43:18 main.go:398: 
2025/06/04 22:43:18 main.go:399: 📊 状态控制:
2025/06/04 22:43:18 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 22:43:18 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 22:43:18 main.go:402:    status off                               - 停止状态输出
2025/06/04 22:43:18 main.go:403: 
2025/06/04 22:43:18 main.go:404: 📋 数据导出:
2025/06/04 22:43:18 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 22:43:18 main.go:406: 
2025/06/04 22:43:18 main.go:407: 💾 数据管理:
2025/06/04 22:43:18 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 22:43:18 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 22:43:18 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 22:43:18 main.go:411: 
2025/06/04 22:43:18 main.go:412: 📋 事件查看:
2025/06/04 22:43:18 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 22:43:18 main.go:414: 
2025/06/04 22:43:18 main.go:415: 🎛️  批量控制:
2025/06/04 22:43:18 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 22:43:18 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 22:43:18 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 22:43:18 main.go:419: 
2025/06/04 22:43:18 main.go:420: 🗑️  任务管理:
2025/06/04 22:43:18 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 22:43:18 main.go:422: 
2025/06/04 22:43:18 main.go:423: 🔧 其他:
2025/06/04 22:43:18 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 22:43:18 main.go:425:    quit | exit                              - 退出程序
2025/06/04 22:43:18 main.go:426: ========================
2025/06/04 22:43:18 main.go:427: 
2025/06/04 22:47:40 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:47:40 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:47:40 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:47:40 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:47:40 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:47:40 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:47:45 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:45 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:45 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:45 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:45 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:48 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:50 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:50 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:50 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:53 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:53 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:53 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:55 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:56 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:56 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:47:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:48:00 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:48:01 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:48:01 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:48:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:48:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:48:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:48:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:48:04 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:53:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:53:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 22:53:53 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:53:53 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:53:53 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:53:53 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:53:53 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:53:53 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 22:53:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:53:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:53:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:53:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:53:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:53:58 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:03 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:08 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:08 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:08 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:08 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:08 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:54:08 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 22:58:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 22:58:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:03:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:03:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:08:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:08:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:13:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:13:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:16:03 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 23:16:03 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 23:16:03 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 23:16:03 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 23:16:03 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 23:16:03 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/04 23:16:09 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:09 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:09 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:09 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:09 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:09 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:14 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:14 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:14 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:14 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:14 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:17 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:19 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:19 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:19 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:19 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:22 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:25 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:27 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:27 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:27 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:27 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:16:30 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/04 23:18:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:18:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:23:03 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:23:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:26:25 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 23:26:25 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 23:26:25 manager.go:131: 停止对冲任务: 9952000
2025/06/04 23:26:25 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 23:26:25 manager.go:131: 停止对冲任务: 2440000
2025/06/04 23:26:25 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 23:26:25 manager.go:131: 停止对冲任务: 2809000
2025/06/04 23:26:25 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 23:26:25 manager.go:131: 停止对冲任务: 6495000
2025/06/04 23:26:25 hedge_task.go:60: 任务 2809000: 收到停止信号
2025/06/04 23:26:25 hedge_task.go:470: 任务 2809000: 无仓位，直接停止
2025/06/04 23:26:25 hedge_task.go:62: 对冲任务结束: 2809000
2025/06/04 23:26:25 hedge_task.go:60: 任务 2440000: 收到停止信号
2025/06/04 23:26:25 hedge_task.go:470: 任务 2440000: 无仓位，直接停止
2025/06/04 23:26:25 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 23:26:25 hedge_task.go:62: 对冲任务结束: 2440000
2025/06/04 23:26:25 hedge_task.go:60: 任务 6495000: 收到停止信号
2025/06/04 23:26:25 manager.go:131: 停止对冲任务: 8916000
2025/06/04 23:26:25 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 23:26:25 manager.go:131: 停止对冲任务: 9201000
2025/06/04 23:26:25 hedge_task.go:470: 任务 6495000: 无仓位，直接停止
2025/06/04 23:26:25 hedge_task.go:62: 对冲任务结束: 6495000
2025/06/04 23:26:25 hedge_task.go:60: 任务 9952000: 收到停止信号
2025/06/04 23:26:25 hedge_task.go:470: 任务 9952000: 无仓位，直接停止
2025/06/04 23:26:25 hedge_task.go:62: 对冲任务结束: 9952000
2025/06/04 23:26:25 hedge_task.go:60: 任务 8916000: 收到停止信号
2025/06/04 23:26:25 hedge_task.go:470: 任务 8916000: 无仓位，直接停止
2025/06/04 23:26:25 hedge_task.go:62: 对冲任务结束: 8916000
2025/06/04 23:26:25 hedge_task.go:60: 任务 9201000: 收到停止信号
2025/06/04 23:26:25 hedge_task.go:470: 任务 9201000: 无仓位，直接停止
2025/06/04 23:26:25 hedge_task.go:62: 对冲任务结束: 9201000
2025/06/04 23:26:25 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:26:25 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:26:25 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:26:25 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:26:25 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:26:25 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:26:25 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:26:25 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:26:25 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:26:25 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:26:25 data_manager.go:184: 💾 任务数据已保存 (6个任务)
2025/06/04 23:26:25 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:26:25 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp **********:62384->***********:443: use of closed network connection, 5秒后重试...
2025/06/04 23:26:37 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 23:26:37 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 23:26:37 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 23:26:37 main.go:190: API接口:
2025/06/04 23:26:37 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 23:26:37 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 23:26:37 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 23:26:37 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 23:26:37 main.go:195: 期权系统API接口:
2025/06/04 23:26:37 api.go:35: API服务器启动，端口: 5879
2025/06/04 23:26:37 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 23:26:37 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 23:26:37 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 23:26:37 main.go:384: 
2025/06/04 23:26:37 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:26:37 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:26:37 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:26:37 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:26:37 main.go:390: 
2025/06/04 23:26:37 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:26:37 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:26:37 main.go:393: 
2025/06/04 23:26:37 main.go:394: ⚙️ 参数配置:
2025/06/04 23:26:37 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:26:37 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:26:37 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:26:37 main.go:398: 
2025/06/04 23:26:37 main.go:399: 📊 状态控制:
2025/06/04 23:26:37 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:26:37 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:26:37 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:26:37 main.go:403: 
2025/06/04 23:26:37 main.go:404: 📋 数据导出:
2025/06/04 23:26:37 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:26:37 main.go:406: 
2025/06/04 23:26:37 main.go:407: 💾 数据管理:
2025/06/04 23:26:37 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:26:37 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:26:37 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:26:37 main.go:411: 
2025/06/04 23:26:37 main.go:412: 📋 事件查看:
2025/06/04 23:26:37 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:26:37 main.go:414: 
2025/06/04 23:26:37 main.go:415: 🎛️  批量控制:
2025/06/04 23:26:37 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:26:37 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:26:37 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:26:37 main.go:419: 
2025/06/04 23:26:37 main.go:420: 🗑️  任务管理:
2025/06/04 23:26:37 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:26:37 main.go:422: 
2025/06/04 23:26:37 main.go:423: 🔧 其他:
2025/06/04 23:26:37 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:26:37 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:26:37 main.go:426: ========================
2025/06/04 23:26:37 main.go:427: 
2025/06/04 23:26:52 main.go:521: ❌ 未知命令: list_m，输入 'help' 查看帮助
2025/06/04 23:26:54 main.go:808: 9952000 停止   L      5000 0.200% 无仓位              0  23:26   0m   0.00     0.00   0.00 否       
2025/06/04 23:26:54 main.go:808: 2440000 停止   S      2000 0.300% 无仓位              0  23:26   0m   0.00     0.00   0.00 否       
2025/06/04 23:26:54 main.go:808: 2809000 停止   S      2000 0.300% 无仓位              0  23:26   0m   0.00     0.00   0.00 否       
2025/06/04 23:26:54 main.go:808: 6495000 停止   L      5000 0.200% 无仓位              0  23:26   0m   0.00     0.00   0.00 否       
2025/06/04 23:26:54 main.go:808: 8916000 停止   L      5000 0.200% 无仓位              0  23:26   0m   0.00     0.00   0.00 否       
2025/06/04 23:26:54 main.go:808: 9201000 停止   S      2000 0.300% 无仓位              0  23:26   0m   0.00     0.00   0.00 否       
2025/06/04 23:27:07 main.go:808: 9201000 停止   S      2000 0.300% 无仓位              0  23:27   0m   0.00     0.00   0.00 否       
2025/06/04 23:27:07 main.go:808: 9952000 停止   L      5000 0.200% 无仓位              0  23:27   0m   0.00     0.00   0.00 否       
2025/06/04 23:27:07 main.go:808: 2440000 停止   S      2000 0.300% 无仓位              0  23:27   0m   0.00     0.00   0.00 否       
2025/06/04 23:27:07 main.go:808: 2809000 停止   S      2000 0.300% 无仓位              0  23:27   0m   0.00     0.00   0.00 否       
2025/06/04 23:27:07 main.go:808: 6495000 停止   L      5000 0.200% 无仓位              0  23:27   0m   0.00     0.00   0.00 否       
2025/06/04 23:27:07 main.go:808: 8916000 停止   L      5000 0.200% 无仓位              0  23:27   0m   0.00     0.00   0.00 否       
2025/06/04 23:27:10 main.go:384: 
2025/06/04 23:27:10 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:27:10 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:27:10 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:27:10 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:27:10 main.go:390: 
2025/06/04 23:27:10 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:27:10 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:27:10 main.go:393: 
2025/06/04 23:27:10 main.go:394: ⚙️ 参数配置:
2025/06/04 23:27:10 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:27:10 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:27:10 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:27:10 main.go:398: 
2025/06/04 23:27:10 main.go:399: 📊 状态控制:
2025/06/04 23:27:10 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:27:10 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:27:10 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:27:10 main.go:403: 
2025/06/04 23:27:10 main.go:404: 📋 数据导出:
2025/06/04 23:27:10 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:27:10 main.go:406: 
2025/06/04 23:27:10 main.go:407: 💾 数据管理:
2025/06/04 23:27:10 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:27:10 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:27:10 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:27:10 main.go:411: 
2025/06/04 23:27:10 main.go:412: 📋 事件查看:
2025/06/04 23:27:10 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:27:10 main.go:414: 
2025/06/04 23:27:10 main.go:415: 🎛️  批量控制:
2025/06/04 23:27:10 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:27:10 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:27:10 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:27:10 main.go:419: 
2025/06/04 23:27:10 main.go:420: 🗑️  任务管理:
2025/06/04 23:27:10 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:27:10 main.go:422: 
2025/06/04 23:27:10 main.go:423: 🔧 其他:
2025/06/04 23:27:10 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:27:10 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:27:10 main.go:426: ========================
2025/06/04 23:27:10 main.go:427: 
2025/06/04 23:27:24 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 23:27:24 main.go:220: 停止任务 2809000 失败: 任务已停止: 2809000
2025/06/04 23:27:24 main.go:220: 停止任务 6495000 失败: 任务已停止: 6495000
2025/06/04 23:27:24 main.go:220: 停止任务 8916000 失败: 任务已停止: 8916000
2025/06/04 23:27:24 main.go:220: 停止任务 9201000 失败: 任务已停止: 9201000
2025/06/04 23:27:24 main.go:220: 停止任务 9952000 失败: 任务已停止: 9952000
2025/06/04 23:27:24 main.go:220: 停止任务 2440000 失败: 任务已停止: 2440000
2025/06/04 23:27:49 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 23:27:49 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 23:27:49 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 23:27:49 main.go:190: API接口:
2025/06/04 23:27:49 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 23:27:49 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 23:27:49 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 23:27:49 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 23:27:49 main.go:195: 期权系统API接口:
2025/06/04 23:27:49 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 23:27:49 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 23:27:49 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 23:27:49 main.go:384: 
2025/06/04 23:27:49 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:27:49 api.go:35: API服务器启动，端口: 5879
2025/06/04 23:27:49 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:27:49 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:27:49 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:27:49 main.go:390: 
2025/06/04 23:27:49 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:27:49 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:27:49 main.go:393: 
2025/06/04 23:27:49 main.go:394: ⚙️ 参数配置:
2025/06/04 23:27:49 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:27:49 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:27:49 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:27:49 main.go:398: 
2025/06/04 23:27:49 main.go:399: 📊 状态控制:
2025/06/04 23:27:49 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:27:49 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:27:49 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:27:49 main.go:403: 
2025/06/04 23:27:49 main.go:404: 📋 数据导出:
2025/06/04 23:27:49 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:27:49 main.go:406: 
2025/06/04 23:27:49 main.go:407: 💾 数据管理:
2025/06/04 23:27:49 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:27:49 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:27:49 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:27:49 main.go:411: 
2025/06/04 23:27:49 main.go:412: 📋 事件查看:
2025/06/04 23:27:49 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:27:49 main.go:414: 
2025/06/04 23:27:49 main.go:415: 🎛️  批量控制:
2025/06/04 23:27:49 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:27:49 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:27:49 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:27:49 main.go:419: 
2025/06/04 23:27:49 main.go:420: 🗑️  任务管理:
2025/06/04 23:27:49 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:27:49 main.go:422: 
2025/06/04 23:27:49 main.go:423: 🔧 其他:
2025/06/04 23:27:49 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:27:49 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:27:49 main.go:426: ========================
2025/06/04 23:27:49 main.go:427: 
2025/06/04 23:32:49 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 23:32:49 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:37:49 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 23:37:49 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:42:49 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 23:42:49 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:47:13 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 23:50:45 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 23:50:45 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 23:50:45 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 23:50:45 main.go:190: API接口:
2025/06/04 23:50:45 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 23:50:45 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 23:50:45 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 23:50:45 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 23:50:45 main.go:195: 期权系统API接口:
2025/06/04 23:50:45 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 23:50:45 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 23:50:45 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 23:50:45 main.go:384: 
2025/06/04 23:50:45 api.go:35: API服务器启动，端口: 5879
2025/06/04 23:50:45 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:50:45 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:50:45 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:50:45 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:50:45 main.go:390: 
2025/06/04 23:50:45 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:50:45 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:50:45 main.go:393: 
2025/06/04 23:50:45 main.go:394: ⚙️ 参数配置:
2025/06/04 23:50:45 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:50:45 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:50:45 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:50:45 main.go:398: 
2025/06/04 23:50:45 main.go:399: 📊 状态控制:
2025/06/04 23:50:45 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:50:45 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:50:45 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:50:45 main.go:403: 
2025/06/04 23:50:45 main.go:404: 📋 数据导出:
2025/06/04 23:50:45 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:50:45 main.go:406: 
2025/06/04 23:50:45 main.go:407: 💾 数据管理:
2025/06/04 23:50:45 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:50:45 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:50:45 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:50:45 main.go:411: 
2025/06/04 23:50:45 main.go:412: 📋 事件查看:
2025/06/04 23:50:45 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:50:45 main.go:414: 
2025/06/04 23:50:45 main.go:415: 🎛️  批量控制:
2025/06/04 23:50:45 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:50:45 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:50:45 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:50:45 main.go:419: 
2025/06/04 23:50:45 main.go:420: 🗑️  任务管理:
2025/06/04 23:50:45 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:50:45 main.go:422: 
2025/06/04 23:50:45 main.go:423: 🔧 其他:
2025/06/04 23:50:45 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:50:45 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:50:45 main.go:426: ========================
2025/06/04 23:50:45 main.go:427: 
2025/06/04 23:50:53 main.go:384: 
2025/06/04 23:50:53 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:50:53 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:50:53 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:50:53 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:50:53 main.go:390: 
2025/06/04 23:50:53 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:50:53 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:50:53 main.go:393: 
2025/06/04 23:50:53 main.go:394: ⚙️ 参数配置:
2025/06/04 23:50:53 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:50:53 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:50:53 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:50:53 main.go:398: 
2025/06/04 23:50:53 main.go:399: 📊 状态控制:
2025/06/04 23:50:53 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:50:53 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:50:53 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:50:53 main.go:403: 
2025/06/04 23:50:53 main.go:404: 📋 数据导出:
2025/06/04 23:50:53 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:50:53 main.go:406: 
2025/06/04 23:50:53 main.go:407: 💾 数据管理:
2025/06/04 23:50:53 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:50:53 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:50:53 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:50:53 main.go:411: 
2025/06/04 23:50:53 main.go:412: 📋 事件查看:
2025/06/04 23:50:53 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:50:53 main.go:414: 
2025/06/04 23:50:53 main.go:415: 🎛️  批量控制:
2025/06/04 23:50:53 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:50:53 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:50:53 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:50:53 main.go:419: 
2025/06/04 23:50:53 main.go:420: 🗑️  任务管理:
2025/06/04 23:50:53 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:50:53 main.go:422: 
2025/06/04 23:50:53 main.go:423: 🔧 其他:
2025/06/04 23:50:53 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:50:53 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:50:53 main.go:426: ========================
2025/06/04 23:50:53 main.go:427: 
2025/06/04 23:51:31 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=false
2025/06/04 23:51:31 状态变化:  -> running | 原因: 任务创建
2025/06/04 23:51:31 manager.go:94: 创建对冲任务: 5781000, 交易对: ETHUSDT, 方向: long, 目标价: 5000, 来源: manual
2025/06/04 23:51:31 hedge_task.go:45: 启动对冲任务: 5781000
2025/06/04 23:51:31 api.go:86: API: 创建任务成功 - 5781000
2025/06/04 23:51:31 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/04 23:51:31 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:51:31 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0010, 停止滑点=0.0010, 市价超时=3s, 盘口量阈值=0.30, 限价模式=false
2025/06/04 23:51:31 状态变化:  -> running | 原因: 任务创建
2025/06/04 23:51:31 manager.go:94: 创建对冲任务: 8634000, 交易对: ETHUSDC, 方向: short, 目标价: 2000, 来源: manual
2025/06/04 23:51:31 hedge_task.go:45: 启动对冲任务: 8634000
2025/06/04 23:51:31 api.go:86: API: 创建任务成功 - 8634000
2025/06/04 23:51:31 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/04 23:51:31 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:51:41 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/04 23:51:41 状态变化:  -> running | 原因: 任务创建
2025/06/04 23:51:41 hedge_task.go:45: 启动对冲任务: M662000
2025/06/04 23:51:41 manager.go:94: 创建对冲任务: M662000, 交易对: ETHUSDT, 方向: long, 目标价: 2500, 来源: options
2025/06/04 23:51:41 api.go:283: API: 期权系统创建任务成功 - M662000 (来源: options)
2025/06/04 23:51:41 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/04 23:51:41 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:51:41 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/04 23:51:41 状态变化:  -> running | 原因: 任务创建
2025/06/04 23:51:41 manager.go:94: 创建对冲任务: M119000, 交易对: ETHUSDT, 方向: short, 目标价: 2400, 来源: options
2025/06/04 23:51:41 api.go:283: API: 期权系统创建任务成功 - M119000 (来源: options)
2025/06/04 23:51:41 hedge_task.go:45: 启动对冲任务: M119000
2025/06/04 23:51:41 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:51:41 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:51:41 hedge_task.go:116: 任务 M662000: 触发做多开仓条件，当前价格: 2665.655, 触发价格: 2500.5
2025/06/04 23:51:41 hedge_task.go:151: 任务 M662000: 开始执行下单，类型: open, 方向: long, 触发价格: 2500.5
2025/06/04 23:51:41 hedge_task.go:176: 任务 M662000: 计算理论价格: 2500.5 (事件类型: open, 方向: long)
2025/06/04 23:51:41 hedge_task.go:343: 任务 M662000: 盘口量不足，使用限价单，盘口量比例: 0.0003163585861302
2025/06/04 23:51:42 hedge_task.go:280: 任务 M662000: 挂买入限价单，价格: 2665.65, 数量: 0.1
2025/06/04 23:51:42 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.1 @ 2665.65, 订单ID: 8389765900247637314, 模式: POST_ONLY
2025/06/04 23:51:43 hedge_task.go:453: 任务 M662000: 订单 8389765900247637314 已成交，价格: 2665.65, 成交方式: maker
2025/06/04 23:51:43 hedge_task.go:204: 任务 M662000: 订单成交，记录事件和更新仓位
2025/06/04 23:51:43 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2500.5 | 实际价: 2665.65 | 损耗: 165.15 | 手续费: 0.053313 | 订单ID: 8389765900247637314
2025/06/04 23:51:43 hedge_task.go:235: 任务 M662000: 下单完成，实际价格: 2665.65, 理论价格: 2500.5, 价格损耗: 165.15, 订单ID: 8389765900247637314
2025/06/04 23:51:43 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:51:43 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/04 23:51:43 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:51:43 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/04 23:51:51 状态变化: running -> running | 原因: 数量更新: sell_open (0.100000 -> 0.200000)
2025/06/04 23:51:51 api.go:302: API: 任务数量更新成功 - M662000 (0.100000 -> 0.200000)
2025/06/04 23:51:51 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:51:51 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/04 23:51:51 api.go:314: API: 期权合约查询完成 - ETHUSDT-2JUN25-2500-C (存在: true)
2025/06/04 23:51:51 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=false
2025/06/04 23:51:51 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/04 23:52:06 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 23:52:06 manager.go:141: 停止对冲任务: 5781000
2025/06/04 23:52:06 hedge_task.go:60: 任务 5781000: 收到停止信号
2025/06/04 23:52:06 hedge_task.go:470: 任务 5781000: 无仓位，直接停止
2025/06/04 23:52:06 api.go:147: API: 停止任务成功 - 5781000
2025/06/04 23:52:06 hedge_task.go:62: 对冲任务结束: 5781000
2025/06/04 23:52:06 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:52:06 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/04 23:52:06 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 23:52:06 manager.go:141: 停止对冲任务: 8634000
2025/06/04 23:52:06 hedge_task.go:60: 任务 8634000: 收到停止信号
2025/06/04 23:52:06 hedge_task.go:470: 任务 8634000: 无仓位，直接停止
2025/06/04 23:52:06 hedge_task.go:62: 对冲任务结束: 8634000
2025/06/04 23:52:06 api.go:147: API: 停止任务成功 - 8634000
2025/06/04 23:52:06 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:52:06 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/04 23:52:06 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 23:52:06 manager.go:141: 停止对冲任务: M662000
2025/06/04 23:52:06 api.go:147: API: 停止任务成功 - M662000
2025/06/04 23:52:06 hedge_task.go:60: 任务 M662000: 收到停止信号
2025/06/04 23:52:06 hedge_task.go:481: 任务 M662000: 开始停止平仓，仓位: long 0.1
2025/06/04 23:52:06 hedge_task.go:492: 任务 M662000: 做多平仓，理论平仓价: 2499.5 (目标价-阈值)
2025/06/04 23:52:06 hedge_task.go:506: 任务 M662000: 开始动态平仓，理论价格: 2499.5, 方向: short
2025/06/04 23:52:06 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:52:06 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/04 23:52:06 状态变化: running -> stopped | 原因: 手动停止
2025/06/04 23:52:06 hedge_task.go:60: 任务 M119000: 收到停止信号
2025/06/04 23:52:06 hedge_task.go:470: 任务 M119000: 无仓位，直接停止
2025/06/04 23:52:06 hedge_task.go:62: 对冲任务结束: M119000
2025/06/04 23:52:06 manager.go:141: 停止对冲任务: M119000
2025/06/04 23:52:06 api.go:147: API: 停止任务成功 - M119000
2025/06/04 23:52:06 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp **********:54868->***********:443: use of closed network connection, 5秒后重试...
2025/06/04 23:52:06 hedge_task.go:600: 任务 M662000: 检查卖出平仓条件 - 卖一价: 2665.46, 范围: [2498.25025, 2500.74975]
2025/06/04 23:52:06 hedge_task.go:554: 任务 M662000: 超出滑点范围，执行市价平仓
2025/06/04 23:52:06 hedge_task.go:661: 任务 M662000: 强制市价平仓，数量: 0.2, 方向: short
2025/06/04 23:52:06 binance_client.go:114: ✅ 市价单已下达: ETHUSDT SELL 0.2, 订单ID: 8389765900247805384
2025/06/04 23:52:07 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/06/04 23:52:08 hedge_task.go:689: 任务 M662000: 市价平仓完成，实际价格: 2665.55
2025/06/04 23:52:08 hedge_task.go:62: 对冲任务结束: M662000
2025/06/04 23:52:08 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:52:08 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/04 23:52:08 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/04 23:52:08 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/04 23:52:33 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 23:52:33 main.go:220: 停止任务 5781000 失败: 任务已停止: 5781000
2025/06/04 23:52:33 main.go:220: 停止任务 8634000 失败: 任务已停止: 8634000
2025/06/04 23:52:33 main.go:220: 停止任务 M662000 失败: 任务已停止: M662000
2025/06/04 23:52:33 main.go:220: 停止任务 M119000 失败: 任务已停止: M119000
2025/06/04 23:52:43 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 23:52:43 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 23:52:43 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 23:52:43 main.go:190: API接口:
2025/06/04 23:52:43 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 23:52:43 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 23:52:43 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 23:52:43 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 23:52:43 main.go:195: 期权系统API接口:
2025/06/04 23:52:43 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 23:52:43 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 23:52:43 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 23:52:43 api.go:35: API服务器启动，端口: 5879
2025/06/04 23:52:43 main.go:384: 
2025/06/04 23:52:43 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:52:43 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:52:43 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:52:43 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:52:43 main.go:390: 
2025/06/04 23:52:43 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:52:43 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:52:43 main.go:393: 
2025/06/04 23:52:43 main.go:394: ⚙️ 参数配置:
2025/06/04 23:52:43 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:52:43 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:52:43 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:52:43 main.go:398: 
2025/06/04 23:52:43 main.go:399: 📊 状态控制:
2025/06/04 23:52:43 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:52:43 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:52:43 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:52:43 main.go:403: 
2025/06/04 23:52:43 main.go:404: 📋 数据导出:
2025/06/04 23:52:43 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:52:43 main.go:406: 
2025/06/04 23:52:43 main.go:407: 💾 数据管理:
2025/06/04 23:52:43 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:52:43 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:52:43 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:52:43 main.go:411: 
2025/06/04 23:52:43 main.go:412: 📋 事件查看:
2025/06/04 23:52:43 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:52:43 main.go:414: 
2025/06/04 23:52:43 main.go:415: 🎛️  批量控制:
2025/06/04 23:52:43 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:52:43 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:52:43 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:52:43 main.go:419: 
2025/06/04 23:52:43 main.go:420: 🗑️  任务管理:
2025/06/04 23:52:43 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:52:43 main.go:422: 
2025/06/04 23:52:43 main.go:423: 🔧 其他:
2025/06/04 23:52:43 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:52:43 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:52:43 main.go:426: ========================
2025/06/04 23:52:43 main.go:427: 
2025/06/04 23:52:44 main.go:808: M662000 停止   L      2500 0.020% 无仓位              2  23:52   0m  -0.25    -0.45   0.27 是       
2025/06/04 23:52:44 main.go:808: 5781000 停止   L      5000 0.200% 无仓位              0  23:52   0m   0.00     0.00   0.00 否       
2025/06/04 23:52:44 main.go:808: 8634000 停止   S      2000 0.300% 无仓位              0  23:52   0m   0.00     0.00   0.00 否       
2025/06/04 23:52:44 main.go:808: M119000 停止   S      2400 0.030% 无仓位              0  23:52   0m   0.00     0.00   0.00 是       
2025/06/04 23:53:09 main.go:1524: ✅ 报告已保存到文件: export/hedge_report_20250604_235309.txt
2025/06/04 23:53:09 main.go:1010: ✅ 详细状态已复制到剪切板
2025/06/04 23:53:09 main.go:1012: 📋 详细状态报告:
2025/06/04 23:53:54 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 23:53:54 main.go:220: 停止任务 M662000 失败: 任务已停止: M662000
2025/06/04 23:53:54 main.go:220: 停止任务 5781000 失败: 任务已停止: 5781000
2025/06/04 23:53:54 main.go:220: 停止任务 8634000 失败: 任务已停止: 8634000
2025/06/04 23:53:54 main.go:220: 停止任务 M119000 失败: 任务已停止: M119000
2025/06/04 23:56:12 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 23:56:12 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 23:56:12 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 23:56:12 main.go:190: API接口:
2025/06/04 23:56:12 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 23:56:12 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 23:56:12 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 23:56:12 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 23:56:12 main.go:195: 期权系统API接口:
2025/06/04 23:56:12 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 23:56:12 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 23:56:12 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 23:56:12 main.go:384: 
2025/06/04 23:56:12 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:56:12 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:56:12 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:56:12 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:56:12 main.go:390: 
2025/06/04 23:56:12 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:56:12 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:56:12 main.go:393: 
2025/06/04 23:56:12 main.go:394: ⚙️ 参数配置:
2025/06/04 23:56:12 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:56:12 api.go:35: API服务器启动，端口: 5879
2025/06/04 23:56:12 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:56:12 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:56:12 main.go:398: 
2025/06/04 23:56:12 main.go:399: 📊 状态控制:
2025/06/04 23:56:12 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:56:12 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:56:12 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:56:12 main.go:403: 
2025/06/04 23:56:12 main.go:404: 📋 数据导出:
2025/06/04 23:56:12 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:56:12 main.go:406: 
2025/06/04 23:56:12 main.go:407: 💾 数据管理:
2025/06/04 23:56:12 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:56:12 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:56:12 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:56:12 main.go:411: 
2025/06/04 23:56:12 main.go:412: 📋 事件查看:
2025/06/04 23:56:12 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:56:12 main.go:414: 
2025/06/04 23:56:12 main.go:415: 🎛️  批量控制:
2025/06/04 23:56:12 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:56:12 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:56:12 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:56:12 main.go:419: 
2025/06/04 23:56:12 main.go:420: 🗑️  任务管理:
2025/06/04 23:56:12 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:56:12 main.go:422: 
2025/06/04 23:56:12 main.go:423: 🔧 其他:
2025/06/04 23:56:12 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:56:12 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:56:12 main.go:426: ========================
2025/06/04 23:56:12 main.go:427: 
2025/06/04 23:57:37 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/04 23:57:37 状态变化:  -> running | 原因: 任务创建
2025/06/04 23:57:37 manager.go:94: 创建对冲任务: M864000, 交易对: ETHUSDT, 方向: long, 目标价: 3000, 来源: options
2025/06/04 23:57:37 hedge_task.go:45: 启动对冲任务: M864000
2025/06/04 23:57:37 api.go:283: API: 期权系统创建任务成功 - M864000 (来源: options)
2025/06/04 23:57:37 data_manager.go:184: 💾 任务数据已保存 (5个任务)
2025/06/04 23:57:37 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/04 23:57:47 main.go:808: M662000 停止   L      2500 0.020% 无仓位              2  23:57   0m  -0.25    -0.45   0.27 是       
2025/06/04 23:57:47 main.go:808: 5781000 停止   L      5000 0.200% 无仓位              0  23:57   0m   0.00     0.00   0.00 否       
2025/06/04 23:57:47 main.go:808: 8634000 停止   S      2000 0.300% 无仓位              0  23:57   0m   0.00     0.00   0.00 否       
2025/06/04 23:57:47 main.go:808: M119000 停止   S      2400 0.030% 无仓位              0  23:57   0m   0.00     0.00   0.00 是       
2025/06/04 23:57:47 main.go:808: M864000 运行   L      3000 0.020% 无仓位              0  23:57   0m   0.00     0.00   0.00 是       
2025/06/04 23:58:55 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/04 23:58:55 main.go:220: 停止任务 5781000 失败: 任务已停止: 5781000
2025/06/04 23:58:55 main.go:220: 停止任务 8634000 失败: 任务已停止: 8634000
2025/06/04 23:58:55 main.go:220: 停止任务 M119000 失败: 任务已停止: M119000
2025/06/04 23:58:55 main.go:220: 停止任务 M662000 失败: 任务已停止: M662000
2025/06/04 23:58:55 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/04 23:58:55 manager.go:141: 停止对冲任务: M864000
2025/06/04 23:58:55 hedge_task.go:60: 任务 M864000: 收到停止信号
2025/06/04 23:58:55 hedge_task.go:470: 任务 M864000: 无仓位，直接停止
2025/06/04 23:58:55 hedge_task.go:62: 对冲任务结束: M864000
2025/06/04 23:58:55 data_manager.go:184: 💾 任务数据已保存 (5个任务)
2025/06/04 23:58:55 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/04 23:59:35 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/04 23:59:35 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/04 23:59:35 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/04 23:59:35 main.go:190: API接口:
2025/06/04 23:59:35 main.go:191:   POST /task                           - 创建对冲任务
2025/06/04 23:59:35 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/04 23:59:35 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/04 23:59:35 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/04 23:59:35 main.go:195: 期权系统API接口:
2025/06/04 23:59:35 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/04 23:59:35 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/04 23:59:35 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/04 23:59:35 main.go:384: 
2025/06/04 23:59:35 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/04 23:59:35 api.go:35: API服务器启动，端口: 5879
2025/06/04 23:59:35 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/04 23:59:35 main.go:388:       示例: add long 3500 0.0005
2025/06/04 23:59:35 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/04 23:59:35 main.go:390: 
2025/06/04 23:59:35 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/04 23:59:35 main.go:392:    list                                     - 列出所有任务
2025/06/04 23:59:35 main.go:393: 
2025/06/04 23:59:35 main.go:394: ⚙️ 参数配置:
2025/06/04 23:59:35 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/04 23:59:35 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/04 23:59:35 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/04 23:59:35 main.go:398: 
2025/06/04 23:59:35 main.go:399: 📊 状态控制:
2025/06/04 23:59:35 main.go:400:    status                                   - 查看状态输出设置
2025/06/04 23:59:35 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/04 23:59:35 main.go:402:    status off                               - 停止状态输出
2025/06/04 23:59:35 main.go:403: 
2025/06/04 23:59:35 main.go:404: 📋 数据导出:
2025/06/04 23:59:35 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/04 23:59:35 main.go:406: 
2025/06/04 23:59:35 main.go:407: 💾 数据管理:
2025/06/04 23:59:35 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/04 23:59:35 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/04 23:59:35 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/04 23:59:35 main.go:411: 
2025/06/04 23:59:35 main.go:412: 📋 事件查看:
2025/06/04 23:59:35 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/04 23:59:35 main.go:414: 
2025/06/04 23:59:35 main.go:415: 🎛️  批量控制:
2025/06/04 23:59:35 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/04 23:59:35 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/04 23:59:35 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/04 23:59:35 main.go:419: 
2025/06/04 23:59:35 main.go:420: 🗑️  任务管理:
2025/06/04 23:59:35 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/04 23:59:35 main.go:422: 
2025/06/04 23:59:35 main.go:423: 🔧 其他:
2025/06/04 23:59:35 main.go:424:    help                                     - 显示此帮助信息
2025/06/04 23:59:35 main.go:425:    quit | exit                              - 退出程序
2025/06/04 23:59:35 main.go:426: ========================
2025/06/04 23:59:35 main.go:427: 
2025/06/04 23:59:48 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/04 23:59:48 状态变化:  -> running | 原因: 任务创建
2025/06/04 23:59:48 manager.go:94: 创建对冲任务: M892000, 交易对: ETHUSDT, 方向: long, 目标价: 3000, 来源: options
2025/06/04 23:59:48 hedge_task.go:45: 启动对冲任务: M892000
2025/06/04 23:59:48 api.go:283: API: 期权系统创建任务成功 - M892000 (来源: options)
2025/06/04 23:59:48 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/04 23:59:48 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 23:59:56 main.go:808: M892000 运行   L      3000 0.020% 无仓位              0  23:59   0m   0.00     0.00   0.00 是       
2025/06/05 00:00:58 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/05 00:00:58 状态变化:  -> running | 原因: 任务创建
2025/06/05 00:00:58 manager.go:94: 创建对冲任务: 6355000, 交易对: ETHUSDT, 方向: long, 目标价: 3000, 来源: manual
2025/06/05 00:00:58 hedge_task.go:45: 启动对冲任务: 6355000
2025/06/05 00:00:58 main.go:657: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=3000.00, 阈值=0.0200%, 数量=0.0100
2025/06/05 00:00:58 main.go:664: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/05 00:00:58 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:00:58 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:01:13 main.go:808: M892000 运行   L      3000 0.020% 无仓位              0  00:01   0m   0.00     0.00   0.00 是       
2025/06/05 00:01:13 main.go:808: 6355000 运行   L      3000 0.020% 无仓位              0  00:01   0m   0.00     0.00   0.00 是       
2025/06/05 00:02:12 main.go:808: M892000 运行   L      3000 0.020% 无仓位              0  00:02   0m   0.00     0.00   0.00 是       
2025/06/05 00:02:12 main.go:808: 6355000 运行   L      3000 0.020% 无仓位              0  00:02   0m   0.00     0.00   0.00 是       
2025/06/05 00:04:35 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:04:35 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:04:37 状态变化: running -> running | 原因: 数量更新: sell_open (0.010000 -> 0.030000)
2025/06/05 00:04:37 api.go:302: API: 任务数量更新成功 - M892000 (0.010000 -> 0.030000)
2025/06/05 00:04:37 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:04:37 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:05:58 api.go:314: API: 期权合约查询完成 - ETHUSDT-4JUN25-3000-C (存在: true)
2025/06/05 00:09:35 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:09:35 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:15:13 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:15:13 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:15:26 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/05 00:15:26 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/05 00:17:12 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/05 00:17:12 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/05 00:20:39 main.go:808: M892000 运行   L      3000 0.020% 无仓位              0  00:20   0m   0.00     0.00   0.00 是       
2025/06/05 00:20:39 main.go:808: 6355000 运行   L      3000 0.020% 无仓位              0  00:20   0m   0.00     0.00   0.00 是       
2025/06/05 00:21:12 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:21:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:26:12 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:26:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:31:12 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:31:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:33:06 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/05 00:36:12 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:36:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:36:46 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/05 00:36:46 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/05 00:36:46 manager.go:141: 停止对冲任务: M892000
2025/06/05 00:36:46 hedge_task.go:60: 任务 M892000: 收到停止信号
2025/06/05 00:36:46 hedge_task.go:470: 任务 M892000: 无仓位，直接停止
2025/06/05 00:36:46 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/05 00:36:46 hedge_task.go:62: 对冲任务结束: M892000
2025/06/05 00:36:46 manager.go:141: 停止对冲任务: 6355000
2025/06/05 00:36:46 hedge_task.go:60: 任务 6355000: 收到停止信号
2025/06/05 00:36:46 hedge_task.go:470: 任务 6355000: 无仓位，直接停止
2025/06/05 00:36:46 hedge_task.go:62: 对冲任务结束: 6355000
2025/06/05 00:36:46 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:36:46 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/05 00:36:46 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/05 00:36:46 data_manager.go:228: 💾 事件数据已保存 (0个事件)
