2025/06/06 21:29:08 main.go:185: 启动API服务模式（包含交互功能），端口: 5879
2025/06/06 21:29:08 main.go:186: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/06 21:29:08 main.go:202: API服务器已启动，访问地址: http://localhost:5879
2025/06/06 21:29:08 main.go:203: API接口:
2025/06/06 21:29:08 main.go:204:   POST /task                           - 创建对冲任务
2025/06/06 21:29:08 main.go:205:   POST /task/stop                      - 停止对冲任务
2025/06/06 21:29:08 main.go:206:   GET  /tasks                          - 获取所有任务
2025/06/06 21:29:08 main.go:207:   GET  /task/{id}                      - 获取单个任务
2025/06/06 21:29:08 main.go:208: 期权系统API接口:
2025/06/06 21:29:08 main.go:209:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/06 21:29:08 main.go:210:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/06 21:29:08 main.go:211:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/06 21:29:08 main.go:402: 
2025/06/06 21:29:08 main.go:403: 🔧 === 交互命令帮助 ===
2025/06/06 21:29:08 main.go:405:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/06 21:29:08 main.go:406:       示例: add long 3500 0.0005
2025/06/06 21:29:08 main.go:407:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/06 21:29:08 main.go:408: 
2025/06/06 21:29:08 main.go:409:    stop <task_id>                           - 停止指定任务
2025/06/06 21:29:08 main.go:410:    list                                     - 列出所有任务
2025/06/06 21:29:08 main.go:411: 
2025/06/06 21:29:08 main.go:412: ⚙️ 参数配置:
2025/06/06 21:29:08 main.go:413:    set <参数名> <值>                         - 设置默认参数
2025/06/06 21:29:08 main.go:414:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/06 21:29:08 main.go:415:    show defaults                            - 显示当前默认参数
2025/06/06 21:29:08 main.go:416: 
2025/06/06 21:29:08 main.go:417: 📊 状态控制:
2025/06/06 21:29:08 main.go:418:    status                                   - 查看状态输出设置
2025/06/06 21:29:08 main.go:419:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/06 21:29:08 main.go:420:    status off                               - 停止状态输出
2025/06/06 21:29:08 main.go:421: 
2025/06/06 21:29:08 main.go:422: 📋 数据导出:
2025/06/06 21:29:08 main.go:423:    export | detail                          - 导出详细状态到剪切板
2025/06/06 21:29:08 main.go:424: 
2025/06/06 21:29:08 main.go:425: 💾 数据管理:
2025/06/06 21:29:08 main.go:426:    save                                     - 手动保存数据到本地文件
2025/06/06 21:29:08 main.go:427:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/06 21:29:08 main.go:428:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/06 21:29:08 main.go:429: 
2025/06/06 21:29:08 api.go:35: API服务器启动，端口: 5879
2025/06/06 21:29:08 main.go:430: 📋 事件查看:
2025/06/06 21:29:08 main.go:431:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/06 21:29:08 main.go:432: 
2025/06/06 21:29:08 main.go:433: 🎛️  批量控制:
2025/06/06 21:29:08 main.go:434:    startall                                 - 启动所有已停止的任务
2025/06/06 21:29:08 main.go:435:    stopall                                  - 停止所有运行中的任务
2025/06/06 21:29:08 main.go:436:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/06 21:29:08 main.go:437: 
2025/06/06 21:29:08 main.go:438: 🗑️  任务管理:
2025/06/06 21:29:08 main.go:439:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/06 21:29:08 main.go:440: 
2025/06/06 21:29:08 main.go:441: 🔧 其他:
2025/06/06 21:29:08 main.go:442:    help                                     - 显示此帮助信息
2025/06/06 21:29:08 main.go:443:    quit | exit                              - 退出程序
2025/06/06 21:29:08 main.go:444: ========================
2025/06/06 21:29:08 main.go:445: 
2025/06/06 21:29:10 main.go:759: __________________________________________________________________________________________________________________________
2025/06/06 21:29:10 main.go:760: 
2025/06/06 21:29:10 main.go:861: 4245000 停止   L    2600 0.020% 0.010  无仓位                   4  18:30  27h   0.17     4.27   0.02 是   manual   -                     
2025/06/06 21:29:10 main.go:861: M512000 停止   L    2600 0.020% 0.100  无仓位                   4  17:12  28h   2.18     5.46   0.21 是   options  ETH-6JUN25-2600-C-USDT
2025/06/06 21:29:10 main.go:861: 8635000 停止   L    2300 0.020% 0.030  L0.01@2590            3  21:45  24h   0.04    98.31   0.02 是   manual   -                     
2025/06/06 21:29:10 main.go:879: __________________________________________________________________________________________________________________________
2025/06/06 21:30:44 main.go:227: 收到停止信号，正在停止所有任务...
2025/06/06 21:30:44 main.go:233: 停止任务 8635000 失败: 任务已停止: 8635000
2025/06/06 21:30:44 main.go:233: 停止任务 M512000 失败: 任务已停止: M512000
2025/06/06 21:30:44 main.go:233: 停止任务 4245000 失败: 任务已停止: 4245000
2025/06/06 21:30:54 main.go:185: 启动API服务模式（包含交互功能），端口: 5879
2025/06/06 21:30:54 main.go:186: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/06 21:30:54 main.go:202: API服务器已启动，访问地址: http://localhost:5879
2025/06/06 21:30:54 main.go:203: API接口:
2025/06/06 21:30:54 main.go:204:   POST /task                           - 创建对冲任务
2025/06/06 21:30:54 main.go:205:   POST /task/stop                      - 停止对冲任务
2025/06/06 21:30:54 main.go:206:   GET  /tasks                          - 获取所有任务
2025/06/06 21:30:54 main.go:207:   GET  /task/{id}                      - 获取单个任务
2025/06/06 21:30:54 main.go:208: 期权系统API接口:
2025/06/06 21:30:54 main.go:209:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/06 21:30:54 main.go:210:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/06 21:30:54 main.go:211:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/06 21:30:54 main.go:402: 
2025/06/06 21:30:54 main.go:403: 🔧 === 交互命令帮助 ===
2025/06/06 21:30:54 main.go:405:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/06 21:30:54 main.go:406:       示例: add long 3500 0.0005
2025/06/06 21:30:54 main.go:407:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/06 21:30:54 main.go:408: 
2025/06/06 21:30:54 main.go:409:    stop <task_id>                           - 停止指定任务
2025/06/06 21:30:54 main.go:410:    list                                     - 列出所有任务
2025/06/06 21:30:54 main.go:411: 
2025/06/06 21:30:54 main.go:412: ⚙️ 参数配置:
2025/06/06 21:30:54 main.go:413:    set <参数名> <值>                         - 设置默认参数
2025/06/06 21:30:54 main.go:414:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/06 21:30:54 main.go:415:    show defaults                            - 显示当前默认参数
2025/06/06 21:30:54 main.go:416: 
2025/06/06 21:30:54 main.go:417: 📊 状态控制:
2025/06/06 21:30:54 main.go:418:    status                                   - 查看状态输出设置
2025/06/06 21:30:54 main.go:419:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/06 21:30:54 main.go:420:    status off                               - 停止状态输出
2025/06/06 21:30:54 main.go:421: 
2025/06/06 21:30:54 main.go:422: 📋 数据导出:
2025/06/06 21:30:54 main.go:423:    export | detail                          - 导出详细状态到剪切板
2025/06/06 21:30:54 api.go:35: API服务器启动，端口: 5879
2025/06/06 21:30:54 main.go:424: 
2025/06/06 21:30:54 main.go:425: 💾 数据管理:
2025/06/06 21:30:54 main.go:426:    save                                     - 手动保存数据到本地文件
2025/06/06 21:30:54 main.go:427:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/06 21:30:54 main.go:428:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/06 21:30:54 main.go:429: 
2025/06/06 21:30:54 main.go:430: 📋 事件查看:
2025/06/06 21:30:54 main.go:431:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/06 21:30:54 main.go:432: 
2025/06/06 21:30:54 main.go:433: 🎛️  批量控制:
2025/06/06 21:30:54 main.go:434:    startall                                 - 启动所有已停止的任务
2025/06/06 21:30:54 main.go:435:    stopall                                  - 停止所有运行中的任务
2025/06/06 21:30:54 main.go:436:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/06 21:30:54 main.go:437: 
2025/06/06 21:30:54 main.go:438: 🗑️  任务管理:
2025/06/06 21:30:54 main.go:439:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/06 21:30:54 main.go:440: 
2025/06/06 21:30:54 main.go:441: 🔧 其他:
2025/06/06 21:30:54 main.go:442:    help                                     - 显示此帮助信息
2025/06/06 21:30:54 main.go:443:    quit | exit                              - 退出程序
2025/06/06 21:30:54 main.go:444: ========================
2025/06/06 21:30:54 main.go:445: 
2025/06/06 21:31:21 main.go:726: 📋 当前没有运行的任务
2025/06/06 21:31:53 manager.go:203: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/06 21:31:53 状态变化:  -> running | 原因: 任务创建
2025/06/06 21:31:53 manager.go:98: 创建对冲任务: 5440000, 交易对: ETHUSDT, 方向: long, 目标价: 2480, 来源: manual
2025/06/06 21:31:53 main.go:694: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2480.00, 阈值=0.0200%, 数量=0.0100
2025/06/06 21:31:53 hedge_task.go:50: 启动对冲任务: 5440000
2025/06/06 21:31:53 main.go:701: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/06 21:31:53 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/06 21:31:53 data_manager.go:233: 💾 事件数据已保存 (0个事件)
2025/06/06 21:31:54 websocket_manager.go:73: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/06 21:31:54 binance_client.go:391: ✅ listenKey创建成功: paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:31:54 websocket_manager.go:73: WebSocket连接成功: wss://fstream.binance.com/ws/paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:31:54 smart_order.go:104: 任务5440000智能订单管理器初始化成功
2025/06/06 21:31:54 hedge_task.go:914: 任务 5440000: 智能订单系统初始化成功
2025/06/06 21:31:55 websocket_manager.go:73: WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth@100ms
2025/06/06 21:31:55 market_data_manager.go:169: 成功创建ETHUSDT市场数据连接: wss://fstream.binance.com/ws/ethusdt@depth@100ms
2025/06/06 21:31:55 market_data_manager.go:115: 任务5440000成功订阅ETHUSDT市场数据，当前订阅者数量: 1
2025/06/06 21:32:17 main.go:759: __________________________________________________________________________________________________________________________
2025/06/06 21:32:17 main.go:760: 
2025/06/06 21:32:17 main.go:861: 5440000 运行   L    2480 0.020% 0.010  无仓位                   0  21:31   0m   0.00     0.00   0.00 是   manual   -                     
2025/06/06 21:32:17 main.go:879: __________________________________________________________________________________________________________________________
2025/06/06 21:32:48 hedge_task.go:1066: 任务 5440000: 触发智能开仓，方向: long
2025/06/06 21:32:48 smart_order.go:206: 任务5440000发送智能下单请求: long 0.01 5440000_long_1749216768826629000_001
2025/06/06 21:32:49 websocket_manager.go:162: WebSocket读取消息失败: websocket: close 1008 (policy violation): disconnected
2025/06/06 21:32:49 websocket_manager.go:298: WebSocket开始重连，第1次尝试
2025/06/06 21:32:49 hedge_task.go:963: 任务 5440000: 交易WebSocket错误: websocket: close 1008 (policy violation): disconnected
2025/06/06 21:32:51 smart_order.go:364: 任务5440000请求5440000_place_order_1749216768826634000超时，主动查询状态
2025/06/06 21:32:54 websocket_manager.go:73: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/06 21:32:54 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:32:54 hedge_task.go:965: 任务 5440000: 交易WebSocket重连成功
2025/06/06 21:33:22 main.go:759: __________________________________________________________________________________________________________________________
2025/06/06 21:33:22 main.go:760: 
2025/06/06 21:33:22 main.go:861: 5440000 运行   L    2480 0.020% 0.010  无仓位                   0  21:31   1m   0.00     0.00   0.00 是   manual   -                     
2025/06/06 21:33:22 main.go:879: __________________________________________________________________________________________________________________________
2025/06/06 21:33:28 main.go:1600: ✅ 报告已保存到文件: export/hedge_report_20250606_213328.txt
2025/06/06 21:33:28 main.go:1063: ✅ 详细状态已复制到剪切板
2025/06/06 21:33:28 main.go:1065: 📋 详细状态报告:
2025/06/06 21:33:40 main.go:759: __________________________________________________________________________________________________________________________
2025/06/06 21:33:40 main.go:760: 
2025/06/06 21:33:40 main.go:861: 5440000 运行   L    2480 0.020% 0.010  无仓位                   0  21:31   2m   0.00     0.00   0.00 是   manual   -                     
2025/06/06 21:33:40 main.go:879: __________________________________________________________________________________________________________________________
2025/06/06 21:34:29 websocket_manager.go:162: WebSocket读取消息失败: websocket: close 1006 (abnormal closure): unexpected EOF
2025/06/06 21:34:29 websocket_manager.go:298: WebSocket开始重连，第1次尝试
2025/06/06 21:34:29 hedge_task.go:963: 任务 5440000: 交易WebSocket错误: websocket: close 1006 (abnormal closure): unexpected EOF
2025/06/06 21:34:35 websocket_manager.go:73: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/06 21:34:35 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:34:35 hedge_task.go:965: 任务 5440000: 交易WebSocket重连成功
2025/06/06 21:34:47 websocket_manager.go:162: WebSocket读取消息失败: websocket: close 1006 (abnormal closure): unexpected EOF
2025/06/06 21:34:47 websocket_manager.go:298: WebSocket开始重连，第1次尝试
2025/06/06 21:34:47 hedge_task.go:975: 任务 5440000: 用户数据WebSocket错误: websocket: close 1006 (abnormal closure): unexpected EOF
2025/06/06 21:34:52 binance_client.go:391: ✅ listenKey创建成功: paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:34:52 websocket_manager.go:73: WebSocket连接成功: wss://fstream.binance.com/ws/paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:34:52 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:34:52 hedge_task.go:977: 任务 5440000: 用户数据WebSocket重连成功
2025/06/06 21:34:54 websocket_manager.go:239: WebSocket pong超时，触发重连
2025/06/06 21:34:54 websocket_manager.go:298: WebSocket开始重连，第1次尝试
2025/06/06 21:34:54 websocket_manager.go:239: WebSocket pong超时，触发重连
2025/06/06 21:34:54 websocket_manager.go:298: WebSocket开始重连，第1次尝试
2025/06/06 21:34:55 websocket_manager.go:239: WebSocket pong超时，触发重连
2025/06/06 21:34:55 websocket_manager.go:298: WebSocket开始重连，第1次尝试
2025/06/06 21:34:59 websocket_manager.go:73: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/06 21:34:59 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:34:59 hedge_task.go:965: 任务 5440000: 交易WebSocket重连成功
2025/06/06 21:35:00 binance_client.go:391: ✅ listenKey创建成功: paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:35:00 websocket_manager.go:73: WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth@100ms
2025/06/06 21:35:00 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:35:00 market_data_manager.go:197: ETHUSDT市场数据重连成功
2025/06/06 21:35:00 websocket_manager.go:73: WebSocket连接成功: wss://fstream.binance.com/ws/paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:35:00 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:35:00 hedge_task.go:977: 任务 5440000: 用户数据WebSocket重连成功
2025/06/06 21:35:04 websocket_manager.go:162: WebSocket读取消息失败: websocket: RSV2 set, RSV3 set
2025/06/06 21:35:04 websocket_manager.go:298: WebSocket开始重连，第1次尝试
2025/06/06 21:35:04 websocket_manager.go:162: WebSocket读取消息失败: websocket: RSV1 set, RSV3 set, bad opcode 13
2025/06/06 21:35:04 websocket_manager.go:298: WebSocket开始重连，第2次尝试
2025/06/06 21:35:04 market_data_manager.go:195: ETHUSDT市场数据错误: websocket: RSV2 set, RSV3 set
2025/06/06 21:35:04 market_data_manager.go:195: ETHUSDT市场数据错误: websocket: RSV1 set, RSV3 set, bad opcode 13
2025/06/06 21:35:09 websocket_manager.go:73: WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth@100ms
2025/06/06 21:35:09 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:35:09 market_data_manager.go:197: ETHUSDT市场数据重连成功
2025/06/06 21:35:09 websocket_manager.go:328: WebSocket重连成功
2025/06/06 21:35:09 market_data_manager.go:197: ETHUSDT市场数据重连成功
2025/06/06 21:35:20 main.go:227: 收到停止信号，正在停止所有任务...
2025/06/06 21:35:20 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/06 21:35:20 manager.go:145: 停止对冲任务: 5440000
2025/06/06 21:35:20 hedge_task.go:104: 任务 5440000: 收到停止信号
2025/06/06 21:35:20 hedge_task.go:519: 任务 5440000: 无仓位，直接停止
2025/06/06 21:35:20 websocket_manager.go:162: WebSocket读取消息失败: read tcp 198.18.0.1:57971->198.18.1.239:443: use of closed network connection
2025/06/06 21:35:20 websocket_manager.go:99: WebSocket连接已断开: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/06 21:35:20 websocket_manager.go:162: WebSocket读取消息失败: read tcp 198.18.0.1:57973->198.18.0.45:443: use of closed network connection
2025/06/06 21:35:20 websocket_manager.go:99: WebSocket连接已断开: wss://fstream.binance.com/ws/paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:35:20 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/06 21:35:20 data_manager.go:233: 💾 事件数据已保存 (0个事件)
2025/06/06 21:35:20 binance_client.go:413: ✅ listenKey删除成功: paI9GmFRqbtAz7ombyaFdjPatOyGHhqw6uEJFOUT4Unsr3rDVPMHxlZcSejBUFyE
2025/06/06 21:35:20 smart_order.go:127: 任务5440000智能订单管理器已关闭
2025/06/06 21:35:20 market_data_manager.go:132: 任务5440000取消订阅ETHUSDT市场数据，剩余订阅者数量: 0
2025/06/06 21:35:20 market_data_manager.go:138: 关闭ETHUSDT市场数据连接（无订阅者）
2025/06/06 21:35:20 websocket_manager.go:162: WebSocket读取消息失败: read tcp 198.18.0.1:58031->198.18.0.45:443: use of closed network connection
2025/06/06 21:35:20 websocket_manager.go:99: WebSocket连接已断开: wss://fstream.binance.com/ws/ethusdt@depth@100ms
2025/06/06 21:35:20 hedge_task.go:930: 任务 5440000: 智能订单系统已清理
2025/06/06 21:35:20 hedge_task.go:106: 对冲任务结束: 5440000
2025/06/06 21:35:20 market_data_manager.go:195: ETHUSDT市场数据错误: read tcp 198.18.0.1:58031->198.18.0.45:443: use of closed network connection
