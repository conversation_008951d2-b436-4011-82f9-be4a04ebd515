2025/05/31 18:09:58 main.go:770: 2199000 停止   L      2527 0.002% 无仓位              4  18:09   0m   0.03     1.21   0.02 否       
2025/05/31 18:09:58 main.go:770: 2315000 停止   L      2524 0.002% 无仓位              4  18:09   0m   0.03     1.30   0.03 否       
2025/05/31 18:09:58 main.go:770: 4697000 停止   L      2520 0.020% 无仓位              2  18:09   0m   0.01     0.28   0.01 是       
2025/05/31 18:10:02 main.go:483: ❌ 未知命令: start，输入 'help' 查看帮助
2025/05/31 18:10:05 状态变化: stopped -> running | 原因: 批量启动
2025/05/31 18:10:05 状态变化: stopped -> running | 原因: 批量启动
2025/05/31 18:10:05 状态变化: stopped -> running | 原因: 批量启动
2025/05/31 18:10:05 hedge_task.go:45: 启动对冲任务: 2199000
2025/05/31 18:10:05 hedge_task.go:45: 启动对冲任务: 4697000
2025/05/31 18:10:05 hedge_task.go:45: 启动对冲任务: 2315000
2025/05/31 18:10:05 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:10:05 data_manager.go:228: 💾 事件数据已保存 (10个事件)
2025/05/31 18:10:06 hedge_task.go:116: 任务 4697000: 触发做多开仓条件，当前价格: 2523.835, 触发价格: 2520.504
2025/05/31 18:10:06 hedge_task.go:151: 任务 4697000: 开始执行下单，类型: open, 方向: long, 触发价格: 2520.504
2025/05/31 18:10:06 hedge_task.go:176: 任务 4697000: 计算理论价格: 2520.504 (事件类型: open, 方向: long)
2025/05/31 18:10:07 hedge_task.go:280: 任务 4697000: 挂买入限价单，价格: 2523.83, 数量: 0.01
2025/05/31 18:10:07 hedge_task.go:286: 任务 4697000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:10:07 hedge_task.go:199: 任务 4697000: 下单失败，不更新仓位状态
2025/05/31 18:10:07 hedge_task.go:116: 任务 4697000: 触发做多开仓条件，当前价格: 2523.145, 触发价格: 2520.504
2025/05/31 18:10:07 hedge_task.go:151: 任务 4697000: 开始执行下单，类型: open, 方向: long, 触发价格: 2520.504
2025/05/31 18:10:07 hedge_task.go:176: 任务 4697000: 计算理论价格: 2520.504 (事件类型: open, 方向: long)
2025/05/31 18:10:08 hedge_task.go:280: 任务 4697000: 挂买入限价单，价格: 2523.14, 数量: 0.01
2025/05/31 18:10:08 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2523.14, 订单ID: 8389765898328965196, 模式: POST_ONLY
2025/05/31 18:10:11 hedge_task.go:116: 任务 2315000: 触发做多开仓条件，当前价格: 2524.09, 触发价格: 2524.05048
2025/05/31 18:10:11 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: open, 方向: long, 触发价格: 2524.05048
2025/05/31 18:10:11 hedge_task.go:176: 任务 2315000: 计算理论价格: 2524.05048 (事件类型: open, 方向: long)
2025/05/31 18:10:11 hedge_task.go:280: 任务 2315000: 挂买入限价单，价格: 2524.08, 数量: 0.01
2025/05/31 18:10:11 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2524.08, 订单ID: 8389765898328986651, 模式: GTC
2025/05/31 18:10:13 hedge_task.go:426: 任务 4697000: 订单 8389765898328965196 超时，尝试撤销
2025/05/31 18:10:14 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898328965196
2025/05/31 18:10:14 hedge_task.go:195: 任务 4697000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:10:14 hedge_task.go:116: 任务 4697000: 触发做多开仓条件，当前价格: 2524.175, 触发价格: 2520.504
2025/05/31 18:10:14 hedge_task.go:151: 任务 4697000: 开始执行下单，类型: open, 方向: long, 触发价格: 2520.504
2025/05/31 18:10:14 hedge_task.go:176: 任务 4697000: 计算理论价格: 2520.504 (事件类型: open, 方向: long)
2025/05/31 18:10:14 hedge_task.go:280: 任务 4697000: 挂买入限价单，价格: 2524.17, 数量: 0.01
2025/05/31 18:10:15 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2524.17, 订单ID: 8389765898329008040, 模式: POST_ONLY
2025/05/31 18:10:16 hedge_task.go:453: 任务 2315000: 订单 8389765898328986651 已成交，价格: 2524.08, 成交方式: maker
2025/05/31 18:10:16 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:10:16 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2524.05048 | 实际价: 2524.08 | 损耗: 0.02952 | 手续费: 0.00504816 | 订单ID: 8389765898328986651
2025/05/31 18:10:16 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2524.08, 理论价格: 2524.05048, 价格损耗: 0.02952, 订单ID: 8389765898328986651
2025/05/31 18:10:16 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2523.535, 触发价格: 2523.94952
2025/05/31 18:10:16 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:10:16 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:10:16 hedge_task.go:453: 任务 4697000: 订单 8389765898329008040 已成交，价格: 2524.17, 成交方式: maker
2025/05/31 18:10:16 hedge_task.go:204: 任务 4697000: 订单成交，记录事件和更新仓位
2025/05/31 18:10:16 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2520.504 | 实际价: 2524.17 | 损耗: 3.666 | 手续费: 0.00504834 | 订单ID: 8389765898329008040
2025/05/31 18:10:16 hedge_task.go:235: 任务 4697000: 下单完成，实际价格: 2524.17, 理论价格: 2520.504, 价格损耗: 3.666, 订单ID: 8389765898329008040
2025/05/31 18:10:16 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:10:16 data_manager.go:228: 💾 事件数据已保存 (12个事件)
2025/05/31 18:10:17 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2523.54, 数量: 0.01
2025/05/31 18:10:17 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523.54, 订单ID: 8389765898329014318, 模式: GTC
2025/05/31 18:10:22 hedge_task.go:426: 任务 2315000: 订单 8389765898329014318 超时，尝试撤销
2025/05/31 18:10:22 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898329014318
2025/05/31 18:10:22 hedge_task.go:195: 任务 2315000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:10:22 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2522.765, 触发价格: 2523.94952
2025/05/31 18:10:22 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:10:22 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:10:22 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:10:22 data_manager.go:228: 💾 事件数据已保存 (12个事件)
2025/05/31 18:10:23 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2522.77, 数量: 0.01
2025/05/31 18:10:23 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.77, 订单ID: 8389765898329075857, 模式: GTC
2025/05/31 18:10:28 hedge_task.go:426: 任务 2315000: 订单 8389765898329075857 超时，尝试撤销
2025/05/31 18:10:28 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898329075857
2025/05/31 18:10:28 hedge_task.go:195: 任务 2315000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:10:28 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2522.355, 触发价格: 2523.94952
2025/05/31 18:10:28 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:10:28 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:10:28 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:10:28 data_manager.go:228: 💾 事件数据已保存 (12个事件)
2025/05/31 18:10:29 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2522.36, 数量: 0.01
2025/05/31 18:10:29 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.36, 订单ID: 8389765898329100134, 模式: GTC
2025/05/31 18:10:30 hedge_task.go:453: 任务 2315000: 订单 8389765898329100134 已成交，价格: 2522.69, 成交方式: taker
2025/05/31 18:10:30 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:10:30 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2523.94952 | 实际价: 2522.69 | 损耗: 1.25952 | 手续费: 0.01009076 | 订单ID: 8389765898329100134
2025/05/31 18:10:30 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2522.69, 理论价格: 2523.94952, 价格损耗: 1.25952, 订单ID: 8389765898329100134
2025/05/31 18:10:30 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:10:30 data_manager.go:228: 💾 事件数据已保存 (13个事件)
2025/05/31 18:10:30 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:10:30 data_manager.go:228: 💾 事件数据已保存 (13个事件)
2025/05/31 18:10:30 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:10:30 data_manager.go:228: 💾 事件数据已保存 (13个事件)
2025/05/31 18:10:45 main.go:770: 2315000 运行   L      2524 0.002% 无仓位              6  18:10   0m   0.03     1.08   0.04 否       
2025/05/31 18:10:45 main.go:770: 2199000 运行   L      2527 0.002% 无仓位              4  18:10   0m   0.03     1.21   0.02 否       
2025/05/31 18:10:45 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:10   0m   0.01     1.41   0.02 是       
2025/05/31 18:11:21 hedge_task.go:116: 任务 2315000: 触发做多开仓条件，当前价格: 2524.135, 触发价格: 2524.05048
2025/05/31 18:11:21 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: open, 方向: long, 触发价格: 2524.05048
2025/05/31 18:11:21 hedge_task.go:176: 任务 2315000: 计算理论价格: 2524.05048 (事件类型: open, 方向: long)
2025/05/31 18:11:21 hedge_task.go:280: 任务 2315000: 挂买入限价单，价格: 2524.13, 数量: 0.01
2025/05/31 18:11:21 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2524.13, 订单ID: 8389765898329498134, 模式: GTC
2025/05/31 18:11:26 hedge_task.go:426: 任务 2315000: 订单 8389765898329498134 超时，尝试撤销
2025/05/31 18:11:27 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898329498134
2025/05/31 18:11:27 hedge_task.go:195: 任务 2315000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:11:27 hedge_task.go:116: 任务 2315000: 触发做多开仓条件，当前价格: 2524.995, 触发价格: 2524.05048
2025/05/31 18:11:27 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: open, 方向: long, 触发价格: 2524.05048
2025/05/31 18:11:27 hedge_task.go:176: 任务 2315000: 计算理论价格: 2524.05048 (事件类型: open, 方向: long)
2025/05/31 18:11:27 hedge_task.go:280: 任务 2315000: 挂买入限价单，价格: 2524.99, 数量: 0.01
2025/05/31 18:11:27 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2524.99, 订单ID: 8389765898329524091, 模式: GTC
2025/05/31 18:11:31 hedge_task.go:453: 任务 2315000: 订单 8389765898329524091 已成交，价格: 2524.99, 成交方式: maker
2025/05/31 18:11:31 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:11:31 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2524.05048 | 实际价: 2524.99 | 损耗: 0.93952 | 手续费: 0.00504998 | 订单ID: 8389765898329524091
2025/05/31 18:11:31 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2524.99, 理论价格: 2524.05048, 价格损耗: 0.93952, 订单ID: 8389765898329524091
2025/05/31 18:11:31 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:11:31 data_manager.go:228: 💾 事件数据已保存 (14个事件)
2025/05/31 18:11:31 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:11:31 data_manager.go:228: 💾 事件数据已保存 (14个事件)
2025/05/31 18:11:40 main.go:999: ❌ 获取任务事件失败: 任务不存在: events
2025/05/31 18:12:39 main.go:770: 2315000 运行   L      2524 0.002% L0.01@2524       7  18:12   0m   0.03     1.06   0.05 否       
2025/05/31 18:12:39 main.go:770: 2199000 运行   L      2527 0.002% 无仓位              4  18:12   0m   0.03     1.21   0.02 否       
2025/05/31 18:12:39 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:12   0m   0.01     1.41   0.02 是       
2025/05/31 18:12:46 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:12:46 data_manager.go:228: 💾 事件数据已保存 (14个事件)
2025/05/31 18:12:50 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2523.945, 触发价格: 2523.94952
2025/05/31 18:12:50 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:12:50 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:12:51 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2523.95, 数量: 0.01
2025/05/31 18:12:52 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523.95, 订单ID: 8389765898330009571, 模式: GTC
2025/05/31 18:12:53 hedge_task.go:453: 任务 2315000: 订单 8389765898330009571 已成交，价格: 2524.2, 成交方式: taker
2025/05/31 18:12:53 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:12:53 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2523.94952 | 实际价: 2524.2 | 损耗: -0.25048 | 手续费: 0.0100968 | 订单ID: 8389765898330009571
2025/05/31 18:12:53 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2524.2, 理论价格: 2523.94952, 价格损耗: -0.25048, 订单ID: 8389765898330009571
2025/05/31 18:12:53 hedge_task.go:116: 任务 2315000: 触发做多开仓条件，当前价格: 2524.205, 触发价格: 2524.05048
2025/05/31 18:12:53 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: open, 方向: long, 触发价格: 2524.05048
2025/05/31 18:12:53 hedge_task.go:176: 任务 2315000: 计算理论价格: 2524.05048 (事件类型: open, 方向: long)
2025/05/31 18:12:53 hedge_task.go:280: 任务 2315000: 挂买入限价单，价格: 2524.2, 数量: 0.01
2025/05/31 18:12:54 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2524.2, 订单ID: 8389765898330052543, 模式: GTC
2025/05/31 18:12:55 hedge_task.go:453: 任务 2315000: 订单 8389765898330052543 已成交，价格: 2524.2, 成交方式: maker
2025/05/31 18:12:55 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:12:55 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2524.05048 | 实际价: 2524.2 | 损耗: 0.14952 | 手续费: 0.0050484 | 订单ID: 8389765898330052543
2025/05/31 18:12:55 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2524.2, 理论价格: 2524.05048, 价格损耗: 0.14952, 订单ID: 8389765898330052543
2025/05/31 18:12:55 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2523.705, 触发价格: 2523.94952
2025/05/31 18:12:55 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:12:55 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:12:55 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:12:55 data_manager.go:228: 💾 事件数据已保存 (16个事件)
2025/05/31 18:12:55 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2523.71, 数量: 0.01
2025/05/31 18:12:56 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523.71, 订单ID: 8389765898330062174, 模式: GTC
2025/05/31 18:12:57 hedge_task.go:453: 任务 2315000: 订单 8389765898330062174 已成交，价格: 2523.78, 成交方式: taker
2025/05/31 18:12:57 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:12:57 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2523.94952 | 实际价: 2523.78 | 损耗: 0.16952 | 手续费: 0.01009512 | 订单ID: 8389765898330062174
2025/05/31 18:12:57 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2523.78, 理论价格: 2523.94952, 价格损耗: 0.16952, 订单ID: 8389765898330062174
2025/05/31 18:12:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:12:57 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:12:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:12:57 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:12:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:12:57 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:12:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:12:57 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:12:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:12:57 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:13:04 main.go:130: 收到停止信号，正在停止所有任务...
2025/05/31 18:13:04 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/05/31 18:13:04 manager.go:123: 停止对冲任务: 2315000
2025/05/31 18:13:04 hedge_task.go:60: 任务 2315000: 收到停止信号
2025/05/31 18:13:04 hedge_task.go:470: 任务 2315000: 无仓位，直接停止
2025/05/31 18:13:04 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/05/31 18:13:04 hedge_task.go:62: 对冲任务结束: 2315000
2025/05/31 18:13:04 manager.go:123: 停止对冲任务: 4697000
2025/05/31 18:13:04 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/05/31 18:13:04 manager.go:123: 停止对冲任务: 2199000
2025/05/31 18:13:04 hedge_task.go:60: 任务 2199000: 收到停止信号
2025/05/31 18:13:04 hedge_task.go:470: 任务 2199000: 无仓位，直接停止
2025/05/31 18:13:04 hedge_task.go:62: 对冲任务结束: 2199000
2025/05/31 18:13:04 hedge_task.go:60: 任务 4697000: 收到停止信号
2025/05/31 18:13:04 hedge_task.go:476: 任务 4697000: 跳过平仓，保留仓位: long 0.01
2025/05/31 18:13:04 hedge_task.go:62: 对冲任务结束: 4697000
2025/05/31 18:13:04 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:13:04 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:13:04 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:13:04 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:13:04 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:13:04 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:13:11 main.go:109: 启动本地测试模式...
2025/05/31 18:13:11 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/05/31 18:13:11 main.go:352: 
2025/05/31 18:13:11 main.go:353: 🔧 === 交互命令帮助 ===
2025/05/31 18:13:11 main.go:355:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/05/31 18:13:11 main.go:356:       示例: add long 3500 0.0005
2025/05/31 18:13:11 main.go:357:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/05/31 18:13:11 main.go:358: 
2025/05/31 18:13:11 main.go:359:    stop <task_id>                           - 停止指定任务
2025/05/31 18:13:11 main.go:360:    list                                     - 列出所有任务
2025/05/31 18:13:11 main.go:361: 
2025/05/31 18:13:11 main.go:362: ⚙️ 参数配置:
2025/05/31 18:13:11 main.go:363:    set <参数名> <值>                         - 设置默认参数
2025/05/31 18:13:11 main.go:364:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/05/31 18:13:11 main.go:365:    show defaults                            - 显示当前默认参数
2025/05/31 18:13:11 main.go:366: 
2025/05/31 18:13:11 main.go:367: 📊 状态控制:
2025/05/31 18:13:11 main.go:368:    status                                   - 查看状态输出设置
2025/05/31 18:13:11 main.go:369:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/05/31 18:13:11 main.go:370:    status off                               - 停止状态输出
2025/05/31 18:13:11 main.go:371: 
2025/05/31 18:13:11 main.go:372: 📋 数据导出:
2025/05/31 18:13:11 main.go:373:    export | detail                          - 导出详细状态到剪切板
2025/05/31 18:13:11 main.go:374: 
2025/05/31 18:13:11 main.go:375: 💾 数据管理:
2025/05/31 18:13:11 main.go:376:    save                                     - 手动保存数据到本地文件
2025/05/31 18:13:11 main.go:377:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/05/31 18:13:11 main.go:378:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/05/31 18:13:11 main.go:379: 
2025/05/31 18:13:11 main.go:380: 📋 事件查看:
2025/05/31 18:13:11 main.go:381:    events <task_id>                         - 查看指定任务的所有事件日志
2025/05/31 18:13:11 main.go:382: 
2025/05/31 18:13:11 main.go:383: 🎛️  批量控制:
2025/05/31 18:13:11 main.go:384:    startall                                 - 启动所有已停止的任务
2025/05/31 18:13:11 main.go:385:    stopall                                  - 停止所有运行中的任务
2025/05/31 18:13:11 main.go:386:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/05/31 18:13:11 main.go:387: 
2025/05/31 18:13:11 main.go:388: 🗑️  任务管理:
2025/05/31 18:13:11 main.go:389:    delete <task_id>                         - 删除指定任务及其所有记录
2025/05/31 18:13:11 main.go:390: 
2025/05/31 18:13:11 main.go:391: 🔧 其他:
2025/05/31 18:13:11 main.go:392:    help                                     - 显示此帮助信息
2025/05/31 18:13:11 main.go:393:    quit | exit                              - 退出程序
2025/05/31 18:13:11 main.go:394: ========================
2025/05/31 18:13:11 main.go:395: 
2025/05/31 18:13:13 main.go:770: 2315000 停止   L      2524 0.002% 无仓位             10  18:13   0m   0.01     0.75   0.07 否       
2025/05/31 18:13:13 main.go:770: 2199000 停止   L      2527 0.002% 无仓位              4  18:13   0m   0.03     1.21   0.02 否       
2025/05/31 18:13:13 main.go:770: 4697000 停止   L      2520 0.020% L0.01@2524       3  18:13   0m   0.01     1.41   0.02 是       
2025/05/31 18:13:16 状态变化: stopped -> running | 原因: 批量启动
2025/05/31 18:13:16 状态变化: stopped -> running | 原因: 批量启动
2025/05/31 18:13:16 状态变化: stopped -> running | 原因: 批量启动
2025/05/31 18:13:16 hedge_task.go:45: 启动对冲任务: 2199000
2025/05/31 18:13:16 hedge_task.go:45: 启动对冲任务: 2315000
2025/05/31 18:13:16 hedge_task.go:45: 启动对冲任务: 4697000
2025/05/31 18:13:16 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:13:16 data_manager.go:228: 💾 事件数据已保存 (17个事件)
2025/05/31 18:13:16 hedge_task.go:116: 任务 2315000: 触发做多开仓条件，当前价格: 2524.635, 触发价格: 2524.05048
2025/05/31 18:13:16 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: open, 方向: long, 触发价格: 2524.05048
2025/05/31 18:13:16 hedge_task.go:176: 任务 2315000: 计算理论价格: 2524.05048 (事件类型: open, 方向: long)
2025/05/31 18:13:17 hedge_task.go:280: 任务 2315000: 挂买入限价单，价格: 2524.63, 数量: 0.01
2025/05/31 18:13:17 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2524.63, 订单ID: 8389765898330204409, 模式: GTC
2025/05/31 18:13:21 hedge_task.go:453: 任务 2315000: 订单 8389765898330204409 已成交，价格: 2524.63, 成交方式: maker
2025/05/31 18:13:21 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:13:21 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2524.05048 | 实际价: 2524.63 | 损耗: 0.57952 | 手续费: 0.00504926 | 订单ID: 8389765898330204409
2025/05/31 18:13:21 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2524.63, 理论价格: 2524.05048, 价格损耗: 0.57952, 订单ID: 8389765898330204409
2025/05/31 18:13:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:13:21 data_manager.go:228: 💾 事件数据已保存 (18个事件)
2025/05/31 18:13:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:13:21 data_manager.go:228: 💾 事件数据已保存 (18个事件)
2025/05/31 18:13:35 main.go:770: 2315000 运行   L      2524 0.002% L0.01@2524      11  18:13   0m   0.01     0.73   0.08 否       
2025/05/31 18:13:35 main.go:770: 2199000 运行   L      2527 0.002% 无仓位              4  18:13   0m   0.03     1.21   0.02 否       
2025/05/31 18:13:35 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:13   0m   0.01     1.41   0.02 是       
2025/05/31 18:14:09 main.go:770: 2315000 运行   L      2524 0.002% L0.01@2524      11  18:14   0m   0.01     0.73   0.08 否       
2025/05/31 18:14:09 main.go:770: 2199000 运行   L      2527 0.002% 无仓位              4  18:14   0m   0.03     1.21   0.02 否       
2025/05/31 18:14:09 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:14   0m   0.01     1.41   0.02 是       
2025/05/31 18:14:31 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2527.365, 触发价格: 2527.05054
2025/05/31 18:14:31 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:14:31 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:14:32 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2527.32, 数量: 0.01
2025/05/31 18:14:32 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2527.32, 订单ID: 8389765898330655583, 模式: GTC
2025/05/31 18:14:37 hedge_task.go:426: 任务 2199000: 订单 8389765898330655583 超时，尝试撤销
2025/05/31 18:14:38 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898330655583
2025/05/31 18:14:38 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:14:38 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2532.075, 触发价格: 2527.05054
2025/05/31 18:14:38 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:14:38 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:14:39 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2532.07, 数量: 0.01
2025/05/31 18:14:40 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2532.07, 订单ID: 8389765898330719608, 模式: GTC
2025/05/31 18:14:45 hedge_task.go:426: 任务 2199000: 订单 8389765898330719608 超时，尝试撤销
2025/05/31 18:14:45 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898330719608
2025/05/31 18:14:45 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:14:45 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2536.5, 触发价格: 2527.05054
2025/05/31 18:14:45 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:14:45 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:14:46 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2536.49, 数量: 0.01
2025/05/31 18:14:46 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2536.49, 订单ID: 8389765898330742287, 模式: GTC
2025/05/31 18:14:48 hedge_task.go:453: 任务 2199000: 订单 8389765898330742287 已成交，价格: 2536.49, 成交方式: maker
2025/05/31 18:14:48 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:14:48 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2527.05054 | 实际价: 2536.49 | 损耗: 9.43946 | 手续费: 0.00507298 | 订单ID: 8389765898330742287
2025/05/31 18:14:48 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2536.49, 理论价格: 2527.05054, 价格损耗: 9.43946, 订单ID: 8389765898330742287
2025/05/31 18:14:48 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:14:48 data_manager.go:228: 💾 事件数据已保存 (19个事件)
2025/05/31 18:14:48 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:14:48 data_manager.go:228: 💾 事件数据已保存 (19个事件)
2025/05/31 18:14:52 main.go:770: 2315000 运行   L      2524 0.002% L0.01@2524      11  18:14   0m   0.01     0.73   0.08 否       
2025/05/31 18:14:52 main.go:770: 2199000 运行   L      2527 0.002% L0.01@2536       5  18:14   0m   0.03     2.86   0.03 否       
2025/05/31 18:14:52 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:14   0m   0.01     1.41   0.02 是       
2025/05/31 18:14:55 main.go:1463: ✅ 报告已保存到文件: export/hedge_report_20250531_181455.txt
2025/05/31 18:14:55 main.go:972: ✅ 详细状态已复制到剪切板
2025/05/31 18:14:55 main.go:974: 📋 详细状态报告:
2025/05/31 18:18:11 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:18:11 data_manager.go:228: 💾 事件数据已保存 (19个事件)
2025/05/31 18:18:29 main.go:770: 2315000 运行   L      2524 0.002% L0.01@2524      11  18:18   0m   0.01     0.73   0.08 否       
2025/05/31 18:18:29 main.go:770: 2199000 运行   L      2527 0.002% L0.01@2536       5  18:18   0m   0.03     2.86   0.03 否       
2025/05/31 18:18:29 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:18   0m   0.01     1.41   0.02 是       
2025/05/31 18:21:10 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.845, 触发价格: 2526.94946
2025/05/31 18:21:10 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:21:10 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:21:11 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.85, 数量: 0.01
2025/05/31 18:21:12 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.85, 订单ID: 8389765898333311154, 模式: GTC
2025/05/31 18:21:15 hedge_task.go:453: 任务 2199000: 订单 8389765898333311154 已成交，价格: 2526.85, 成交方式: maker
2025/05/31 18:21:15 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:21:15 事件类型: close | 订单类型: limit | 成交方式: maker | 理论价: 2526.94946 | 实际价: 2526.85 | 损耗: 0.09946 | 手续费: 0.0050537 | 订单ID: 8389765898333311154
2025/05/31 18:21:15 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2526.85, 理论价格: 2526.94946, 价格损耗: 0.09946, 订单ID: 8389765898333311154
2025/05/31 18:21:15 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2527.845, 触发价格: 2527.05054
2025/05/31 18:21:15 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:21:15 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:21:16 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2527.81, 数量: 0.01
2025/05/31 18:21:16 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2527.81, 订单ID: 8389765898333357251, 模式: GTC
2025/05/31 18:21:17 hedge_task.go:453: 任务 2199000: 订单 8389765898333357251 已成交，价格: 2527.63, 成交方式: taker
2025/05/31 18:21:17 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:21:17 事件类型: open | 订单类型: limit | 成交方式: taker | 理论价: 2527.05054 | 实际价: 2527.63 | 损耗: 0.57946 | 手续费: 0.01011052 | 订单ID: 8389765898333357251
2025/05/31 18:21:17 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2527.63, 理论价格: 2527.05054, 价格损耗: 0.57946, 订单ID: 8389765898333357251
2025/05/31 18:21:17 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:21:17 data_manager.go:228: 💾 事件数据已保存 (21个事件)
2025/05/31 18:21:17 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:21:17 data_manager.go:228: 💾 事件数据已保存 (21个事件)
2025/05/31 18:21:17 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:21:17 data_manager.go:228: 💾 事件数据已保存 (21个事件)
2025/05/31 18:21:17 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:21:17 data_manager.go:228: 💾 事件数据已保存 (21个事件)
2025/05/31 18:21:19 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.785, 触发价格: 2526.94946
2025/05/31 18:21:19 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:21:19 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:21:19 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.79, 数量: 0.01
2025/05/31 18:21:20 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.79, 订单ID: 8389765898333386756, 模式: GTC
2025/05/31 18:21:25 hedge_task.go:426: 任务 2199000: 订单 8389765898333386756 超时，尝试撤销
2025/05/31 18:21:25 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898333386756
2025/05/31 18:21:25 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:21:25 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.585, 触发价格: 2526.94946
2025/05/31 18:21:25 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:21:25 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:21:26 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.59, 数量: 0.01
2025/05/31 18:21:26 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.59, 订单ID: 8389765898333443674, 模式: GTC
2025/05/31 18:21:27 hedge_task.go:453: 任务 2199000: 订单 8389765898333443674 已成交，价格: 2526.86, 成交方式: taker
2025/05/31 18:21:27 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:21:27 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2526.94946 | 实际价: 2526.86 | 损耗: 0.08946 | 手续费: 0.01010744 | 订单ID: 8389765898333443674
2025/05/31 18:21:27 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2526.86, 理论价格: 2526.94946, 价格损耗: 0.08946, 订单ID: 8389765898333443674
2025/05/31 18:21:27 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:21:27 data_manager.go:228: 💾 事件数据已保存 (22个事件)
2025/05/31 18:21:27 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:21:27 data_manager.go:228: 💾 事件数据已保存 (22个事件)
2025/05/31 18:22:06 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2527.12, 触发价格: 2527.05054
2025/05/31 18:22:06 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:22:06 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:22:06 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2527.11, 数量: 0.01
2025/05/31 18:22:06 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2527.11, 订单ID: 8389765898333673546, 模式: GTC
2025/05/31 18:22:08 hedge_task.go:453: 任务 2199000: 订单 8389765898333673546 已成交，价格: 2527.11, 成交方式: maker
2025/05/31 18:22:08 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:22:08 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2527.05054 | 实际价: 2527.11 | 损耗: 0.05946 | 手续费: 0.00505422 | 订单ID: 8389765898333673546
2025/05/31 18:22:08 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2527.11, 理论价格: 2527.05054, 价格损耗: 0.05946, 订单ID: 8389765898333673546
2025/05/31 18:22:08 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:22:08 data_manager.go:228: 💾 事件数据已保存 (23个事件)
2025/05/31 18:22:08 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:22:08 data_manager.go:228: 💾 事件数据已保存 (23个事件)
2025/05/31 18:23:11 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:23:11 data_manager.go:228: 💾 事件数据已保存 (23个事件)
2025/05/31 18:24:32 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.775, 触发价格: 2526.94946
2025/05/31 18:24:32 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:24:32 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:24:33 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.78, 数量: 0.01
2025/05/31 18:24:33 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.78, 订单ID: 8389765898334368986, 模式: GTC
2025/05/31 18:24:38 hedge_task.go:426: 任务 2199000: 订单 8389765898334368986 超时，尝试撤销
2025/05/31 18:24:39 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334368986
2025/05/31 18:24:39 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:24:39 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.505, 触发价格: 2526.94946
2025/05/31 18:24:39 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:24:39 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:24:39 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.51, 数量: 0.01
2025/05/31 18:24:39 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.51, 订单ID: 8389765898334401330, 模式: GTC
2025/05/31 18:24:44 hedge_task.go:426: 任务 2199000: 订单 8389765898334401330 超时，尝试撤销
2025/05/31 18:24:45 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334401330
2025/05/31 18:24:45 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:24:45 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.505, 触发价格: 2526.94946
2025/05/31 18:24:45 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:24:45 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:24:45 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.51, 数量: 0.01
2025/05/31 18:24:46 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.51, 订单ID: 8389765898334439862, 模式: GTC
2025/05/31 18:24:47 hedge_task.go:453: 任务 2199000: 订单 8389765898334439862 已成交，价格: 2526.51, 成交方式: maker
2025/05/31 18:24:47 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:24:47 事件类型: close | 订单类型: limit | 成交方式: maker | 理论价: 2526.94946 | 实际价: 2526.51 | 损耗: 0.43946 | 手续费: 0.00505302 | 订单ID: 8389765898334439862
2025/05/31 18:24:47 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2526.51, 理论价格: 2526.94946, 价格损耗: 0.43946, 订单ID: 8389765898334439862
2025/05/31 18:24:47 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2527.255, 触发价格: 2527.05054
2025/05/31 18:24:47 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:24:47 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:24:48 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2527.25, 数量: 0.01
2025/05/31 18:24:48 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2527.25, 订单ID: 8389765898334468236, 模式: GTC
2025/05/31 18:24:49 hedge_task.go:453: 任务 2199000: 订单 8389765898334468236 已成交，价格: 2527.22, 成交方式: taker
2025/05/31 18:24:49 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:24:49 事件类型: open | 订单类型: limit | 成交方式: taker | 理论价: 2527.05054 | 实际价: 2527.22 | 损耗: 0.16946 | 手续费: 0.01010888 | 订单ID: 8389765898334468236
2025/05/31 18:24:49 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2527.22, 理论价格: 2527.05054, 价格损耗: 0.16946, 订单ID: 8389765898334468236
2025/05/31 18:24:49 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.845, 触发价格: 2526.94946
2025/05/31 18:24:49 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:24:49 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:24:49 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:24:49 data_manager.go:228: 💾 事件数据已保存 (25个事件)
2025/05/31 18:24:49 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.85, 数量: 0.01
2025/05/31 18:24:50 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.85, 订单ID: 8389765898334481625, 模式: GTC
2025/05/31 18:24:55 hedge_task.go:426: 任务 2199000: 订单 8389765898334481625 超时，尝试撤销
2025/05/31 18:24:55 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334481625
2025/05/31 18:24:55 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:24:55 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.605, 触发价格: 2526.94946
2025/05/31 18:24:55 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:24:55 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:24:55 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:24:55 data_manager.go:228: 💾 事件数据已保存 (25个事件)
2025/05/31 18:24:56 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.61, 数量: 0.01
2025/05/31 18:24:56 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.61, 订单ID: 8389765898334498960, 模式: GTC
2025/05/31 18:25:01 hedge_task.go:426: 任务 2199000: 订单 8389765898334498960 超时，尝试撤销
2025/05/31 18:25:01 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334498960
2025/05/31 18:25:01 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:25:01 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.065, 触发价格: 2526.94946
2025/05/31 18:25:01 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:25:01 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:25:01 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:25:01 data_manager.go:228: 💾 事件数据已保存 (25个事件)
2025/05/31 18:25:02 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.07, 数量: 0.01
2025/05/31 18:25:02 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.07, 订单ID: 8389765898334513852, 模式: GTC
2025/05/31 18:25:03 hedge_task.go:453: 任务 2199000: 订单 8389765898334513852 已成交，价格: 2526.12, 成交方式: taker
2025/05/31 18:25:03 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:25:03 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2526.94946 | 实际价: 2526.12 | 损耗: 0.82946 | 手续费: 0.01010448 | 订单ID: 8389765898334513852
2025/05/31 18:25:03 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2526.12, 理论价格: 2526.94946, 价格损耗: 0.82946, 订单ID: 8389765898334513852
2025/05/31 18:25:03 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:25:03 data_manager.go:228: 💾 事件数据已保存 (26个事件)
2025/05/31 18:25:03 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:25:03 data_manager.go:228: 💾 事件数据已保存 (26个事件)
2025/05/31 18:25:03 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:25:03 data_manager.go:228: 💾 事件数据已保存 (26个事件)
2025/05/31 18:25:43 main.go:770: 2199000 运行   L      2527 0.002% 无仓位             12  18:25   0m   0.09     1.38   0.08 否       
2025/05/31 18:25:43 main.go:770: 2315000 运行   L      2524 0.002% L0.01@2524      11  18:25   0m   0.01     0.73   0.08 否       
2025/05/31 18:25:43 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:25   0m   0.01     1.41   0.02 是       
2025/05/31 18:26:07 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2527.105, 触发价格: 2527.05054
2025/05/31 18:26:07 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:26:07 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:26:08 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2527.08, 数量: 0.01
2025/05/31 18:26:09 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2527.08, 订单ID: 8389765898334758469, 模式: GTC
2025/05/31 18:26:14 hedge_task.go:426: 任务 2199000: 订单 8389765898334758469 超时，尝试撤销
2025/05/31 18:26:14 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334758469
2025/05/31 18:26:14 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:26:14 hedge_task.go:116: 任务 2199000: 触发做多开仓条件，当前价格: 2528.345, 触发价格: 2527.05054
2025/05/31 18:26:14 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: open, 方向: long, 触发价格: 2527.05054
2025/05/31 18:26:14 hedge_task.go:176: 任务 2199000: 计算理论价格: 2527.05054 (事件类型: open, 方向: long)
2025/05/31 18:26:15 hedge_task.go:280: 任务 2199000: 挂买入限价单，价格: 2528.34, 数量: 0.01
2025/05/31 18:26:15 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2528.34, 订单ID: 8389765898334798439, 模式: GTC
2025/05/31 18:26:20 hedge_task.go:453: 任务 2199000: 订单 8389765898334798439 已成交，价格: 2528.34, 成交方式: maker
2025/05/31 18:26:20 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:26:20 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2527.05054 | 实际价: 2528.34 | 损耗: 1.28946 | 手续费: 0.00505668 | 订单ID: 8389765898334798439
2025/05/31 18:26:20 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2528.34, 理论价格: 2527.05054, 价格损耗: 1.28946, 订单ID: 8389765898334798439
2025/05/31 18:26:20 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:26:20 data_manager.go:228: 💾 事件数据已保存 (27个事件)
2025/05/31 18:26:20 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:26:20 data_manager.go:228: 💾 事件数据已保存 (27个事件)
2025/05/31 18:26:23 main.go:352: 
2025/05/31 18:26:23 main.go:353: 🔧 === 交互命令帮助 ===
2025/05/31 18:26:23 main.go:355:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/05/31 18:26:23 main.go:356:       示例: add long 3500 0.0005
2025/05/31 18:26:23 main.go:357:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/05/31 18:26:23 main.go:358: 
2025/05/31 18:26:23 main.go:359:    stop <task_id>                           - 停止指定任务
2025/05/31 18:26:23 main.go:360:    list                                     - 列出所有任务
2025/05/31 18:26:23 main.go:361: 
2025/05/31 18:26:23 main.go:362: ⚙️ 参数配置:
2025/05/31 18:26:23 main.go:363:    set <参数名> <值>                         - 设置默认参数
2025/05/31 18:26:23 main.go:364:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/05/31 18:26:23 main.go:365:    show defaults                            - 显示当前默认参数
2025/05/31 18:26:23 main.go:366: 
2025/05/31 18:26:23 main.go:367: 📊 状态控制:
2025/05/31 18:26:23 main.go:368:    status                                   - 查看状态输出设置
2025/05/31 18:26:23 main.go:369:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/05/31 18:26:23 main.go:370:    status off                               - 停止状态输出
2025/05/31 18:26:23 main.go:371: 
2025/05/31 18:26:23 main.go:372: 📋 数据导出:
2025/05/31 18:26:23 main.go:373:    export | detail                          - 导出详细状态到剪切板
2025/05/31 18:26:23 main.go:374: 
2025/05/31 18:26:23 main.go:375: 💾 数据管理:
2025/05/31 18:26:23 main.go:376:    save                                     - 手动保存数据到本地文件
2025/05/31 18:26:23 main.go:377:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/05/31 18:26:23 main.go:378:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/05/31 18:26:23 main.go:379: 
2025/05/31 18:26:23 main.go:380: 📋 事件查看:
2025/05/31 18:26:23 main.go:381:    events <task_id>                         - 查看指定任务的所有事件日志
2025/05/31 18:26:23 main.go:382: 
2025/05/31 18:26:23 main.go:383: 🎛️  批量控制:
2025/05/31 18:26:23 main.go:384:    startall                                 - 启动所有已停止的任务
2025/05/31 18:26:23 main.go:385:    stopall                                  - 停止所有运行中的任务
2025/05/31 18:26:23 main.go:386:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/05/31 18:26:23 main.go:387: 
2025/05/31 18:26:23 main.go:388: 🗑️  任务管理:
2025/05/31 18:26:23 main.go:389:    delete <task_id>                         - 删除指定任务及其所有记录
2025/05/31 18:26:23 main.go:390: 
2025/05/31 18:26:23 main.go:391: 🔧 其他:
2025/05/31 18:26:23 main.go:392:    help                                     - 显示此帮助信息
2025/05/31 18:26:23 main.go:393:    quit | exit                              - 退出程序
2025/05/31 18:26:23 main.go:394: ========================
2025/05/31 18:26:23 main.go:395: 
2025/05/31 18:26:37 main.go:1463: ✅ 报告已保存到文件: export/hedge_report_20250531_182637.txt
2025/05/31 18:26:37 main.go:972: ✅ 详细状态已复制到剪切板
2025/05/31 18:26:37 main.go:974: 📋 详细状态报告:
2025/05/31 18:26:39 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2526.72, 触发价格: 2526.94946
2025/05/31 18:26:39 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:26:39 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:26:39 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2526.78, 数量: 0.01
2025/05/31 18:26:39 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2526.78, 订单ID: 8389765898334882926, 模式: GTC
2025/05/31 18:26:44 hedge_task.go:426: 任务 2199000: 订单 8389765898334882926 超时，尝试撤销
2025/05/31 18:26:45 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334882926
2025/05/31 18:26:45 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:26:45 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2525.875, 触发价格: 2526.94946
2025/05/31 18:26:45 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:26:45 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:26:45 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2525.88, 数量: 0.01
2025/05/31 18:26:46 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2525.88, 订单ID: 8389765898334890347, 模式: GTC
2025/05/31 18:26:51 hedge_task.go:426: 任务 2199000: 订单 8389765898334890347 超时，尝试撤销
2025/05/31 18:26:51 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334890347
2025/05/31 18:26:51 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:26:51 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2525.805, 触发价格: 2526.94946
2025/05/31 18:26:51 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:26:51 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:26:52 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2525.81, 数量: 0.01
2025/05/31 18:26:52 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2525.81, 订单ID: 8389765898334899339, 模式: GTC
2025/05/31 18:26:57 hedge_task.go:426: 任务 2199000: 订单 8389765898334899339 超时，尝试撤销
2025/05/31 18:26:57 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334899339
2025/05/31 18:26:57 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:26:57 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2525.425, 触发价格: 2526.94946
2025/05/31 18:26:57 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:26:57 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:26:58 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2525.43, 数量: 0.01
2025/05/31 18:26:58 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2525.43, 订单ID: 8389765898334913330, 模式: GTC
2025/05/31 18:27:03 hedge_task.go:426: 任务 2199000: 订单 8389765898334913330 超时，尝试撤销
2025/05/31 18:27:03 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898334913330
2025/05/31 18:27:03 hedge_task.go:195: 任务 2199000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:27:03 hedge_task.go:135: 任务 2199000: 触发多仓平仓条件，当前价格: 2524.845, 触发价格: 2526.94946
2025/05/31 18:27:03 hedge_task.go:151: 任务 2199000: 开始执行下单，类型: close, 方向: short, 触发价格: 2526.94946
2025/05/31 18:27:03 hedge_task.go:176: 任务 2199000: 计算理论价格: 2526.94946 (事件类型: close, 方向: short)
2025/05/31 18:27:04 hedge_task.go:309: 任务 2199000: 挂卖出限价单，价格: 2524.85, 数量: 0.01
2025/05/31 18:27:04 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2524.85, 订单ID: 8389765898334957537, 模式: GTC
2025/05/31 18:27:05 hedge_task.go:453: 任务 2199000: 订单 8389765898334957537 已成交，价格: 2525.12, 成交方式: taker
2025/05/31 18:27:05 hedge_task.go:204: 任务 2199000: 订单成交，记录事件和更新仓位
2025/05/31 18:27:05 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2526.94946 | 实际价: 2525.12 | 损耗: 1.82946 | 手续费: 0.01010048 | 订单ID: 8389765898334957537
2025/05/31 18:27:05 hedge_task.go:235: 任务 2199000: 下单完成，实际价格: 2525.12, 理论价格: 2526.94946, 价格损耗: 1.82946, 订单ID: 8389765898334957537
2025/05/31 18:27:05 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:27:05 data_manager.go:228: 💾 事件数据已保存 (28个事件)
2025/05/31 18:27:05 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:27:05 data_manager.go:228: 💾 事件数据已保存 (28个事件)
2025/05/31 18:27:09 main.go:770: 2199000 运行   L      2527 0.002% 无仓位             14  18:27   0m   0.11     1.40   0.10 否       
2025/05/31 18:27:09 main.go:770: 2315000 运行   L      2524 0.002% L0.01@2524      11  18:27   0m   0.01     0.73   0.08 否       
2025/05/31 18:27:09 main.go:770: 4697000 运行   L      2520 0.020% L0.01@2524       3  18:27   0m   0.01     1.41   0.02 是       
2025/05/31 18:27:18 main.go:483: ❌ 未知命令: yes，输入 'help' 查看帮助
2025/05/31 18:27:18 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2523.415, 触发价格: 2523.94952
2025/05/31 18:27:18 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:27:18 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:27:19 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2523.81, 数量: 0.01
2025/05/31 18:27:19 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523.81, 订单ID: 8389765898335036173, 模式: GTC
2025/05/31 18:27:24 hedge_task.go:426: 任务 2315000: 订单 8389765898335036173 超时，尝试撤销
2025/05/31 18:27:24 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335036173
2025/05/31 18:27:24 hedge_task.go:195: 任务 2315000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:27:24 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2523.215, 触发价格: 2523.94952
2025/05/31 18:27:24 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:27:24 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:27:25 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2523.22, 数量: 0.01
2025/05/31 18:27:25 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523.22, 订单ID: 8389765898335073526, 模式: GTC
2025/05/31 18:27:30 hedge_task.go:426: 任务 2315000: 订单 8389765898335073526 超时，尝试撤销
2025/05/31 18:27:30 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335073526
2025/05/31 18:27:30 hedge_task.go:195: 任务 2315000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 18:27:30 hedge_task.go:135: 任务 2315000: 触发多仓平仓条件，当前价格: 2523.025, 触发价格: 2523.94952
2025/05/31 18:27:30 hedge_task.go:151: 任务 2315000: 开始执行下单，类型: close, 方向: short, 触发价格: 2523.94952
2025/05/31 18:27:30 hedge_task.go:176: 任务 2315000: 计算理论价格: 2523.94952 (事件类型: close, 方向: short)
2025/05/31 18:27:31 hedge_task.go:309: 任务 2315000: 挂卖出限价单，价格: 2523.03, 数量: 0.01
2025/05/31 18:27:31 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523.03, 订单ID: 8389765898335082436, 模式: GTC
2025/05/31 18:27:32 hedge_task.go:453: 任务 2315000: 订单 8389765898335082436 已成交，价格: 2523.29, 成交方式: taker
2025/05/31 18:27:32 hedge_task.go:204: 任务 2315000: 订单成交，记录事件和更新仓位
2025/05/31 18:27:32 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2523.94952 | 实际价: 2523.29 | 损耗: 0.65952 | 手续费: 0.01009316 | 订单ID: 8389765898335082436
2025/05/31 18:27:32 hedge_task.go:235: 任务 2315000: 下单完成，实际价格: 2523.29, 理论价格: 2523.94952, 价格损耗: 0.65952, 订单ID: 8389765898335082436
2025/05/31 18:27:32 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:27:32 data_manager.go:228: 💾 事件数据已保存 (29个事件)
2025/05/31 18:27:32 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:27:32 data_manager.go:228: 💾 事件数据已保存 (29个事件)
2025/05/31 18:27:45 main.go:640: ❌ 停止任务失败: 任务不存在: all
2025/05/31 18:27:48 状态变化: running -> stopped | 原因: 手动停止
2025/05/31 18:27:48 manager.go:123: 停止对冲任务: 2199000
2025/05/31 18:27:48 状态变化: running -> stopped | 原因: 手动停止
2025/05/31 18:27:48 manager.go:123: 停止对冲任务: 2315000
2025/05/31 18:27:48 状态变化: running -> stopped | 原因: 手动停止
2025/05/31 18:27:48 manager.go:123: 停止对冲任务: 4697000
2025/05/31 18:27:48 hedge_task.go:60: 任务 4697000: 收到停止信号
2025/05/31 18:27:48 hedge_task.go:481: 任务 4697000: 开始停止平仓，仓位: long 0.01
2025/05/31 18:27:48 hedge_task.go:492: 任务 4697000: 做多平仓，理论平仓价: 2519.496 (目标价-阈值)
2025/05/31 18:27:48 hedge_task.go:506: 任务 4697000: 开始动态平仓，理论价格: 2519.496, 方向: short
2025/05/31 18:27:48 hedge_task.go:60: 任务 2199000: 收到停止信号
2025/05/31 18:27:48 hedge_task.go:470: 任务 2199000: 无仓位，直接停止
2025/05/31 18:27:48 hedge_task.go:62: 对冲任务结束: 2199000
2025/05/31 18:27:48 hedge_task.go:60: 任务 2315000: 收到停止信号
2025/05/31 18:27:48 hedge_task.go:470: 任务 2315000: 无仓位，直接停止
2025/05/31 18:27:48 hedge_task.go:62: 对冲任务结束: 2315000
2025/05/31 18:27:48 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.65, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:49 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.65, 数量: 0.01, 方向: short
2025/05/31 18:27:49 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:49 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.4, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:50 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:50 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.4, 数量: 0.01, 方向: short
2025/05/31 18:27:50 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:50 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.64, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:51 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:51 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.64, 数量: 0.01, 方向: short
2025/05/31 18:27:52 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:52 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:52 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:52 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:53 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:53 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:53 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:53 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:54 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:54 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:54 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:54 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:54 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:54 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:55 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:55 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:55 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:55 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:56 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:56 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:56 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:56 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:57 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:57 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:57 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:57 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:58 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:58 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:58 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:58 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2520.97, 范围: [2494.30104, 2544.69096]
2025/05/31 18:27:59 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:27:59 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2520.97, 数量: 0.01, 方向: short
2025/05/31 18:27:59 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:27:59 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2521.05, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:00 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:00 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2521.05, 数量: 0.01, 方向: short
2025/05/31 18:28:00 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:28:00 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2521.2, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:01 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:01 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2521.2, 数量: 0.01, 方向: short
2025/05/31 18:28:01 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:28:01 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2521.75, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:01 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:02 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2521.75, 数量: 0.01, 方向: short
2025/05/31 18:28:02 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:28:02 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.3, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:02 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:03 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.3, 数量: 0.01, 方向: short
2025/05/31 18:28:03 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:28:03 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.3, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:03 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:04 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.3, 数量: 0.01, 方向: short
2025/05/31 18:28:04 hedge_task.go:643: 任务 4697000: 平仓下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/05/31 18:28:04 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.67, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:04 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:05 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.67, 数量: 0.01, 方向: short
2025/05/31 18:28:05 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.67, 订单ID: 8389765898335263408, 模式: POST_ONLY
2025/05/31 18:28:05 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.78, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:05 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335263408
2025/05/31 18:28:05 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:06 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335263408
2025/05/31 18:28:06 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.78, 数量: 0.01, 方向: short
2025/05/31 18:28:07 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.78, 订单ID: 8389765898335274031, 模式: POST_ONLY
2025/05/31 18:28:07 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.78, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:07 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335274031
2025/05/31 18:28:07 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:07 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335274031
2025/05/31 18:28:08 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.78, 数量: 0.01, 方向: short
2025/05/31 18:28:08 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.78, 订单ID: 8389765898335280359, 模式: POST_ONLY
2025/05/31 18:28:08 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.91, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:08 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335280359
2025/05/31 18:28:08 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335280359
2025/05/31 18:28:08 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:09 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.91, 数量: 0.01, 方向: short
2025/05/31 18:28:09 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.91, 订单ID: 8389765898335283201, 模式: POST_ONLY
2025/05/31 18:28:09 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2523, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:09 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335283201
2025/05/31 18:28:10 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335283201
2025/05/31 18:28:10 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:10 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2523, 数量: 0.01, 方向: short
2025/05/31 18:28:11 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523, 订单ID: 8389765898335288804, 模式: POST_ONLY
2025/05/31 18:28:11 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2523, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:11 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335288804
2025/05/31 18:28:11 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:11 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335288804
2025/05/31 18:28:12 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2523, 数量: 0.01, 方向: short
2025/05/31 18:28:12 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523, 订单ID: 8389765898335297569, 模式: POST_ONLY
2025/05/31 18:28:12 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2523, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:12 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335297569
2025/05/31 18:28:12 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335297569
2025/05/31 18:28:12 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:13 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2523, 数量: 0.01, 方向: short
2025/05/31 18:28:13 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523, 订单ID: 8389765898335302228, 模式: POST_ONLY
2025/05/31 18:28:13 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2523, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:13 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335302228
2025/05/31 18:28:14 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:14 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335302228
2025/05/31 18:28:14 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2523, 数量: 0.01, 方向: short
2025/05/31 18:28:15 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2523, 订单ID: 8389765898335303663, 模式: POST_ONLY
2025/05/31 18:28:15 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.91, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:15 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335303663
2025/05/31 18:28:15 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:15 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335303663
2025/05/31 18:28:16 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.91, 数量: 0.01, 方向: short
2025/05/31 18:28:16 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.91, 订单ID: 8389765898335322616, 模式: POST_ONLY
2025/05/31 18:28:16 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.81, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:16 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335322616
2025/05/31 18:28:16 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:17 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335322616
2025/05/31 18:28:17 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.81, 数量: 0.01, 方向: short
2025/05/31 18:28:17 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.81, 订单ID: 8389765898335327858, 模式: POST_ONLY
2025/05/31 18:28:17 hedge_task.go:600: 任务 4697000: 检查卖出平仓条件 - 卖一价: 2522.81, 范围: [2494.30104, 2544.69096]
2025/05/31 18:28:17 hedge_task.go:541: 任务 4697000: 撤销当前平仓订单: 8389765898335327858
2025/05/31 18:28:18 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335327858
2025/05/31 18:28:18 binance_client.go:251: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 18:28:18 hedge_task.go:638: 任务 4697000: 下平仓限价单，价格: 2522.81, 数量: 0.01, 方向: short
2025/05/31 18:28:19 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2522.81, 订单ID: 8389765898335329940, 模式: POST_ONLY
2025/05/31 18:28:19 hedge_task.go:527: 任务 4697000: 平仓超时，强制市价平仓
2025/05/31 18:28:19 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898335329940
2025/05/31 18:28:19 hedge_task.go:661: 任务 4697000: 强制市价平仓，数量: 0.01, 方向: short
2025/05/31 18:28:19 binance_client.go:114: ✅ 市价单已下达: ETHUSDT SELL 0.01, 订单ID: 8389765898335330424
2025/05/31 18:28:21 main.go:130: 收到停止信号，正在停止所有任务...
2025/05/31 18:28:21 main.go:136: 停止任务 2199000 失败: 任务已停止: 2199000
2025/05/31 18:28:21 main.go:136: 停止任务 2315000 失败: 任务已停止: 2315000
2025/05/31 18:28:21 main.go:136: 停止任务 4697000 失败: 任务已停止: 4697000
2025/05/31 18:28:21 hedge_task.go:689: 任务 4697000: 市价平仓完成，实际价格: 2521.44
2025/05/31 18:28:21 hedge_task.go:62: 对冲任务结束: 4697000
2025/05/31 18:28:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:28:21 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 18:28:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:28:21 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 18:28:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:28:21 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 18:28:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:28:21 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 18:28:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:28:21 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 18:28:41 main.go:109: 启动本地测试模式...
2025/05/31 18:28:41 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/05/31 18:28:41 main.go:352: 
2025/05/31 18:28:41 main.go:353: 🔧 === 交互命令帮助 ===
2025/05/31 18:28:41 main.go:355:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/05/31 18:28:41 main.go:356:       示例: add long 3500 0.0005
2025/05/31 18:28:41 main.go:357:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/05/31 18:28:41 main.go:358: 
2025/05/31 18:28:41 main.go:359:    stop <task_id>                           - 停止指定任务
2025/05/31 18:28:41 main.go:360:    list                                     - 列出所有任务
2025/05/31 18:28:41 main.go:361: 
2025/05/31 18:28:41 main.go:362: ⚙️ 参数配置:
2025/05/31 18:28:41 main.go:363:    set <参数名> <值>                         - 设置默认参数
2025/05/31 18:28:41 main.go:364:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/05/31 18:28:41 main.go:365:    show defaults                            - 显示当前默认参数
2025/05/31 18:28:41 main.go:366: 
2025/05/31 18:28:41 main.go:367: 📊 状态控制:
2025/05/31 18:28:41 main.go:368:    status                                   - 查看状态输出设置
2025/05/31 18:28:41 main.go:369:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/05/31 18:28:41 main.go:370:    status off                               - 停止状态输出
2025/05/31 18:28:41 main.go:371: 
2025/05/31 18:28:41 main.go:372: 📋 数据导出:
2025/05/31 18:28:41 main.go:373:    export | detail                          - 导出详细状态到剪切板
2025/05/31 18:28:41 main.go:374: 
2025/05/31 18:28:41 main.go:375: 💾 数据管理:
2025/05/31 18:28:41 main.go:376:    save                                     - 手动保存数据到本地文件
2025/05/31 18:28:41 main.go:377:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/05/31 18:28:41 main.go:378:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/05/31 18:28:41 main.go:379: 
2025/05/31 18:28:41 main.go:380: 📋 事件查看:
2025/05/31 18:28:41 main.go:381:    events <task_id>                         - 查看指定任务的所有事件日志
2025/05/31 18:28:41 main.go:382: 
2025/05/31 18:28:41 main.go:383: 🎛️  批量控制:
2025/05/31 18:28:41 main.go:384:    startall                                 - 启动所有已停止的任务
2025/05/31 18:28:41 main.go:385:    stopall                                  - 停止所有运行中的任务
2025/05/31 18:28:41 main.go:386:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/05/31 18:28:41 main.go:387: 
2025/05/31 18:28:41 main.go:388: 🗑️  任务管理:
2025/05/31 18:28:41 main.go:389:    delete <task_id>                         - 删除指定任务及其所有记录
2025/05/31 18:28:41 main.go:390: 
2025/05/31 18:28:41 main.go:391: 🔧 其他:
2025/05/31 18:28:41 main.go:392:    help                                     - 显示此帮助信息
2025/05/31 18:28:41 main.go:393:    quit | exit                              - 退出程序
2025/05/31 18:28:41 main.go:394: ========================
2025/05/31 18:28:41 main.go:395: 
2025/05/31 18:28:43 main.go:770: 2199000 停止   L      2527 0.002% 无仓位             14  18:28   0m   0.11     1.40   0.10 否       
2025/05/31 18:28:43 main.go:770: 2315000 停止   L      2524 0.002% 无仓位             12  18:28   0m   0.01     0.73   0.09 否       
2025/05/31 18:28:43 main.go:770: 4697000 停止   L      2520 0.020% 无仓位              4  18:28   0m   0.02     0.57   0.03 是       
2025/05/31 18:28:55 main.go:483: ❌ 未知命令: yes，输入 'help' 查看帮助
2025/05/31 18:29:00 main.go:483: ❌ 未知命令: y，输入 'help' 查看帮助
2025/05/31 18:29:13 main.go:483: ❌ 未知命令: yes，输入 'help' 查看帮助
2025/05/31 18:33:41 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:33:41 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 18:38:41 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 18:38:41 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 19:08:44 main.go:770: 2199000 停止   L      2527 0.002% 无仓位             14  19:08   0m   0.11     1.40   0.10 否       
2025/05/31 19:08:44 main.go:770: 2315000 停止   L      2524 0.002% 无仓位             12  19:08   0m   0.01     0.73   0.09 否       
2025/05/31 19:08:44 main.go:770: 4697000 停止   L      2520 0.020% 无仓位              4  19:08   0m   0.02     0.57   0.03 是       
2025/05/31 19:10:28 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:10:28 data_manager.go:228: 💾 事件数据已保存 (30个事件)
2025/05/31 19:10:50 main.go:130: 收到停止信号，正在停止所有任务...
2025/05/31 19:10:50 main.go:136: 停止任务 2199000 失败: 任务已停止: 2199000
2025/05/31 19:10:50 main.go:136: 停止任务 2315000 失败: 任务已停止: 2315000
2025/05/31 19:10:50 main.go:136: 停止任务 4697000 失败: 任务已停止: 4697000
2025/05/31 19:12:06 main.go:109: 启动本地测试模式...
2025/05/31 19:12:06 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/05/31 19:12:06 main.go:352: 
2025/05/31 19:12:06 main.go:353: 🔧 === 交互命令帮助 ===
2025/05/31 19:12:06 main.go:355:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/05/31 19:12:06 main.go:356:       示例: add long 3500 0.0005
2025/05/31 19:12:06 main.go:357:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/05/31 19:12:06 main.go:358: 
2025/05/31 19:12:06 main.go:359:    stop <task_id>                           - 停止指定任务
2025/05/31 19:12:06 main.go:360:    list                                     - 列出所有任务
2025/05/31 19:12:06 main.go:361: 
2025/05/31 19:12:06 main.go:362: ⚙️ 参数配置:
2025/05/31 19:12:06 main.go:363:    set <参数名> <值>                         - 设置默认参数
2025/05/31 19:12:06 main.go:364:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/05/31 19:12:06 main.go:365:    show defaults                            - 显示当前默认参数
2025/05/31 19:12:06 main.go:366: 
2025/05/31 19:12:06 main.go:367: 📊 状态控制:
2025/05/31 19:12:06 main.go:368:    status                                   - 查看状态输出设置
2025/05/31 19:12:06 main.go:369:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/05/31 19:12:06 main.go:370:    status off                               - 停止状态输出
2025/05/31 19:12:06 main.go:371: 
2025/05/31 19:12:06 main.go:372: 📋 数据导出:
2025/05/31 19:12:06 main.go:373:    export | detail                          - 导出详细状态到剪切板
2025/05/31 19:12:06 main.go:374: 
2025/05/31 19:12:06 main.go:375: 💾 数据管理:
2025/05/31 19:12:06 main.go:376:    save                                     - 手动保存数据到本地文件
2025/05/31 19:12:06 main.go:377:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/05/31 19:12:06 main.go:378:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/05/31 19:12:06 main.go:379: 
2025/05/31 19:12:06 main.go:380: 📋 事件查看:
2025/05/31 19:12:06 main.go:381:    events <task_id>                         - 查看指定任务的所有事件日志
2025/05/31 19:12:06 main.go:382: 
2025/05/31 19:12:06 main.go:383: 🎛️  批量控制:
2025/05/31 19:12:06 main.go:384:    startall                                 - 启动所有已停止的任务
2025/05/31 19:12:06 main.go:385:    stopall                                  - 停止所有运行中的任务
2025/05/31 19:12:06 main.go:386:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/05/31 19:12:06 main.go:387: 
2025/05/31 19:12:06 main.go:388: 🗑️  任务管理:
2025/05/31 19:12:06 main.go:389:    delete <task_id>                         - 删除指定任务及其所有记录
2025/05/31 19:12:06 main.go:390: 
2025/05/31 19:12:06 main.go:391: 🔧 其他:
2025/05/31 19:12:06 main.go:392:    help                                     - 显示此帮助信息
2025/05/31 19:12:06 main.go:393:    quit | exit                              - 退出程序
2025/05/31 19:12:06 main.go:394: ========================
2025/05/31 19:12:06 main.go:395: 
2025/05/31 19:12:21 main.go:776: 2199000 停止   L      2527 0.002% 无仓位             14  19:12   0m   0.11     1.40   0.10 否       
2025/05/31 19:12:21 main.go:776: 2315000 停止   L      2524 0.002% 无仓位             12  19:12   0m   0.01     0.73   0.09 否       
2025/05/31 19:12:21 main.go:776: 4697000 停止   L      2520 0.020% 无仓位              4  19:12   0m   0.02     0.57   0.03 是       
2025/05/31 19:13:18 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/05/31 19:13:18 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/05/31 19:13:18 data_manager.go:228: 💾 事件数据已保存 (16个事件)
2025/05/31 19:13:52 main.go:776: 2315000 停止   L      2524 0.002% 无仓位             12  19:13   0m   0.01     0.73   0.09 否       
2025/05/31 19:13:52 main.go:776: 4697000 停止   L      2520 0.020% 无仓位              4  19:13   0m   0.02     0.57   0.03 是       
2025/05/31 19:14:54 main.go:776: 2315000 停止   L      2524 0.002% 无仓位             12  19:14   0m   0.01     0.73   0.09 否       
2025/05/31 19:14:54 main.go:776: 4697000 停止   L      2520 0.020% 无仓位              4  19:14   0m   0.02     0.57   0.03 是       
2025/05/31 19:16:21 main.go:109: 启动本地测试模式...
2025/05/31 19:16:21 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/05/31 19:16:21 main.go:352: 
2025/05/31 19:16:21 main.go:353: 🔧 === 交互命令帮助 ===
2025/05/31 19:16:21 main.go:355:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/05/31 19:16:21 main.go:356:       示例: add long 3500 0.0005
2025/05/31 19:16:21 main.go:357:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/05/31 19:16:21 main.go:358: 
2025/05/31 19:16:21 main.go:359:    stop <task_id>                           - 停止指定任务
2025/05/31 19:16:21 main.go:360:    list                                     - 列出所有任务
2025/05/31 19:16:21 main.go:361: 
2025/05/31 19:16:21 main.go:362: ⚙️ 参数配置:
2025/05/31 19:16:21 main.go:363:    set <参数名> <值>                         - 设置默认参数
2025/05/31 19:16:21 main.go:364:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/05/31 19:16:21 main.go:365:    show defaults                            - 显示当前默认参数
2025/05/31 19:16:21 main.go:366: 
2025/05/31 19:16:21 main.go:367: 📊 状态控制:
2025/05/31 19:16:21 main.go:368:    status                                   - 查看状态输出设置
2025/05/31 19:16:21 main.go:369:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/05/31 19:16:21 main.go:370:    status off                               - 停止状态输出
2025/05/31 19:16:21 main.go:371: 
2025/05/31 19:16:21 main.go:372: 📋 数据导出:
2025/05/31 19:16:21 main.go:373:    export | detail                          - 导出详细状态到剪切板
2025/05/31 19:16:21 main.go:374: 
2025/05/31 19:16:21 main.go:375: 💾 数据管理:
2025/05/31 19:16:21 main.go:376:    save                                     - 手动保存数据到本地文件
2025/05/31 19:16:21 main.go:377:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/05/31 19:16:21 main.go:378:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/05/31 19:16:21 main.go:379: 
2025/05/31 19:16:21 main.go:380: 📋 事件查看:
2025/05/31 19:16:21 main.go:381:    events <task_id>                         - 查看指定任务的所有事件日志
2025/05/31 19:16:21 main.go:382: 
2025/05/31 19:16:21 main.go:383: 🎛️  批量控制:
2025/05/31 19:16:21 main.go:384:    startall                                 - 启动所有已停止的任务
2025/05/31 19:16:21 main.go:385:    stopall                                  - 停止所有运行中的任务
2025/05/31 19:16:21 main.go:386:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/05/31 19:16:21 main.go:387: 
2025/05/31 19:16:21 main.go:388: 🗑️  任务管理:
2025/05/31 19:16:21 main.go:389:    delete <task_id>                         - 删除指定任务及其所有记录
2025/05/31 19:16:21 main.go:390: 
2025/05/31 19:16:21 main.go:391: 🔧 其他:
2025/05/31 19:16:21 main.go:392:    help                                     - 显示此帮助信息
2025/05/31 19:16:21 main.go:393:    quit | exit                              - 退出程序
2025/05/31 19:16:21 main.go:394: ========================
2025/05/31 19:16:21 main.go:395: 
2025/05/31 19:16:23 main.go:776: 2315000 停止   L      2524 0.002% 无仓位             12  19:16   0m   0.01     0.73   0.09 否       
2025/05/31 19:16:23 main.go:776: 4697000 停止   L      2520 0.020% 无仓位              4  19:16   0m   0.02     0.57   0.03 是       
2025/05/31 19:16:34 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/05/31 19:16:34 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/05/31 19:16:34 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/05/31 19:16:38 main.go:776: 4697000 停止   L      2520 0.020% 无仓位              4  19:16   0m   0.02     0.57   0.03 是       
2025/05/31 19:16:54 main.go:1492: ✅ 报告已保存到文件: export/hedge_report_20250531_191654.txt
2025/05/31 19:16:54 main.go:978: ✅ 详细状态已复制到剪切板
2025/05/31 19:16:54 main.go:980: 📋 详细状态报告:
2025/05/31 19:17:02 main.go:776: 4697000 停止   L      2520 0.020% 无仓位              4  19:17   0m   0.02     0.57   0.03 是       
2025/05/31 19:17:32 main.go:489: ❌ 未知命令: start，输入 'help' 查看帮助
2025/05/31 19:17:36 状态变化: stopped -> running | 原因: 批量启动
2025/05/31 19:17:36 hedge_task.go:45: 启动对冲任务: 4697000
2025/05/31 19:17:36 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/05/31 19:17:36 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/05/31 19:17:37 main.go:776: 4697000 运行   L      2520 0.020% 无仓位              4  19:17   0m   0.02     0.57   0.03 是       
2025/05/31 19:19:25 状态变化:  -> running | 原因: 任务创建
2025/05/31 19:19:25 manager.go:76: 创建对冲任务: 0400000, 交易对: ETHUSDT, 方向: long, 目标价: 2517
2025/05/31 19:19:25 hedge_task.go:45: 启动对冲任务: 0400000
2025/05/31 19:19:25 main.go:625: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2517.00, 阈值=0.0200%, 数量=0.0100
2025/05/31 19:19:25 main.go:632: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/05/31 19:19:25 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/05/31 19:19:25 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/05/31 19:19:32 hedge_task.go:116: 任务 0400000: 触发做多开仓条件，当前价格: 2517.545, 触发价格: 2517.5034
2025/05/31 19:19:32 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: open, 方向: long, 触发价格: 2517.5034
2025/05/31 19:19:32 hedge_task.go:176: 任务 0400000: 计算理论价格: 2517.5034 (事件类型: open, 方向: long)
2025/05/31 19:19:33 hedge_task.go:280: 任务 0400000: 挂买入限价单，价格: 2517.54, 数量: 0.01
2025/05/31 19:19:33 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2517.54, 订单ID: 8389765898350040183, 模式: POST_ONLY
2025/05/31 19:19:35 hedge_task.go:453: 任务 0400000: 订单 8389765898350040183 已成交，价格: 2517.54, 成交方式: maker
2025/05/31 19:19:35 hedge_task.go:204: 任务 0400000: 订单成交，记录事件和更新仓位
2025/05/31 19:19:35 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2517.5034 | 实际价: 2517.54 | 损耗: 0.0366 | 手续费: 0.00503508 | 订单ID: 8389765898350040183
2025/05/31 19:19:35 hedge_task.go:235: 任务 0400000: 下单完成，实际价格: 2517.54, 理论价格: 2517.5034, 价格损耗: 0.0366, 订单ID: 8389765898350040183
2025/05/31 19:19:35 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/05/31 19:19:35 data_manager.go:228: 💾 事件数据已保存 (5个事件)
2025/05/31 19:19:35 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/05/31 19:19:35 data_manager.go:228: 💾 事件数据已保存 (5个事件)
2025/05/31 19:19:38 main.go:776: 4697000 运行   L      2520 0.020% 无仓位              4  19:19   0m   0.02     0.57   0.03 是       
2025/05/31 19:19:38 main.go:776: 0400000 运行   L      2517 0.020% L0.01@2517       1  19:19   0m   0.00     0.04   0.01 是       
2025/05/31 19:20:14 状态变化:  -> running | 原因: 任务创建
2025/05/31 19:20:14 manager.go:76: 创建对冲任务: 4067000, 交易对: ETHUSDT, 方向: short, 目标价: 2515
2025/05/31 19:20:14 main.go:625: 📊 任务参数: 交易对=ETHUSDT, 方向=short, 目标价=2515.00, 阈值=0.0200%, 数量=0.0100
2025/05/31 19:20:14 main.go:632: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/05/31 19:20:14 hedge_task.go:45: 启动对冲任务: 4067000
2025/05/31 19:20:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:20:14 data_manager.go:228: 💾 事件数据已保存 (5个事件)
2025/05/31 19:20:16 main.go:776: 4697000 运行   L      2520 0.020% 无仓位              4  19:20   0m   0.02     0.57   0.03 是       
2025/05/31 19:20:16 main.go:776: 0400000 运行   L      2517 0.020% L0.01@2517       1  19:20   0m   0.00     0.04   0.01 是       
2025/05/31 19:20:16 main.go:776: 4067000 运行   S      2515 0.020% 无仓位              0  19:20   0m   0.00     0.00   0.00 是       
2025/05/31 19:21:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:21:21 data_manager.go:228: 💾 事件数据已保存 (5个事件)
2025/05/31 19:22:26 hedge_task.go:135: 任务 0400000: 触发多仓平仓条件，当前价格: 2516.465, 触发价格: 2516.4966
2025/05/31 19:22:26 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: close, 方向: short, 触发价格: 2516.4966
2025/05/31 19:22:26 hedge_task.go:176: 任务 0400000: 计算理论价格: 2516.4966 (事件类型: close, 方向: short)
2025/05/31 19:22:27 hedge_task.go:309: 任务 0400000: 挂卖出限价单，价格: 2516.47, 数量: 0.01
2025/05/31 19:22:27 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2516.47, 订单ID: 8389765898350675207, 模式: POST_ONLY
2025/05/31 19:22:32 hedge_task.go:426: 任务 0400000: 订单 8389765898350675207 超时，尝试撤销
2025/05/31 19:22:33 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898350675207
2025/05/31 19:22:33 hedge_task.go:195: 任务 0400000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 19:22:33 hedge_task.go:135: 任务 0400000: 触发多仓平仓条件，当前价格: 2516.005, 触发价格: 2516.4966
2025/05/31 19:22:33 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: close, 方向: short, 触发价格: 2516.4966
2025/05/31 19:22:33 hedge_task.go:176: 任务 0400000: 计算理论价格: 2516.4966 (事件类型: close, 方向: short)
2025/05/31 19:22:33 hedge_task.go:309: 任务 0400000: 挂卖出限价单，价格: 2516.01, 数量: 0.01
2025/05/31 19:22:34 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2516.01, 订单ID: 8389765898350682606, 模式: POST_ONLY
2025/05/31 19:22:39 hedge_task.go:426: 任务 0400000: 订单 8389765898350682606 超时，尝试撤销
2025/05/31 19:22:39 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898350682606
2025/05/31 19:22:39 hedge_task.go:195: 任务 0400000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 19:22:39 hedge_task.go:135: 任务 0400000: 触发多仓平仓条件，当前价格: 2516.005, 触发价格: 2516.4966
2025/05/31 19:22:39 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: close, 方向: short, 触发价格: 2516.4966
2025/05/31 19:22:39 hedge_task.go:176: 任务 0400000: 计算理论价格: 2516.4966 (事件类型: close, 方向: short)
2025/05/31 19:22:40 hedge_task.go:309: 任务 0400000: 挂卖出限价单，价格: 2516.01, 数量: 0.01
2025/05/31 19:22:40 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2516.01, 订单ID: 8389765898350685148, 模式: POST_ONLY
2025/05/31 19:22:45 hedge_task.go:426: 任务 0400000: 订单 8389765898350685148 超时，尝试撤销
2025/05/31 19:22:45 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898350685148
2025/05/31 19:22:45 hedge_task.go:195: 任务 0400000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 19:22:45 hedge_task.go:135: 任务 0400000: 触发多仓平仓条件，当前价格: 2516.005, 触发价格: 2516.4966
2025/05/31 19:22:45 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: close, 方向: short, 触发价格: 2516.4966
2025/05/31 19:22:45 hedge_task.go:176: 任务 0400000: 计算理论价格: 2516.4966 (事件类型: close, 方向: short)
2025/05/31 19:22:46 hedge_task.go:309: 任务 0400000: 挂卖出限价单，价格: 2516.01, 数量: 0.01
2025/05/31 19:22:46 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2516.01, 订单ID: 8389765898350687431, 模式: POST_ONLY
2025/05/31 19:22:51 hedge_task.go:426: 任务 0400000: 订单 8389765898350687431 超时，尝试撤销
2025/05/31 19:22:51 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898350687431
2025/05/31 19:22:51 hedge_task.go:195: 任务 0400000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 19:22:51 hedge_task.go:135: 任务 0400000: 触发多仓平仓条件，当前价格: 2516.005, 触发价格: 2516.4966
2025/05/31 19:22:51 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: close, 方向: short, 触发价格: 2516.4966
2025/05/31 19:22:51 hedge_task.go:176: 任务 0400000: 计算理论价格: 2516.4966 (事件类型: close, 方向: short)
2025/05/31 19:22:52 hedge_task.go:309: 任务 0400000: 挂卖出限价单，价格: 2516.01, 数量: 0.01
2025/05/31 19:22:52 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2516.01, 订单ID: 8389765898350690042, 模式: POST_ONLY
2025/05/31 19:22:57 hedge_task.go:453: 任务 0400000: 订单 8389765898350690042 已成交，价格: 2516.01, 成交方式: maker
2025/05/31 19:22:57 hedge_task.go:204: 任务 0400000: 订单成交，记录事件和更新仓位
2025/05/31 19:22:57 事件类型: close | 订单类型: limit | 成交方式: maker | 理论价: 2516.4966 | 实际价: 2516.01 | 损耗: 0.4866 | 手续费: 0.00503202 | 订单ID: 8389765898350690042
2025/05/31 19:22:57 hedge_task.go:235: 任务 0400000: 下单完成，实际价格: 2516.01, 理论价格: 2516.4966, 价格损耗: 0.4866, 订单ID: 8389765898350690042
2025/05/31 19:22:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:22:57 data_manager.go:228: 💾 事件数据已保存 (6个事件)
2025/05/31 19:22:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:22:57 data_manager.go:228: 💾 事件数据已保存 (6个事件)
2025/05/31 19:23:49 main.go:1492: ✅ 报告已保存到文件: export/hedge_report_20250531_192349.txt
2025/05/31 19:23:49 main.go:978: ✅ 详细状态已复制到剪切板
2025/05/31 19:23:49 main.go:980: 📋 详细状态报告:
2025/05/31 19:24:33 hedge_task.go:116: 任务 0400000: 触发做多开仓条件，当前价格: 2517.565, 触发价格: 2517.5034
2025/05/31 19:24:33 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: open, 方向: long, 触发价格: 2517.5034
2025/05/31 19:24:33 hedge_task.go:176: 任务 0400000: 计算理论价格: 2517.5034 (事件类型: open, 方向: long)
2025/05/31 19:24:34 hedge_task.go:280: 任务 0400000: 挂买入限价单，价格: 2517.56, 数量: 0.01
2025/05/31 19:24:34 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2517.56, 订单ID: 8389765898351207097, 模式: POST_ONLY
2025/05/31 19:24:39 hedge_task.go:426: 任务 0400000: 订单 8389765898351207097 超时，尝试撤销
2025/05/31 19:24:39 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898351207097
2025/05/31 19:24:39 hedge_task.go:195: 任务 0400000: 订单被撤销，未成交，不更新仓位状态
2025/05/31 19:24:39 hedge_task.go:116: 任务 0400000: 触发做多开仓条件，当前价格: 2517.995, 触发价格: 2517.5034
2025/05/31 19:24:39 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: open, 方向: long, 触发价格: 2517.5034
2025/05/31 19:24:39 hedge_task.go:176: 任务 0400000: 计算理论价格: 2517.5034 (事件类型: open, 方向: long)
2025/05/31 19:24:40 hedge_task.go:280: 任务 0400000: 挂买入限价单，价格: 2517.99, 数量: 0.01
2025/05/31 19:24:40 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2517.99, 订单ID: 8389765898351218047, 模式: POST_ONLY
2025/05/31 19:24:42 hedge_task.go:453: 任务 0400000: 订单 8389765898351218047 已成交，价格: 2517.99, 成交方式: maker
2025/05/31 19:24:42 hedge_task.go:204: 任务 0400000: 订单成交，记录事件和更新仓位
2025/05/31 19:24:42 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2517.5034 | 实际价: 2517.99 | 损耗: 0.4866 | 手续费: 0.00503598 | 订单ID: 8389765898351218047
2025/05/31 19:24:42 hedge_task.go:235: 任务 0400000: 下单完成，实际价格: 2517.99, 理论价格: 2517.5034, 价格损耗: 0.4866, 订单ID: 8389765898351218047
2025/05/31 19:24:42 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:24:42 data_manager.go:228: 💾 事件数据已保存 (7个事件)
2025/05/31 19:24:42 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:24:42 data_manager.go:228: 💾 事件数据已保存 (7个事件)
2025/05/31 19:24:55 main.go:1492: ✅ 报告已保存到文件: export/hedge_report_20250531_192455.txt
2025/05/31 19:24:55 main.go:978: ✅ 详细状态已复制到剪切板
2025/05/31 19:24:55 main.go:980: 📋 详细状态报告:
2025/05/31 19:26:21 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:26:21 data_manager.go:228: 💾 事件数据已保存 (7个事件)
2025/05/31 19:26:56 main.go:453: ❌ 未知show命令: deafults，输入 'help' 查看帮助
2025/05/31 19:27:02 main.go:898: === 当前默认参数 ===
2025/05/31 19:27:02 main.go:899: 📊 交易对: ETHUSDT
2025/05/31 19:27:02 main.go:900: 📊 滑点容忍度: 1.000% (0.010000)
2025/05/31 19:27:02 main.go:901: 📊 停止滑点: 1.000% (0.010000)
2025/05/31 19:27:02 main.go:902: 📊 市价单超时: 5秒
2025/05/31 19:27:02 main.go:903: 📊 盘口量阈值: 50.0% (0.500)
2025/05/31 19:27:02 main.go:909: 📊 限价单模式: 仅限挂单成交 (true)
2025/05/31 19:27:02 main.go:910: ==================
2025/05/31 19:27:07 hedge_task.go:135: 任务 0400000: 触发多仓平仓条件，当前价格: 2516.465, 触发价格: 2516.4966
2025/05/31 19:27:07 hedge_task.go:151: 任务 0400000: 开始执行下单，类型: close, 方向: short, 触发价格: 2516.4966
2025/05/31 19:27:07 hedge_task.go:176: 任务 0400000: 计算理论价格: 2516.4966 (事件类型: close, 方向: short)
2025/05/31 19:27:09 hedge_task.go:309: 任务 0400000: 挂卖出限价单，价格: 2516.47, 数量: 0.01
2025/05/31 19:27:09 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2516.47, 订单ID: 8389765898352117430, 模式: POST_ONLY
2025/05/31 19:27:14 hedge_task.go:453: 任务 0400000: 订单 8389765898352117430 已成交，价格: 2516.47, 成交方式: maker
2025/05/31 19:27:14 hedge_task.go:204: 任务 0400000: 订单成交，记录事件和更新仓位
2025/05/31 19:27:14 事件类型: close | 订单类型: limit | 成交方式: maker | 理论价: 2516.4966 | 实际价: 2516.47 | 损耗: 0.0266 | 手续费: 0.00503294 | 订单ID: 8389765898352117430
2025/05/31 19:27:14 hedge_task.go:235: 任务 0400000: 下单完成，实际价格: 2516.47, 理论价格: 2516.4966, 价格损耗: 0.0266, 订单ID: 8389765898352117430
2025/05/31 19:27:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:27:14 data_manager.go:228: 💾 事件数据已保存 (8个事件)
2025/05/31 19:27:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:27:14 data_manager.go:228: 💾 事件数据已保存 (8个事件)
2025/05/31 19:27:44 main.go:776: 0400000 运行   L      2517 0.020% 无仓位              4  19:27   0m   0.01     0.26   0.02 是       
2025/05/31 19:27:44 main.go:776: 4697000 运行   L      2520 0.020% 无仓位              4  19:27   0m   0.02     0.57   0.03 是       
2025/05/31 19:27:44 main.go:776: 4067000 运行   S      2515 0.020% 无仓位              0  19:27   0m   0.00     0.00   0.00 是       
2025/05/31 19:28:20 main.go:776: 0400000 运行   L      2517 0.020% 无仓位              4  19:28   0m   0.01     0.26   0.02 是       
2025/05/31 19:28:20 main.go:776: 4697000 运行   L      2520 0.020% 无仓位              4  19:28   0m   0.02     0.57   0.03 是       
2025/05/31 19:28:20 main.go:776: 4067000 运行   S      2515 0.020% 无仓位              0  19:28   0m   0.00     0.00   0.00 是       
2025/05/31 19:28:23 main.go:1492: ✅ 报告已保存到文件: export/hedge_report_20250531_192823.txt
2025/05/31 19:28:23 main.go:978: ✅ 详细状态已复制到剪切板
2025/05/31 19:28:23 main.go:980: 📋 详细状态报告:
2025/05/31 19:30:33 main.go:130: 收到停止信号，正在停止所有任务...
2025/05/31 19:30:33 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/05/31 19:30:33 manager.go:123: 停止对冲任务: 0400000
2025/05/31 19:30:33 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/05/31 19:30:33 manager.go:123: 停止对冲任务: 4697000
2025/05/31 19:30:33 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/05/31 19:30:33 manager.go:123: 停止对冲任务: 4067000
2025/05/31 19:30:33 hedge_task.go:60: 任务 0400000: 收到停止信号
2025/05/31 19:30:33 hedge_task.go:470: 任务 0400000: 无仓位，直接停止
2025/05/31 19:30:33 hedge_task.go:62: 对冲任务结束: 0400000
2025/05/31 19:30:33 hedge_task.go:60: 任务 4067000: 收到停止信号
2025/05/31 19:30:33 hedge_task.go:470: 任务 4067000: 无仓位，直接停止
2025/05/31 19:30:33 hedge_task.go:62: 对冲任务结束: 4067000
2025/05/31 19:30:33 hedge_task.go:60: 任务 4697000: 收到停止信号
2025/05/31 19:30:33 hedge_task.go:470: 任务 4697000: 无仓位，直接停止
2025/05/31 19:30:33 hedge_task.go:62: 对冲任务结束: 4697000
2025/05/31 19:30:33 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:30:33 data_manager.go:228: 💾 事件数据已保存 (8个事件)
2025/05/31 19:30:33 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:30:33 data_manager.go:228: 💾 事件数据已保存 (8个事件)
2025/05/31 19:30:33 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/05/31 19:30:33 data_manager.go:228: 💾 事件数据已保存 (8个事件)
2025/05/31 19:30:33 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp 198.18.0.1:64100->198.18.0.45:443: use of closed network connection, 5秒后重试...
