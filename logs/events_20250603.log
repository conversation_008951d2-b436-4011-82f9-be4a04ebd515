2025/06/03 14:25:28 main.go:109: 启动本地测试模式...
2025/06/03 14:25:28 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/03 14:25:28 main.go:356: 
2025/06/03 14:25:28 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/03 14:25:28 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/03 14:25:28 main.go:360:       示例: add long 3500 0.0005
2025/06/03 14:25:28 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/03 14:25:28 main.go:362: 
2025/06/03 14:25:28 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/03 14:25:28 main.go:364:    list                                     - 列出所有任务
2025/06/03 14:25:28 main.go:365: 
2025/06/03 14:25:28 main.go:366: ⚙️ 参数配置:
2025/06/03 14:25:28 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/03 14:25:28 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/03 14:25:28 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/03 14:25:28 main.go:370: 
2025/06/03 14:25:28 main.go:371: 📊 状态控制:
2025/06/03 14:25:28 main.go:372:    status                                   - 查看状态输出设置
2025/06/03 14:25:28 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/03 14:25:28 main.go:374:    status off                               - 停止状态输出
2025/06/03 14:25:28 main.go:375: 
2025/06/03 14:25:28 main.go:376: 📋 数据导出:
2025/06/03 14:25:28 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/03 14:25:28 main.go:378: 
2025/06/03 14:25:28 main.go:379: 💾 数据管理:
2025/06/03 14:25:28 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/03 14:25:28 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/03 14:25:28 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/03 14:25:28 main.go:383: 
2025/06/03 14:25:28 main.go:384: 📋 事件查看:
2025/06/03 14:25:28 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/03 14:25:28 main.go:386: 
2025/06/03 14:25:28 main.go:387: 🎛️  批量控制:
2025/06/03 14:25:28 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/03 14:25:28 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/03 14:25:28 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/03 14:25:28 main.go:391: 
2025/06/03 14:25:28 main.go:392: 🗑️  任务管理:
2025/06/03 14:25:28 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/03 14:25:28 main.go:394: 
2025/06/03 14:25:28 main.go:395: 🔧 其他:
2025/06/03 14:25:28 main.go:396:    help                                     - 显示此帮助信息
2025/06/03 14:25:28 main.go:397:    quit | exit                              - 退出程序
2025/06/03 14:25:28 main.go:398: ========================
2025/06/03 14:25:28 main.go:399: 
2025/06/03 15:42:54 main.go:109: 启动本地测试模式...
2025/06/03 15:42:54 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/03 15:42:54 main.go:356: 
2025/06/03 15:42:54 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/03 15:42:54 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/03 15:42:54 main.go:360:       示例: add long 3500 0.0005
2025/06/03 15:42:54 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/03 15:42:54 main.go:362: 
2025/06/03 15:42:54 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/03 15:42:54 main.go:364:    list                                     - 列出所有任务
2025/06/03 15:42:54 main.go:365: 
2025/06/03 15:42:54 main.go:366: ⚙️ 参数配置:
2025/06/03 15:42:54 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/03 15:42:54 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/03 15:42:54 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/03 15:42:54 main.go:370: 
2025/06/03 15:42:54 main.go:371: 📊 状态控制:
2025/06/03 15:42:54 main.go:372:    status                                   - 查看状态输出设置
2025/06/03 15:42:54 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/03 15:42:54 main.go:374:    status off                               - 停止状态输出
2025/06/03 15:42:54 main.go:375: 
2025/06/03 15:42:54 main.go:376: 📋 数据导出:
2025/06/03 15:42:54 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/03 15:42:54 main.go:378: 
2025/06/03 15:42:54 main.go:379: 💾 数据管理:
2025/06/03 15:42:54 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/03 15:42:54 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/03 15:42:54 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/03 15:42:54 main.go:383: 
2025/06/03 15:42:54 main.go:384: 📋 事件查看:
2025/06/03 15:42:54 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/03 15:42:54 main.go:386: 
2025/06/03 15:42:54 main.go:387: 🎛️  批量控制:
2025/06/03 15:42:54 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/03 15:42:54 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/03 15:42:54 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/03 15:42:54 main.go:391: 
2025/06/03 15:42:54 main.go:392: 🗑️  任务管理:
2025/06/03 15:42:54 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/03 15:42:54 main.go:394: 
2025/06/03 15:42:54 main.go:395: 🔧 其他:
2025/06/03 15:42:54 main.go:396:    help                                     - 显示此帮助信息
2025/06/03 15:42:54 main.go:397:    quit | exit                              - 退出程序
2025/06/03 15:42:54 main.go:398: ========================
2025/06/03 15:42:54 main.go:399: 
2025/06/03 15:43:00 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/03 15:43:00 main.go:136: 停止任务 0400000 失败: 任务已停止: 0400000
2025/06/03 15:43:00 main.go:136: 停止任务 4067000 失败: 任务已停止: 4067000
2025/06/03 15:43:00 main.go:136: 停止任务 4697000 失败: 任务已停止: 4697000
2025/06/03 21:40:41 main.go:109: 启动本地测试模式...
2025/06/03 21:40:41 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/03 21:40:41 main.go:356: 
2025/06/03 21:40:41 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/03 21:40:41 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/03 21:40:41 main.go:360:       示例: add long 3500 0.0005
2025/06/03 21:40:41 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/03 21:40:41 main.go:362: 
2025/06/03 21:40:41 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/03 21:40:41 main.go:364:    list                                     - 列出所有任务
2025/06/03 21:40:41 main.go:365: 
2025/06/03 21:40:41 main.go:366: ⚙️ 参数配置:
2025/06/03 21:40:41 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/03 21:40:41 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/03 21:40:41 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/03 21:40:41 main.go:370: 
2025/06/03 21:40:41 main.go:371: 📊 状态控制:
2025/06/03 21:40:41 main.go:372:    status                                   - 查看状态输出设置
2025/06/03 21:40:41 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/03 21:40:41 main.go:374:    status off                               - 停止状态输出
2025/06/03 21:40:41 main.go:375: 
2025/06/03 21:40:41 main.go:376: 📋 数据导出:
2025/06/03 21:40:41 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/03 21:40:41 main.go:378: 
2025/06/03 21:40:41 main.go:379: 💾 数据管理:
2025/06/03 21:40:41 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/03 21:40:41 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/03 21:40:41 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/03 21:40:41 main.go:383: 
2025/06/03 21:40:41 main.go:384: 📋 事件查看:
2025/06/03 21:40:41 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/03 21:40:41 main.go:386: 
2025/06/03 21:40:41 main.go:387: 🎛️  批量控制:
2025/06/03 21:40:41 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/03 21:40:41 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/03 21:40:41 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/03 21:40:41 main.go:391: 
2025/06/03 21:40:41 main.go:392: 🗑️  任务管理:
2025/06/03 21:40:41 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/03 21:40:41 main.go:394: 
2025/06/03 21:40:41 main.go:395: 🔧 其他:
2025/06/03 21:40:41 main.go:396:    help                                     - 显示此帮助信息
2025/06/03 21:40:41 main.go:397:    quit | exit                              - 退出程序
2025/06/03 21:40:41 main.go:398: ========================
2025/06/03 21:40:41 main.go:399: 
2025/06/03 21:43:36 main.go:109: 启动本地测试模式...
2025/06/03 21:43:36 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/03 21:43:36 main.go:356: 
2025/06/03 21:43:36 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/03 21:43:36 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/03 21:43:36 main.go:360:       示例: add long 3500 0.0005
2025/06/03 21:43:36 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/03 21:43:36 main.go:362: 
2025/06/03 21:43:36 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/03 21:43:36 main.go:364:    list                                     - 列出所有任务
2025/06/03 21:43:36 main.go:365: 
2025/06/03 21:43:36 main.go:366: ⚙️ 参数配置:
2025/06/03 21:43:36 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/03 21:43:36 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/03 21:43:36 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/03 21:43:36 main.go:370: 
2025/06/03 21:43:36 main.go:371: 📊 状态控制:
2025/06/03 21:43:36 main.go:372:    status                                   - 查看状态输出设置
2025/06/03 21:43:36 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/03 21:43:36 main.go:374:    status off                               - 停止状态输出
2025/06/03 21:43:36 main.go:375: 
2025/06/03 21:43:36 main.go:376: 📋 数据导出:
2025/06/03 21:43:36 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/03 21:43:36 main.go:378: 
2025/06/03 21:43:36 main.go:379: 💾 数据管理:
2025/06/03 21:43:36 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/03 21:43:36 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/03 21:43:36 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/03 21:43:36 main.go:383: 
2025/06/03 21:43:36 main.go:384: 📋 事件查看:
2025/06/03 21:43:36 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/03 21:43:36 main.go:386: 
2025/06/03 21:43:36 main.go:387: 🎛️  批量控制:
2025/06/03 21:43:36 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/03 21:43:36 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/03 21:43:36 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/03 21:43:36 main.go:391: 
2025/06/03 21:43:36 main.go:392: 🗑️  任务管理:
2025/06/03 21:43:36 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/03 21:43:36 main.go:394: 
2025/06/03 21:43:36 main.go:395: 🔧 其他:
2025/06/03 21:43:36 main.go:396:    help                                     - 显示此帮助信息
2025/06/03 21:43:36 main.go:397:    quit | exit                              - 退出程序
2025/06/03 21:43:36 main.go:398: ========================
2025/06/03 21:43:36 main.go:399: 
2025/06/03 21:43:48 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/03 21:43:48 main.go:136: 停止任务 4067000 失败: 任务已停止: 4067000
2025/06/03 21:43:48 main.go:136: 停止任务 4697000 失败: 任务已停止: 4697000
2025/06/03 21:43:48 main.go:136: 停止任务 0400000 失败: 任务已停止: 0400000
2025/06/03 22:59:19 main.go:176: 启动API服务模式，端口: 5879
2025/06/03 22:59:19 main.go:192: API服务器已启动，访问地址: http://localhost:5879
2025/06/03 22:59:19 main.go:193: API接口:
2025/06/03 22:59:19 main.go:194:   POST /task                           - 创建对冲任务
2025/06/03 22:59:19 main.go:195:   POST /task/stop                      - 停止对冲任务
2025/06/03 22:59:19 main.go:196:   GET  /tasks                          - 获取所有任务
2025/06/03 22:59:19 main.go:197:   GET  /task/{id}                      - 获取单个任务
2025/06/03 22:59:19 main.go:198: 期权系统API接口:
2025/06/03 22:59:19 main.go:199:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/03 22:59:19 main.go:200:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/03 22:59:19 main.go:201:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/03 22:59:19 api.go:35: API服务器启动，端口: 5879
2025/06/03 22:59:51 main.go:205: 收到停止信号，正在关闭服务...
2025/06/03 22:59:51 main.go:212: 停止任务 0400000 失败: 任务已停止: 0400000
2025/06/03 22:59:51 main.go:212: 停止任务 4067000 失败: 任务已停止: 4067000
2025/06/03 22:59:51 main.go:212: 停止任务 4697000 失败: 任务已停止: 4697000
2025/06/03 23:01:12 main.go:176: 启动API服务模式，端口: 5879
2025/06/03 23:01:12 main.go:192: API服务器已启动，访问地址: http://localhost:5879
2025/06/03 23:01:12 main.go:193: API接口:
2025/06/03 23:01:12 main.go:194:   POST /task                           - 创建对冲任务
2025/06/03 23:01:12 main.go:195:   POST /task/stop                      - 停止对冲任务
2025/06/03 23:01:12 main.go:196:   GET  /tasks                          - 获取所有任务
2025/06/03 23:01:12 main.go:197:   GET  /task/{id}                      - 获取单个任务
2025/06/03 23:01:12 main.go:198: 期权系统API接口:
2025/06/03 23:01:12 main.go:199:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/03 23:01:12 main.go:200:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/03 23:01:12 api.go:35: API服务器启动，端口: 5879
2025/06/03 23:01:12 main.go:201:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/03 23:01:23 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/03 23:02:04 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/03 23:06:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:06:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:11:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:11:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:16:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:16:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:16:17 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/03 23:17:37 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/03 23:19:54 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/03 23:21:04 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/03 23:21:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:21:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:26:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:26:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:31:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:31:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:36:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:36:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:41:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:41:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:46:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:46:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:51:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:51:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/03 23:56:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/03 23:56:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:01:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:01:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:06:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:06:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:11:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:11:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:16:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:16:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:21:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:21:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:26:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:26:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:27:35 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:28:59 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:29:17 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:31:05 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:31:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:31:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:33:46 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:34:54 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:35:50 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:36:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:36:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:38:02 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:41:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:41:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:46:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:46:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:51:12 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/04 00:51:12 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/04 00:51:16 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:53:41 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/04 00:56:10 main.go:205: 收到停止信号，正在关闭服务...
