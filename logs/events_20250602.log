2025/06/02 17:08:35 main.go:176: 启动API服务模式，端口: 5879
2025/06/02 17:08:35 main.go:192: API服务器已启动，访问地址: http://localhost:5879
2025/06/02 17:08:35 main.go:193: API接口:
2025/06/02 17:08:35 main.go:194:   POST /task                           - 创建对冲任务
2025/06/02 17:08:35 main.go:195:   POST /task/stop                      - 停止对冲任务
2025/06/02 17:08:35 main.go:196:   GET  /tasks                          - 获取所有任务
2025/06/02 17:08:35 main.go:197:   GET  /task/{id}                      - 获取单个任务
2025/06/02 17:08:35 main.go:198: 期权系统API接口:
2025/06/02 17:08:35 main.go:199:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/02 17:08:35 main.go:200:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/02 17:08:35 main.go:201:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/02 17:08:35 api.go:35: API服务器启动，端口: 5879
2025/06/02 17:09:10 main.go:205: 收到停止信号，正在关闭服务...
2025/06/02 17:09:10 main.go:212: 停止任务 4697000 失败: 任务已停止: 4697000
2025/06/02 17:09:10 main.go:212: 停止任务 0400000 失败: 任务已停止: 0400000
2025/06/02 17:09:10 main.go:212: 停止任务 4067000 失败: 任务已停止: 4067000
2025/06/02 17:09:20 main.go:109: 启动本地测试模式...
2025/06/02 17:09:20 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/02 17:09:20 main.go:356: 
2025/06/02 17:09:20 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/02 17:09:20 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/02 17:09:20 main.go:360:       示例: add long 3500 0.0005
2025/06/02 17:09:20 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/02 17:09:20 main.go:362: 
2025/06/02 17:09:20 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/02 17:09:20 main.go:364:    list                                     - 列出所有任务
2025/06/02 17:09:20 main.go:365: 
2025/06/02 17:09:20 main.go:366: ⚙️ 参数配置:
2025/06/02 17:09:20 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/02 17:09:20 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/02 17:09:20 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/02 17:09:20 main.go:370: 
2025/06/02 17:09:20 main.go:371: 📊 状态控制:
2025/06/02 17:09:20 main.go:372:    status                                   - 查看状态输出设置
2025/06/02 17:09:20 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/02 17:09:20 main.go:374:    status off                               - 停止状态输出
2025/06/02 17:09:20 main.go:375: 
2025/06/02 17:09:20 main.go:376: 📋 数据导出:
2025/06/02 17:09:20 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/02 17:09:20 main.go:378: 
2025/06/02 17:09:20 main.go:379: 💾 数据管理:
2025/06/02 17:09:20 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/02 17:09:20 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/02 17:09:20 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/02 17:09:20 main.go:383: 
2025/06/02 17:09:20 main.go:384: 📋 事件查看:
2025/06/02 17:09:20 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/02 17:09:20 main.go:386: 
2025/06/02 17:09:20 main.go:387: 🎛️  批量控制:
2025/06/02 17:09:20 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/02 17:09:20 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/02 17:09:20 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/02 17:09:20 main.go:391: 
2025/06/02 17:09:20 main.go:392: 🗑️  任务管理:
2025/06/02 17:09:20 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/02 17:09:20 main.go:394: 
2025/06/02 17:09:20 main.go:395: 🔧 其他:
2025/06/02 17:09:20 main.go:396:    help                                     - 显示此帮助信息
2025/06/02 17:09:20 main.go:397:    quit | exit                              - 退出程序
2025/06/02 17:09:20 main.go:398: ========================
2025/06/02 17:09:20 main.go:399: 
2025/06/02 17:09:22 main.go:780: 0400000 停止   L      2517 0.020% 无仓位              4  17:09   0m   0.01     0.26   0.02 是       
2025/06/02 17:09:22 main.go:780: 4697000 停止   L      2520 0.020% 无仓位              4  17:09   0m   0.02     0.57   0.03 是       
2025/06/02 17:09:22 main.go:780: 4067000 停止   S      2515 0.020% 无仓位              0  17:09   0m   0.00     0.00   0.00 是       
2025/06/02 17:14:20 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/02 17:14:20 data_manager.go:228: 💾 事件数据已保存 (8个事件)
2025/06/02 17:14:45 main.go:176: 启动API服务模式，端口: 5879
2025/06/02 17:14:45 main.go:192: API服务器已启动，访问地址: http://localhost:5879
2025/06/02 17:14:45 main.go:193: API接口:
2025/06/02 17:14:45 main.go:194:   POST /task                           - 创建对冲任务
2025/06/02 17:14:45 main.go:195:   POST /task/stop                      - 停止对冲任务
2025/06/02 17:14:45 main.go:196:   GET  /tasks                          - 获取所有任务
2025/06/02 17:14:45 main.go:197:   GET  /task/{id}                      - 获取单个任务
2025/06/02 17:14:45 main.go:198: 期权系统API接口:
2025/06/02 17:14:45 main.go:199:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/02 17:14:45 main.go:200:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/02 17:14:45 main.go:201:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/02 17:14:45 api.go:35: API服务器启动，端口: 5879
2025/06/02 17:22:39 main.go:109: 启动本地测试模式...
2025/06/02 17:22:39 main.go:110: 💡 使用交互命令手动创建任务，输入 'help' 查看可用命令
2025/06/02 17:22:39 main.go:356: 
2025/06/02 17:22:39 main.go:357: 🔧 === 交互命令帮助 ===
2025/06/02 17:22:39 main.go:359:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/02 17:22:39 main.go:360:       示例: add long 3500 0.0005
2025/06/02 17:22:39 main.go:361:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/02 17:22:39 main.go:362: 
2025/06/02 17:22:39 main.go:363:    stop <task_id>                           - 停止指定任务
2025/06/02 17:22:39 main.go:364:    list                                     - 列出所有任务
2025/06/02 17:22:39 main.go:365: 
2025/06/02 17:22:39 main.go:366: ⚙️ 参数配置:
2025/06/02 17:22:39 main.go:367:    set <参数名> <值>                         - 设置默认参数
2025/06/02 17:22:39 main.go:368:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/02 17:22:39 main.go:369:    show defaults                            - 显示当前默认参数
2025/06/02 17:22:39 main.go:370: 
2025/06/02 17:22:39 main.go:371: 📊 状态控制:
2025/06/02 17:22:39 main.go:372:    status                                   - 查看状态输出设置
2025/06/02 17:22:39 main.go:373:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/02 17:22:39 main.go:374:    status off                               - 停止状态输出
2025/06/02 17:22:39 main.go:375: 
2025/06/02 17:22:39 main.go:376: 📋 数据导出:
2025/06/02 17:22:39 main.go:377:    export | detail                          - 导出详细状态到剪切板
2025/06/02 17:22:39 main.go:378: 
2025/06/02 17:22:39 main.go:379: 💾 数据管理:
2025/06/02 17:22:39 main.go:380:    save                                     - 手动保存数据到本地文件
2025/06/02 17:22:39 main.go:381:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/02 17:22:39 main.go:382:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/02 17:22:39 main.go:383: 
2025/06/02 17:22:39 main.go:384: 📋 事件查看:
2025/06/02 17:22:39 main.go:385:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/02 17:22:39 main.go:386: 
2025/06/02 17:22:39 main.go:387: 🎛️  批量控制:
2025/06/02 17:22:39 main.go:388:    startall                                 - 启动所有已停止的任务
2025/06/02 17:22:39 main.go:389:    stopall                                  - 停止所有运行中的任务
2025/06/02 17:22:39 main.go:390:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/02 17:22:39 main.go:391: 
2025/06/02 17:22:39 main.go:392: 🗑️  任务管理:
2025/06/02 17:22:39 main.go:393:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/02 17:22:39 main.go:394: 
2025/06/02 17:22:39 main.go:395: 🔧 其他:
2025/06/02 17:22:39 main.go:396:    help                                     - 显示此帮助信息
2025/06/02 17:22:39 main.go:397:    quit | exit                              - 退出程序
2025/06/02 17:22:39 main.go:398: ========================
2025/06/02 17:22:39 main.go:399: 
2025/06/02 17:22:53 main.go:130: 收到停止信号，正在停止所有任务...
2025/06/02 17:22:53 main.go:136: 停止任务 0400000 失败: 任务已停止: 0400000
2025/06/02 17:22:53 main.go:136: 停止任务 4067000 失败: 任务已停止: 4067000
2025/06/02 17:22:53 main.go:136: 停止任务 4697000 失败: 任务已停止: 4697000
