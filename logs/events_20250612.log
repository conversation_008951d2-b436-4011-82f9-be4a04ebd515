2025/06/12 21:03:18 main.go:185: 启动API服务模式（包含交互功能），端口: 5899
2025/06/12 21:03:18 main.go:186: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 21:03:18 main.go:202: API服务器已启动，访问地址: http://localhost:5899
2025/06/12 21:03:18 main.go:203: API接口:
2025/06/12 21:03:18 main.go:204:   POST /task                           - 创建对冲任务
2025/06/12 21:03:18 main.go:205:   POST /task/stop                      - 停止对冲任务
2025/06/12 21:03:18 main.go:206:   GET  /tasks                          - 获取所有任务
2025/06/12 21:03:18 main.go:207:   GET  /task/{id}                      - 获取单个任务
2025/06/12 21:03:18 main.go:208: 期权系统API接口:
2025/06/12 21:03:18 main.go:209:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 21:03:18 main.go:210:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 21:03:18 main.go:211:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 21:03:18 main.go:402: 
2025/06/12 21:03:18 main.go:403: 🔧 === 交互命令帮助 ===
2025/06/12 21:03:18 main.go:405:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 21:03:18 main.go:406:       示例: add long 3500 0.0005
2025/06/12 21:03:18 main.go:407:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 21:03:18 main.go:408: 
2025/06/12 21:03:18 main.go:409:    stop <task_id>                           - 停止指定任务
2025/06/12 21:03:18 main.go:410:    list                                     - 列出所有任务
2025/06/12 21:03:18 main.go:411: 
2025/06/12 21:03:18 main.go:412: ⚙️ 参数配置:
2025/06/12 21:03:18 main.go:413:    set <参数名> <值>                         - 设置默认参数
2025/06/12 21:03:18 main.go:414:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 21:03:18 main.go:415:    show defaults                            - 显示当前默认参数
2025/06/12 21:03:18 main.go:416: 
2025/06/12 21:03:18 main.go:417: 📊 状态控制:
2025/06/12 21:03:18 api.go:35: API服务器启动，端口: 5899
2025/06/12 21:03:18 main.go:418:    status                                   - 查看状态输出设置
2025/06/12 21:03:18 main.go:419:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 21:03:18 main.go:420:    status off                               - 停止状态输出
2025/06/12 21:03:18 main.go:421: 
2025/06/12 21:03:18 main.go:422: 📋 数据导出:
2025/06/12 21:03:18 main.go:423:    export | detail                          - 导出详细状态到剪切板
2025/06/12 21:03:18 main.go:424: 
2025/06/12 21:03:18 main.go:425: 💾 数据管理:
2025/06/12 21:03:18 main.go:426:    save                                     - 手动保存数据到本地文件
2025/06/12 21:03:18 main.go:427:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 21:03:18 main.go:428:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 21:03:18 main.go:429: 
2025/06/12 21:03:18 main.go:430: 📋 事件查看:
2025/06/12 21:03:18 main.go:431:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 21:03:18 main.go:432: 
2025/06/12 21:03:18 main.go:433: 🎛️  批量控制:
2025/06/12 21:03:18 main.go:434:    startall                                 - 启动所有已停止的任务
2025/06/12 21:03:18 main.go:435:    stopall                                  - 停止所有运行中的任务
2025/06/12 21:03:18 main.go:436:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 21:03:18 main.go:437: 
2025/06/12 21:03:18 main.go:438: 🗑️  任务管理:
2025/06/12 21:03:18 main.go:439:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 21:03:18 main.go:440: 
2025/06/12 21:03:18 main.go:441: 🔧 其他:
2025/06/12 21:03:18 main.go:442:    help                                     - 显示此帮助信息
2025/06/12 21:03:18 main.go:443:    quit | exit                              - 退出程序
2025/06/12 21:03:18 main.go:444: ========================
2025/06/12 21:03:18 main.go:445: 
2025/06/12 21:03:27 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:03:27 main.go:760: 
2025/06/12 21:03:27 main.go:861: 1728000 停止   L    2775 0.020% 0.010  无仓位                   2  19:38  49h   0.09     3.84   0.00 是   manual   -                     
2025/06/12 21:03:27 main.go:861: 2350000 停止   L    2780 0.020% 0.010  无仓位                   2  19:39  49h   0.16     7.49   0.00 是   manual   -                     
2025/06/12 21:03:27 main.go:861: 1364000 停止   L    2672 0.020% 0.010  L0.01@2672            1  16:59  52h   0.00     0.46   0.00 是   manual   -                     
2025/06/12 21:03:27 main.go:861: 6400000 停止   L    2683 0.020% 0.010  L0.01@2683            1  17:23  52h   0.00     0.04   0.00 是   manual   -                     
2025/06/12 21:03:27 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:03:30 main.go:227: 收到停止信号，正在停止所有任务...
2025/06/12 21:03:30 main.go:233: 停止任务 1364000 失败: 任务已停止: 1364000
2025/06/12 21:03:30 main.go:233: 停止任务 1728000 失败: 任务已停止: 1728000
2025/06/12 21:03:30 main.go:233: 停止任务 2350000 失败: 任务已停止: 2350000
2025/06/12 21:03:30 main.go:233: 停止任务 6400000 失败: 任务已停止: 6400000
2025/06/12 21:03:35 main.go:185: 启动API服务模式（包含交互功能），端口: 5899
2025/06/12 21:03:35 main.go:186: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 21:03:35 main.go:202: API服务器已启动，访问地址: http://localhost:5899
2025/06/12 21:03:35 main.go:203: API接口:
2025/06/12 21:03:35 main.go:204:   POST /task                           - 创建对冲任务
2025/06/12 21:03:35 main.go:205:   POST /task/stop                      - 停止对冲任务
2025/06/12 21:03:35 main.go:206:   GET  /tasks                          - 获取所有任务
2025/06/12 21:03:35 main.go:207:   GET  /task/{id}                      - 获取单个任务
2025/06/12 21:03:35 main.go:208: 期权系统API接口:
2025/06/12 21:03:35 main.go:209:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 21:03:35 main.go:210:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 21:03:35 main.go:211:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 21:03:35 main.go:402: 
2025/06/12 21:03:35 main.go:403: 🔧 === 交互命令帮助 ===
2025/06/12 21:03:35 main.go:405:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 21:03:35 main.go:406:       示例: add long 3500 0.0005
2025/06/12 21:03:35 main.go:407:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 21:03:35 main.go:408: 
2025/06/12 21:03:35 main.go:409:    stop <task_id>                           - 停止指定任务
2025/06/12 21:03:35 main.go:410:    list                                     - 列出所有任务
2025/06/12 21:03:35 main.go:411: 
2025/06/12 21:03:35 main.go:412: ⚙️ 参数配置:
2025/06/12 21:03:35 main.go:413:    set <参数名> <值>                         - 设置默认参数
2025/06/12 21:03:35 main.go:414:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 21:03:35 main.go:415:    show defaults                            - 显示当前默认参数
2025/06/12 21:03:35 main.go:416: 
2025/06/12 21:03:35 main.go:417: 📊 状态控制:
2025/06/12 21:03:35 main.go:418:    status                                   - 查看状态输出设置
2025/06/12 21:03:35 main.go:419:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 21:03:35 main.go:420:    status off                               - 停止状态输出
2025/06/12 21:03:35 main.go:421: 
2025/06/12 21:03:35 main.go:422: 📋 数据导出:
2025/06/12 21:03:35 main.go:423:    export | detail                          - 导出详细状态到剪切板
2025/06/12 21:03:35 main.go:424: 
2025/06/12 21:03:35 main.go:425: 💾 数据管理:
2025/06/12 21:03:35 main.go:426:    save                                     - 手动保存数据到本地文件
2025/06/12 21:03:35 main.go:427:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 21:03:35 main.go:428:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 21:03:35 main.go:429: 
2025/06/12 21:03:35 main.go:430: 📋 事件查看:
2025/06/12 21:03:35 main.go:431:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 21:03:35 main.go:432: 
2025/06/12 21:03:35 main.go:433: 🎛️  批量控制:
2025/06/12 21:03:35 main.go:434:    startall                                 - 启动所有已停止的任务
2025/06/12 21:03:35 main.go:435:    stopall                                  - 停止所有运行中的任务
2025/06/12 21:03:35 main.go:436:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 21:03:35 main.go:437: 
2025/06/12 21:03:35 main.go:438: 🗑️  任务管理:
2025/06/12 21:03:35 main.go:439:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 21:03:35 main.go:440: 
2025/06/12 21:03:35 main.go:441: 🔧 其他:
2025/06/12 21:03:35 main.go:442:    help                                     - 显示此帮助信息
2025/06/12 21:03:35 main.go:443:    quit | exit                              - 退出程序
2025/06/12 21:03:35 main.go:444: ========================
2025/06/12 21:03:35 main.go:445: 
2025/06/12 21:03:35 api.go:35: API服务器启动，端口: 5899
2025/06/12 21:03:36 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:03:36 main.go:760: 
2025/06/12 21:03:36 main.go:861: 2350000 停止   L    2780 0.020% 0.010  无仓位                   2  19:39  49h   0.16     7.49   0.00 是   manual   -                     
2025/06/12 21:03:36 main.go:861: 1728000 停止   L    2775 0.020% 0.010  无仓位                   2  19:38  49h   0.09     3.84   0.00 是   manual   -                     
2025/06/12 21:03:36 main.go:861: 6400000 停止   L    2683 0.020% 0.010  L0.01@2683            1  17:23  52h   0.00     0.04   0.00 是   manual   -                     
2025/06/12 21:03:36 main.go:861: 1364000 停止   L    2672 0.020% 0.010  L0.01@2672            1  16:59  52h   0.00     0.46   0.00 是   manual   -                     
2025/06/12 21:03:36 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:03:50 状态变化: stopped -> running | 原因: 批量启动
2025/06/12 21:03:50 状态变化: stopped -> running | 原因: 批量启动
2025/06/12 21:03:50 状态变化: stopped -> running | 原因: 批量启动
2025/06/12 21:03:50 状态变化: stopped -> running | 原因: 批量启动
2025/06/12 21:03:50 hedge_task.go:109: 启动对冲任务: 1364000
2025/06/12 21:03:50 hedge_task.go:977: 🔧 [1364000] 使用现有配置，MaxRetries: 3
2025/06/12 21:03:50 hedge_task.go:981: 🔧 [1364000] 调用NewSmartOrderManager，传递配置MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:34: 🔧 [1364000] NewSmartOrderManager 接收到的配置:
2025/06/12 21:03:50 smart_order.go:35:    - InitConfig.MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:36:    - InitConfig.RetryInterval: 2s
2025/06/12 21:03:50 smart_order.go:37:    - InitConfig.EnableAsyncInit: true
2025/06/12 21:03:50 smart_order.go:38:    - InitConfig.EnableRollback: true
2025/06/12 21:03:50 smart_order.go:57: 🔧 [1364000] SmartOrderManager 创建完成，存储的配置:
2025/06/12 21:03:50 smart_order.go:58:    - som.config.InitConfig.MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:374: 🚀 [1364000] 开始异步初始化...
2025/06/12 21:03:50 smart_order.go:408: 🔵 [1364000] 智能订单管理器开始初始化...
2025/06/12 21:03:50 smart_order.go:177: 🔗 [1364000] 准备连接交易WebSocket: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 smart_order.go:178: 🔧 [1364000] initTradingWebSocket 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:67: 🔧 [1364000] 交易WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:75: 🔄 [1364000] 交易WebSocket连接尝试 1/3
2025/06/12 21:03:50 hedge_task.go:109: 启动对冲任务: 1728000
2025/06/12 21:03:50 hedge_task.go:109: 启动对冲任务: 2350000
2025/06/12 21:03:50 hedge_task.go:977: 🔧 [2350000] 使用现有配置，MaxRetries: 3
2025/06/12 21:03:50 hedge_task.go:981: 🔧 [2350000] 调用NewSmartOrderManager，传递配置MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:34: 🔧 [2350000] NewSmartOrderManager 接收到的配置:
2025/06/12 21:03:50 smart_order.go:35:    - InitConfig.MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:36:    - InitConfig.RetryInterval: 2s
2025/06/12 21:03:50 smart_order.go:37:    - InitConfig.EnableAsyncInit: true
2025/06/12 21:03:50 smart_order.go:38:    - InitConfig.EnableRollback: true
2025/06/12 21:03:50 smart_order.go:57: 🔧 [2350000] SmartOrderManager 创建完成，存储的配置:
2025/06/12 21:03:50 smart_order.go:58:    - som.config.InitConfig.MaxRetries: 3
2025/06/12 21:03:50 hedge_task.go:977: 🔧 [1728000] 使用现有配置，MaxRetries: 3
2025/06/12 21:03:50 hedge_task.go:981: 🔧 [1728000] 调用NewSmartOrderManager，传递配置MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:34: 🔧 [1728000] NewSmartOrderManager 接收到的配置:
2025/06/12 21:03:50 smart_order.go:35:    - InitConfig.MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:36:    - InitConfig.RetryInterval: 2s
2025/06/12 21:03:50 smart_order.go:374: 🚀 [2350000] 开始异步初始化...
2025/06/12 21:03:50 smart_order.go:408: 🔵 [2350000] 智能订单管理器开始初始化...
2025/06/12 21:03:50 smart_order.go:177: 🔗 [2350000] 准备连接交易WebSocket: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 smart_order.go:178: 🔧 [2350000] initTradingWebSocket 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:67: 🔧 [2350000] 交易WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:75: 🔄 [2350000] 交易WebSocket连接尝试 1/3
2025/06/12 21:03:50 hedge_task.go:109: 启动对冲任务: 6400000
2025/06/12 21:03:50 hedge_task.go:977: 🔧 [6400000] 使用现有配置，MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:37:    - InitConfig.EnableAsyncInit: true
2025/06/12 21:03:50 smart_order.go:38:    - InitConfig.EnableRollback: true
2025/06/12 21:03:50 smart_order.go:57: 🔧 [1728000] SmartOrderManager 创建完成，存储的配置:
2025/06/12 21:03:50 smart_order.go:58:    - som.config.InitConfig.MaxRetries: 3
2025/06/12 21:03:50 hedge_task.go:981: 🔧 [6400000] 调用NewSmartOrderManager，传递配置MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:34: 🔧 [6400000] NewSmartOrderManager 接收到的配置:
2025/06/12 21:03:50 smart_order.go:374: 🚀 [1728000] 开始异步初始化...
2025/06/12 21:03:50 smart_order.go:408: 🔵 [1728000] 智能订单管理器开始初始化...
2025/06/12 21:03:50 smart_order.go:177: 🔗 [1728000] 准备连接交易WebSocket: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 smart_order.go:178: 🔧 [1728000] initTradingWebSocket 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:67: 🔧 [1728000] 交易WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:35:    - InitConfig.MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:36:    - InitConfig.RetryInterval: 2s
2025/06/12 21:03:50 smart_order.go:37:    - InitConfig.EnableAsyncInit: true
2025/06/12 21:03:50 smart_order.go:38:    - InitConfig.EnableRollback: true
2025/06/12 21:03:50 smart_order.go:75: 🔄 [1728000] 交易WebSocket连接尝试 1/3
2025/06/12 21:03:50 data_manager.go:189: 💾 任务数据已保存 (4个任务)
2025/06/12 21:03:50 smart_order.go:57: 🔧 [6400000] SmartOrderManager 创建完成，存储的配置:
2025/06/12 21:03:50 smart_order.go:58:    - som.config.InitConfig.MaxRetries: 3
2025/06/12 21:03:50 smart_order.go:374: 🚀 [6400000] 开始异步初始化...
2025/06/12 21:03:50 smart_order.go:408: 🔵 [6400000] 智能订单管理器开始初始化...
2025/06/12 21:03:50 smart_order.go:177: 🔗 [6400000] 准备连接交易WebSocket: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 smart_order.go:178: 🔧 [6400000] initTradingWebSocket 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:67: 🔧 [6400000] 交易WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:75: 🔄 [6400000] 交易WebSocket连接尝试 1/3
2025/06/12 21:03:50 data_manager.go:233: 💾 事件数据已保存 (6个事件)
2025/06/12 21:03:50 websocket_manager.go:91: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 smart_order.go:81: ✅ [6400000] 交易WebSocket连接成功
2025/06/12 21:03:50 smart_order.go:344: ✅ [6400000] 初始化步骤成功: 0
2025/06/12 21:03:50 smart_order.go:103: 🔧 [6400000] authenticateWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:104: 🔧 [6400000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:112: 🔐 [6400000] WebSocket会话认证尝试 1/3
2025/06/12 21:03:50 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:03:50 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:03:50 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"6400000_login_001","method":"session.logon","params":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","signature":"9WHD5L/JUudGcA7v2WrtngRks8RuGUgIGDDzHm5VlnwfLYhUAXIfVzG8by3b9JsGS8IHGZlZzGVxj4UuFdgBBg==","timestamp":1749733430655}}
2025/06/12 21:03:50 websocket_manager.go:143: 📏 消息长度: 267 字节
2025/06/12 21:03:50 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:03:50 smart_order.go:1157: 任务6400000发送WebSocket会话登录请求
2025/06/12 21:03:50 smart_order.go:116: ✅ [6400000] WebSocket会话认证成功
2025/06/12 21:03:50 smart_order.go:344: ✅ [6400000] 初始化步骤成功: 1
2025/06/12 21:03:50 smart_order.go:214: 🔧 [6400000] initListenKey 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:141: 🔧 [6400000] createListenKeyWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:142: 🔧 [6400000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:150: 🔑 [6400000] 创建listenKey尝试 1/3
2025/06/12 21:03:50 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733430655&signature=4e5820200bab1c2af3858caf4b7373018da1d3a4ae575321116ffe8b1e817e37
2025/06/12 21:03:50 binance_client.go:189: 🔍 签名字符串: timestamp=1749733430655
2025/06/12 21:03:50 binance_client.go:190: 🔍 Ed25519签名: GpVQ1pjAhIxmwacoS3gNSQUvPRSjvmLKtt0Gw05OSmYECYxXiIo75J1CAdiqDy7zWLlf0O2W2028RxFk9MRwDA==
2025/06/12 21:03:50 websocket_manager.go:91: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 smart_order.go:81: ✅ [1728000] 交易WebSocket连接成功
2025/06/12 21:03:50 smart_order.go:344: ✅ [1728000] 初始化步骤成功: 0
2025/06/12 21:03:50 smart_order.go:103: 🔧 [1728000] authenticateWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:104: 🔧 [1728000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:112: 🔐 [1728000] WebSocket会话认证尝试 1/3
2025/06/12 21:03:50 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:03:50 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:03:50 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"1728000_login_001","method":"session.logon","params":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","signature":"WnMIlhMhqX11Ye/kh7g+w89Xme2I12/LF8cVsOHTVyzjfay/K45LJQAxdtMA+D3TLEhgTMgrcmYSJzKCLxNxBw==","timestamp":1749733430675}}
2025/06/12 21:03:50 websocket_manager.go:143: 📏 消息长度: 267 字节
2025/06/12 21:03:50 websocket_manager.go:91: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:03:50 smart_order.go:1157: 任务1728000发送WebSocket会话登录请求
2025/06/12 21:03:50 smart_order.go:116: ✅ [1728000] WebSocket会话认证成功
2025/06/12 21:03:50 smart_order.go:81: ✅ [1364000] 交易WebSocket连接成功
2025/06/12 21:03:50 smart_order.go:344: ✅ [1728000] 初始化步骤成功: 1
2025/06/12 21:03:50 smart_order.go:214: 🔧 [1728000] initListenKey 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:141: 🔧 [1728000] createListenKeyWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:344: ✅ [1364000] 初始化步骤成功: 0
2025/06/12 21:03:50 smart_order.go:142: 🔧 [1728000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:150: 🔑 [1728000] 创建listenKey尝试 1/3
2025/06/12 21:03:50 smart_order.go:103: 🔧 [1364000] authenticateWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:104: 🔧 [1364000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:112: 🔐 [1364000] WebSocket会话认证尝试 1/3
2025/06/12 21:03:50 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:03:50 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:03:50 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"1364000_login_001","method":"session.logon","params":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","signature":"1LGweMQPN58QEwRhtPDUUz9B77ICfXQOxyU0P3xLbauxnnQPW9hbsB+tRRij4gRDPYvZZpBXyNIn4PeJAQbNDA==","timestamp":1749733430676}}
2025/06/12 21:03:50 websocket_manager.go:143: 📏 消息长度: 267 字节
2025/06/12 21:03:50 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733430676&signature=509c2bbc6744587ea118d2bbee85c5ba2547b5d94c18b1e65eb13c47f52f8fe6
2025/06/12 21:03:50 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:03:50 smart_order.go:1157: 任务1364000发送WebSocket会话登录请求
2025/06/12 21:03:50 smart_order.go:116: ✅ [1364000] WebSocket会话认证成功
2025/06/12 21:03:50 smart_order.go:344: ✅ [1364000] 初始化步骤成功: 1
2025/06/12 21:03:50 smart_order.go:214: 🔧 [1364000] initListenKey 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:141: 🔧 [1364000] createListenKeyWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:142: 🔧 [1364000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:150: 🔑 [1364000] 创建listenKey尝试 1/3
2025/06/12 21:03:50 binance_client.go:189: 🔍 签名字符串: timestamp=1749733430676
2025/06/12 21:03:50 binance_client.go:190: 🔍 Ed25519签名: Vlxoa+jC3AyuKTNWkykfLDYdFprPb9T+v9wt8bfe0R91V3tE9dHzuUTxbpU+Xucotb84wAcDjBwXY7r/ccBiBg==
2025/06/12 21:03:50 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733430676&signature=509c2bbc6744587ea118d2bbee85c5ba2547b5d94c18b1e65eb13c47f52f8fe6
2025/06/12 21:03:50 binance_client.go:189: 🔍 签名字符串: timestamp=1749733430676
2025/06/12 21:03:50 binance_client.go:190: 🔍 Ed25519签名: Vlxoa+jC3AyuKTNWkykfLDYdFprPb9T+v9wt8bfe0R91V3tE9dHzuUTxbpU+Xucotb84wAcDjBwXY7r/ccBiBg==
2025/06/12 21:03:50 websocket_manager.go:91: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:03:50 smart_order.go:81: ✅ [2350000] 交易WebSocket连接成功
2025/06/12 21:03:50 smart_order.go:344: ✅ [2350000] 初始化步骤成功: 0
2025/06/12 21:03:50 smart_order.go:103: 🔧 [2350000] authenticateWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:104: 🔧 [2350000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:112: 🔐 [2350000] WebSocket会话认证尝试 1/3
2025/06/12 21:03:50 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:03:50 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:03:50 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"2350000_login_001","method":"session.logon","params":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","signature":"eUhwAn7HR/kaho//IN3bptwo9gm8OzIPlWuvzxb/HeBmVQuQBlusWl2RVrj6y6HV9pJxUFQswNXrS1hJrr+bDw==","timestamp":1749733430707}}
2025/06/12 21:03:50 websocket_manager.go:143: 📏 消息长度: 267 字节
2025/06/12 21:03:50 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:03:50 smart_order.go:1157: 任务2350000发送WebSocket会话登录请求
2025/06/12 21:03:50 smart_order.go:116: ✅ [2350000] WebSocket会话认证成功
2025/06/12 21:03:50 smart_order.go:344: ✅ [2350000] 初始化步骤成功: 1
2025/06/12 21:03:50 smart_order.go:214: 🔧 [2350000] initListenKey 使用重试次数: 3
2025/06/12 21:03:50 smart_order.go:141: 🔧 [2350000] createListenKeyWithRetry 接收参数: maxRetries=3
2025/06/12 21:03:50 smart_order.go:142: 🔧 [2350000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:03:50 smart_order.go:150: 🔑 [2350000] 创建listenKey尝试 1/3
2025/06/12 21:03:50 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733430707&signature=d22b042aaa8f350d29c03b3e9416262aef517d00eaa325c4d48e3aeb379f39d4
2025/06/12 21:03:50 binance_client.go:189: 🔍 签名字符串: timestamp=1749733430707
2025/06/12 21:03:50 binance_client.go:190: 🔍 Ed25519签名: sMgUvq5Zz8w1I2VgWMOvqR0ZbEI7mErLY+zK+mkMtxjgws+2o7joozMtRvhlyP0AVrDWere6SFXMOO8GmWU/DQ==
2025/06/12 21:03:50 websocket_manager.go:225: 📨 WS[trading]: trading_response (6400000_login_001)
2025/06/12 21:03:50 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:6400000_login_001 rateLimits:[map[count:22 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431208 connectedSince:1749733431025 returnRateLimits:true serverTime:1749733431208] status:200]
2025/06/12 21:03:50 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"6400000_login_001","rateLimits":[{"count":22,"interval":"MINUTE","intervalNum":1,"limit":2400,"rateLimitType":"REQUEST_WEIGHT"}],"result":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","authorizedSince":1749733431208,"connectedSince":1749733431025,"returnRateLimits":true,"serverTime":1749733431208},"status":200}
2025/06/12 21:03:50 binance_client.go:687: ✅ listenKey创建成功: r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:154: ✅ [6400000] listenKey创建成功: r8F1DvtY...
2025/06/12 21:03:50 smart_order.go:344: ✅ [6400000] 初始化步骤成功: 2
2025/06/12 21:03:50 smart_order.go:236: 🔗 [6400000] 准备连接用户数据WebSocket: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:67: 🔧 [6400000] 用户数据WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:75: 🔄 [6400000] 用户数据WebSocket连接尝试 1/3
2025/06/12 21:03:50 binance_client.go:687: ✅ listenKey创建成功: r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:154: ✅ [1728000] listenKey创建成功: r8F1DvtY...
2025/06/12 21:03:50 smart_order.go:344: ✅ [1728000] 初始化步骤成功: 2
2025/06/12 21:03:50 smart_order.go:236: 🔗 [1728000] 准备连接用户数据WebSocket: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:67: 🔧 [1728000] 用户数据WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:75: 🔄 [1728000] 用户数据WebSocket连接尝试 1/3
2025/06/12 21:03:50 binance_client.go:687: ✅ listenKey创建成功: r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:154: ✅ [1364000] listenKey创建成功: r8F1DvtY...
2025/06/12 21:03:50 smart_order.go:344: ✅ [1364000] 初始化步骤成功: 2
2025/06/12 21:03:50 smart_order.go:236: 🔗 [1364000] 准备连接用户数据WebSocket: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:67: 🔧 [1364000] 用户数据WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:75: 🔄 [1364000] 用户数据WebSocket连接尝试 1/3
2025/06/12 21:03:50 websocket_manager.go:225: 📨 WS[trading]: trading_response (1364000_login_001)
2025/06/12 21:03:50 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:1364000_login_001 rateLimits:[map[count:26 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431232 connectedSince:1749733431044 returnRateLimits:true serverTime:1749733431232] status:200]
2025/06/12 21:03:50 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"1364000_login_001","rateLimits":[{"count":26,"interval":"MINUTE","intervalNum":1,"limit":2400,"rateLimitType":"REQUEST_WEIGHT"}],"result":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","authorizedSince":1749733431232,"connectedSince":1749733431044,"returnRateLimits":true,"serverTime":1749733431232},"status":200}
2025/06/12 21:03:50 websocket_manager.go:225: 📨 WS[trading]: trading_response (1728000_login_001)
2025/06/12 21:03:50 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:1728000_login_001 rateLimits:[map[count:24 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431232 connectedSince:1749733431045 returnRateLimits:true serverTime:1749733431232] status:200]
2025/06/12 21:03:50 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"1728000_login_001","rateLimits":[{"count":24,"interval":"MINUTE","intervalNum":1,"limit":2400,"rateLimitType":"REQUEST_WEIGHT"}],"result":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","authorizedSince":1749733431232,"connectedSince":1749733431045,"returnRateLimits":true,"serverTime":1749733431232},"status":200}
2025/06/12 21:03:50 websocket_manager.go:225: 📨 WS[trading]: trading_response (2350000_login_001)
2025/06/12 21:03:50 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:2350000_login_001 rateLimits:[map[count:28 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431260 connectedSince:1749733431078 returnRateLimits:true serverTime:1749733431260] status:200]
2025/06/12 21:03:50 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"2350000_login_001","rateLimits":[{"count":28,"interval":"MINUTE","intervalNum":1,"limit":2400,"rateLimitType":"REQUEST_WEIGHT"}],"result":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","authorizedSince":1749733431260,"connectedSince":1749733431078,"returnRateLimits":true,"serverTime":1749733431260},"status":200}
2025/06/12 21:03:50 binance_client.go:687: ✅ listenKey创建成功: r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:154: ✅ [2350000] listenKey创建成功: r8F1DvtY...
2025/06/12 21:03:50 smart_order.go:344: ✅ [2350000] 初始化步骤成功: 2
2025/06/12 21:03:50 smart_order.go:236: 🔗 [2350000] 准备连接用户数据WebSocket: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:50 smart_order.go:67: 🔧 [2350000] 用户数据WebSocket连接重试配置: maxRetries=3
2025/06/12 21:03:50 smart_order.go:75: 🔄 [2350000] 用户数据WebSocket连接尝试 1/3
2025/06/12 21:03:51 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:51 smart_order.go:81: ✅ [6400000] 用户数据WebSocket连接成功
2025/06/12 21:03:51 smart_order.go:344: ✅ [6400000] 初始化步骤成功: 3
2025/06/12 21:03:51 smart_order.go:434: ⏰ [6400000] 启动listenKey续期协程...
2025/06/12 21:03:51 smart_order.go:437: 🎉 [6400000] 智能订单管理器初始化成功！
2025/06/12 21:03:51 smart_order.go:397: 🎉 [6400000] 异步初始化成功！耗时: 1.175332542s
2025/06/12 21:03:51 hedge_task.go:990: 任务 6400000: 智能订单系统初始化成功
2025/06/12 21:03:51 smart_order.go:1012: 🔄 [6400000] listenKey续期循环启动，续期间隔: 20m0s
2025/06/12 21:03:51 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:51 smart_order.go:81: ✅ [1728000] 用户数据WebSocket连接成功
2025/06/12 21:03:51 smart_order.go:344: ✅ [1728000] 初始化步骤成功: 3
2025/06/12 21:03:51 smart_order.go:434: ⏰ [1728000] 启动listenKey续期协程...
2025/06/12 21:03:51 smart_order.go:437: 🎉 [1728000] 智能订单管理器初始化成功！
2025/06/12 21:03:51 smart_order.go:397: 🎉 [1728000] 异步初始化成功！耗时: 1.214031583s
2025/06/12 21:03:51 smart_order.go:1012: 🔄 [1728000] listenKey续期循环启动，续期间隔: 20m0s
2025/06/12 21:03:51 hedge_task.go:990: 任务 1728000: 智能订单系统初始化成功
2025/06/12 21:03:51 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:51 smart_order.go:81: ✅ [2350000] 用户数据WebSocket连接成功
2025/06/12 21:03:51 smart_order.go:344: ✅ [2350000] 初始化步骤成功: 3
2025/06/12 21:03:51 smart_order.go:434: ⏰ [2350000] 启动listenKey续期协程...
2025/06/12 21:03:51 smart_order.go:437: 🎉 [2350000] 智能订单管理器初始化成功！
2025/06/12 21:03:51 smart_order.go:397: 🎉 [2350000] 异步初始化成功！耗时: 1.355330167s
2025/06/12 21:03:51 hedge_task.go:990: 任务 2350000: 智能订单系统初始化成功
2025/06/12 21:03:51 smart_order.go:1012: 🔄 [2350000] listenKey续期循环启动，续期间隔: 20m0s
2025/06/12 21:03:51 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:03:51 smart_order.go:81: ✅ [1364000] 用户数据WebSocket连接成功
2025/06/12 21:03:51 smart_order.go:344: ✅ [1364000] 初始化步骤成功: 3
2025/06/12 21:03:51 smart_order.go:434: ⏰ [1364000] 启动listenKey续期协程...
2025/06/12 21:03:51 smart_order.go:437: 🎉 [1364000] 智能订单管理器初始化成功！
2025/06/12 21:03:51 smart_order.go:397: 🎉 [1364000] 异步初始化成功！耗时: 1.384224416s
2025/06/12 21:03:51 hedge_task.go:990: 任务 1364000: 智能订单系统初始化成功
2025/06/12 21:03:51 smart_order.go:1012: 🔄 [1364000] listenKey续期循环启动，续期间隔: 20m0s
2025/06/12 21:03:52 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:03:52 market_data_manager.go:174: 成功创建ETHUSDT市场数据连接: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:03:52 market_data_manager.go:117: 任务6400000成功订阅ETHUSDT市场数据，当前订阅者数量: 1
2025/06/12 21:03:52 hedge_task.go:1011: 🔧 [6400000] handleSmartOrderEvents 开始执行
2025/06/12 21:03:52 hedge_task.go:1030: ✅ [6400000] WebSocket管理器检查通过，获取事件通道
2025/06/12 21:03:52 hedge_task.go:1036: ✅ [6400000] 事件通道获取成功，开始监听事件
2025/06/12 21:03:52 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 6400000: 开始处理 trading_response
2025/06/12 21:03:52 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 6400000: 完整数据: map[id:6400000_login_001 rateLimits:[map[count:22 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431208 connectedSince:1749733431025 returnRateLimits:true serverTime:1749733431208] status:200]
2025/06/12 21:03:52 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 6400000: 请求ID 6400000_login_001 不在pendingRequests中，忽略
2025/06/12 21:03:52 market_data_manager.go:117: 任务1728000成功订阅ETHUSDT市场数据，当前订阅者数量: 2
2025/06/12 21:03:52 hedge_task.go:1011: 🔧 [1728000] handleSmartOrderEvents 开始执行
2025/06/12 21:03:52 hedge_task.go:1030: ✅ [1728000] WebSocket管理器检查通过，获取事件通道
2025/06/12 21:03:52 hedge_task.go:1036: ✅ [1728000] 事件通道获取成功，开始监听事件
2025/06/12 21:03:52 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 1728000: 开始处理 trading_response
2025/06/12 21:03:52 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 1728000: 完整数据: map[id:1728000_login_001 rateLimits:[map[count:24 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431232 connectedSince:1749733431045 returnRateLimits:true serverTime:1749733431232] status:200]
2025/06/12 21:03:52 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 1728000: 请求ID 1728000_login_001 不在pendingRequests中，忽略
2025/06/12 21:03:52 market_data_manager.go:117: 任务2350000成功订阅ETHUSDT市场数据，当前订阅者数量: 3
2025/06/12 21:03:52 hedge_task.go:1011: 🔧 [2350000] handleSmartOrderEvents 开始执行
2025/06/12 21:03:52 hedge_task.go:1030: ✅ [2350000] WebSocket管理器检查通过，获取事件通道
2025/06/12 21:03:52 hedge_task.go:1036: ✅ [2350000] 事件通道获取成功，开始监听事件
2025/06/12 21:03:52 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 2350000: 开始处理 trading_response
2025/06/12 21:03:52 market_data_manager.go:117: 任务1364000成功订阅ETHUSDT市场数据，当前订阅者数量: 4
2025/06/12 21:03:52 hedge_task.go:1011: 🔧 [1364000] handleSmartOrderEvents 开始执行
2025/06/12 21:03:52 hedge_task.go:1030: ✅ [1364000] WebSocket管理器检查通过，获取事件通道
2025/06/12 21:03:52 hedge_task.go:1036: ✅ [1364000] 事件通道获取成功，开始监听事件
2025/06/12 21:03:52 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 1364000: 开始处理 trading_response
2025/06/12 21:03:52 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 1364000: 完整数据: map[id:1364000_login_001 rateLimits:[map[count:26 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431232 connectedSince:1749733431044 returnRateLimits:true serverTime:1749733431232] status:200]
2025/06/12 21:03:52 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 1364000: 请求ID 1364000_login_001 不在pendingRequests中，忽略
2025/06/12 21:03:52 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 2350000: 完整数据: map[id:2350000_login_001 rateLimits:[map[count:28 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733431260 connectedSince:1749733431078 returnRateLimits:true serverTime:1749733431260] status:200]
2025/06/12 21:03:52 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 2350000: 请求ID 2350000_login_001 不在pendingRequests中，忽略
2025/06/12 21:03:58 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:03:58 main.go:760: 
2025/06/12 21:03:58 main.go:861: 1728000 运行   L    2775 0.020% 0.010  无仓位                   2  19:38  49h   0.09     3.84   0.00 是   manual   -                     
2025/06/12 21:03:58 main.go:861: 2350000 运行   L    2780 0.020% 0.010  无仓位                   2  19:39  49h   0.16     7.49   0.00 是   manual   -                     
2025/06/12 21:03:58 main.go:861: 6400000 运行   L    2683 0.020% 0.010  L0.01@2683            1  17:23  52h   0.00     0.04   0.00 是   manual   -                     
2025/06/12 21:03:58 main.go:861: 1364000 运行   L    2672 0.020% 0.010  L0.01@2672            1  16:59  52h   0.00     0.46   0.00 是   manual   -                     
2025/06/12 21:03:58 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:04:08 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 21:04:08 hedge_task.go:163: 任务 1728000: 收到停止信号
2025/06/12 21:04:08 hedge_task.go:578: 任务 1728000: 无仓位，直接停止
2025/06/12 21:04:08 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58509->198.18.1.239:443: use of closed network connection
2025/06/12 21:04:08 websocket_manager.go:118: WebSocket连接已断开: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:04:08 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58517->198.18.0.45:443: use of closed network connection
2025/06/12 21:04:08 websocket_manager.go:118: WebSocket连接已断开: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:08 binance_client.go:188: 🔍 Ed25519签名请求: DELETE https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733448817&signature=2a7b0bff32801d8ea4c6bec5ede8971daf7351792f30d9406c9e4f685e25ce75
2025/06/12 21:04:08 binance_client.go:189: 🔍 签名字符串: timestamp=1749733448817&listenKey=r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:08 binance_client.go:190: 🔍 Ed25519签名: reb6o3QAd2+I6oyusBsMx6Zsz3B1x8WhfwBIqkO17xRzyFSngS/S0z+59e/n9/DUgm5fuRDk+SQsWFyzY7kvDQ==
2025/06/12 21:04:08 smart_order.go:1047: 🛑 [1728000] listenKey续期循环停止
2025/06/12 21:04:08 data_manager.go:189: 💾 任务数据已保存 (3个任务)
2025/06/12 21:04:08 data_manager.go:233: 💾 事件数据已保存 (4个事件)
2025/06/12 21:04:08 binance_client.go:709: ✅ listenKey删除成功: r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:08 smart_order.go:515: 任务1728000智能订单管理器已关闭
2025/06/12 21:04:08 market_data_manager.go:134: 任务1728000取消订阅ETHUSDT市场数据，剩余订阅者数量: 3
2025/06/12 21:04:08 hedge_task.go:1006: 任务 1728000: 智能订单系统已清理
2025/06/12 21:04:08 hedge_task.go:165: 对冲任务结束: 1728000
2025/06/12 21:04:09 hedge_task.go:1072: ❌ [1364000] 用户数据WS错误: <nil>
2025/06/12 21:04:09 hedge_task.go:1072: ❌ [2350000] 用户数据WS错误: <nil>
2025/06/12 21:04:09 hedge_task.go:1072: ❌ [6400000] 用户数据WS错误: <nil>
2025/06/12 21:04:10 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:10 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:10 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:11 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:11 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:11 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:12 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:14 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:04:14 main.go:760: 
2025/06/12 21:04:14 main.go:861: 2350000 运行   L    2780 0.020% 0.010  无仓位                   2  19:39  49h   0.16     7.49   0.00 是   manual   -                     
2025/06/12 21:04:14 main.go:861: 6400000 运行   L    2683 0.020% 0.010  L0.01@2683            1  17:23  52h   0.00     0.04   0.00 是   manual   -                     
2025/06/12 21:04:14 main.go:861: 1364000 运行   L    2672 0.020% 0.010  L0.01@2672            1  16:59  52h   0.00     0.46   0.00 是   manual   -                     
2025/06/12 21:04:14 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:04:19 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 21:04:19 hedge_task.go:163: 任务 2350000: 收到停止信号
2025/06/12 21:04:19 hedge_task.go:578: 任务 2350000: 无仓位，直接停止
2025/06/12 21:04:19 smart_order.go:1047: 🛑 [2350000] listenKey续期循环停止
2025/06/12 21:04:19 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58507->198.18.1.239:443: use of closed network connection
2025/06/12 21:04:19 websocket_manager.go:118: WebSocket连接已断开: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:04:19 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58522->198.18.0.45:443: use of closed network connection
2025/06/12 21:04:19 websocket_manager.go:118: WebSocket连接已断开: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:19 binance_client.go:188: 🔍 Ed25519签名请求: DELETE https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733459111&signature=e7dcb78d23ccd4889cb222bf04bb7af64b384ec0a50a6fb984f2d7b5c0ffae62
2025/06/12 21:04:19 binance_client.go:189: 🔍 签名字符串: timestamp=1749733459111&listenKey=r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:19 binance_client.go:190: 🔍 Ed25519签名: ZLdHNRme93VH2bWhStfZRNT3jlPJ5Rh4yR5JiOD1TDKQR3ZPDpG011iZthnyzj16EVUo7MPZnrKm7AGkNa0qBQ==
2025/06/12 21:04:19 data_manager.go:189: 💾 任务数据已保存 (2个任务)
2025/06/12 21:04:19 data_manager.go:233: 💾 事件数据已保存 (2个事件)
2025/06/12 21:04:19 smart_order.go:515: 任务2350000智能订单管理器已关闭
2025/06/12 21:04:19 market_data_manager.go:134: 任务2350000取消订阅ETHUSDT市场数据，剩余订阅者数量: 2
2025/06/12 21:04:19 hedge_task.go:1006: 任务 2350000: 智能订单系统已清理
2025/06/12 21:04:19 hedge_task.go:165: 对冲任务结束: 2350000
2025/06/12 21:04:20 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:04:20 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:04:20 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:04:20 main.go:760: 
2025/06/12 21:04:20 main.go:861: 1364000 运行   L    2672 0.020% 0.010  L0.01@2672            1  16:59  52h   0.00     0.46   0.00 是   manual   -                     
2025/06/12 21:04:20 main.go:861: 6400000 运行   L    2683 0.020% 0.010  L0.01@2683            1  17:23  52h   0.00     0.04   0.00 是   manual   -                     
2025/06/12 21:04:20 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:04:21 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:04:21 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:04:22 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:04:26 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 21:04:26 hedge_task.go:163: 任务 1364000: 收到停止信号
2025/06/12 21:04:26 hedge_task.go:589: 任务 1364000: 开始停止平仓，仓位: long 0.01
2025/06/12 21:04:26 hedge_task.go:600: 任务 1364000: 做多平仓，理论平仓价: 2671.4656 (目标价-阈值)
2025/06/12 21:04:26 hedge_task.go:614: 任务 1364000: 开始动态平仓，理论价格: 2671.4656, 方向: short
2025/06/12 21:04:26 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/12 21:04:26 data_manager.go:233: 💾 事件数据已保存 (1个事件)
2025/06/12 21:04:27 hedge_task.go:708: 任务 1364000: 检查卖出平仓条件 - 卖一价: 2741.18, 范围: [2644.750944, 2698.180256]
2025/06/12 21:04:27 hedge_task.go:662: 任务 1364000: 超出滑点范围，执行市价平仓
2025/06/12 21:04:27 hedge_task.go:769: 任务 1364000: 强制市价平仓，数量: 0.01, 方向: short
2025/06/12 21:04:27 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/order?timestamp=1749733467643&signature=89be2005b04099cccc222b96edc94b3339941842b6c20d36c69029c1c81d4acd
2025/06/12 21:04:27 binance_client.go:189: 🔍 签名字符串: timestamp=1749733467643&newClientOrderId=x-Cb7ytekJ6e17a9d296794861a3105e&newOrderRespType=&quantity=0.01&side=SELL&symbol=ETHUSDT&type=MARKET
2025/06/12 21:04:27 binance_client.go:190: 🔍 Ed25519签名: 5xLPwAQRzrc9gKMW0FZoUpe0HG1PX/PjMtDKwpjxR40mnv+m77hyN8kncuko7uIIUxEqf1Ed/PQzw5hw6SilDA==
2025/06/12 21:04:27 hedge_task.go:774: 任务 1364000: 市价平仓失败: 下市价单失败: <APIError> code=-1022, msg=Signature for this request is not valid.
2025/06/12 21:04:27 smart_order.go:1047: 🛑 [1364000] listenKey续期循环停止
2025/06/12 21:04:27 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58508->198.18.1.239:443: use of closed network connection
2025/06/12 21:04:27 websocket_manager.go:118: WebSocket连接已断开: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:04:27 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58516->198.18.0.45:443: use of closed network connection
2025/06/12 21:04:27 websocket_manager.go:118: WebSocket连接已断开: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:27 binance_client.go:188: 🔍 Ed25519签名请求: DELETE https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733467834&signature=c17d1c2dfa44e6a8dfeb99b80897d71184350fed24f24d33ba5764df514a1bde
2025/06/12 21:04:27 binance_client.go:189: 🔍 签名字符串: timestamp=1749733467834&listenKey=r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:27 binance_client.go:190: 🔍 Ed25519签名: 22ypA7h3v87XsQrUYsAPULaqKaz0BwTs66NvKNPOVGW9wkoIlqNZR+/RakVVZ84tipCuGrdc6Ei6RK9S83ueDg==
2025/06/12 21:04:28 smart_order.go:515: 任务1364000智能订单管理器已关闭
2025/06/12 21:04:28 market_data_manager.go:134: 任务1364000取消订阅ETHUSDT市场数据，剩余订阅者数量: 1
2025/06/12 21:04:28 hedge_task.go:1006: 任务 1364000: 智能订单系统已清理
2025/06/12 21:04:28 hedge_task.go:165: 对冲任务结束: 1364000
2025/06/12 21:04:29 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:04:29 main.go:760: 
2025/06/12 21:04:29 main.go:861: 6400000 运行   L    2683 0.020% 0.010  L0.01@2683            1  17:23  52h   0.00     0.04   0.00 是   manual   -                     
2025/06/12 21:04:29 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:04:30 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:31 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:32 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:04:35 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 21:04:35 hedge_task.go:163: 任务 6400000: 收到停止信号
2025/06/12 21:04:35 hedge_task.go:589: 任务 6400000: 开始停止平仓，仓位: long 0.01
2025/06/12 21:04:35 hedge_task.go:600: 任务 6400000: 做多平仓，理论平仓价: 2682.4634 (目标价-阈值)
2025/06/12 21:04:35 hedge_task.go:614: 任务 6400000: 开始动态平仓，理论价格: 2682.4634, 方向: short
2025/06/12 21:04:35 data_manager.go:189: 💾 任务数据已保存 (0个任务)
2025/06/12 21:04:35 data_manager.go:233: 💾 事件数据已保存 (0个事件)
2025/06/12 21:04:35 hedge_task.go:708: 任务 6400000: 检查卖出平仓条件 - 卖一价: 2741.91, 范围: [2655.638766, 2709.288034]
2025/06/12 21:04:35 hedge_task.go:662: 任务 6400000: 超出滑点范围，执行市价平仓
2025/06/12 21:04:36 hedge_task.go:769: 任务 6400000: 强制市价平仓，数量: 0.01, 方向: short
2025/06/12 21:04:36 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/order?timestamp=1749733476029&signature=2ea8c39bd97ab261c672b4050ed052039377a2b09120864dd094b0b6e028433d
2025/06/12 21:04:36 binance_client.go:189: 🔍 签名字符串: timestamp=1749733476029&newClientOrderId=x-Cb7ytekJ884ce6c4be0e40448ba4ea&newOrderRespType=&quantity=0.01&side=SELL&symbol=ETHUSDT&type=MARKET
2025/06/12 21:04:36 binance_client.go:190: 🔍 Ed25519签名: dWRic0hRkt80cWYEmWTBLeMTLi9FrByNDCc9dtoHz+GND+Pn22myAhyfV06kc27ZoEJoGTH1zPxAa/Q8GJCbCw==
2025/06/12 21:04:36 hedge_task.go:774: 任务 6400000: 市价平仓失败: 下市价单失败: <APIError> code=-1022, msg=Signature for this request is not valid.
2025/06/12 21:04:36 smart_order.go:1047: 🛑 [6400000] listenKey续期循环停止
2025/06/12 21:04:36 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58506->198.18.1.239:443: use of closed network connection
2025/06/12 21:04:36 websocket_manager.go:118: WebSocket连接已断开: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:04:36 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58514->198.18.0.45:443: use of closed network connection
2025/06/12 21:04:36 websocket_manager.go:118: WebSocket连接已断开: wss://fstream.binance.com/ws/r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:36 binance_client.go:188: 🔍 Ed25519签名请求: DELETE https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733476213&signature=df65e229e3fc417af266895336fcfb0c3f9926d03b990fd1f50c81e1e5aa2d12
2025/06/12 21:04:36 binance_client.go:189: 🔍 签名字符串: timestamp=1749733476213&listenKey=r8F1DvtYaKCMc08LTgkETome6G7qKxtPfDjBXTwB5ZsxpJ93lWcdQ5A5Uy1xNM15
2025/06/12 21:04:36 binance_client.go:190: 🔍 Ed25519签名: 3Xlzgkppw2GErc1zKpAPnLnYgcwnc21cbTXaPemJ8mCGrQvYTQzX0YlXTY6oGHtxXblAl6ovIDKSLNcovic0Dg==
2025/06/12 21:04:36 smart_order.go:515: 任务6400000智能订单管理器已关闭
2025/06/12 21:04:36 market_data_manager.go:134: 任务6400000取消订阅ETHUSDT市场数据，剩余订阅者数量: 0
2025/06/12 21:04:36 market_data_manager.go:140: 关闭ETHUSDT市场数据连接（无订阅者）
2025/06/12 21:04:36 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:58526->198.18.0.45:443: use of closed network connection
2025/06/12 21:04:36 websocket_manager.go:118: WebSocket连接已断开: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:04:36 hedge_task.go:1006: 任务 6400000: 智能订单系统已清理
2025/06/12 21:04:36 hedge_task.go:165: 对冲任务结束: 6400000
2025/06/12 21:04:36 market_data_manager.go:205: ETHUSDT市场数据错误: read tcp 198.18.0.1:58526->198.18.0.45:443: use of closed network connection
2025/06/12 21:04:37 main.go:726: 📋 当前没有运行的任务
2025/06/12 21:04:52 manager.go:208: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 21:04:52 状态变化:  -> running | 原因: 任务创建
2025/06/12 21:04:52 manager.go:102: 创建对冲任务: 8441000, 交易对: ETHUSDT, 方向: long, 目标价: 2741, 来源: manual
2025/06/12 21:04:52 hedge_task.go:109: 启动对冲任务: 8441000
2025/06/12 21:04:52 hedge_task.go:977: 🔧 [8441000] 使用现有配置，MaxRetries: 3
2025/06/12 21:04:52 hedge_task.go:981: 🔧 [8441000] 调用NewSmartOrderManager，传递配置MaxRetries: 3
2025/06/12 21:04:52 smart_order.go:34: 🔧 [8441000] NewSmartOrderManager 接收到的配置:
2025/06/12 21:04:52 main.go:694: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2741.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 21:04:52 smart_order.go:35:    - InitConfig.MaxRetries: 3
2025/06/12 21:04:52 smart_order.go:36:    - InitConfig.RetryInterval: 2s
2025/06/12 21:04:52 smart_order.go:37:    - InitConfig.EnableAsyncInit: true
2025/06/12 21:04:52 main.go:701: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 21:04:52 smart_order.go:38:    - InitConfig.EnableRollback: true
2025/06/12 21:04:52 smart_order.go:57: 🔧 [8441000] SmartOrderManager 创建完成，存储的配置:
2025/06/12 21:04:52 smart_order.go:58:    - som.config.InitConfig.MaxRetries: 3
2025/06/12 21:04:52 smart_order.go:374: 🚀 [8441000] 开始异步初始化...
2025/06/12 21:04:52 smart_order.go:408: 🔵 [8441000] 智能订单管理器开始初始化...
2025/06/12 21:04:52 smart_order.go:177: 🔗 [8441000] 准备连接交易WebSocket: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:04:52 smart_order.go:178: 🔧 [8441000] initTradingWebSocket 使用重试次数: 3
2025/06/12 21:04:52 smart_order.go:67: 🔧 [8441000] 交易WebSocket连接重试配置: maxRetries=3
2025/06/12 21:04:52 smart_order.go:75: 🔄 [8441000] 交易WebSocket连接尝试 1/3
2025/06/12 21:04:52 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/12 21:04:52 data_manager.go:233: 💾 事件数据已保存 (0个事件)
2025/06/12 21:04:53 websocket_manager.go:91: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:04:53 smart_order.go:81: ✅ [8441000] 交易WebSocket连接成功
2025/06/12 21:04:53 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 0
2025/06/12 21:04:53 smart_order.go:103: 🔧 [8441000] authenticateWithRetry 接收参数: maxRetries=3
2025/06/12 21:04:53 smart_order.go:104: 🔧 [8441000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:04:53 smart_order.go:112: 🔐 [8441000] WebSocket会话认证尝试 1/3
2025/06/12 21:04:53 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:53 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:53 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"8441000_login_001","method":"session.logon","params":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","signature":"r626BlAFAmaz7T+Wak2D0XDzpx+UzzhJ/jP00Ga3yksgWVJSluTMDXOxeDnGVB7Nuyo/PYCd4Tcf4vf9m5Q7AA==","timestamp":1749733493418}}
2025/06/12 21:04:53 websocket_manager.go:143: 📏 消息长度: 267 字节
2025/06/12 21:04:53 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:53 smart_order.go:1157: 任务8441000发送WebSocket会话登录请求
2025/06/12 21:04:53 smart_order.go:116: ✅ [8441000] WebSocket会话认证成功
2025/06/12 21:04:53 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 1
2025/06/12 21:04:53 smart_order.go:214: 🔧 [8441000] initListenKey 使用重试次数: 3
2025/06/12 21:04:53 smart_order.go:141: 🔧 [8441000] createListenKeyWithRetry 接收参数: maxRetries=3
2025/06/12 21:04:53 smart_order.go:142: 🔧 [8441000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:04:53 smart_order.go:150: 🔑 [8441000] 创建listenKey尝试 1/3
2025/06/12 21:04:53 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733493421&signature=b1fd49978dde749f2cad4efc02d4b61c5e1db190a6b5a75a72b265ef812a09e6
2025/06/12 21:04:53 binance_client.go:189: 🔍 签名字符串: timestamp=1749733493421
2025/06/12 21:04:53 binance_client.go:190: 🔍 Ed25519签名: tO1YfocotIU23+BZ8aoi79+v5MHmC3Q7bb+wG+zKFccyzsocc/T5hR/s89EPaPYqmow1W4WLZBPYfEGMt254DA==
2025/06/12 21:04:53 websocket_manager.go:225: 📨 WS[trading]: trading_response (8441000_login_001)
2025/06/12 21:04:53 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:8441000_login_001 rateLimits:[map[count:7 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733493970 connectedSince:1749733493775 returnRateLimits:true serverTime:1749733493970] status:200]
2025/06/12 21:04:53 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"8441000_login_001","rateLimits":[{"count":7,"interval":"MINUTE","intervalNum":1,"limit":2400,"rateLimitType":"REQUEST_WEIGHT"}],"result":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","authorizedSince":1749733493970,"connectedSince":1749733493775,"returnRateLimits":true,"serverTime":1749733493970},"status":200}
2025/06/12 21:04:53 binance_client.go:687: ✅ listenKey创建成功: LYStgXWgD33uhgeCKMrFwQuHMcGChAqKacbIboQdw3ogSMhnSTzfN7eXQWBvtD07
2025/06/12 21:04:53 smart_order.go:154: ✅ [8441000] listenKey创建成功: LYStgXWg...
2025/06/12 21:04:53 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 2
2025/06/12 21:04:53 smart_order.go:236: 🔗 [8441000] 准备连接用户数据WebSocket: wss://fstream.binance.com/ws/LYStgXWgD33uhgeCKMrFwQuHMcGChAqKacbIboQdw3ogSMhnSTzfN7eXQWBvtD07
2025/06/12 21:04:53 smart_order.go:67: 🔧 [8441000] 用户数据WebSocket连接重试配置: maxRetries=3
2025/06/12 21:04:53 smart_order.go:75: 🔄 [8441000] 用户数据WebSocket连接尝试 1/3
2025/06/12 21:04:54 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/LYStgXWgD33uhgeCKMrFwQuHMcGChAqKacbIboQdw3ogSMhnSTzfN7eXQWBvtD07
2025/06/12 21:04:54 smart_order.go:81: ✅ [8441000] 用户数据WebSocket连接成功
2025/06/12 21:04:54 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 3
2025/06/12 21:04:54 smart_order.go:434: ⏰ [8441000] 启动listenKey续期协程...
2025/06/12 21:04:54 smart_order.go:437: 🎉 [8441000] 智能订单管理器初始化成功！
2025/06/12 21:04:54 smart_order.go:397: 🎉 [8441000] 异步初始化成功！耗时: 1.165244166s
2025/06/12 21:04:54 hedge_task.go:990: 任务 8441000: 智能订单系统初始化成功
2025/06/12 21:04:54 smart_order.go:1012: 🔄 [8441000] listenKey续期循环启动，续期间隔: 20m0s
2025/06/12 21:04:54 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:04:54 market_data_manager.go:174: 成功创建ETHUSDT市场数据连接: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:04:54 market_data_manager.go:117: 任务8441000成功订阅ETHUSDT市场数据，当前订阅者数量: 1
2025/06/12 21:04:54 hedge_task.go:1011: 🔧 [8441000] handleSmartOrderEvents 开始执行
2025/06/12 21:04:54 hedge_task.go:1030: ✅ [8441000] WebSocket管理器检查通过，获取事件通道
2025/06/12 21:04:54 hedge_task.go:1036: ✅ [8441000] 事件通道获取成功，开始监听事件
2025/06/12 21:04:54 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:54 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[id:8441000_login_001 rateLimits:[map[count:7 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733493970 connectedSince:1749733493775 returnRateLimits:true serverTime:1749733493970] status:200]
2025/06/12 21:04:54 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID 8441000_login_001 不在pendingRequests中，忽略
2025/06/12 21:04:54 hedge_task.go:1169: 🎯 任务 8441000: 触发智能开仓，方向: long
2025/06/12 21:04:54 hedge_task.go:1186: 🚀 任务 8441000: 开始执行极简下单流程
2025/06/12 21:04:54 smart_order.go:521: 🔵 [8441000] 下单: long 0.01 postOnly=true
2025/06/12 21:04:54 smart_order.go:537: 🔒 [下单入口2] [8441000] 设置下单流程标识为 true
2025/06/12 21:04:54 smart_order.go:538: 🔍 [下单入口2] [8441000] 调用栈追踪 - PlaceSmartOrder 被调用
2025/06/12 21:04:54 smart_order.go:888: 📋 [8441000] 获取交易对: ETHUSDT
2025/06/12 21:04:54 smart_order.go:584: 📋 [8441000] 开始转换参数为map...
2025/06/12 21:04:54 smart_order.go:589: 📋 [8441000] 参数转换完成: map[newClientOrderId:8441000_L_494645_002 priceMatch:QUEUE quantity:0.01 side:BUY symbol:ETHUSDT timeInForce:GTX timestamp:1749733494645 type:LIMIT]
2025/06/12 21:04:54 smart_order.go:592: 🔐 [8441000] 使用传入的WebSocket会话认证状态: true
2025/06/12 21:04:54 smart_order.go:596: ✅ [8441000] 使用已认证WebSocket会话下单（无需签名）
2025/06/12 21:04:54 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:54 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:54 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"8441000_place_003","method":"order.place","params":{"newClientOrderId":"8441000_L_494645_002","priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733494645,"type":"LIMIT"}}
2025/06/12 21:04:54 websocket_manager.go:143: 📏 消息长度: 234 字节
2025/06/12 21:04:54 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:54 smart_order.go:676: 任务8441000发送智能下单请求: long 0.01 8441000_L_494645_002
2025/06/12 21:04:54 hedge_task.go:1198: ⏳ 任务 8441000: 等待订单初始化完成...
2025/06/12 21:04:54 smart_order.go:934: ⏰ [超时检查] [8441000] 启动超时检查: RequestID=8441000_place_003, 超时时间=3s
2025/06/12 21:04:54 websocket_manager.go:225: 📨 WS[trading]: trading_response (8441000_place_003)
2025/06/12 21:04:54 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:8441000_place_003 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:1 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:1 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] result:map[avgPrice:0.00 clientOrderId:8441000_L_494645_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903970192943 origQty:0.010 origType:LIMIT positionSide:BOTH price:2742.00 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:BUY status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733495201 workingType:CONTRACT_PRICE] status:200]
2025/06/12 21:04:54 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"8441000_place_003","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":1,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":1,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"result":{"avgPrice":"0.00","clientOrderId":"8441000_L_494645_002","closePosition":false,"cumQty":"0.000","cumQuote":"0.00000","executedQty":"0.000","goodTillDate":0,"orderId":"8389765903970192943","origQty":"0.010","origType":"LIMIT","positionSide":"BOTH","price":"2742.00","priceMatch":"QUEUE","priceProtect":false,"reduceOnly":false,"selfTradePreventionMode":"EXPIRE_MAKER","side":"BUY","status":"NEW","stopPrice":"0.00","symbol":"ETHUSDT","timeInForce":"GTX","type":"LIMIT","updateTime":1749733495201,"workingType":"CONTRACT_PRICE"},"status":200}
2025/06/12 21:04:54 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:54 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[id:8441000_place_003 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:1 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:1 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] result:map[avgPrice:0.00 clientOrderId:8441000_L_494645_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903970192943 origQty:0.010 origType:LIMIT positionSide:BOTH price:2742.00 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:BUY status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733495201 workingType:CONTRACT_PRICE] status:200]
2025/06/12 21:04:54 hedge_task.go:2003: ✅ [processTradingResponse] 任务 8441000: 调用 handleSuccessfulTradingResponse
2025/06/12 21:04:54 hedge_task.go:2019: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: 开始处理成功响应
2025/06/12 21:04:54 hedge_task.go:2020: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: 请求类型: place_order
2025/06/12 21:04:54 hedge_task.go:2028: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: result数据: map[avgPrice:0.00 clientOrderId:8441000_L_494645_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903970192943 origQty:0.010 origType:LIMIT positionSide:BOTH price:2742.00 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:BUY status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733495201 workingType:CONTRACT_PRICE]
2025/06/12 21:04:54 hedge_task.go:2032: 🎯 [handleSuccessfulTradingResponse] 任务 8441000: 调用 handleOrderPlaceResponse
2025/06/12 21:04:54 hedge_task.go:2097: 🔍 [handleOrderPlaceResponse] 客户端订单ID: 8441000_L_494645_002
2025/06/12 21:04:54 hedge_task.go:1713: 🎯 任务 8441000: 尝试通过 trading_response 初始化订单跟踪
2025/06/12 21:04:54 hedge_task.go:1738: 🚀 任务 8441000: 订单初始化完成 - 订单ID: 8389765903970192943, 价格: 2742
2025/06/12 21:04:54 hedge_task.go:1750: 🚀 任务 8441000: 启动三并发监控机制
2025/06/12 21:04:54 hedge_task.go:2136: 🎯 任务 8441000: 通过 trading_response 完成订单初始化
2025/06/12 21:04:54 hedge_task.go:1284: 🔍 任务 8441000: 启动用户数据流监控，订单ID: 8441000_L_494645_002
2025/06/12 21:04:54 hedge_task.go:1381: 🔍 任务 8441000: 启动参数化修改循环，间隔: 30ms, 最大次数: 500, 排名: 1
2025/06/12 21:04:54 hedge_task.go:1343: 🔍 任务 8441000: 启动REST API轮询监控，间隔: 1s
2025/06/12 21:04:54 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903970192943&timestamp=1749733494831
2025/06/12 21:04:54 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:54 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:54 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733494831","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733494831}}
2025/06/12 21:04:54 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:54 binance_client.go:808: 🔍 Ed25519签名: IDhpBlZiABjX0+v0Hp9+ftU6mHoRKotfy4WE92o13S47Conhk+ZcGBPmKETfgUYv8A53KamE2q0SP9TZEHhMAQ==
2025/06/12 21:04:54 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:54 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:54 websocket_manager.go:223: 📨 WS[userdata]: ORDER_TRADE_UPDATE
2025/06/12 21:04:54 hedge_task.go:2070: 🔍 [用户数据] 任务 8441000: 收到用户数据事件，开始处理
2025/06/12 21:04:54 hedge_task.go:2071: 🔍 [用户数据] 任务 8441000: 完整事件数据: map[E:1749733495201 T:1749733495201 e:ORDER_TRADE_UPDATE o:map[L:0 N:USDT R:false S:BUY T:1749733495201 V:EXPIRE_MAKER X:NEW a:0 ap:0 b:75.44741 c:8441000_L_494645_002 cp:false f:GTX gtd:0 i:8389765903970192943 l:0 m:false n:0 o:LIMIT ot:LIMIT p:2742 pP:false pm:QUEUE ps:BOTH q:0.01 rp:0 s:ETHUSDT si:0 sp:0 ss:0 t:0 wt:CONTRACT_PRICE x:NEW z:0]]
2025/06/12 21:04:54 hedge_task.go:2079: 📋 [用户数据] 任务 8441000: 用户数据事件类型: ORDER_TRADE_UPDATE
2025/06/12 21:04:54 hedge_task.go:2083: 🎯 [用户数据] 任务 8441000: 开始处理 ORDER_TRADE_UPDATE 事件
2025/06/12 21:04:54 hedge_task.go:2233: 任务 8441000: ===== 开始处理 ORDER_TRADE_UPDATE 事件 =====
2025/06/12 21:04:54 hedge_task.go:2248: 任务 8441000: 收到 ORDER_TRADE_UPDATE 事件，客户端订单ID: 8441000_L_494645_002
2025/06/12 21:04:54 hedge_task.go:2257: ✅ [消息路由] 任务 8441000: 确认为本任务订单，继续处理
2025/06/12 21:04:54 hedge_task.go:2278: 任务 8441000: 订单状态: NEW
2025/06/12 21:04:54 hedge_task.go:2320: 任务 8441000: ===== ORDER_TRADE_UPDATE 事件处理完成 =====
2025/06/12 21:04:54 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:54 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:54 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733494863","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733494863}}
2025/06/12 21:04:54 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:54 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:54 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:54 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:54 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:54 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733494895","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733494895}}
2025/06/12 21:04:54 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:54 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:54 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:54 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:54 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:54 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733494926","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733494926}}
2025/06/12 21:04:54 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:54 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:54 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:54 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:54 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:54 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733494957","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733494957}}
2025/06/12 21:04:54 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:54 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:54 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:54 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:54 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:54 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733494989","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733494989}}
2025/06/12 21:04:54 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:54 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:54 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 hedge_task.go:1510: 🔍 任务 8441000: REST API查询成功 - 订单ID: 8389765903970192943, 状态: NEW
2025/06/12 21:04:55 hedge_task.go:1358: 🔍 任务 8441000: REST API查询成功，订单状态: NEW
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495019","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495019}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733494831)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494831 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:2 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:2 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733494831","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":2,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":2,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494831 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:2 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:2 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733494831 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733494863)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494863 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:3 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:3 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733494863","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":3,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":3,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494863 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:3 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:3 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733494863 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495050","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495050}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733494895)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494895 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:4 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:4 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733494895","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":4,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":4,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494895 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:4 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:4 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733494895 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495081","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495081}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733494926)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494926 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:5 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:5 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733494926","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":5,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":5,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494926 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:5 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:5 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733494926 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495111","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495111}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733494957)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494957 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:6 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:6 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733494957","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":6,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":6,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494957 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:6 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:6 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733494957 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495142","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495142}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733494989)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494989 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:7 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:7 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733494989","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":7,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":7,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733494989 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:7 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:7 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733494989 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495173","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495173}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495019)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495019 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:8 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:8 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495019","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":8,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":8,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495019 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:8 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:8 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495019 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495203","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495203}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495050)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495050 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:9 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:9 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495050","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":9,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":9,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495050 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:9 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:9 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495050 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495234","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495234}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495265","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495265}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495081)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495081 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:10 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:10 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495081","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":10,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":10,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495081 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:10 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:10 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495081 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495111)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495111 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:11 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:11 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495111","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":11,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":11,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495111 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:11 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:11 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495111 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495296","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495296}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495142)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495142 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:12 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:12 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495142","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":12,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":12,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495142 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:12 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:12 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495142 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495326","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495326}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495173)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495173 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:13 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:13 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495173","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":13,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":13,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495173 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:13 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:13 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495173 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495357","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495357}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495388","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495388}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495203)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495203 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:14 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:14 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495203","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":14,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":14,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495203 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:14 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:14 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495203 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495234)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495234 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:15 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:15 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495234","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":15,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":15,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495234 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:15 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:15 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495234 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495419","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495419}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495265)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495265 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:16 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:16 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495265","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":16,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":16,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495265 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:16 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:16 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495265 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495449","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495449}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495296)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495296 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:17 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:17 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495296","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":17,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":17,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495296 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:17 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:17 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495296 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495480","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495480}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495326)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495326 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:18 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:18 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495326","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":18,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":18,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495326 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:18 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:18 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495326 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495511","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495511}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495542","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495542}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495357)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495357 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:19 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:19 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495357","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":19,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":19,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495357 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:19 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:19 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495357 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495575","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495575}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495388)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495388 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:20 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:20 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495388","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":20,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":20,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495388 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:20 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:20 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495388 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495419)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495419 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:21 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:21 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495419","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":21,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":21,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495419 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:21 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:21 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495419 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495606","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495606}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495449)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495449 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:22 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:22 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495449","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":22,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":22,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495449 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:22 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:22 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495449 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495637","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495637}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495480)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495480 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:23 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:23 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495480","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":23,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":23,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495480 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:23 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:23 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495480 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495668","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495668}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495700","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495700}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495511)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495511 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:24 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:24 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495511","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":24,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":24,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495511 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:24 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:24 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495511 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495542)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495542 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:25 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:25 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495542","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":25,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":25,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495542 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:25 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:25 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495542 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495730","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495730}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495575)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495575 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:26 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:26 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495575","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":26,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":26,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495575 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:26 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:26 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495575 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495763","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495763}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495606)
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495797","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495797}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495606 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:27 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:27 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495606","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":27,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":27,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495606 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:27 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:27 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495606 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495637)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495637 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:28 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:28 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495637","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":28,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":28,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495637 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:28 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:28 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495637 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495828","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495828}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495668)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495668 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:29 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:29 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495668","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":29,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":29,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495668 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:29 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:29 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495668 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495859","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495859}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495700)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495700 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:30 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:30 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495700","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":30,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":30,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495700 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:30 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:30 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495700 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495890","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495890}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495730)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495730 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:31 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:31 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495730","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":31,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":31,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495730 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:31 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:31 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495730 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495921","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495921}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495763)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495763 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:32 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:32 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495763","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":32,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":32,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495763 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:32 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:32 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495763 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495952","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495952}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:55 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495797)
2025/06/12 21:04:55 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495797 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:33 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:33 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495797","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":33,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":33,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:55 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:55 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495797 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:33 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:33 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:55 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495797 不在pendingRequests中，忽略
2025/06/12 21:04:55 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:55 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:55 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733495983","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733495983}}
2025/06/12 21:04:55 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:55 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:55 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495828)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495828 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:34 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:34 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733495828","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":34,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":34,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733495828 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:34 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:34 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495828 不在pendingRequests中，忽略
2025/06/12 21:04:56 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:56 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:56 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733496014","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733496014}}
2025/06/12 21:04:56 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:56 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:56 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:56 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903970192943&timestamp=*************
2025/06/12 21:04:56 binance_client.go:808: 🔍 Ed25519签名: uS7rwYeWugewWNq0WIFvKXlTE72mDy0bftIJFOhlTCZ1QBbnctWS5PWRyPVeKEgM5jjqZBAbONaE0SjEPFahCg==
2025/06/12 21:04:56 websocket_manager.go:223: 📨 WS[userdata]: ACCOUNT_UPDATE
2025/06/12 21:04:56 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:56 hedge_task.go:2070: 🔍 [用户数据] 任务 8441000: 收到用户数据事件，开始处理
2025/06/12 21:04:56 hedge_task.go:2071: 🔍 [用户数据] 任务 8441000: 完整事件数据: map[E:************* T:************* a:map[B:[map[a:USDT bc:0 cw:104.******** wb:104.********]] P:[map[bep:2750.316075 cr:46.******** ep:2749.1475 iw:0 ma:USDT mt:cross pa:0.02 ps:BOTH s:ETHUSDT up:-0.********]] m:ORDER] e:ACCOUNT_UPDATE]
2025/06/12 21:04:56 hedge_task.go:2079: 📋 [用户数据] 任务 8441000: 用户数据事件类型: ACCOUNT_UPDATE
2025/06/12 21:04:56 hedge_task.go:2086: 💰 [用户数据] 任务 8441000: 开始处理 ACCOUNT_UPDATE 事件
2025/06/12 21:04:56 hedge_task.go:2326: 任务 8441000: 收到账户更新事件
2025/06/12 21:04:56 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:56 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_*************","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":*************}}
2025/06/12 21:04:56 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:56 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:56 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:56 websocket_manager.go:223: 📨 WS[userdata]: ORDER_TRADE_UPDATE
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495859)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495859 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:35 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:35 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733495859","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":35,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":35,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495859 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:35 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:35 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495859 不在pendingRequests中，忽略
2025/06/12 21:04:56 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:56 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:56 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733496075","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733496075}}
2025/06/12 21:04:56 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:56 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:56 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495890)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495890 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:36 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:36 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733495890","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":36,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":36,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495890 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:36 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:36 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495890 不在pendingRequests中，忽略
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495921)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495921 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:37 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:37 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733495921","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":37,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":37,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495921 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:37 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:37 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495921 不在pendingRequests中，忽略
2025/06/12 21:04:56 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:56 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:56 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733496105","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733496105}}
2025/06/12 21:04:56 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:56 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:56 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495952)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495952 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:38 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:38 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733495952","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":38,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":38,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495952 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:38 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:38 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495952 不在pendingRequests中，忽略
2025/06/12 21:04:56 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:56 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:56 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733496136","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733496136}}
2025/06/12 21:04:56 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:56 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:56 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733495983)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495983 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:39 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:39 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733495983","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":39,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":39,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733495983 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:39 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:39 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733495983 不在pendingRequests中，忽略
2025/06/12 21:04:56 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:04:56 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:04:56 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733496166","method":"order.modify","params":{"orderId":8389765903970192943,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733496166}}
2025/06/12 21:04:56 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:04:56 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:04:56 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:04:56 hedge_task.go:1510: 🔍 任务 8441000: REST API查询成功 - 订单ID: 8389765903970192943, 状态: FILLED
2025/06/12 21:04:56 hedge_task.go:1358: 🔍 任务 8441000: REST API查询成功，订单状态: FILLED
2025/06/12 21:04:56 hedge_task.go:1360: 🎯 任务 8441000: REST API检测到订单成交
2025/06/12 21:04:56 hedge_task.go:1771: 🎯 任务 8441000: REST API成交首次触发任务完成
2025/06/12 21:04:56 hedge_task.go:1788: 🔍 任务 8441000: REST API成交，查询完整订单数据 - 订单ID: 8389765903970192943
2025/06/12 21:04:56 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903970192943&timestamp=1749733496194
2025/06/12 21:04:56 binance_client.go:808: 🔍 Ed25519签名: MGqveO3ElQSjGRIAt1pAnsLa5ase6bx0vFTBf4XyVLY+l9K3UXYp+aDohb55MwKIuXSxsrkZmEuhg8txMJygAA==
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733496014)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496014 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:40 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:40 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733496014","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":40,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":40,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496014 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:40 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:40 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733496014 不在pendingRequests中，忽略
2025/06/12 21:04:56 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_*************)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_************* rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:41 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:41 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_*************","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":41,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":41,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_************* rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:41 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:41 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_************* 不在pendingRequests中，忽略
2025/06/12 21:04:56 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733496075)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496075 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:42 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:42 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733496075","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":42,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":42,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496075 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:42 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:42 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733496075 不在pendingRequests中，忽略
2025/06/12 21:04:56 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733496105)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496105 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:43 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:43 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733496105","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":43,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":43,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496105 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:43 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:43 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733496105 不在pendingRequests中，忽略
2025/06/12 21:04:56 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:04:56 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733496136)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496136 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:44 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:44 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733496136","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":44,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":44,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496136 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:44 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:44 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733496136 不在pendingRequests中，忽略
2025/06/12 21:04:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733496166)
2025/06/12 21:04:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496166 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:45 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:45 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733496166","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":45,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":45,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:04:56 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:04:56 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733496166 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:45 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:45 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:04:56 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733496166 不在pendingRequests中，忽略
2025/06/12 21:04:56 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:04:56 hedge_task.go:1599: 🔍 任务 8441000: REST API查询完整订单数据成功 - 订单ID: 8389765903970192943, 平均价格: 2742.00000, 成交数量: 0.010
2025/06/12 21:04:56 hedge_task.go:2537: 🔍 [调试] 任务 8441000: processOrderFilled 接收到的完整数据: map[avgPrice:2742.00000 executedQty:0.010 m:false n:0 orderId:8.389765903970193e+18]
2025/06/12 21:04:56 hedge_task.go:2545: 🔍 [调试] 任务 8441000: 尝试解析成交价格 - actualPriceStr: '2742.00000', ok: false
2025/06/12 21:04:56 hedge_task.go:2566: 🔍 [调试] 任务 8441000: 尝试解析订单ID - 查找字段 'i'
2025/06/12 21:04:56 hedge_task.go:2569: 🔍 [调试] 任务 8441000: 字段 'i' 解析失败: 订单ID字段 'i' 不存在, 尝试备用字段 'orderId'
2025/06/12 21:04:56 hedge_task.go:2577: 🔍 [调试] 任务 8441000: 成功解析订单ID: 8389765903970193408
2025/06/12 21:04:56 hedge_task.go:2635: 🧹 [状态清理] 任务 8441000: 开始清空 orderTracker（订单成交完成）
2025/06/12 21:04:56 hedge_task.go:2636: 🧹 [状态清理] 任务 8441000: 清空前状态检查:
2025/06/12 21:04:56 hedge_task.go:2638: 🧹 [状态清理] 任务 8441000:   - orderTracker.ClientOrderID: 8441000_L_494645_002
2025/06/12 21:04:56 hedge_task.go:2639: 🧹 [状态清理] 任务 8441000:   - orderTracker.State: completed
2025/06/12 21:04:56 hedge_task.go:2640: 🧹 [状态清理] 任务 8441000:   - orderTracker.OrderID: 8389765903970192943
2025/06/12 21:04:56 hedge_task.go:2644: 🧹 [状态清理] 任务 8441000:   - isInOrderProcess: true
2025/06/12 21:04:56 hedge_task.go:2650: 🧹 [状态清理] 任务 8441000: orderTracker 已清空
2025/06/12 21:04:56 hedge_task.go:2651: 🧹 [状态清理] 任务 8441000: isInOrderProcess 已设置为 false
2025/06/12 21:04:56 hedge_task.go:2652: 🧹 [状态清理] 任务 8441000: 状态清理完成，任务可以接受新的下单请求
2025/06/12 21:04:56 事件类型: open | 订单类型: limit | 成交方式: taker | 理论价: 2741.5482 | 实际价: 2742 | 损耗: 0.4518 | 手续费: 0 | 订单ID: 8389765903970193408
2025/06/12 21:04:56 hedge_task.go:2668: 🎉 任务 8441000: 智能订单成交完成，实际价格: 2742, 理论价格: 2741.5482, 订单ID: 8389765903970193408
2025/06/12 21:04:56 hedge_task.go:2670: 🔍 [调试] 任务 8441000: 订单成交详情 - 成交价格: 2742, 成交数量: 0.010, 手续费: 0, 成交方式: taker
2025/06/12 21:04:56 hedge_task.go:1834: 🎯 任务 8441000: 任务完成处理结束 - 原因: REST API成交
2025/06/12 21:04:56 hedge_task.go:1299: 🔍 任务 8441000: 用户数据流监控收到停止信号
2025/06/12 21:04:56 hedge_task.go:1387: 🔍 任务 8441000: 修改循环收到停止信号
2025/06/12 21:04:57 smart_order.go:943: ✅ [超时检查] [8441000] 请求8441000_place_003已正常处理，无需超时处理
2025/06/12 21:05:05 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:05 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:213: ETHUSDT市场数据连接断开，尝试重连
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:06 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:07 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:08 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:09 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:10 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:11 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:12 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:05:17 main.go:760: 
2025/06/12 21:05:17 main.go:861: 8441000 运行   L    2741 0.020% 0.010  L0.01@2742            1  21:04   0m   0.00     0.45   0.00 是   manual   -                     
2025/06/12 21:05:17 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 main.go:227: 收到停止信号，正在停止所有任务...
2025/06/12 21:05:21 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 21:05:21 manager.go:150: 停止对冲任务: 8441000
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 main.go:239: 💾 保存数据...
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:24 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:05:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:05:36 main.go:185: 启动API服务模式（包含交互功能），端口: 5899
2025/06/12 21:05:36 main.go:186: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 21:05:36 main.go:202: API服务器已启动，访问地址: http://localhost:5899
2025/06/12 21:05:36 main.go:203: API接口:
2025/06/12 21:05:36 main.go:204:   POST /task                           - 创建对冲任务
2025/06/12 21:05:36 main.go:205:   POST /task/stop                      - 停止对冲任务
2025/06/12 21:05:36 main.go:206:   GET  /tasks                          - 获取所有任务
2025/06/12 21:05:36 main.go:207:   GET  /task/{id}                      - 获取单个任务
2025/06/12 21:05:36 main.go:208: 期权系统API接口:
2025/06/12 21:05:36 main.go:209:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 21:05:36 main.go:210:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 21:05:36 main.go:211:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 21:05:36 api.go:35: API服务器启动，端口: 5899
2025/06/12 21:05:36 main.go:402: 
2025/06/12 21:05:36 main.go:403: 🔧 === 交互命令帮助 ===
2025/06/12 21:05:36 main.go:405:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 21:05:36 main.go:406:       示例: add long 3500 0.0005
2025/06/12 21:05:36 main.go:407:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 21:05:36 main.go:408: 
2025/06/12 21:05:36 main.go:409:    stop <task_id>                           - 停止指定任务
2025/06/12 21:05:36 main.go:410:    list                                     - 列出所有任务
2025/06/12 21:05:36 main.go:411: 
2025/06/12 21:05:36 main.go:412: ⚙️ 参数配置:
2025/06/12 21:05:36 main.go:413:    set <参数名> <值>                         - 设置默认参数
2025/06/12 21:05:36 main.go:414:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 21:05:36 main.go:415:    show defaults                            - 显示当前默认参数
2025/06/12 21:05:36 main.go:416: 
2025/06/12 21:05:36 main.go:417: 📊 状态控制:
2025/06/12 21:05:36 main.go:418:    status                                   - 查看状态输出设置
2025/06/12 21:05:36 main.go:419:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 21:05:36 main.go:420:    status off                               - 停止状态输出
2025/06/12 21:05:36 main.go:421: 
2025/06/12 21:05:36 main.go:422: 📋 数据导出:
2025/06/12 21:05:36 main.go:423:    export | detail                          - 导出详细状态到剪切板
2025/06/12 21:05:36 main.go:424: 
2025/06/12 21:05:36 main.go:425: 💾 数据管理:
2025/06/12 21:05:36 main.go:426:    save                                     - 手动保存数据到本地文件
2025/06/12 21:05:36 main.go:427:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 21:05:36 main.go:428:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 21:05:36 main.go:429: 
2025/06/12 21:05:36 main.go:430: 📋 事件查看:
2025/06/12 21:05:36 main.go:431:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 21:05:36 main.go:432: 
2025/06/12 21:05:36 main.go:433: 🎛️  批量控制:
2025/06/12 21:05:36 main.go:434:    startall                                 - 启动所有已停止的任务
2025/06/12 21:05:36 main.go:435:    stopall                                  - 停止所有运行中的任务
2025/06/12 21:05:36 main.go:436:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 21:05:36 main.go:437: 
2025/06/12 21:05:36 main.go:438: 🗑️  任务管理:
2025/06/12 21:05:36 main.go:439:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 21:05:36 main.go:440: 
2025/06/12 21:05:36 main.go:441: 🔧 其他:
2025/06/12 21:05:36 main.go:442:    help                                     - 显示此帮助信息
2025/06/12 21:05:36 main.go:443:    quit | exit                              - 退出程序
2025/06/12 21:05:36 main.go:444: ========================
2025/06/12 21:05:36 main.go:445: 
2025/06/12 21:05:45 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:05:45 main.go:760: 
2025/06/12 21:05:45 main.go:861: 8441000 停止   L    2741 0.020% 0.010  L0.01@2742            1  21:04   1m   0.00     0.45   0.00 是   manual   -                     
2025/06/12 21:05:45 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:05:56 状态变化: stopped -> running | 原因: 批量启动
2025/06/12 21:05:56 hedge_task.go:109: 启动对冲任务: 8441000
2025/06/12 21:05:56 hedge_task.go:977: 🔧 [8441000] 使用现有配置，MaxRetries: 3
2025/06/12 21:05:56 hedge_task.go:981: 🔧 [8441000] 调用NewSmartOrderManager，传递配置MaxRetries: 3
2025/06/12 21:05:56 smart_order.go:34: 🔧 [8441000] NewSmartOrderManager 接收到的配置:
2025/06/12 21:05:56 smart_order.go:35:    - InitConfig.MaxRetries: 3
2025/06/12 21:05:56 smart_order.go:36:    - InitConfig.RetryInterval: 2s
2025/06/12 21:05:56 smart_order.go:37:    - InitConfig.EnableAsyncInit: true
2025/06/12 21:05:56 smart_order.go:38:    - InitConfig.EnableRollback: true
2025/06/12 21:05:56 smart_order.go:57: 🔧 [8441000] SmartOrderManager 创建完成，存储的配置:
2025/06/12 21:05:56 smart_order.go:58:    - som.config.InitConfig.MaxRetries: 3
2025/06/12 21:05:56 smart_order.go:374: 🚀 [8441000] 开始异步初始化...
2025/06/12 21:05:56 smart_order.go:408: 🔵 [8441000] 智能订单管理器开始初始化...
2025/06/12 21:05:56 smart_order.go:177: 🔗 [8441000] 准备连接交易WebSocket: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:05:56 smart_order.go:178: 🔧 [8441000] initTradingWebSocket 使用重试次数: 3
2025/06/12 21:05:56 smart_order.go:67: 🔧 [8441000] 交易WebSocket连接重试配置: maxRetries=3
2025/06/12 21:05:56 smart_order.go:75: 🔄 [8441000] 交易WebSocket连接尝试 1/3
2025/06/12 21:05:56 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/12 21:05:56 data_manager.go:233: 💾 事件数据已保存 (1个事件)
2025/06/12 21:05:56 websocket_manager.go:91: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:05:56 smart_order.go:81: ✅ [8441000] 交易WebSocket连接成功
2025/06/12 21:05:56 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 0
2025/06/12 21:05:56 smart_order.go:103: 🔧 [8441000] authenticateWithRetry 接收参数: maxRetries=3
2025/06/12 21:05:56 smart_order.go:104: 🔧 [8441000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:05:56 smart_order.go:112: 🔐 [8441000] WebSocket会话认证尝试 1/3
2025/06/12 21:05:56 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:05:56 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:05:56 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"8441000_login_001","method":"session.logon","params":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","signature":"gu8UbpHac1YsR3nBASAZwq1BBLG99bnXKX1rJphZ1p4pkEmJFl6jgrKKPNm/Xlwlk1l+gekafcTdHgdw9FWZAw==","timestamp":1749733556642}}
2025/06/12 21:05:56 websocket_manager.go:143: 📏 消息长度: 267 字节
2025/06/12 21:05:56 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:05:56 smart_order.go:1157: 任务8441000发送WebSocket会话登录请求
2025/06/12 21:05:56 smart_order.go:116: ✅ [8441000] WebSocket会话认证成功
2025/06/12 21:05:56 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 1
2025/06/12 21:05:56 smart_order.go:214: 🔧 [8441000] initListenKey 使用重试次数: 3
2025/06/12 21:05:56 smart_order.go:141: 🔧 [8441000] createListenKeyWithRetry 接收参数: maxRetries=3
2025/06/12 21:05:56 smart_order.go:142: 🔧 [8441000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:05:56 smart_order.go:150: 🔑 [8441000] 创建listenKey尝试 1/3
2025/06/12 21:05:56 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733556643&signature=4029f4a9157581ce59bccb915f9784b318d6fbe2a5e1c8b0e6e8ceca12f11179
2025/06/12 21:05:56 binance_client.go:189: 🔍 签名字符串: timestamp=1749733556643
2025/06/12 21:05:56 binance_client.go:190: 🔍 Ed25519签名: IxAdz7Jntvok6hG6OzxL4A1pGUQugqGCGHSqLEAB60ZlaXXuCBHXRFlX7UuOEe927cmVFyUUdtC+sEiYHRmFBw==
2025/06/12 21:05:56 websocket_manager.go:225: 📨 WS[trading]: trading_response (8441000_login_001)
2025/06/12 21:05:56 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:8441000_login_001 rateLimits:[map[count:7 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733557197 connectedSince:1749733557012 returnRateLimits:true serverTime:1749733557197] status:200]
2025/06/12 21:05:56 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"8441000_login_001","rateLimits":[{"count":7,"interval":"MINUTE","intervalNum":1,"limit":2400,"rateLimitType":"REQUEST_WEIGHT"}],"result":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","authorizedSince":1749733557197,"connectedSince":1749733557012,"returnRateLimits":true,"serverTime":1749733557197},"status":200}
2025/06/12 21:05:56 binance_client.go:687: ✅ listenKey创建成功: DuYoVasSAoHVV6SBaqXtcBSL1qqfDpb6BKchCzMB272tgH8JUyPYoE3TyaSozlqU
2025/06/12 21:05:56 smart_order.go:154: ✅ [8441000] listenKey创建成功: DuYoVasS...
2025/06/12 21:05:56 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 2
2025/06/12 21:05:56 smart_order.go:236: 🔗 [8441000] 准备连接用户数据WebSocket: wss://fstream.binance.com/ws/DuYoVasSAoHVV6SBaqXtcBSL1qqfDpb6BKchCzMB272tgH8JUyPYoE3TyaSozlqU
2025/06/12 21:05:56 smart_order.go:67: 🔧 [8441000] 用户数据WebSocket连接重试配置: maxRetries=3
2025/06/12 21:05:56 smart_order.go:75: 🔄 [8441000] 用户数据WebSocket连接尝试 1/3
2025/06/12 21:05:57 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/DuYoVasSAoHVV6SBaqXtcBSL1qqfDpb6BKchCzMB272tgH8JUyPYoE3TyaSozlqU
2025/06/12 21:05:57 smart_order.go:81: ✅ [8441000] 用户数据WebSocket连接成功
2025/06/12 21:05:57 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 3
2025/06/12 21:05:57 smart_order.go:434: ⏰ [8441000] 启动listenKey续期协程...
2025/06/12 21:05:57 smart_order.go:437: 🎉 [8441000] 智能订单管理器初始化成功！
2025/06/12 21:05:57 smart_order.go:397: 🎉 [8441000] 异步初始化成功！耗时: 1.189315042s
2025/06/12 21:05:57 hedge_task.go:990: 任务 8441000: 智能订单系统初始化成功
2025/06/12 21:05:57 smart_order.go:1012: 🔄 [8441000] listenKey续期循环启动，续期间隔: 20m0s
2025/06/12 21:05:57 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:05:57 market_data_manager.go:174: 成功创建ETHUSDT市场数据连接: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:05:57 market_data_manager.go:117: 任务8441000成功订阅ETHUSDT市场数据，当前订阅者数量: 1
2025/06/12 21:05:57 hedge_task.go:1011: 🔧 [8441000] handleSmartOrderEvents 开始执行
2025/06/12 21:05:57 hedge_task.go:1030: ✅ [8441000] WebSocket管理器检查通过，获取事件通道
2025/06/12 21:05:57 hedge_task.go:1036: ✅ [8441000] 事件通道获取成功，开始监听事件
2025/06/12 21:05:57 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:05:57 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[id:8441000_login_001 rateLimits:[map[count:7 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733557197 connectedSince:1749733557012 returnRateLimits:true serverTime:1749733557197] status:200]
2025/06/12 21:05:57 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID 8441000_login_001 不在pendingRequests中，忽略
2025/06/12 21:06:00 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:06:00 main.go:760: 
2025/06/12 21:06:00 main.go:861: 8441000 运行   L    2741 0.020% 0.010  L0.01@2742            1  21:04   1m   0.00     0.45   0.00 是   manual   -                     
2025/06/12 21:06:00 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:06:02 hedge_task.go:1177: 🎯 任务 8441000: 触发智能平仓，方向: short
2025/06/12 21:06:02 hedge_task.go:1186: 🚀 任务 8441000: 开始执行极简下单流程
2025/06/12 21:06:02 smart_order.go:521: 🔵 [8441000] 下单: short 0.01 postOnly=true
2025/06/12 21:06:02 smart_order.go:537: 🔒 [下单入口2] [8441000] 设置下单流程标识为 true
2025/06/12 21:06:02 smart_order.go:538: 🔍 [下单入口2] [8441000] 调用栈追踪 - PlaceSmartOrder 被调用
2025/06/12 21:06:02 smart_order.go:888: 📋 [8441000] 获取交易对: ETHUSDT
2025/06/12 21:06:02 smart_order.go:584: 📋 [8441000] 开始转换参数为map...
2025/06/12 21:06:02 smart_order.go:589: 📋 [8441000] 参数转换完成: map[newClientOrderId:8441000_S_562222_002 priceMatch:QUEUE quantity:0.01 side:SELL symbol:ETHUSDT timeInForce:GTX timestamp:1749733562222 type:LIMIT]
2025/06/12 21:06:02 smart_order.go:592: 🔐 [8441000] 使用传入的WebSocket会话认证状态: true
2025/06/12 21:06:02 smart_order.go:596: ✅ [8441000] 使用已认证WebSocket会话下单（无需签名）
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"8441000_place_003","method":"order.place","params":{"newClientOrderId":"8441000_S_562222_002","priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562222,"type":"LIMIT"}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 235 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 smart_order.go:676: 任务8441000发送智能下单请求: short 0.01 8441000_S_562222_002
2025/06/12 21:06:02 hedge_task.go:1198: ⏳ 任务 8441000: 等待订单初始化完成...
2025/06/12 21:06:02 smart_order.go:934: ⏰ [超时检查] [8441000] 启动超时检查: RequestID=8441000_place_003, 超时时间=3s
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (8441000_place_003)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:8441000_place_003 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:1 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:1 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] result:map[avgPrice:0.00 clientOrderId:8441000_S_562222_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903970796296 origQty:0.010 origType:LIMIT positionSide:BOTH price:2739.94 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:SELL status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733562788 workingType:CONTRACT_PRICE] status:200]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"8441000_place_003","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":1,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":1,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"result":{"avgPrice":"0.00","clientOrderId":"8441000_S_562222_002","closePosition":false,"cumQty":"0.000","cumQuote":"0.00000","executedQty":"0.000","goodTillDate":0,"orderId":"8389765903970796296","origQty":"0.010","origType":"LIMIT","positionSide":"BOTH","price":"2739.94","priceMatch":"QUEUE","priceProtect":false,"reduceOnly":false,"selfTradePreventionMode":"EXPIRE_MAKER","side":"SELL","status":"NEW","stopPrice":"0.00","symbol":"ETHUSDT","timeInForce":"GTX","type":"LIMIT","updateTime":1749733562788,"workingType":"CONTRACT_PRICE"},"status":200}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 websocket_manager.go:223: 📨 WS[userdata]: ORDER_TRADE_UPDATE
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[id:8441000_place_003 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:1 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:1 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] result:map[avgPrice:0.00 clientOrderId:8441000_S_562222_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903970796296 origQty:0.010 origType:LIMIT positionSide:BOTH price:2739.94 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:SELL status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733562788 workingType:CONTRACT_PRICE] status:200]
2025/06/12 21:06:02 hedge_task.go:2003: ✅ [processTradingResponse] 任务 8441000: 调用 handleSuccessfulTradingResponse
2025/06/12 21:06:02 hedge_task.go:2019: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: 开始处理成功响应
2025/06/12 21:06:02 hedge_task.go:2020: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: 请求类型: place_order
2025/06/12 21:06:02 hedge_task.go:2028: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: result数据: map[avgPrice:0.00 clientOrderId:8441000_S_562222_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903970796296 origQty:0.010 origType:LIMIT positionSide:BOTH price:2739.94 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:SELL status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733562788 workingType:CONTRACT_PRICE]
2025/06/12 21:06:02 hedge_task.go:2032: 🎯 [handleSuccessfulTradingResponse] 任务 8441000: 调用 handleOrderPlaceResponse
2025/06/12 21:06:02 hedge_task.go:2097: 🔍 [handleOrderPlaceResponse] 客户端订单ID: 8441000_S_562222_002
2025/06/12 21:06:02 hedge_task.go:1713: 🎯 任务 8441000: 尝试通过 trading_response 初始化订单跟踪
2025/06/12 21:06:02 hedge_task.go:1738: 🚀 任务 8441000: 订单初始化完成 - 订单ID: 8389765903970796296, 价格: 2739.94
2025/06/12 21:06:02 hedge_task.go:1750: 🚀 任务 8441000: 启动三并发监控机制
2025/06/12 21:06:02 hedge_task.go:2136: 🎯 任务 8441000: 通过 trading_response 完成订单初始化
2025/06/12 21:06:02 hedge_task.go:2070: 🔍 [用户数据] 任务 8441000: 收到用户数据事件，开始处理
2025/06/12 21:06:02 hedge_task.go:1343: 🔍 任务 8441000: 启动REST API轮询监控，间隔: 1s
2025/06/12 21:06:02 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903970796296&timestamp=1749733562419
2025/06/12 21:06:02 hedge_task.go:2071: 🔍 [用户数据] 任务 8441000: 完整事件数据: map[E:1749733562788 T:1749733562788 e:ORDER_TRADE_UPDATE o:map[L:0 N:USDT R:false S:SELL T:1749733562788 V:EXPIRE_MAKER X:NEW a:27.42679 ap:0 b:72 c:8441000_S_562222_002 cp:false f:GTX gtd:0 i:8389765903970796296 l:0 m:false n:0 o:LIMIT ot:LIMIT p:2739.94 pP:false pm:QUEUE ps:BOTH q:0.01 rp:0 s:ETHUSDT si:0 sp:0 ss:0 t:0 wt:CONTRACT_PRICE x:NEW z:0]]
2025/06/12 21:06:02 hedge_task.go:2079: 📋 [用户数据] 任务 8441000: 用户数据事件类型: ORDER_TRADE_UPDATE
2025/06/12 21:06:02 hedge_task.go:2083: 🎯 [用户数据] 任务 8441000: 开始处理 ORDER_TRADE_UPDATE 事件
2025/06/12 21:06:02 hedge_task.go:1284: 🔍 任务 8441000: 启动用户数据流监控，订单ID: 8441000_S_562222_002
2025/06/12 21:06:02 binance_client.go:808: 🔍 Ed25519签名: ubc4yKD9VV7SCyqg++k8g8fDCzARSwUw69BJvNyfAIntba7m2QgPaKWvswt65CtmNwt3pZT3gyIRgYJEUZufAA==
2025/06/12 21:06:02 hedge_task.go:1381: 🔍 任务 8441000: 启动参数化修改循环，间隔: 30ms, 最大次数: 500, 排名: 1
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 hedge_task.go:2233: 任务 8441000: ===== 开始处理 ORDER_TRADE_UPDATE 事件 =====
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562419","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562419}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 hedge_task.go:2248: 任务 8441000: 收到 ORDER_TRADE_UPDATE 事件，客户端订单ID: 8441000_S_562222_002
2025/06/12 21:06:02 hedge_task.go:2257: ✅ [消息路由] 任务 8441000: 确认为本任务订单，继续处理
2025/06/12 21:06:02 hedge_task.go:2278: 任务 8441000: 订单状态: NEW
2025/06/12 21:06:02 hedge_task.go:2320: 任务 8441000: ===== ORDER_TRADE_UPDATE 事件处理完成 =====
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562450","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562450}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562482","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562482}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562513","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562513}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562544","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562544}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562575","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562575}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562607","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562607}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562419)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733562419 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:2 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:2 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733562419","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":2,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":2,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733562419 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:2 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:2 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562419 不在pendingRequests中，忽略
2025/06/12 21:06:02 hedge_task.go:1510: 🔍 任务 8441000: REST API查询成功 - 订单ID: 8389765903970796296, 状态: NEW
2025/06/12 21:06:02 hedge_task.go:1358: 🔍 任务 8441000: REST API查询成功，订单状态: NEW
2025/06/12 21:06:02 websocket_manager.go:223: 📨 WS[userdata]: ACCOUNT_UPDATE
2025/06/12 21:06:02 hedge_task.go:2070: 🔍 [用户数据] 任务 8441000: 收到用户数据事件，开始处理
2025/06/12 21:06:02 websocket_manager.go:223: 📨 WS[userdata]: ORDER_TRADE_UPDATE
2025/06/12 21:06:02 hedge_task.go:2071: 🔍 [用户数据] 任务 8441000: 完整事件数据: map[E:************* T:************* a:map[B:[map[a:USDT bc:0 cw:104.******** wb:104.********]] P:[map[bep:2761.240138 cr:46.******** ep:2749.1475 iw:0 ma:USDT mt:cross pa:0.01 ps:BOTH s:ETHUSDT up:-0.083575]] m:ORDER] e:ACCOUNT_UPDATE]
2025/06/12 21:06:02 hedge_task.go:2079: 📋 [用户数据] 任务 8441000: 用户数据事件类型: ACCOUNT_UPDATE
2025/06/12 21:06:02 hedge_task.go:2086: 💰 [用户数据] 任务 8441000: 开始处理 ACCOUNT_UPDATE 事件
2025/06/12 21:06:02 hedge_task.go:2326: 任务 8441000: 收到账户更新事件
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562450)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562450 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:3 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:3 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562450","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":3,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":3,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562450 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:3 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:3 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562450 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562637","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562637}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562668","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562668}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562482)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562482 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:4 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:4 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562482","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":4,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":4,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562482 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:4 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:4 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562482 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562513)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562513 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:5 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:5 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562513","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":5,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":5,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562513 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:5 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:5 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562513 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562699","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562699}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562544)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562544 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:6 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:6 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562544","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":6,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":6,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562544 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:6 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:6 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562544 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562730","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562730}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562575)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562575 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:7 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:7 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562575","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":7,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":7,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562575 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:7 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:7 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562575 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562760","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562760}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562607)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562607 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:8 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:8 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562607","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":8,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":8,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562607 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:8 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:8 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562607 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562791","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562791}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562637)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562637 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:9 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:9 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562637","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":9,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":9,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562637 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:9 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:9 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562637 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562821","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562821}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562668)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562668 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:10 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:10 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562668","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":10,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":10,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562668 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:10 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:10 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562668 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562852","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562852}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562699)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562699 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:11 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:11 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562699","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":11,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":11,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562699 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:11 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:11 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562699 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562882","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562882}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562730)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562730 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:12 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:12 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562730","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":12,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":12,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562915","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562915}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562730 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:12 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:12 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562730 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562946","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562946}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562760)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562760 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:13 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:13 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562760","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":13,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":13,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562760 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:13 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:13 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562760 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562791)
2025/06/12 21:06:02 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562791 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:14 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:14 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562791","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":14,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":14,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:02 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:02 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562791 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:14 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:14 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:02 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562791 不在pendingRequests中，忽略
2025/06/12 21:06:02 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:02 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:02 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733562977","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733562977}}
2025/06/12 21:06:02 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:02 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:02 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562821)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562821 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:15 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:15 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562821","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":15,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":15,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562821 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:15 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:15 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562821 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563013","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563013}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562852)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562852 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:16 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:16 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562852","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":16,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":16,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562852 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:16 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:16 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562852 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563050","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563050}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562882)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562882 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:17 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:17 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562882","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":17,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":17,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562882 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:17 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:17 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562882 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563080","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563080}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562915)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562915 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:18 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:18 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562915","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":18,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":18,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562915 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:18 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:18 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562915 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563115","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563115}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562946)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562946 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:19 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:19 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562946","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":19,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":19,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562946 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:19 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:19 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562946 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563146","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563146}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733562977)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562977 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:20 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:20 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733562977","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":20,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":20,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733562977 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:20 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:20 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733562977 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563182","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563182}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563013)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563013 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:21 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:21 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563013","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":21,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":21,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563013 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:21 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:21 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563013 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563213","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563213}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563050)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563050 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:22 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:22 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563050","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":22,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":22,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563050 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:22 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:22 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563050 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563248","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563248}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563080)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563080 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:23 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:23 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563080","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":23,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":23,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563080 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:23 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:23 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563080 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563279","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563279}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563115)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563115 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:24 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:24 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563115","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":24,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":24,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563115 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:24 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:24 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563115 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563310","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563310}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563146)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563146 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:25 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:25 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563146","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":25,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":25,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563146 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:25 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:25 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563146 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563341","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563341}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563182)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563182 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:26 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:26 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563182","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":26,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":26,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563182 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:26 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:26 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563182 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563372","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563372}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563213)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563213 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:27 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:27 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563213","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":27,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":27,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563213 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:27 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:27 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563213 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563403","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563403}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563248)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563248 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:28 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:28 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563248","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":28,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":28,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563248 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:28 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:28 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563248 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563434","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563434}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563279)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563279 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:29 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:29 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563279","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":29,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":29,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563279 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:29 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:29 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563279 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563466","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563466}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563310)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563310 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:30 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:30 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563310","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":30,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":30,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563310 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:30 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:30 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563310 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563496","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563496}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563526","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563526}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563341)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563341 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:31 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:31 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563341","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":31,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":31,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563341 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:31 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:31 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563341 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563372)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563372 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:32 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:32 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563372","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":32,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":32,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563372 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:32 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:32 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563372 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563557","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563557}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563403)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563403 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:33 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:33 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563403","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":33,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":33,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563403 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:33 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:33 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563403 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563590","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563590}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903970796296&timestamp=1749733563614
2025/06/12 21:06:03 binance_client.go:808: 🔍 Ed25519签名: hqM85ZTuhSodHxzFZhhDgBAfwsHYsrPV/wTxR85ppv5hkAVxB83g1yOTksD6wGWFGUHMEpMAPxigEj2stMAhCQ==
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563434)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563434 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:34 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:34 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563434","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":34,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":34,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563621","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563621}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563434 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:34 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:34 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563434 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563466)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563466 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:35 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:35 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563466","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":35,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":35,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563466 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:35 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:35 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563466 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563651","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563651}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563496)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563496 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:36 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:36 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563496","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":36,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":36,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563496 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:36 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:36 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563496 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563682","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563682}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563526)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563526 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:37 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:37 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563526","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":37,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":37,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563526 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:37 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:37 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563526 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563712","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563712}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563557)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563557 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:38 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:38 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563557","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":38,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":38,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563557 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:38 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:38 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563557 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563743","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563743}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563590)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563590 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:39 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:39 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563590","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":39,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":39,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563590 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:39 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:39 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563590 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563774","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563774}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:06:03 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:06:03 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733563805","method":"order.modify","params":{"orderId":8389765903970796296,"priceMatch":"QUEUE","quantity":"0.01","side":"SELL","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733563805}}
2025/06/12 21:06:03 websocket_manager.go:143: 📏 消息长度: 220 字节
2025/06/12 21:06:03 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:06:03 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563621)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563621 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:40 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:40 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563621","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":40,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":40,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563621 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:40 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:40 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563621 不在pendingRequests中，忽略
2025/06/12 21:06:03 hedge_task.go:1510: 🔍 任务 8441000: REST API查询成功 - 订单ID: 8389765903970796296, 状态: FILLED
2025/06/12 21:06:03 hedge_task.go:1358: 🔍 任务 8441000: REST API查询成功，订单状态: FILLED
2025/06/12 21:06:03 hedge_task.go:1360: 🎯 任务 8441000: REST API检测到订单成交
2025/06/12 21:06:03 hedge_task.go:1771: 🎯 任务 8441000: REST API成交首次触发任务完成
2025/06/12 21:06:03 hedge_task.go:1788: 🔍 任务 8441000: REST API成交，查询完整订单数据 - 订单ID: 8389765903970796296
2025/06/12 21:06:03 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903970796296&timestamp=1749733563807
2025/06/12 21:06:03 binance_client.go:808: 🔍 Ed25519签名: vR3pKZrU0pAqIewUZUF9A5sma2e3G+QODbATckiwP3gJZOx6FdBgzc6Az7PZiD3d3vDUqjY9AKTiu9ejmq+FBw==
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563651)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563651 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:41 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:41 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563651","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":41,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":41,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563651 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:41 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:41 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563651 不在pendingRequests中，忽略
2025/06/12 21:06:03 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:06:03 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563682)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563682 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:42 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:42 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563682","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":42,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":42,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563682 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:42 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:42 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563682 不在pendingRequests中，忽略
2025/06/12 21:06:03 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563712)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563712 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:43 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:43 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563712","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":43,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":43,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563712 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:43 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:43 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563712 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563743)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563743 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:44 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:44 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563743","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":44,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":44,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563743 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:44 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:44 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563743 不在pendingRequests中，忽略
2025/06/12 21:06:03 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563774)
2025/06/12 21:06:03 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563774 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:45 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:45 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563774","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":45,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":45,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:03 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:03 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563774 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:45 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:45 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:03 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563774 不在pendingRequests中，忽略
2025/06/12 21:06:03 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:06:03 hedge_task.go:1615: ⚠️ 任务 8441000: 订单状态不是Active，跳过修改请求，当前状态: completed
2025/06/12 21:06:04 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733563805)
2025/06/12 21:06:04 hedge_task.go:1599: 🔍 任务 8441000: REST API查询完整订单数据成功 - 订单ID: 8389765903970796296, 平均价格: 2739.94000, 成交数量: 0.010
2025/06/12 21:06:04 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563805 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:46 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:46 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:04 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733563805","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":46,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":46,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:06:04 hedge_task.go:2537: 🔍 [调试] 任务 8441000: processOrderFilled 接收到的完整数据: map[avgPrice:2739.94000 executedQty:0.010 m:false n:0 orderId:8.389765903970797e+18]
2025/06/12 21:06:04 hedge_task.go:2545: 🔍 [调试] 任务 8441000: 尝试解析成交价格 - actualPriceStr: '2739.94000', ok: false
2025/06/12 21:06:04 hedge_task.go:2566: 🔍 [调试] 任务 8441000: 尝试解析订单ID - 查找字段 'i'
2025/06/12 21:06:04 hedge_task.go:2569: 🔍 [调试] 任务 8441000: 字段 'i' 解析失败: 订单ID字段 'i' 不存在, 尝试备用字段 'orderId'
2025/06/12 21:06:04 hedge_task.go:1940: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:06:04 hedge_task.go:1941: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733563805 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:46 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:46 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:06:04 hedge_task.go:1967: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733563805 不在pendingRequests中，忽略
2025/06/12 21:06:04 hedge_task.go:2577: 🔍 [调试] 任务 8441000: 成功解析订单ID: 8389765903970796544
2025/06/12 21:06:04 hedge_task.go:2635: 🧹 [状态清理] 任务 8441000: 开始清空 orderTracker（订单成交完成）
2025/06/12 21:06:04 hedge_task.go:2636: 🧹 [状态清理] 任务 8441000: 清空前状态检查:
2025/06/12 21:06:04 hedge_task.go:2638: 🧹 [状态清理] 任务 8441000:   - orderTracker.ClientOrderID: 8441000_S_562222_002
2025/06/12 21:06:04 hedge_task.go:2639: 🧹 [状态清理] 任务 8441000:   - orderTracker.State: completed
2025/06/12 21:06:04 hedge_task.go:2640: 🧹 [状态清理] 任务 8441000:   - orderTracker.OrderID: 8389765903970796296
2025/06/12 21:06:04 hedge_task.go:2644: 🧹 [状态清理] 任务 8441000:   - isInOrderProcess: true
2025/06/12 21:06:04 hedge_task.go:2650: 🧹 [状态清理] 任务 8441000: orderTracker 已清空
2025/06/12 21:06:04 hedge_task.go:2651: 🧹 [状态清理] 任务 8441000: isInOrderProcess 已设置为 false
2025/06/12 21:06:04 hedge_task.go:2652: 🧹 [状态清理] 任务 8441000: 状态清理完成，任务可以接受新的下单请求
2025/06/12 21:06:04 事件类型: close | 订单类型: limit | 成交方式: taker | 理论价: 2740.4518 | 实际价: 2739.94 | 损耗: 0.5118 | 手续费: 0 | 订单ID: 8389765903970796544
2025/06/12 21:06:04 hedge_task.go:2668: 🎉 任务 8441000: 智能订单成交完成，实际价格: 2739.94, 理论价格: 2740.4518, 订单ID: 8389765903970796544
2025/06/12 21:06:04 hedge_task.go:2670: 🔍 [调试] 任务 8441000: 订单成交详情 - 成交价格: 2739.94, 成交数量: 0.010, 手续费: 0, 成交方式: taker
2025/06/12 21:06:04 hedge_task.go:1834: 🎯 任务 8441000: 任务完成处理结束 - 原因: REST API成交
2025/06/12 21:06:04 hedge_task.go:1299: 🔍 任务 8441000: 用户数据流监控收到停止信号
2025/06/12 21:06:04 hedge_task.go:1387: 🔍 任务 8441000: 修改循环收到停止信号
2025/06/12 21:06:05 smart_order.go:943: ✅ [超时检查] [8441000] 请求8441000_place_003已正常处理，无需超时处理
2025/06/12 21:06:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:13 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:14 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:15 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:16 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:17 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:06:17 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:18 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:19 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:20 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:21 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:22 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:23 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:24 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:25 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:26 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:27 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:06:27 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:28 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 main.go:227: 收到停止信号，正在停止所有任务...
2025/06/12 21:06:29 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 21:06:29 manager.go:150: 停止对冲任务: 8441000
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:29 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:30 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:31 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:31 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:31 main.go:239: 💾 保存数据...
2025/06/12 21:06:31 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:31 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:31 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:06:31 market_data_manager.go:259: ⚠️ 任务8441000通道满
2025/06/12 21:12:20 main.go:185: 启动API服务模式（包含交互功能），端口: 5899
2025/06/12 21:12:20 main.go:186: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 21:12:20 main.go:202: API服务器已启动，访问地址: http://localhost:5899
2025/06/12 21:12:20 main.go:203: API接口:
2025/06/12 21:12:20 main.go:204:   POST /task                           - 创建对冲任务
2025/06/12 21:12:20 main.go:205:   POST /task/stop                      - 停止对冲任务
2025/06/12 21:12:20 main.go:206:   GET  /tasks                          - 获取所有任务
2025/06/12 21:12:20 main.go:207:   GET  /task/{id}                      - 获取单个任务
2025/06/12 21:12:20 main.go:208: 期权系统API接口:
2025/06/12 21:12:20 main.go:209:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 21:12:20 main.go:210:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 21:12:20 main.go:211:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 21:12:20 main.go:402: 
2025/06/12 21:12:20 main.go:403: 🔧 === 交互命令帮助 ===
2025/06/12 21:12:20 main.go:405:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 21:12:20 main.go:406:       示例: add long 3500 0.0005
2025/06/12 21:12:20 main.go:407:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 21:12:20 main.go:408: 
2025/06/12 21:12:20 main.go:409:    stop <task_id>                           - 停止指定任务
2025/06/12 21:12:20 main.go:410:    list                                     - 列出所有任务
2025/06/12 21:12:20 main.go:411: 
2025/06/12 21:12:20 main.go:412: ⚙️ 参数配置:
2025/06/12 21:12:20 main.go:413:    set <参数名> <值>                         - 设置默认参数
2025/06/12 21:12:20 main.go:414:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 21:12:20 main.go:415:    show defaults                            - 显示当前默认参数
2025/06/12 21:12:20 main.go:416: 
2025/06/12 21:12:20 main.go:417: 📊 状态控制:
2025/06/12 21:12:20 main.go:418:    status                                   - 查看状态输出设置
2025/06/12 21:12:20 main.go:419:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 21:12:20 main.go:420:    status off                               - 停止状态输出
2025/06/12 21:12:20 main.go:421: 
2025/06/12 21:12:20 main.go:422: 📋 数据导出:
2025/06/12 21:12:20 main.go:423:    export | detail                          - 导出详细状态到剪切板
2025/06/12 21:12:20 main.go:424: 
2025/06/12 21:12:20 main.go:425: 💾 数据管理:
2025/06/12 21:12:20 api.go:35: API服务器启动，端口: 5899
2025/06/12 21:12:20 main.go:426:    save                                     - 手动保存数据到本地文件
2025/06/12 21:12:20 main.go:427:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 21:12:20 main.go:428:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 21:12:20 main.go:429: 
2025/06/12 21:12:20 main.go:430: 📋 事件查看:
2025/06/12 21:12:20 main.go:431:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 21:12:20 main.go:432: 
2025/06/12 21:12:20 main.go:433: 🎛️  批量控制:
2025/06/12 21:12:20 main.go:434:    startall                                 - 启动所有已停止的任务
2025/06/12 21:12:20 main.go:435:    stopall                                  - 停止所有运行中的任务
2025/06/12 21:12:20 main.go:436:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 21:12:20 main.go:437: 
2025/06/12 21:12:20 main.go:438: 🗑️  任务管理:
2025/06/12 21:12:20 main.go:439:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 21:12:20 main.go:440: 
2025/06/12 21:12:20 main.go:441: 🔧 其他:
2025/06/12 21:12:20 main.go:442:    help                                     - 显示此帮助信息
2025/06/12 21:12:20 main.go:443:    quit | exit                              - 退出程序
2025/06/12 21:12:20 main.go:444: ========================
2025/06/12 21:12:20 main.go:445: 
2025/06/12 21:12:27 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:12:27 main.go:760: 
2025/06/12 21:12:27 main.go:861: 8441000 停止   L    2741 0.020% 0.010  无仓位                   2  21:04   8m   0.02     0.48   0.00 是   manual   -                     
2025/06/12 21:12:27 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:12:30 状态变化: stopped -> running | 原因: 批量启动
2025/06/12 21:12:30 hedge_task.go:109: 启动对冲任务: 8441000
2025/06/12 21:12:30 hedge_task.go:977: 🔧 [8441000] 使用现有配置，MaxRetries: 3
2025/06/12 21:12:30 hedge_task.go:981: 🔧 [8441000] 调用NewSmartOrderManager，传递配置MaxRetries: 3
2025/06/12 21:12:30 smart_order.go:34: 🔧 [8441000] NewSmartOrderManager 接收到的配置:
2025/06/12 21:12:30 smart_order.go:35:    - InitConfig.MaxRetries: 3
2025/06/12 21:12:30 smart_order.go:36:    - InitConfig.RetryInterval: 2s
2025/06/12 21:12:30 smart_order.go:37:    - InitConfig.EnableAsyncInit: true
2025/06/12 21:12:30 smart_order.go:38:    - InitConfig.EnableRollback: true
2025/06/12 21:12:30 smart_order.go:57: 🔧 [8441000] SmartOrderManager 创建完成，存储的配置:
2025/06/12 21:12:30 smart_order.go:58:    - som.config.InitConfig.MaxRetries: 3
2025/06/12 21:12:30 smart_order.go:374: 🚀 [8441000] 开始异步初始化...
2025/06/12 21:12:30 smart_order.go:408: 🔵 [8441000] 智能订单管理器开始初始化...
2025/06/12 21:12:30 smart_order.go:177: 🔗 [8441000] 准备连接交易WebSocket: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:12:30 smart_order.go:178: 🔧 [8441000] initTradingWebSocket 使用重试次数: 3
2025/06/12 21:12:30 smart_order.go:67: 🔧 [8441000] 交易WebSocket连接重试配置: maxRetries=3
2025/06/12 21:12:30 smart_order.go:75: 🔄 [8441000] 交易WebSocket连接尝试 1/3
2025/06/12 21:12:30 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/12 21:12:30 data_manager.go:233: 💾 事件数据已保存 (2个事件)
2025/06/12 21:12:30 websocket_manager.go:91: WebSocket连接成功: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:12:30 smart_order.go:81: ✅ [8441000] 交易WebSocket连接成功
2025/06/12 21:12:30 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 0
2025/06/12 21:12:30 smart_order.go:103: 🔧 [8441000] authenticateWithRetry 接收参数: maxRetries=3
2025/06/12 21:12:30 smart_order.go:104: 🔧 [8441000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:12:30 smart_order.go:112: 🔐 [8441000] WebSocket会话认证尝试 1/3
2025/06/12 21:12:30 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:30 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:30 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"8441000_login_001","method":"session.logon","params":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","signature":"O4rHgG9ZNFNyHXrdOupMD/yKBzTh8zNkeV7ytQyWQh0YgUcHUpNrstW+cP1iEuNRhCwW2mTTCBdeh4w1u7inDw==","timestamp":1749733950578}}
2025/06/12 21:12:30 websocket_manager.go:143: 📏 消息长度: 267 字节
2025/06/12 21:12:30 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:30 smart_order.go:1157: 任务8441000发送WebSocket会话登录请求
2025/06/12 21:12:30 smart_order.go:116: ✅ [8441000] WebSocket会话认证成功
2025/06/12 21:12:30 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 1
2025/06/12 21:12:30 smart_order.go:214: 🔧 [8441000] initListenKey 使用重试次数: 3
2025/06/12 21:12:30 smart_order.go:141: 🔧 [8441000] createListenKeyWithRetry 接收参数: maxRetries=3
2025/06/12 21:12:30 smart_order.go:142: 🔧 [8441000] som.config.InitConfig.MaxRetries=3
2025/06/12 21:12:30 smart_order.go:150: 🔑 [8441000] 创建listenKey尝试 1/3
2025/06/12 21:12:30 binance_client.go:188: 🔍 Ed25519签名请求: POST https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733950579&signature=4393f5585e0ce59af17c63a0ba55ffe3c8b5275d363900fbc3e16917429533bc
2025/06/12 21:12:30 binance_client.go:189: 🔍 签名字符串: timestamp=1749733950579
2025/06/12 21:12:30 binance_client.go:190: 🔍 Ed25519签名: aiqCtfnJkaujOFYCVdlppyn+TJkmsduSiMI72FeNVDiM+N3b4PoYTx6FpRFJjLZKKI0LrTrOZS/PLun1kJ0SCA==
2025/06/12 21:12:30 binance_client.go:687: ✅ listenKey创建成功: vZNihI5gQkQ8ttMJUYKIvVWCjbUOoR3NKKZHgYtJtdJTYWWpCieVyyYC9rrfQyzl
2025/06/12 21:12:30 smart_order.go:154: ✅ [8441000] listenKey创建成功: vZNihI5g...
2025/06/12 21:12:30 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 2
2025/06/12 21:12:30 smart_order.go:236: 🔗 [8441000] 准备连接用户数据WebSocket: wss://fstream.binance.com/ws/vZNihI5gQkQ8ttMJUYKIvVWCjbUOoR3NKKZHgYtJtdJTYWWpCieVyyYC9rrfQyzl
2025/06/12 21:12:30 smart_order.go:67: 🔧 [8441000] 用户数据WebSocket连接重试配置: maxRetries=3
2025/06/12 21:12:30 smart_order.go:75: 🔄 [8441000] 用户数据WebSocket连接尝试 1/3
2025/06/12 21:12:30 websocket_manager.go:225: 📨 WS[trading]: trading_response (8441000_login_001)
2025/06/12 21:12:30 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:8441000_login_001 rateLimits:[map[count:7 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733951133 connectedSince:1749733950944 returnRateLimits:true serverTime:1749733951133] status:200]
2025/06/12 21:12:30 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"8441000_login_001","rateLimits":[{"count":7,"interval":"MINUTE","intervalNum":1,"limit":2400,"rateLimitType":"REQUEST_WEIGHT"}],"result":{"apiKey":"3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso","authorizedSince":1749733951133,"connectedSince":1749733950944,"returnRateLimits":true,"serverTime":1749733951133},"status":200}
2025/06/12 21:12:31 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/vZNihI5gQkQ8ttMJUYKIvVWCjbUOoR3NKKZHgYtJtdJTYWWpCieVyyYC9rrfQyzl
2025/06/12 21:12:31 smart_order.go:81: ✅ [8441000] 用户数据WebSocket连接成功
2025/06/12 21:12:31 smart_order.go:344: ✅ [8441000] 初始化步骤成功: 3
2025/06/12 21:12:31 smart_order.go:434: ⏰ [8441000] 启动listenKey续期协程...
2025/06/12 21:12:31 smart_order.go:437: 🎉 [8441000] 智能订单管理器初始化成功！
2025/06/12 21:12:31 smart_order.go:397: 🎉 [8441000] 异步初始化成功！耗时: 1.668756083s
2025/06/12 21:12:31 hedge_task.go:990: 任务 8441000: 智能订单系统初始化成功
2025/06/12 21:12:31 smart_order.go:1012: 🔄 [8441000] listenKey续期循环启动，续期间隔: 20m0s
2025/06/12 21:12:37 websocket_manager.go:91: WebSocket连接成功: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:12:37 market_data_manager.go:174: 成功创建ETHUSDT市场数据连接: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:12:37 market_data_manager.go:117: 任务8441000成功订阅ETHUSDT市场数据，当前订阅者数量: 1
2025/06/12 21:12:37 hedge_task.go:1011: 🔧 [8441000] handleSmartOrderEvents 开始执行
2025/06/12 21:12:37 hedge_task.go:1030: ✅ [8441000] WebSocket管理器检查通过，获取事件通道
2025/06/12 21:12:37 hedge_task.go:1036: ✅ [8441000] 事件通道获取成功，开始监听事件
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[id:8441000_login_001 rateLimits:[map[count:7 interval:MINUTE intervalNum:1 limit:2400 rateLimitType:REQUEST_WEIGHT]] result:map[apiKey:3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso authorizedSince:1749733951133 connectedSince:1749733950944 returnRateLimits:true serverTime:1749733951133] status:200]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID 8441000_login_001 不在pendingRequests中，忽略
2025/06/12 21:12:37 hedge_task.go:1169: 🎯 任务 8441000: 触发智能开仓，方向: long
2025/06/12 21:12:37 hedge_task.go:1186: 🚀 任务 8441000: 开始执行极简下单流程
2025/06/12 21:12:37 smart_order.go:521: 🔵 [8441000] 下单: long 0.01 postOnly=true
2025/06/12 21:12:37 smart_order.go:537: 🔒 [下单入口2] [8441000] 设置下单流程标识为 true
2025/06/12 21:12:37 smart_order.go:538: 🔍 [下单入口2] [8441000] 调用栈追踪 - PlaceSmartOrder 被调用
2025/06/12 21:12:37 smart_order.go:888: 📋 [8441000] 获取交易对: ETHUSDT
2025/06/12 21:12:37 smart_order.go:584: 📋 [8441000] 开始转换参数为map...
2025/06/12 21:12:37 smart_order.go:589: 📋 [8441000] 参数转换完成: map[newClientOrderId:8441000_L_957377_002 priceMatch:QUEUE quantity:0.01 side:BUY symbol:ETHUSDT timeInForce:GTX timestamp:1749733957377 type:LIMIT]
2025/06/12 21:12:37 smart_order.go:592: 🔐 [8441000] 使用传入的WebSocket会话认证状态: true
2025/06/12 21:12:37 smart_order.go:596: ✅ [8441000] 使用已认证WebSocket会话下单（无需签名）
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"8441000_place_003","method":"order.place","params":{"newClientOrderId":"8441000_L_957377_002","priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957377,"type":"LIMIT"}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 234 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 smart_order.go:676: 任务8441000发送智能下单请求: long 0.01 8441000_L_957377_002
2025/06/12 21:12:37 hedge_task.go:1198: ⏳ 任务 8441000: 等待订单初始化完成...
2025/06/12 21:12:37 smart_order.go:934: ⏰ [超时检查] [8441000] 启动超时检查: RequestID=8441000_place_003, 超时时间=3s
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (8441000_place_003)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[id:8441000_place_003 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:1 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:2 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] result:map[avgPrice:0.00 clientOrderId:8441000_L_957377_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903973920784 origQty:0.010 origType:LIMIT positionSide:BOTH price:2744.36 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:BUY status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733957925 workingType:CONTRACT_PRICE] status:200]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"id":"8441000_place_003","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":1,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":2,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"result":{"avgPrice":"0.00","clientOrderId":"8441000_L_957377_002","closePosition":false,"cumQty":"0.000","cumQuote":"0.00000","executedQty":"0.000","goodTillDate":0,"orderId":"8389765903973920784","origQty":"0.010","origType":"LIMIT","positionSide":"BOTH","price":"2744.36","priceMatch":"QUEUE","priceProtect":false,"reduceOnly":false,"selfTradePreventionMode":"EXPIRE_MAKER","side":"BUY","status":"NEW","stopPrice":"0.00","symbol":"ETHUSDT","timeInForce":"GTX","type":"LIMIT","updateTime":1749733957925,"workingType":"CONTRACT_PRICE"},"status":200}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[id:8441000_place_003 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:1 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:2 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] result:map[avgPrice:0.00 clientOrderId:8441000_L_957377_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903973920784 origQty:0.010 origType:LIMIT positionSide:BOTH price:2744.36 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:BUY status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733957925 workingType:CONTRACT_PRICE] status:200]
2025/06/12 21:12:37 hedge_task.go:2014: ✅ [processTradingResponse] 任务 8441000: 调用 handleSuccessfulTradingResponse
2025/06/12 21:12:37 hedge_task.go:2030: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: 开始处理成功响应
2025/06/12 21:12:37 hedge_task.go:2031: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: 请求类型: place_order
2025/06/12 21:12:37 hedge_task.go:2039: 🔍 [handleSuccessfulTradingResponse] 任务 8441000: result数据: map[avgPrice:0.00 clientOrderId:8441000_L_957377_002 closePosition:false cumQty:0.000 cumQuote:0.00000 executedQty:0.000 goodTillDate:0 orderId:8389765903973920784 origQty:0.010 origType:LIMIT positionSide:BOTH price:2744.36 priceMatch:QUEUE priceProtect:false reduceOnly:false selfTradePreventionMode:EXPIRE_MAKER side:BUY status:NEW stopPrice:0.00 symbol:ETHUSDT timeInForce:GTX type:LIMIT updateTime:1749733957925 workingType:CONTRACT_PRICE]
2025/06/12 21:12:37 hedge_task.go:2043: 🎯 [handleSuccessfulTradingResponse] 任务 8441000: 调用 handleOrderPlaceResponse
2025/06/12 21:12:37 hedge_task.go:2108: 🔍 [handleOrderPlaceResponse] 客户端订单ID: 8441000_L_957377_002
2025/06/12 21:12:37 hedge_task.go:1713: 🎯 任务 8441000: 尝试通过 trading_response 初始化订单跟踪
2025/06/12 21:12:37 hedge_task.go:1738: 🚀 任务 8441000: 订单初始化完成 - 订单ID: 8389765903973920784, 价格: 2744.36
2025/06/12 21:12:37 hedge_task.go:1750: 🚀 任务 8441000: 启动三并发监控机制
2025/06/12 21:12:37 hedge_task.go:2147: 🎯 任务 8441000: 通过 trading_response 完成订单初始化
2025/06/12 21:12:37 hedge_task.go:1284: 🔍 任务 8441000: 启动用户数据流监控，订单ID: 8441000_L_957377_002
2025/06/12 21:12:37 hedge_task.go:1381: 🔍 任务 8441000: 启动参数化修改循环，间隔: 30ms, 最大次数: 500, 排名: 1
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 hedge_task.go:1343: 🔍 任务 8441000: 启动REST API轮询监控，间隔: 1s
2025/06/12 21:12:37 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903973920784&timestamp=1749733957560
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957560","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957560}}
2025/06/12 21:12:37 binance_client.go:808: 🔍 Ed25519签名: STK3kAInOjEw4U537plgCHWjSYGn6OUMs5zkvBVtYzZOAwhTr8ufbdmrapWp0tEu8S6CT/ijeNEu5S+zpn6hAg==
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:223: 📨 WS[userdata]: ORDER_TRADE_UPDATE
2025/06/12 21:12:37 hedge_task.go:2081: 🔍 [用户数据] 任务 8441000: 收到用户数据事件，开始处理
2025/06/12 21:12:37 hedge_task.go:2082: 🔍 [用户数据] 任务 8441000: 完整事件数据: map[E:1749733957927 T:1749733957925 e:ORDER_TRADE_UPDATE o:map[L:0 N:USDT R:false S:BUY T:1749733957925 V:EXPIRE_MAKER X:NEW a:0 ap:0 b:123.47114 c:8441000_L_957377_002 cp:false f:GTX gtd:0 i:8389765903973920784 l:0 m:false n:0 o:LIMIT ot:LIMIT p:2744.36 pP:false pm:QUEUE ps:BOTH q:0.01 rp:0 s:ETHUSDT si:0 sp:0 ss:0 t:0 wt:CONTRACT_PRICE x:NEW z:0]]
2025/06/12 21:12:37 hedge_task.go:2090: 📋 [用户数据] 任务 8441000: 用户数据事件类型: ORDER_TRADE_UPDATE
2025/06/12 21:12:37 hedge_task.go:2094: 🎯 [用户数据] 任务 8441000: 开始处理 ORDER_TRADE_UPDATE 事件
2025/06/12 21:12:37 hedge_task.go:2244: 任务 8441000: ===== 开始处理 ORDER_TRADE_UPDATE 事件 =====
2025/06/12 21:12:37 hedge_task.go:2259: 任务 8441000: 收到 ORDER_TRADE_UPDATE 事件，客户端订单ID: 8441000_L_957377_002
2025/06/12 21:12:37 hedge_task.go:2268: ✅ [消息路由] 任务 8441000: 确认为本任务订单，继续处理
2025/06/12 21:12:37 hedge_task.go:2289: 任务 8441000: 订单状态: NEW
2025/06/12 21:12:37 hedge_task.go:2331: 任务 8441000: ===== ORDER_TRADE_UPDATE 事件处理完成 =====
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957592","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957592}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957623","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957623}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957654","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957654}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957685","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957685}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957716","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957716}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 hedge_task.go:1510: 🔍 任务 8441000: REST API查询成功 - 订单ID: 8389765903973920784, 状态: NEW
2025/06/12 21:12:37 hedge_task.go:1358: 🔍 任务 8441000: REST API查询成功，订单状态: NEW
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957560)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957560 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:2 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:3 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957560","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":2,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":3,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957560 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:2 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:3 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957560 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957747","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957747}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957592)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957592 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:3 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:4 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957592","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":3,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":4,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957592 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:3 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:4 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957592 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957778","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957778}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957623)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957623 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:4 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:5 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957623","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":4,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":5,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957623 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:4 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:5 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957623 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957809","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957809}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957654)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957654 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:5 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:6 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957654","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":5,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":6,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957654 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:5 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:6 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957654 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957840","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957840}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957685)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957685 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:6 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:7 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957685","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":6,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":7,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957685 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:6 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:7 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957685 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957870","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957870}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957716)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957716 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:7 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:8 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957716","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":7,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":8,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957716 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:7 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:8 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957716 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957901","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957901}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957747)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957747 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:8 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:9 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957747","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":8,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":9,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957747 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:8 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:9 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957747 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957932","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957932}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957963","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957963}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957778)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957778 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:9 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:10 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957778","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":9,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":10,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957778 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:9 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:10 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957778 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957809)
2025/06/12 21:12:37 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957809 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:10 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:11 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957809","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":10,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":11,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:37 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:37 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957809 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:10 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:11 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:37 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957809 不在pendingRequests中，忽略
2025/06/12 21:12:37 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:37 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:37 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733957994","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733957994}}
2025/06/12 21:12:37 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:37 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:37 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957840)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957840 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:11 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:12 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957840","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":11,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":12,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957840 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:11 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:12 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957840 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958024","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958024}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957870)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957870 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:12 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:13 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957870","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":12,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":13,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957870 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:12 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:13 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957870 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958055","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958055}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957901)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957901 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:13 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:14 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957901","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":13,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":14,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957901 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:13 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:14 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957901 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958085","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958085}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957932)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957932 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:14 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:15 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957932","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":14,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":15,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957932 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:14 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:15 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957932 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958116","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958116}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957963)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957963 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:15 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:16 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957963","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":15,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":16,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957963 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:15 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:16 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957963 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958147","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958147}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733957994)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957994 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:16 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:17 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733957994","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":16,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":17,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733957994 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:16 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:17 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733957994 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958177","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958177}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958024)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958024 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:17 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:18 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958024","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":17,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":18,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958024 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:17 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:18 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958024 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958208","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958208}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958055)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958055 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:18 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:19 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958055","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":18,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":19,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958055 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:18 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:19 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958055 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958238","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958238}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958085)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958085 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:19 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:20 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958085","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":19,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":20,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958085 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:19 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:20 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958085 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958268","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958268}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958299","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958299}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958116)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958116 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:20 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:21 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958116","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":20,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":21,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958116 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:20 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:21 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958116 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958330","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958330}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958147)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958147 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:21 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:22 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958147","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":21,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":22,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958147 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:21 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:22 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958147 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958361","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958361}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958177)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958177 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:22 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:23 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958177","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":22,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":23,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958177 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:22 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:23 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958177 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958393","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958393}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958208)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958208 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:23 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:24 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958208","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":23,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":24,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958208 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:23 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:24 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958208 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958238)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958238 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:24 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:25 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958238","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":24,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":25,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958238 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:24 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:25 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958238 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958423","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958423}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958268)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958268 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:25 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:26 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958268","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":25,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":26,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958268 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:25 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:26 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958268 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958454","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958454}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958299)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958299 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:26 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:27 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958299","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":26,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":27,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958299 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:26 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:27 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958299 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958484","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958484}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958515","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958515}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958330)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958330 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:27 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:28 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958330","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":27,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":28,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958330 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:27 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:28 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958330 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958361)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958361 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:28 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:29 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958361","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":28,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":29,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958361 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:28 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:29 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958361 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958546","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958546}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958393)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958393 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:29 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:30 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958393","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":29,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":30,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958393 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:29 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:30 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958393 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958576","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958576}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958423)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958423 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:30 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:31 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958423","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":30,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":31,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958607","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958607}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958423 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:30 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:31 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958423 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958454)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958454 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:31 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:32 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958454","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":31,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":32,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958454 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:31 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:32 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958454 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958638","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958638}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958668","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958668}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958699","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958699}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958730","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958730}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 binance_client.go:802: 🔍 签名字符串: symbol=ETHUSDT&orderId=8389765903973920784&timestamp=1749733958739
2025/06/12 21:12:38 binance_client.go:808: 🔍 Ed25519签名: nK9WhBeYEfu31MxAu5Q5AHK69NjUNpGUWJKMEidbcGgRdJ6SaKW9GO+pQuss2Ba1QsbiHsiUxMqzSX1Gl09fDQ==
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958762","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958762}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958484)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958484 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:32 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:33 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958484","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":32,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":33,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958484 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:32 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:33 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958484 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958515)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958515 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:33 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:34 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958515","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":33,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":34,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958546)
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958515 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:33 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:34 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958515 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958546 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:34 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:35 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958546","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":34,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":35,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958546 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:34 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:35 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958546 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958576)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958576 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:35 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:36 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958576","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":35,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":36,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958576 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:35 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:36 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958576 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958793","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958793}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958607)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958607 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:36 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:37 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958607","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":36,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":37,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958607 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:36 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:37 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958607 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958638)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958638 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:37 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:38 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958638","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":37,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":38,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958823","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958823}}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958638 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:37 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:38 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958638 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:124: 🔵 WebSocket准备发送消息，连接状态检查...
2025/06/12 21:12:38 websocket_manager.go:133: ✅ WebSocket连接状态正常
2025/06/12 21:12:38 websocket_manager.go:142: 🔍 WebSocket发送消息: {"id":"modify_8441000_1749733958855","method":"order.modify","params":{"orderId":8389765903973920784,"priceMatch":"QUEUE","quantity":"0.01","side":"BUY","symbol":"ETHUSDT","timeInForce":"GTX","timestamp":1749733958855}}
2025/06/12 21:12:38 websocket_manager.go:143: 📏 消息长度: 219 字节
2025/06/12 21:12:38 websocket_manager.go:156: ✅ WebSocket消息发送成功
2025/06/12 21:12:38 hedge_task.go:1662: 📤 任务 8441000: 发送修改请求成功，排名: 1, priceMatch: QUEUE
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958668)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958668 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:38 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:39 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-5027,"msg":"No need to modify the order."},"id":"modify_8441000_1749733958668","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":38,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":39,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-5027 msg:No need to modify the order.] id:modify_8441000_1749733958668 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:38 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:39 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958668 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958699)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958699 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:39 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:40 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733958699","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":39,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":40,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958699 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:39 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:40 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958699 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:223: 📨 WS[userdata]: ACCOUNT_UPDATE
2025/06/12 21:12:38 websocket_manager.go:223: 📨 WS[userdata]: ORDER_TRADE_UPDATE
2025/06/12 21:12:38 hedge_task.go:2081: 🔍 [用户数据] 任务 8441000: 收到用户数据事件，开始处理
2025/06/12 21:12:38 hedge_task.go:2082: 🔍 [用户数据] 任务 8441000: 完整事件数据: map[E:************* T:************* e:ORDER_TRADE_UPDATE o:map[L:2744.36 N:USDT R:false S:BUY T:************* V:EXPIRE_MAKER X:FILLED a:0 ap:2744.36 b:96 c:8441000_L_957377_002 cp:false f:GTX gtd:0 i:8389765903973920784 l:0.01 m:true n:0.******** o:LIMIT ot:LIMIT p:2744.36 pP:false pm:QUEUE ps:BOTH q:0.01 rp:0 s:ETHUSDT si:0 sp:0 ss:0 t:********** wt:CONTRACT_PRICE x:TRADE z:0.01]]
2025/06/12 21:12:38 hedge_task.go:2090: 📋 [用户数据] 任务 8441000: 用户数据事件类型: ORDER_TRADE_UPDATE
2025/06/12 21:12:38 hedge_task.go:2094: 🎯 [用户数据] 任务 8441000: 开始处理 ORDER_TRADE_UPDATE 事件
2025/06/12 21:12:38 hedge_task.go:2244: 任务 8441000: ===== 开始处理 ORDER_TRADE_UPDATE 事件 =====
2025/06/12 21:12:38 hedge_task.go:2259: 任务 8441000: 收到 ORDER_TRADE_UPDATE 事件，客户端订单ID: 8441000_L_957377_002
2025/06/12 21:12:38 hedge_task.go:2268: ✅ [消息路由] 任务 8441000: 确认为本任务订单，继续处理
2025/06/12 21:12:38 hedge_task.go:2289: 任务 8441000: 订单状态: FILLED
2025/06/12 21:12:38 hedge_task.go:2327: 🎯 任务 8441000: 检测到订单成交，开始处理
2025/06/12 21:12:38 hedge_task.go:1850: 🎯 任务 8441000: ===== 处理订单成交事件 =====
2025/06/12 21:12:38 hedge_task.go:1771: 🎯 任务 8441000: WebSocket成交首次触发任务完成
2025/06/12 21:12:38 hedge_task.go:2548: 🔍 [调试] 任务 8441000: processOrderFilled 接收到的完整数据: map[L:2744.36 N:USDT R:false S:BUY T:************* V:EXPIRE_MAKER X:FILLED a:0 ap:2744.36 b:96 c:8441000_L_957377_002 cp:false f:GTX gtd:0 i:8389765903973920784 l:0.01 m:true n:0.******** o:LIMIT ot:LIMIT p:2744.36 pP:false pm:QUEUE ps:BOTH q:0.01 rp:0 s:ETHUSDT si:0 sp:0 ss:0 t:********** wt:CONTRACT_PRICE x:TRADE z:0.01]
2025/06/12 21:12:38 hedge_task.go:2556: 🔍 [调试] 任务 8441000: 尝试解析成交价格 - actualPriceStr: '2744.36', ok: true
2025/06/12 21:12:38 hedge_task.go:2577: 🔍 [调试] 任务 8441000: 尝试解析订单ID - 查找字段 'i'
2025/06/12 21:12:38 hedge_task.go:2588: 🔍 [调试] 任务 8441000: 成功解析订单ID: 8389765903973920784
2025/06/12 21:12:38 hedge_task.go:2646: 🧹 [状态清理] 任务 8441000: 开始清空 orderTracker（订单成交完成）
2025/06/12 21:12:38 hedge_task.go:2647: 🧹 [状态清理] 任务 8441000: 清空前状态检查:
2025/06/12 21:12:38 hedge_task.go:2649: 🧹 [状态清理] 任务 8441000:   - orderTracker.ClientOrderID: 8441000_L_957377_002
2025/06/12 21:12:38 hedge_task.go:2650: 🧹 [状态清理] 任务 8441000:   - orderTracker.State: completed
2025/06/12 21:12:38 hedge_task.go:2651: 🧹 [状态清理] 任务 8441000:   - orderTracker.OrderID: 8389765903973920784
2025/06/12 21:12:38 hedge_task.go:2655: 🧹 [状态清理] 任务 8441000:   - isInOrderProcess: true
2025/06/12 21:12:38 hedge_task.go:2661: 🧹 [状态清理] 任务 8441000: orderTracker 已清空
2025/06/12 21:12:38 hedge_task.go:2662: 🧹 [状态清理] 任务 8441000: isInOrderProcess 已设置为 false
2025/06/12 21:12:38 hedge_task.go:2663: 🧹 [状态清理] 任务 8441000: 状态清理完成，任务可以接受新的下单请求
2025/06/12 21:12:38 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2741.5482 | 实际价: 2744.36 | 损耗: 2.8118 | 手续费: 0.******** | 订单ID: 8389765903973920784
2025/06/12 21:12:38 hedge_task.go:2679: 🎉 任务 8441000: 智能订单成交完成，实际价格: 2744.36, 理论价格: 2741.5482, 订单ID: 8389765903973920784
2025/06/12 21:12:38 hedge_task.go:2681: 🔍 [调试] 任务 8441000: 订单成交详情 - 成交价格: 2744.36, 成交数量: 0.01, 手续费: 0.********, 成交方式: maker
2025/06/12 21:12:38 market_data_manager.go:134: 任务8441000取消订阅ETHUSDT市场数据，剩余订阅者数量: 0
2025/06/12 21:12:38 market_data_manager.go:140: 关闭ETHUSDT市场数据连接（无订阅者）
2025/06/12 21:12:38 hedge_task.go:1299: 🔍 任务 8441000: 用户数据流监控收到停止信号
2025/06/12 21:12:38 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:61676->198.18.0.45:443: use of closed network connection
2025/06/12 21:12:38 websocket_manager.go:118: WebSocket连接已断开: wss://fstream.binance.com/ws/ethusdt@depth20@100ms
2025/06/12 21:12:38 hedge_task.go:1845: 🎯 任务 8441000: 任务完成处理结束 - 原因: WebSocket成交
2025/06/12 21:12:38 hedge_task.go:1855: 🎯 任务 8441000: ===== 订单成交处理完成 =====
2025/06/12 21:12:38 hedge_task.go:2331: 任务 8441000: ===== ORDER_TRADE_UPDATE 事件处理完成 =====
2025/06/12 21:12:38 market_data_manager.go:205: ETHUSDT市场数据错误: read tcp 198.18.0.1:61676->198.18.0.45:443: use of closed network connection
2025/06/12 21:12:38 hedge_task.go:1387: 🔍 任务 8441000: 修改循环收到停止信号
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958730)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958730 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:40 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:41 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733958730","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":40,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":41,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958730 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:40 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:41 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958730 不在pendingRequests中，忽略
2025/06/12 21:12:38 hedge_task.go:1510: 🔍 任务 8441000: REST API查询成功 - 订单ID: 8389765903973920784, 状态: FILLED
2025/06/12 21:12:38 hedge_task.go:1358: 🔍 任务 8441000: REST API查询成功，订单状态: FILLED
2025/06/12 21:12:38 hedge_task.go:1360: 🎯 任务 8441000: REST API检测到订单成交
2025/06/12 21:12:38 hedge_task.go:1767: 🔄 任务 8441000: 任务已完成，忽略来自REST API成交的重复信号
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958762)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958762 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:41 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:42 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733958762","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":41,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":42,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958762 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:41 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:42 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958762 不在pendingRequests中，忽略
2025/06/12 21:12:38 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958793)
2025/06/12 21:12:38 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958793 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:42 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:43 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733958793","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":42,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":43,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:38 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:38 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958793 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:42 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:43 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:38 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958793 不在pendingRequests中，忽略
2025/06/12 21:12:39 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958823)
2025/06/12 21:12:39 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958823 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:43 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:44 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:39 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733958823","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":43,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":44,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:39 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:39 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958823 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:43 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:44 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:39 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958823 不在pendingRequests中，忽略
2025/06/12 21:12:39 websocket_manager.go:225: 📨 WS[trading]: trading_response (modify_8441000_1749733958855)
2025/06/12 21:12:39 websocket_manager.go:229: 🔍 [调试] trading_response 原始数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958855 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:44 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:45 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:39 websocket_manager.go:233: 🔍 [调试] trading_response JSON: {"error":{"code":-2013,"msg":"Order does not exist."},"id":"modify_8441000_1749733958855","rateLimits":[{"count":-1,"interval":"MINUTE","intervalNum":1,"limit":-1,"rateLimitType":"REQUEST_WEIGHT"},{"count":44,"interval":"SECOND","intervalNum":10,"limit":300,"rateLimitType":"ORDERS"},{"count":45,"interval":"MINUTE","intervalNum":1,"limit":1200,"rateLimitType":"ORDERS"}],"status":400}
2025/06/12 21:12:39 hedge_task.go:1951: 🔍 [processTradingResponse] 任务 8441000: 开始处理 trading_response
2025/06/12 21:12:39 hedge_task.go:1952: 🔍 [processTradingResponse] 任务 8441000: 完整数据: map[error:map[code:-2013 msg:Order does not exist.] id:modify_8441000_1749733958855 rateLimits:[map[count:-1 interval:MINUTE intervalNum:1 limit:-1 rateLimitType:REQUEST_WEIGHT] map[count:44 interval:SECOND intervalNum:10 limit:300 rateLimitType:ORDERS] map[count:45 interval:MINUTE intervalNum:1 limit:1200 rateLimitType:ORDERS]] status:400]
2025/06/12 21:12:39 hedge_task.go:1978: ⚠️ [processTradingResponse] 任务 8441000: 请求ID modify_8441000_1749733958855 不在pendingRequests中，忽略
2025/06/12 21:12:40 smart_order.go:943: ✅ [超时检查] [8441000] 请求8441000_place_003已正常处理，无需超时处理
2025/06/12 21:12:49 main.go:759: __________________________________________________________________________________________________________________________
2025/06/12 21:12:49 main.go:760: 
2025/06/12 21:12:49 main.go:861: 8441000 运行   L    2741 0.020% 0.010  L0.01@2744            3  21:04   8m   0.02     1.26   0.01 是   manual   -                     
2025/06/12 21:12:49 main.go:879: __________________________________________________________________________________________________________________________
2025/06/12 21:12:50 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:12:51 websocket_manager.go:321: 💓 增强心跳发送ping消息
2025/06/12 21:13:00 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:13:01 websocket_manager.go:340: 🏓 增强心跳发送pong消息
2025/06/12 21:13:07 hedge_task.go:1767: 🔄 任务 8441000: 任务已完成，忽略来自监控超时的重复信号
2025/06/12 21:13:07 hedge_task.go:1216: 🧹 任务 8441000: 清理orderBusy状态
2025/06/12 21:13:07 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:61659->198.18.1.239:443: use of closed network connection
2025/06/12 21:13:07 smart_order.go:1047: 🛑 [8441000] listenKey续期循环停止
2025/06/12 21:13:07 websocket_manager.go:118: WebSocket连接已断开: wss://ws-fapi.binance.com/ws-fapi/v1
2025/06/12 21:13:07 websocket_manager.go:191: WebSocket读取消息失败: read tcp 198.18.0.1:61670->198.18.0.45:443: use of closed network connection
2025/06/12 21:13:07 websocket_manager.go:118: WebSocket连接已断开: wss://fstream.binance.com/ws/vZNihI5gQkQ8ttMJUYKIvVWCjbUOoR3NKKZHgYtJtdJTYWWpCieVyyYC9rrfQyzl
2025/06/12 21:13:07 binance_client.go:188: 🔍 Ed25519签名请求: DELETE https://fapi.binance.com/fapi/v1/listenKey?timestamp=1749733987582&signature=f3ae858f8b36929488941401227642e8575d34087ea37b6c2dd27c827be4754f
2025/06/12 21:13:07 binance_client.go:189: 🔍 签名字符串: timestamp=1749733987582&listenKey=vZNihI5gQkQ8ttMJUYKIvVWCjbUOoR3NKKZHgYtJtdJTYWWpCieVyyYC9rrfQyzl
2025/06/12 21:13:07 binance_client.go:190: 🔍 Ed25519签名: ppUV5JOl9Zww16svJEPYpHvRC/XG3mTu6W+9vcA7EyAF/OEtx0n9WOCYls9I8D/jAqroQbud01TwD/2svOVIAA==
2025/06/12 21:13:07 hedge_task.go:1058: ❌ [8441000] 交易WS错误: read tcp 198.18.0.1:61659->198.18.1.239:443: use of closed network connection
2025/06/12 21:13:07 hedge_task.go:1072: ❌ [8441000] 用户数据WS错误: read tcp 198.18.0.1:61670->198.18.0.45:443: use of closed network connection
2025/06/12 21:13:07 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/12 21:13:07 data_manager.go:233: 💾 事件数据已保存 (3个事件)
2025/06/12 21:13:07 data_manager.go:189: 💾 任务数据已保存 (1个任务)
2025/06/12 21:13:07 data_manager.go:233: 💾 事件数据已保存 (3个事件)
