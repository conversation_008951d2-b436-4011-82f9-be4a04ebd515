# 期权动态对冲项目 - Cursor Rules

## 项目结构说明
- **根目录** (`/Users/<USER>/开发使用/期权动态对冲下单/`): 动态对冲程序
- **options-trading-system/** 子目录: 期权下单程序

## 目录操作规则

### 1. 程序识别规则
- 当用户提到"动态对冲程序"、"对冲系统"、"hedge"时 → 操作根目录
- 当用户提到"期权下单程序"、"期权系统"、"options"时 → 操作 options-trading-system/ 目录
- 如果用户没有明确指定，根据文件名和上下文判断目标程序

### 2. 文件操作规则
- **编译**: 
  - 动态对冲程序: `cd /Users/<USER>/开发使用/期权动态对冲下单 && go build`
  - 期权下单程序: `cd /Users/<USER>/开发使用/期权动态对冲下单/options-trading-system && go build`

- **运行**:
  - 动态对冲程序: 在根目录执行 `./hedge-system` 或相关可执行文件
  - 期权下单程序: 在 options-trading-system/ 目录执行 `./options-system`

- **测试程序创建**:
  - 动态对冲程序测试: 在根目录创建测试文件夹，如 `test_hedge/`
  - 期权下单程序测试: 在 options-trading-system/ 目录创建测试文件夹，如 `test_options/`

### 3. 代码修改规则
- **增删改查**: 始终确认目标文件属于哪个程序，在对应目录下操作
- **导入路径**: 注意两个程序的 go.mod 是独立的
- **依赖管理**: 分别在各自目录下管理依赖

### 4. 强制检查清单
在执行任何操作前，必须确认：
1. ✅ 当前操作的是哪个程序？(动态对冲 vs 期权下单)
2. ✅ 工作目录是否正确？
3. ✅ 文件路径是否基于正确的程序根目录？
4. ✅ 编译/运行命令是否在正确目录执行？

### 5. 目录切换命令模板
```bash
# 切换到动态对冲程序
cd /Users/<USER>/开发使用/期权动态对冲下单

# 切换到期权下单程序  
cd /Users/<USER>/开发使用/期权动态对冲下单/options-trading-system
```

### 6. 常见文件标识
- **动态对冲程序**: `hedge_*.go`, `manager.go`, `api.go`, `types.go`, `binance_client.go`
- **期权下单程序**: `options-trading-system/` 下的所有 `.go` 文件

### 7. 错误预防
- ❌ 不要在错误目录下编译
- ❌ 不要混淆两个程序的配置文件
- ❌ 不要在根目录操作期权程序文件
- ❌ 不要在 options-trading-system/ 目录操作对冲程序文件

## 操作示例

### 编译示例
```bash
# 编译动态对冲程序
cd /Users/<USER>/开发使用/期权动态对冲下单
go build -o hedge-system

# 编译期权下单程序
cd /Users/<USER>/开发使用/期权动态对冲下单/options-trading-system
go build -o options-system
```

### 测试程序创建示例
```bash
# 为动态对冲程序创建测试
cd /Users/<USER>/开发使用/期权动态对冲下单
mkdir test_hedge_feature
cd test_hedge_feature

# 为期权下单程序创建测试
cd /Users/<USER>/开发使用/期权动态对冲下单/options-trading-system
mkdir test_options_feature
cd test_options_feature
```

## 重要提醒
- 每次操作前必须明确当前在操作哪个程序
- 使用绝对路径确保目录切换正确
- 两个程序功能独立，避免交叉引用
- 测试程序必须在对应程序目录下创建，避免 main 函数冲突

## 用户特定规则
- 总是用中文回答用户
- 总是相对详细地编写中文注释
- 不要创建模拟版本或模拟数据
- 如果是 go 程序的检查错误中需要新建一个程序来小范围测试错误原因的，一定要新建一个文件夹，在文件夹里面新建测试程序，这样才能避免 main 的冲突
- 一个目录下面可能有多个程序
- 你一定要记住你当前在那个程序的目录下
- 在修改某个程序需创建测试程序的时候，要在正在修改这个程序的目录下创建测试程序
- 运行调试的时候也要注意这些路径 

https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/private/position
    这个是 bybit 的私有 ws 订阅持仓文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/private/execution
    这个是 bybit 的私有 ws 订阅成交文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/private/fast-execution
    这个是 bybit 的私有 ws 订阅快速成交文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/private/order
    这个是 bybit 的私有 ws 订阅订单文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/private/wallet
    这个是 bybit 的私有 ws 订阅钱包文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/private/greek
    这个是 bybit 的私有 ws 订阅希腊字母文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/private/dcp
    这个是 bybit 的私有 ws 订阅 dcp 文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/websocket/trade/guideline
    这个是 bybit 的私有 ws 下单交易指南文档，你一定要仔细阅读，不要遗漏任何细节

    以下是 关于 bybit的 reset api 交易文档，你一定要仔细阅读，不要遗漏任何细节
https://bybit-exchange.github.io/docs/zh-TW/v5/order/create-order
https://bybit-exchange.github.io/docs/zh-TW/v5/order/amend-order
https://bybit-exchange.github.io/docs/zh-TW/v5/order/cancel-order
https://bybit-exchange.github.io/docs/zh-TW/v5/order/open-order
https://bybit-exchange.github.io/docs/zh-TW/v5/order/cancel-all
https://bybit-exchange.github.io/docs/zh-TW/v5/order/order-list
https://bybit-exchange.github.io/docs/zh-TW/v5/order/execution
https://bybit-exchange.github.io/docs/zh-TW/v5/order/batch-place
https://bybit-exchange.github.io/docs/zh-TW/v5/order/batch-amend
https://bybit-exchange.github.io/docs/zh-TW/v5/order/batch-cancel
https://bybit-exchange.github.io/docs/zh-TW/v5/order/dcp


-https://bybit-exchange.github.io/docs/v5/intro
  这个是 bybit 的 api 文档(索引页，里面有所有交互分类用到的 api 包括 webcoekt)，你一定要仔细阅读，不要遗漏任何细节 所有用到的 api(rest api 和 webcoekt api) 都要仔细阅读
