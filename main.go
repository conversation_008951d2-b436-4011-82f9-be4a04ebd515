package main

import (
	"bufio"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sort"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/atotto/clipboard"
	"github.com/shopspring/decimal"
)

// DefaultParams 默认参数配置
type DefaultParams struct {
	Symbol            string  // 交易对
	SlippageTolerance float64 // 滑点容忍度
	StopSlippage      float64 // 停止滑点
	MarketTimeout     int     // 市价单超时时间（秒）
	VolumeThreshold   float64 // 盘口量阈值
	PostOnlyMode      bool    // 是否只允许限价成交（POST_ONLY模式）
}

// 全局默认参数
var globalDefaults = &DefaultParams{
	Symbol:            "ETHUSDT",
	SlippageTolerance: 0.01, // 1%
	StopSlippage:      0.01, // 1%
	MarketTimeout:     5,    // 5秒
	VolumeThreshold:   0.5,  // 50%
	PostOnlyMode:      true, // 默认仅限挂单成交
}

// 状态输出控制变量
var (
	statusOutputEnabled  = false // 状态输出默认关闭
	statusOutputInterval = 10    // 状态输出间隔（秒）
	statusTicker         *time.Ticker
)

// 全局事件日志管理器
var globalEventLogger *EventLogger

func main() {
	// 解析命令行参数
	var (
		apiMode = flag.Bool("api", false, "启用API服务模式（在交互模式基础上额外开启API服务）")
		port    = flag.String("port", "5899", "API服务端口")
	)
	flag.Parse()

	// 设置日志格式
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("动态对冲系统启动...")

	// 加载配置
	log.Printf("📄 加载内嵌配置...")
	err := LoadEmbeddedConfig()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化币安客户端
	log.Println("🔴 初始化币安API连接...")
	err = InitBinanceClient()
	if err != nil {
		log.Fatalf("初始化币安客户端失败: %v", err)
	}

	// 初始化全局市场数据管理器
	log.Println("🌐 初始化全局市场数据管理器...")
	mdm := GetMarketDataManager()
	err = mdm.Start()
	if err != nil {
		log.Fatalf("初始化市场数据管理器失败: %v", err)
	}

	// 创建任务管理器
	taskManager := NewTaskManager()

	// 加载持久化数据
	log.Println("📄 加载持久化数据...")
	if err := taskManager.LoadData(); err != nil {
		log.Printf("⚠️  加载数据失败: %v", err)
		log.Println("将以空数据状态启动")
	}

	// 启动数据管理器
	taskManager.StartDataManager()

	// 设置全局事件日志管理器
	globalEventLogger = taskManager.eventLogger

	// 设置日志拦截器，将所有日志输出同时保存到事件日志
	SetupLogInterceptor(globalEventLogger)

	// 根据模式运行
	if *apiMode {
		runAPIMode(taskManager, *port)
	} else {
		runTestMode(taskManager)
	}
}

// runTestMode 运行本地测试模式
func runTestMode(taskManager *TaskManager) {
	log.Println("启动本地测试模式...")
	log.Println("💡 使用交互命令手动创建任务，输入 'help' 查看可用命令")

	// 显示交互命令帮助
	printInteractiveHelp()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动终端交互处理器（独立goroutine，不影响主循环性能）
	commandChan := make(chan string, 10) // 缓冲通道，避免阻塞
	go startInteractiveTerminal(taskManager, commandChan)

	// 初始化状态输出ticker
	statusTicker = time.NewTicker(time.Duration(statusOutputInterval) * time.Second)
	defer statusTicker.Stop()

	for {
		select {
		case <-sigChan:
			log.Println("收到停止信号，正在停止所有任务...")
			// 停止所有任务（跳过平仓）
			tasks := taskManager.GetAllTasks()
			for taskID := range tasks {
				err := taskManager.StopTaskWithOptions(taskID, true) // 跳过平仓
				if err != nil {
					log.Printf("停止任务 %s 失败: %v", taskID, err)
				}
			}
			time.Sleep(2 * time.Second) // 等待任务完全停止

			// 保存数据
			log.Println("💾 保存数据...")
			if err := taskManager.SaveData(); err != nil {
				log.Printf("❌ 保存数据失败: %v", err)
			}

			// 关闭事件日志管理器
			if globalEventLogger != nil {
				globalEventLogger.Close()
			}

			// 关闭全局市场数据管理器
			log.Println("🌐 关闭全局市场数据管理器...")
			mdm := GetMarketDataManager()
			mdm.Stop()

			// 恢复原始日志输出
			RestoreOriginalLog()

			log.Println("测试模式结束")
			return

		case command := <-commandChan:
			// 处理用户命令（非阻塞，快速处理）
			go handleInteractiveCommand(taskManager, command)

		case <-statusTicker.C:
			// 根据状态输出开关决定是否输出任务状态
			if statusOutputEnabled {
				tasks := taskManager.GetAllTasks()
				if len(tasks) > 0 {
					printAllTasksStatus(taskManager)
				}
			}
		}
	}
}

// runAPIMode 运行API服务模式（包含交互功能）
func runAPIMode(taskManager *TaskManager, port string) {
	log.Printf("启动API服务模式（包含交互功能），端口: %s", port)
	log.Println("💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令")

	// 创建API服务器
	apiServer := NewAPIServer(taskManager, port)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 在goroutine中启动API服务器
	go func() {
		if err := apiServer.Start(); err != nil {
			log.Fatalf("API服务器启动失败: %v", err)
		}
	}()

	log.Printf("API服务器已启动，访问地址: http://localhost:%s", port)
	log.Println("API接口:")
	log.Println("  POST /task                           - 创建对冲任务")
	log.Println("  POST /task/stop                      - 停止对冲任务")
	log.Println("  GET  /tasks                          - 获取所有任务")
	log.Println("  GET  /task/{id}                      - 获取单个任务")
	log.Println("期权系统API接口:")
	log.Println("  POST /api/tasks                      - 创建对冲任务(期权系统)")
	log.Println("  PUT  /api/tasks/{id}/amount          - 更新任务数量")
	log.Println("  GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务")

	// 显示交互命令帮助
	printInteractiveHelp()

	// 启动终端交互处理器（独立goroutine，不影响主循环性能）
	commandChan := make(chan string, 10) // 缓冲通道，避免阻塞
	go startInteractiveTerminal(taskManager, commandChan)

	// 初始化状态输出ticker
	statusTicker = time.NewTicker(time.Duration(statusOutputInterval) * time.Second)
	defer statusTicker.Stop()

	for {
		select {
		case <-sigChan:
			log.Println("收到停止信号，正在停止所有任务...")
			// 停止所有任务（跳过平仓）
			tasks := taskManager.GetAllTasks()
			for taskID := range tasks {
				err := taskManager.StopTaskWithOptions(taskID, true) // 跳过平仓
				if err != nil {
					log.Printf("停止任务 %s 失败: %v", taskID, err)
				}
			}
			time.Sleep(2 * time.Second) // 等待任务完全停止

			// 保存数据
			log.Println("💾 保存数据...")
			if err := taskManager.SaveData(); err != nil {
				log.Printf("❌ 保存数据失败: %v", err)
			}

			// 关闭事件日志管理器
			if globalEventLogger != nil {
				globalEventLogger.Close()
			}

			// 关闭全局市场数据管理器
			log.Println("🌐 关闭全局市场数据管理器...")
			mdm := GetMarketDataManager()
			mdm.Stop()

			// 恢复原始日志输出
			RestoreOriginalLog()

			log.Println("API服务模式结束")
			return

		case command := <-commandChan:
			// 处理用户命令（非阻塞，快速处理）
			go handleInteractiveCommand(taskManager, command)

		case <-statusTicker.C:
			// 根据状态输出开关决定是否输出任务状态
			if statusOutputEnabled {
				tasks := taskManager.GetAllTasks()
				if len(tasks) > 0 {
					printAllTasksStatus(taskManager)
				}
			}
		}
	}
}

// printTaskStatus 打印任务状态
func printTaskStatus(task *HedgeTask) {
	task.mutex.RLock()
	defer task.mutex.RUnlock()

	// 计算各种目标价格
	var openTargetPrice, closeTargetPrice, hedgePrice decimal.Decimal

	if task.Direction == DirectionLong {
		// 做多对冲：价格上涨时开多仓对冲
		openTargetPrice = task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))  // 开仓目标价 = 目标价 + 阈值
		closeTargetPrice = task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate)) // 平仓目标价 = 目标价 - 阈值
		hedgePrice = task.TargetPrice                                                // 动态对冲价 = 目标价
	} else {
		// 做空对冲：价格下跌时开空仓对冲
		openTargetPrice = task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))  // 开仓目标价 = 目标价 - 阈值
		closeTargetPrice = task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate)) // 平仓目标价 = 目标价 + 阈值
		hedgePrice = task.TargetPrice                                                // 动态对冲价 = 目标价
	}

	// 显示任务状态
	log.Printf("=== 任务状态 %s ===", task.ID)
	log.Printf("交易对: %s | 方向: %s | 阈值: %.3f%%",
		task.Symbol, task.Direction, task.StopRate.Mul(decimal.NewFromInt(100)).InexactFloat64())
	log.Printf("开仓目标价: %s | 平仓目标价: %s | 动态对冲价: %s",
		openTargetPrice.String(), closeTargetPrice.String(), hedgePrice.String())

	if task.Position != nil {
		log.Printf("当前仓位: %s %s @ %s | 总交易: %d | 总盈亏: %s",
			task.Position.Side, task.Position.Size.String(), task.Position.EntryPrice.String(),
			task.Statistics.TotalTrades, task.Statistics.TotalPnL.String())
	} else {
		log.Printf("当前仓位: 无 | 总交易: %d | 总盈亏: %s",
			task.Statistics.TotalTrades, task.Statistics.TotalPnL.String())
	}

	log.Printf("总手续费: %s | 总损耗: %s",
		task.Statistics.TotalFees.String(), task.Statistics.TotalPriceLoss.String())

	// 显示最近的订单成交记录（最多显示5条）
	if len(task.Events) > 0 {
		log.Printf("--- 最近订单成交记录 ---")
		startIdx := len(task.Events) - 5
		if startIdx < 0 {
			startIdx = 0
		}

		for i := startIdx; i < len(task.Events); i++ {
			event := task.Events[i]
			eventTime := event.Timestamp.Format("15:04:05")

			// 根据OrderType和ExecutionType字段判断成交方式
			var tradeType string

			// 处理特殊状态
			if event.OrderType == OrderTypeCancelled {
				tradeType = "撤单"
			} else if event.OrderType == OrderTypeFailed {
				tradeType = "失败"
			} else if strings.HasPrefix(event.OrderID, "cancelled_") {
				tradeType = "撤单"
			} else if strings.HasPrefix(event.OrderID, "failed_") {
				tradeType = "失败"
			} else {
				// 正常成交，显示订单类型和实际成交方式
				orderTypeDesc := "限价"
				if event.OrderType == OrderTypeMarket {
					orderTypeDesc = "市价"
				}

				executionTypeDesc := ""
				if event.ExecutionType == ExecutionTypeMaker {
					executionTypeDesc = "(挂单)"
				} else if event.ExecutionType == ExecutionTypeTaker {
					executionTypeDesc = "(吃单)"
				}

				tradeType = orderTypeDesc + executionTypeDesc
			}

			log.Printf("%s | %s | %s | 理论价: %s | 实际价: %s | 损耗: %s | 订单ID: %s",
				eventTime, event.Type, tradeType,
				event.TheoreticalPrice.String(), event.ActualPrice.String(), event.PriceLoss.String(), event.OrderID)
		}
	}

	log.Printf("========================")
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println("动态对冲系统 - 使用说明")
	fmt.Println("========================")
	fmt.Println()
	fmt.Println("运行模式:")
	fmt.Println("  默认模式                 交互测试模式（默认）")
	fmt.Println("  --api                   启用API服务模式（在交互模式基础上额外开启API服务）")
	fmt.Println("  --port=5879             API服务端口（默认5879）")
	fmt.Println("  --config=config.yaml    配置文件路径（默认config.yaml）")
	fmt.Println()
	fmt.Println("配置文件（config.yaml）:")
	fmt.Println("  binance:")
	fmt.Println("    api_key: \"你的API密钥\"")
	fmt.Println("    secret_key: \"你的密钥\"")
	fmt.Println("    testnet: true  # 是否使用测试网")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  ./hedge-system                                   # 启动交互测试模式")
	fmt.Println("  ./hedge-system --config=my_config.yaml          # 使用指定配置文件启动交互模式")
	fmt.Println("  ./hedge-system --api --port=5879                # 启动API服务模式（包含交互功能）")
	fmt.Println()
	fmt.Println("模式说明:")
	fmt.Println("  - 交互模式：支持命令行交互操作，可手动创建和管理对冲任务")
	fmt.Println("  - API模式：在交互模式基础上额外提供HTTP API接口，支持程序化调用")
	fmt.Println("  - API模式下仍可使用所有交互命令进行手动操作")
	fmt.Println()
	fmt.Println("注意:")
	fmt.Println("  - 系统使用真实币安API进行交易")
	fmt.Println("  - 请在配置文件中设置有效的API密钥")
	fmt.Println("  - 建议先在测试网环境验证策略")
	fmt.Println("  - 确保API密钥有期货交易权限")
	fmt.Println("  - 请谨慎设置交易参数")
}

// printInteractiveHelp 打印交互命令帮助
func printInteractiveHelp() {
	log.Println()
	log.Println("🔧 === 交互命令帮助 ===")
	log.Println("📋 任务管理:")
	log.Println("   add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]")
	log.Println("      示例: add long 3500 0.0005")
	log.Println("      示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5")
	log.Println()
	log.Println("   stop <task_id>                           - 停止指定任务")
	log.Println("   list                                     - 列出所有任务")
	log.Println()
	log.Println("⚙️ 参数配置:")
	log.Println("   set <参数名> <值>                         - 设置默认参数")
	log.Println("      参数名: symbol, slippage, stop_slippage, timeout, volume, post_only")
	log.Println("   show defaults                            - 显示当前默认参数")
	log.Println()
	log.Println("📊 状态控制:")
	log.Println("   status                                   - 查看状态输出设置")
	log.Println("   status on [间隔秒数]                     - 启动状态输出（默认10秒）")
	log.Println("   status off                               - 停止状态输出")
	log.Println()
	log.Println("📋 数据导出:")
	log.Println("   export | detail                          - 导出详细状态到剪切板")
	log.Println()
	log.Println("💾 数据管理:")
	log.Println("   save                                     - 手动保存数据到本地文件")
	log.Println("   saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)")
	log.Println("   backup [on|off]                          - 控制备份功能(默认关闭)")
	log.Println()
	log.Println("📋 事件查看:")
	log.Println("   events <task_id>                         - 查看指定任务的所有事件日志")
	log.Println()
	log.Println("🎛️  批量控制:")
	log.Println("   startall                                 - 启动所有已停止的任务")
	log.Println("   stopall                                  - 停止所有运行中的任务")
	log.Println("   stopall --skip-close                     - 停止所有任务(跳过平仓)")
	log.Println()
	log.Println("🗑️  任务管理:")
	log.Println("   delete <task_id>                         - 删除指定任务及其所有记录")
	log.Println()
	log.Println("🔧 其他:")
	log.Println("   help                                     - 显示此帮助信息")
	log.Println("   quit | exit                              - 退出程序")
	log.Println("========================")
	log.Println()
}

// startInteractiveTerminal 启动终端交互处理器
func startInteractiveTerminal(taskManager *TaskManager, commandChan chan<- string) {
	// 使用bufio.Scanner读取用户输入，避免阻塞主循环
	scanner := bufio.NewScanner(os.Stdin)

	for {
		// 非阻塞地读取用户输入
		if scanner.Scan() {
			command := strings.TrimSpace(scanner.Text())
			if command != "" {
				// 将命令发送到主循环处理，使用非阻塞发送
				select {
				case commandChan <- command:
					// 命令已发送
				default:
					// 通道满了，丢弃命令并提示
					log.Println("⚠️  命令处理繁忙，请稍后重试")
				}
			}
		}

		// 检查扫描错误
		if err := scanner.Err(); err != nil {
			log.Printf("读取输入错误: %v", err)
			break
		}
	}
}

// handleInteractiveCommand 处理交互命令
func handleInteractiveCommand(taskManager *TaskManager, command string) {
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return
	}

	// 如果正在等待删除确认，处理确认输入
	if waitingForDeleteConfirmation {
		handleDeleteConfirmation(taskManager, command)
		return
	}

	switch strings.ToLower(parts[0]) {
	case "add":
		handleAddTaskCommand(taskManager, parts[1:])
	case "stop":
		handleStopTaskCommand(taskManager, parts[1:])
	case "list":
		handleListTasksCommand(taskManager)
	case "set":
		handleSetDefaultCommand(parts[1:])
	case "show":
		if len(parts) > 1 && strings.ToLower(parts[1]) == "defaults" {
			handleShowDefaultsCommand()
		} else {
			log.Printf("❌ 未知show命令: %s，输入 'help' 查看帮助", strings.Join(parts[1:], " "))
		}
	case "status":
		handleStatusCommand(parts[1:])
	case "export", "detail":
		handleExportCommand(taskManager)
	case "save":
		handleSaveCommand(taskManager)
	case "events":
		handleEventsCommand(taskManager, parts[1:])
	case "startall":
		handleStartAllCommand(taskManager)
	case "stopall":
		handleStopAllCommand(taskManager, parts[1:])
	case "saveinterval":
		handleSaveIntervalCommand(taskManager, parts[1:])
	case "backup":
		handleBackupCommand(taskManager, parts[1:])
	case "delete", "del", "remove", "rm":
		handleDeleteCommand(taskManager, parts[1:])
	case "help":
		printInteractiveHelp()
	case "debug-market":
		debugMarketData()
	case "fix-market":
		fixMarketDataSubscriptions(taskManager)
	case "test-ws":
		testWebSocketConnection()
	case "check-order":
		if len(parts) < 2 {
			fmt.Println("用法: check-order <clientOrderId>")
		} else {
			checkOrderStatus(parts[1])
		}
	case "quit", "exit":
		log.Println("正在退出程序...")
		// 退出前保存数据
		if err := taskManager.SaveData(); err != nil {
			log.Printf("❌ 保存数据失败: %v", err)
		}
		// 关闭事件日志管理器
		if globalEventLogger != nil {
			globalEventLogger.Close()
		}
		// 恢复原始日志输出
		RestoreOriginalLog()
		os.Exit(0)
	default:
		log.Printf("❌ 未知命令: %s，输入 'help' 查看帮助", parts[0])
	}
}

// handleAddTaskCommand 处理新增任务命令
func handleAddTaskCommand(taskManager *TaskManager, args []string) {
	if len(args) < 4 {
		log.Println("❌ 参数不足，格式: add <long|short> <目标价> <阈值> <数量> [可选参数...]")
		log.Println("   示例: add long 3500 0.002 0.01")
		log.Println("   可选参数: slippage=0.01 stop_slippage=0.01 timeout=5 volume=0.5")
		return
	}

	direction := strings.ToLower(args[0])
	if direction != "long" && direction != "short" {
		log.Println("❌ 方向必须是 'long' 或 'short'")
		return
	}

	// 解析必需参数
	targetPrice, err := strconv.ParseFloat(args[1], 64)
	if err != nil {
		log.Printf("❌ 目标价格格式错误: %s", args[1])
		return
	}

	stopRate, err := strconv.ParseFloat(args[2], 64)
	if err != nil {
		log.Printf("❌ 阈值格式错误: %s", args[2])
		return
	}

	amount, err := strconv.ParseFloat(args[3], 64)
	if err != nil {
		log.Printf("❌ 数量格式错误: %s", args[3])
		return
	}

	// 使用全局默认值初始化任务请求
	taskReq := &TaskRequest{
		Symbol:            globalDefaults.Symbol,
		Direction:         direction,
		TargetPrice:       targetPrice,
		StopRate:          stopRate,
		Amount:            amount,
		SlippageTolerance: globalDefaults.SlippageTolerance,
		StopSlippage:      globalDefaults.StopSlippage,
		MarketTimeout:     globalDefaults.MarketTimeout,
		VolumeThreshold:   globalDefaults.VolumeThreshold,
		PostOnlyMode:      globalDefaults.PostOnlyMode,
		SmartOrderEnabled: true, // 默认启用智能订单
	}

	// 解析可选参数
	for i := 4; i < len(args); i++ {
		param := args[i]
		if !strings.Contains(param, "=") {
			log.Printf("⚠️  忽略无效参数格式: %s (应为 key=value)", param)
			continue
		}

		parts := strings.SplitN(param, "=", 2)
		key := strings.ToLower(strings.TrimSpace(parts[0]))
		value := strings.TrimSpace(parts[1])

		switch key {
		case "slippage":
			if val, err := strconv.ParseFloat(value, 64); err == nil && val >= 0 && val <= 1 {
				taskReq.SlippageTolerance = val
				log.Printf("📊 使用自定义滑点容忍度: %.3f%%", val*100)
			} else {
				log.Printf("⚠️  忽略无效滑点值: %s", value)
			}

		case "stop_slippage":
			if val, err := strconv.ParseFloat(value, 64); err == nil && val >= 0 && val <= 1 {
				taskReq.StopSlippage = val
				log.Printf("📊 使用自定义停止滑点: %.3f%%", val*100)
			} else {
				log.Printf("⚠️  忽略无效停止滑点值: %s", value)
			}

		case "timeout":
			if val, err := strconv.Atoi(value); err == nil && val >= 1 && val <= 300 {
				taskReq.MarketTimeout = val
				log.Printf("📊 使用自定义超时时间: %d秒", val)
			} else {
				log.Printf("⚠️  忽略无效超时值: %s", value)
			}

		case "volume":
			if val, err := strconv.ParseFloat(value, 64); err == nil && val >= 0 && val <= 1 {
				taskReq.VolumeThreshold = val
				log.Printf("📊 使用自定义盘口量阈值: %.1f%%", val*100)
			} else {
				log.Printf("⚠️  忽略无效盘口量阈值: %s", value)
			}

		case "symbol":
			if len(value) >= 6 {
				taskReq.Symbol = strings.ToUpper(value)
				log.Printf("📊 使用自定义交易对: %s", taskReq.Symbol)
			} else {
				log.Printf("⚠️  忽略无效交易对: %s", value)
			}

		case "post_only":
			lowerValue := strings.ToLower(value)
			var boolValue bool
			if lowerValue == "true" || lowerValue == "1" || lowerValue == "on" || lowerValue == "yes" {
				boolValue = true
			} else if lowerValue == "false" || lowerValue == "0" || lowerValue == "off" || lowerValue == "no" {
				boolValue = false
			} else {
				log.Printf("⚠️  忽略无效post_only值: %s (应为 true/false)", value)
				continue
			}
			taskReq.PostOnlyMode = boolValue
			modeDesc := "允许立即成交"
			if boolValue {
				modeDesc = "仅限挂单成交"
			}
			log.Printf("📊 使用自定义限价单模式: %s", modeDesc)

		case "smart_order":
			lowerValue := strings.ToLower(value)
			var boolValue bool
			if lowerValue == "true" || lowerValue == "1" || lowerValue == "on" || lowerValue == "yes" {
				boolValue = true
			} else if lowerValue == "false" || lowerValue == "0" || lowerValue == "off" || lowerValue == "no" {
				boolValue = false
			} else {
				log.Printf("⚠️  忽略无效smart_order值: %s (应为 true/false)", value)
				continue
			}
			taskReq.SmartOrderEnabled = boolValue
			modeDesc := "传统订单"
			if boolValue {
				modeDesc = "智能订单"
			}
			log.Printf("📊 使用订单模式: %s", modeDesc)

		default:
			log.Printf("⚠️  忽略未知参数: %s", key)
		}
	}

	// 添加任务
	task, err := taskManager.AddTask(taskReq)
	if err != nil {
		log.Printf("❌ 创建任务失败: %v", err)
		return
	}

	log.Printf("✅ 任务创建成功: %s", task.ID)
	log.Printf("📊 任务参数: 交易对=%s, 方向=%s, 目标价=%.2f, 阈值=%.4f%%, 数量=%.4f",
		task.Symbol, task.Direction, targetPrice, stopRate*100, amount)

	postOnlyDesc := "允许立即成交"
	if taskReq.PostOnlyMode {
		postOnlyDesc = "仅限挂单成交"
	}
	log.Printf("📊 高级参数: 滑点=%.3f%%, 停止滑点=%.3f%%, 超时=%d秒, 盘口阈值=%.1f%%, 限价模式=%s",
		taskReq.SlippageTolerance*100, taskReq.StopSlippage*100, taskReq.MarketTimeout, taskReq.VolumeThreshold*100, postOnlyDesc)
}

// handleStopTaskCommand 处理停止任务命令
func handleStopTaskCommand(taskManager *TaskManager, args []string) {
	if len(args) < 1 {
		log.Println("❌ 缺少任务ID，格式: stop <task_id>")
		return
	}

	taskID := args[0]
	err := taskManager.StopTask(taskID)
	if err != nil {
		log.Printf("❌ 停止任务失败: %v", err)
		return
	}

	log.Printf("✅ 任务 %s 已停止", taskID)
}

// handleListTasksCommand 处理列出任务命令
func handleListTasksCommand(taskManager *TaskManager) {
	tasks := taskManager.GetAllTasks()
	if len(tasks) == 0 {
		log.Println("📋 当前没有运行的任务")
		return
	}

	// 转换为切片并按交易次数排序
	type TaskInfo struct {
		Task      *HedgeTask
		ID        string
		StartTime time.Time
	}

	var taskList []TaskInfo
	for taskID, task := range tasks {
		// 修复：直接使用任务的CreatedAt时间，而不是从ID解析
		taskList = append(taskList, TaskInfo{
			Task:      task,
			ID:        taskID,
			StartTime: task.CreatedAt,
		})
	}

	// 按交易次数降序排序
	sort.Slice(taskList, func(i, j int) bool {
		return taskList[i].Task.Statistics.TotalTrades > taskList[j].Task.Statistics.TotalTrades
	})

	// 计算总统计
	var totalTrades int
	var totalPriceLoss decimal.Decimal
	var totalFees decimal.Decimal

	// 打印表头
	log.Println("任务ID  状态 方向 目标价 阈值   数量   仓位状态               交易 开始   持续 总盈亏 平均损耗 手续费 限价模式 来源    来源名")
	log.Println("__________________________________________________________________________________________________________________________")
	log.Println("")
	// 打印每个任务
	for _, taskInfo := range taskList {
		task := taskInfo.Task

		// 计算显示信息
		taskIDShort := taskInfo.ID
		if len(taskIDShort) > 7 {
			taskIDShort = taskIDShort[:7]
		}

		// 任务状态
		status := "运行"
		if task.Status == StatusStopped {
			status = "停止"
		} else if task.Status == StatusError {
			status = "错误"
		}

		direction := "L"
		if task.Direction == DirectionShort {
			direction = "S"
		}

		targetPrice := int(task.TargetPrice.InexactFloat64())
		// 修复阈值显示精度问题，使用字符串格式化确保精确显示
		stopRatePercent := task.StopRate.Mul(decimal.NewFromInt(100))
		stopRateStr := stopRatePercent.StringFixed(3) + "%"

		// 如果阈值字符串太长，截断显示
		if len(stopRateStr) > 6 {
			stopRateStr = stopRateStr[:6]
		}

		// 设定的下单数量
		amountStr := fmt.Sprintf("%.3f", task.Amount.InexactFloat64())
		// 如果数量字符串太长，截断显示
		if len(amountStr) > 6 {
			amountStr = amountStr[:6]
		}

		// 仓位状态
		positionInfo := "无仓位"
		if task.Position != nil {
			if task.Position.Side == SideLong {
				positionInfo = fmt.Sprintf("L%.2f@%d",
					task.Position.Size.InexactFloat64(),
					int(task.Position.EntryPrice.InexactFloat64()))
			} else {
				positionInfo = fmt.Sprintf("S%.2f@%d",
					task.Position.Size.InexactFloat64(),
					int(task.Position.EntryPrice.InexactFloat64()))
			}
		}
		if len(positionInfo) > 13 {
			positionInfo = positionInfo[:13]
		}

		// 开始时间和持续时间 - 修复：使用task.CreatedAt而不是从ID解析
		startTimeStr := task.CreatedAt.Format("15:04")
		duration := time.Since(task.CreatedAt)
		durationStr := ""
		if duration.Hours() >= 1 {
			durationStr = fmt.Sprintf("%.0fh", duration.Hours())
		} else {
			durationStr = fmt.Sprintf("%.0fm", duration.Minutes())
		}

		// 统计信息
		totalPnLFloat := task.Statistics.TotalPnL.InexactFloat64()
		totalFeesFloat := task.Statistics.TotalFees.InexactFloat64()

		// 计算平均价格损耗
		avgPriceLoss := 0.0
		if task.Statistics.TotalTrades > 0 {
			avgPriceLoss = task.Statistics.TotalPriceLoss.InexactFloat64() / float64(task.Statistics.TotalTrades)
		}

		// Post Only模式显示
		postOnlyMode := "否"
		if task.PostOnlyMode {
			postOnlyMode = "是"
		}

		// 来源信息
		source := task.Source
		if source == "" {
			source = "manual" // 兼容旧数据
		}

		// 来源名逻辑
		sourceName := "-"
		if source == "options" && task.OptionContract != "" {
			sourceName = task.OptionContract
			// 如果来源名太长，截断显示
			if len(sourceName) > 22 {
				sourceName = sourceName[:19] + "..."
			}
		}

		// 打印任务行
		log.Printf("%-7s %-4s %-2s %6d %-6s %-6s %-18s %4d %6s %4s %6.2f %8.2f %6.2f %-3s %-8s %-22s",
			taskIDShort, status, direction, targetPrice, stopRateStr, amountStr, positionInfo,
			task.Statistics.TotalTrades, startTimeStr, durationStr,
			totalPnLFloat, avgPriceLoss, totalFeesFloat, postOnlyMode, source, sourceName)

		// 累计总统计
		totalTrades += task.Statistics.TotalTrades
		totalPriceLoss = totalPriceLoss.Add(task.Statistics.TotalPriceLoss)
		totalFees = totalFees.Add(task.Statistics.TotalFees)
	}

	// 计算总平均价格损耗
	totalAvgPriceLoss := 0.0
	if totalTrades > 0 {
		totalAvgPriceLoss = totalPriceLoss.InexactFloat64() / float64(totalTrades)
	}

	// 打印分隔线和合计
	log.Println("__________________________________________________________________________________________________________________________")
	log.Printf("%-7s %-4s %-4s %6s %-6s %-6s %-18s %4d %6s %4s %6s %8.2f %6.2f %-8s %-7s %-22s",
		"合计", "", "", "", "", "", "", totalTrades, "", "", "",
		totalAvgPriceLoss, totalFees.InexactFloat64(), "", "", "")
}

// printAllTasksStatus 打印所有任务状态
func printAllTasksStatus(taskManager *TaskManager) {
	tasks := taskManager.GetAllTasks()
	if len(tasks) == 0 {
		log.Println("📋 当前没有运行的任务")
		return
	}

	log.Printf("=== 所有任务状态 (%d个任务) ===", len(tasks))
	for _, task := range tasks {
		printTaskStatus(task)
	}
}

// handleSetDefaultCommand 处理设置默认参数命令
func handleSetDefaultCommand(args []string) {
	if len(args) < 2 {
		log.Println("❌ 参数不足，格式: set <参数名> <值>")
		log.Println("   可设置参数: symbol, slippage, stop_slippage, timeout, volume, post_only")
		return
	}

	paramName := strings.ToLower(args[0])
	paramValue := args[1]

	switch paramName {
	case "symbol":
		// 验证交易对格式（简单验证）
		if len(paramValue) < 6 || !strings.HasSuffix(strings.ToUpper(paramValue), "USDT") {
			log.Printf("❌ 交易对格式可能不正确: %s，建议使用如ETHUSDT格式", paramValue)
			return
		}
		globalDefaults.Symbol = strings.ToUpper(paramValue)
		log.Printf("✅ 默认交易对已设置为: %s", globalDefaults.Symbol)

	case "slippage":
		value, err := strconv.ParseFloat(paramValue, 64)
		if err != nil || value < 0 || value > 1 {
			log.Printf("❌ 滑点值格式错误或超出范围(0-1): %s", paramValue)
			return
		}
		globalDefaults.SlippageTolerance = value
		log.Printf("✅ 默认滑点容忍度已设置为: %.3f%% (%.6f)", value*100, value)

	case "stop_slippage":
		value, err := strconv.ParseFloat(paramValue, 64)
		if err != nil || value < 0 || value > 1 {
			log.Printf("❌ 停止滑点值格式错误或超出范围(0-1): %s", paramValue)
			return
		}
		globalDefaults.StopSlippage = value
		log.Printf("✅ 默认停止滑点已设置为: %.3f%% (%.6f)", value*100, value)

	case "timeout":
		value, err := strconv.Atoi(paramValue)
		if err != nil || value < 1 || value > 300 {
			log.Printf("❌ 超时时间格式错误或超出范围(1-300秒): %s", paramValue)
			return
		}
		globalDefaults.MarketTimeout = value
		log.Printf("✅ 默认市价单超时已设置为: %d秒", value)

	case "volume":
		value, err := strconv.ParseFloat(paramValue, 64)
		if err != nil || value < 0 || value > 1 {
			log.Printf("❌ 盘口量阈值格式错误或超出范围(0-1): %s", paramValue)
			return
		}
		globalDefaults.VolumeThreshold = value
		log.Printf("✅ 默认盘口量阈值已设置为: %.1f%% (%.3f)", value*100, value)

	case "post_only":
		value := strings.ToLower(paramValue)
		var boolValue bool
		if value == "true" || value == "1" || value == "on" || value == "yes" {
			boolValue = true
		} else if value == "false" || value == "0" || value == "off" || value == "no" {
			boolValue = false
		} else {
			log.Printf("❌ post_only参数格式错误，请使用: true/false, on/off, yes/no, 1/0")
			return
		}
		globalDefaults.PostOnlyMode = boolValue

		modeDesc := "允许立即成交"
		if boolValue {
			modeDesc = "仅限挂单成交"
		}
		log.Printf("✅ 限价单模式已设置为: %s (%t)", modeDesc, boolValue)

	default:
		log.Printf("❌ 未知参数: %s", paramName)
		log.Println("   可设置参数: symbol, slippage, stop_slippage, timeout, volume, post_only")
	}
}

// handleShowDefaultsCommand 显示当前默认参数
func handleShowDefaultsCommand() {
	log.Println("=== 当前默认参数 ===")
	log.Printf("📊 交易对: %s", globalDefaults.Symbol)
	log.Printf("📊 滑点容忍度: %.3f%% (%.6f)", globalDefaults.SlippageTolerance*100, globalDefaults.SlippageTolerance)
	log.Printf("📊 停止滑点: %.3f%% (%.6f)", globalDefaults.StopSlippage*100, globalDefaults.StopSlippage)
	log.Printf("📊 市价单超时: %d秒", globalDefaults.MarketTimeout)
	log.Printf("📊 盘口量阈值: %.1f%% (%.3f)", globalDefaults.VolumeThreshold*100, globalDefaults.VolumeThreshold)

	modeDesc := "允许立即成交"
	if globalDefaults.PostOnlyMode {
		modeDesc = "仅限挂单成交"
	}
	log.Printf("📊 限价单模式: %s (%t)", modeDesc, globalDefaults.PostOnlyMode)
	log.Println("==================")
}

// handleStatusCommand 处理状态输出控制命令
func handleStatusCommand(args []string) {
	if len(args) == 0 {
		// 查看当前状态
		status := "停用"
		if statusOutputEnabled {
			status = "启用"
		}
		log.Printf("📊 状态输出: %s | 间隔: %d秒", status, statusOutputInterval)
		return
	}

	switch strings.ToLower(args[0]) {
	case "on":
		// 启动状态输出
		interval := statusOutputInterval // 使用当前间隔作为默认值

		if len(args) > 1 {
			// 如果提供了间隔参数
			if newInterval, err := strconv.Atoi(args[1]); err == nil && newInterval > 0 && newInterval <= 300 {
				interval = newInterval
			} else {
				log.Printf("❌ 无效的间隔时间: %s，使用默认值: %d秒", args[1], statusOutputInterval)
			}
		}

		statusOutputEnabled = true
		statusOutputInterval = interval

		// 重新设置ticker
		if statusTicker != nil {
			statusTicker.Stop()
		}
		statusTicker = time.NewTicker(time.Duration(statusOutputInterval) * time.Second)

		log.Printf("✅ 状态输出已启动，间隔: %d秒", statusOutputInterval)

	case "off":
		// 停止状态输出
		statusOutputEnabled = false
		log.Println("✅ 状态输出已停止")

	default:
		log.Printf("❌ 未知状态命令: %s，使用 'status on/off [间隔]' 或 'status' 查看状态", args[0])
	}
}

// handleExportCommand 处理详细状态导出命令
func handleExportCommand(taskManager *TaskManager) {
	report := generateDetailedReport(taskManager)
	listReport := generateListReport(taskManager)

	// 保存到文件
	err := saveReportToFile(listReport, report)
	if err != nil {
		log.Printf("❌ 保存报告到文件失败: %v", err)
	}

	// 复制到剪切板
	err = clipboard.WriteAll(report)
	if err != nil {
		log.Printf("❌ 复制到剪切板失败: %v", err)
		log.Println("📋 详细状态报告:")
		log.Println(report)
	} else {
		log.Println("✅ 详细状态已复制到剪切板")
		// 同时在终端显示
		log.Println("📋 详细状态报告:")
		log.Println(report)
	}
}

// handleSaveCommand 处理手动保存命令
func handleSaveCommand(taskManager *TaskManager) {
	log.Println("💾 手动保存数据...")
	if err := taskManager.SaveData(); err != nil {
		log.Printf("❌ 保存数据失败: %v", err)
	} else {
		log.Println("✅ 数据保存成功")
	}
}

// handleEventsCommand 处理查看任务事件命令
func handleEventsCommand(taskManager *TaskManager, args []string) {
	if len(args) < 1 {
		log.Println("❌ 缺少任务ID，格式: events <task_id>")
		return
	}

	taskID := args[0]
	events, err := taskManager.GetTaskEvents(taskID)
	if err != nil {
		log.Printf("❌ 获取任务事件失败: %v", err)
		return
	}

	if len(events) == 0 {
		log.Printf("📋 任务 %s 暂无事件记录", taskID)
		return
	}

	log.Printf("📋 任务 %s 的事件记录 (共%d条):", taskID, len(events))
	log.Println("=" + strings.Repeat("=", 80))
	for _, event := range events {
		log.Println(event)
	}
	log.Println("=" + strings.Repeat("=", 80))
}

// handleStartAllCommand 处理启动所有任务命令
func handleStartAllCommand(taskManager *TaskManager) {
	log.Println("🚀 启动所有已停止的任务...")
	count, err := taskManager.StartAllTasks()
	if err != nil {
		log.Printf("❌ 批量启动失败: %v", err)
	} else {
		log.Printf("✅ 成功启动 %d 个任务", count)
	}
}

// handleStopAllCommand 处理停止所有任务命令
func handleStopAllCommand(taskManager *TaskManager, args []string) {
	skipClosePosition := false

	// 检查是否有 --skip-close 参数
	for _, arg := range args {
		if arg == "--skip-close" || arg == "-s" {
			skipClosePosition = true
			break
		}
	}

	if skipClosePosition {
		log.Println("⏹️  停止所有任务 (跳过平仓)...")
	} else {
		log.Println("⏹️  停止所有任务...")
	}

	count, err := taskManager.StopAllTasks(skipClosePosition)
	if err != nil {
		log.Printf("❌ 批量停止失败: %v", err)
	} else {
		if skipClosePosition {
			log.Printf("✅ 成功停止 %d 个任务 (已跳过平仓)", count)
		} else {
			log.Printf("✅ 成功停止 %d 个任务", count)
		}
	}
}

// handleSaveIntervalCommand 处理设置自动保存间隔命令
func handleSaveIntervalCommand(taskManager *TaskManager, args []string) {
	if len(args) < 1 {
		log.Println("❌ 缺少时间参数，格式: saveinterval <分钟数>")
		log.Println("   示例: saveinterval 5  (设置为5分钟)")
		return
	}

	minutes, err := strconv.Atoi(args[0])
	if err != nil || minutes <= 0 {
		log.Println("❌ 无效的时间参数，请输入正整数（分钟）")
		return
	}

	interval := time.Duration(minutes) * time.Minute
	taskManager.SetAutoSaveInterval(interval)
	log.Printf("✅ 自动保存间隔已设置为 %d 分钟", minutes)
}

// handleBackupCommand 处理备份控制命令
func handleBackupCommand(taskManager *TaskManager, args []string) {
	if len(args) < 1 {
		// 显示当前备份状态
		if taskManager.IsBackupEnabled() {
			log.Println("📁 备份功能状态: 已启用")
		} else {
			log.Println("📁 备份功能状态: 已禁用")
		}
		log.Println("   使用方法:")
		log.Println("   backup on   - 启用备份功能")
		log.Println("   backup off  - 禁用备份功能")
		return
	}

	switch args[0] {
	case "on", "enable", "true":
		taskManager.EnableBackup()
	case "off", "disable", "false":
		taskManager.DisableBackup()
	default:
		log.Println("❌ 无效的参数，请使用 'on' 或 'off'")
		log.Println("   backup on   - 启用备份功能")
		log.Println("   backup off  - 禁用备份功能")
	}
}

// 全局变量用于处理删除确认状态
var (
	pendingDeleteTaskID          string
	waitingForDeleteConfirmation bool
)

// handleDeleteCommand 处理删除任务命令
func handleDeleteCommand(taskManager *TaskManager, args []string) {
	if len(args) < 1 {
		log.Println("❌ 缺少任务ID，格式: delete <task_id>")
		log.Println("   示例: delete 1234567")
		return
	}

	taskID := args[0]

	// 检查任务是否存在
	if _, err := taskManager.GetTask(taskID); err != nil {
		log.Printf("❌ 任务 %s 不存在", taskID)
		return
	}

	// 设置等待确认状态
	pendingDeleteTaskID = taskID
	waitingForDeleteConfirmation = true

	// 确认删除操作
	log.Printf("⚠️  确认删除任务 %s 及其所有记录？这个操作不可撤销！", taskID)
	log.Println("   输入 'yes' 确认删除，其他任何输入取消操作")
}

// handleDeleteConfirmation 处理删除确认
func handleDeleteConfirmation(taskManager *TaskManager, confirmation string) {
	if !waitingForDeleteConfirmation {
		return
	}

	// 重置确认状态
	waitingForDeleteConfirmation = false
	taskID := pendingDeleteTaskID
	pendingDeleteTaskID = ""

	if strings.TrimSpace(strings.ToLower(confirmation)) == "yes" {
		if err := taskManager.DeleteTask(taskID); err != nil {
			log.Printf("❌ 删除任务失败: %v", err)
		} else {
			log.Printf("✅ 任务 %s 已成功删除", taskID)
		}
	} else {
		log.Println("❌ 删除操作已取消")
	}
}

// generateDetailedReport 生成详细状态报告
func generateDetailedReport(taskManager *TaskManager) string {
	var report strings.Builder

	// 报告头部
	report.WriteString("=== 动态对冲系统详细状态报告 ===\n")
	report.WriteString(fmt.Sprintf("生成时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	tasks := taskManager.GetAllTasks()
	if len(tasks) == 0 {
		report.WriteString("📋 当前没有运行的任务\n")
		report.WriteString("=== 报告结束 ===\n")
		return report.String()
	}

	// 计算总统计
	totalTasks := len(tasks)
	runningTasks := 0
	totalTrades := 0
	var totalPriceLoss, totalFees, totalPnL decimal.Decimal

	for _, task := range tasks {
		if task.Status == "running" {
			runningTasks++
		}
		totalTrades += task.Statistics.TotalTrades
		totalPriceLoss = totalPriceLoss.Add(task.Statistics.TotalPriceLoss)
		totalFees = totalFees.Add(task.Statistics.TotalFees)
		totalPnL = totalPnL.Add(task.Statistics.TotalPnL)
	}

	// 任务总览
	report.WriteString("📋 任务总览:\n")
	report.WriteString(fmt.Sprintf("总任务数: %d | 运行中: %d | 已停止: %d\n",
		totalTasks, runningTasks, totalTasks-runningTasks))
	report.WriteString(fmt.Sprintf("总交易次数: %d | 总损耗: %.2f USDT | 总手续费: %.2f USDT | 总盈亏: %.2f USDT\n\n",
		totalTrades, totalPriceLoss.InexactFloat64(), totalFees.InexactFloat64(), totalPnL.InexactFloat64()))

	// 按任务ID排序
	type TaskInfo struct {
		ID   string
		Task *HedgeTask
	}
	var taskList []TaskInfo
	for taskID, task := range tasks {
		taskList = append(taskList, TaskInfo{ID: taskID, Task: task})
	}
	sort.Slice(taskList, func(i, j int) bool {
		return taskList[i].ID < taskList[j].ID
	})

	// 详细任务信息
	report.WriteString("📊 任务详情:\n\n")
	for i, taskInfo := range taskList {
		task := taskInfo.Task
		taskID := taskInfo.ID

		// 任务基本信息
		direction := "做多对冲"
		if task.Direction == DirectionShort {
			direction = "做空对冲"
		}

		report.WriteString(fmt.Sprintf("【任务%d】%s (%s)\n", i+1, taskID, direction))

		// Post Only模式显示
		postOnlyMode := "否"
		if task.PostOnlyMode {
			postOnlyMode = "是"
		}

		report.WriteString(fmt.Sprintf("├─ 基本信息: %s | 目标价: %.0f | 阈值: %.3f%% | 限价模式: %s\n",
			task.Symbol, task.TargetPrice.InexactFloat64(), task.StopRate.Mul(decimal.NewFromInt(100)).InexactFloat64(), postOnlyMode))

		// 运行状态
		status := task.Status
		if status == "" {
			status = "运行中"
		}
		startTime := task.CreatedAt.Format("15:04")
		duration := time.Since(task.CreatedAt)
		durationStr := ""
		if duration.Hours() >= 1 {
			durationStr = fmt.Sprintf("%.0fh", duration.Hours())
		} else {
			durationStr = fmt.Sprintf("%.0fm", duration.Minutes())
		}

		report.WriteString(fmt.Sprintf("├─ 运行状态: %s | 开始: %s | 持续: %s\n", status, startTime, durationStr))

		// 当前仓位
		positionStr := "无仓位"
		if task.Position != nil {
			positionStr = fmt.Sprintf("%s %.4f @ %.2f",
				task.Position.Side, task.Position.Size.InexactFloat64(), task.Position.EntryPrice.InexactFloat64())
		}
		report.WriteString(fmt.Sprintf("├─ 当前仓位: %s\n", positionStr))

		// 统计数据
		report.WriteString(fmt.Sprintf("├─ 统计数据: 交易%d次 | 损耗: %.2f | 手续费: %.2f | 盈亏: %.2f\n",
			task.Statistics.TotalTrades,
			task.Statistics.TotalPriceLoss.InexactFloat64(),
			task.Statistics.TotalFees.InexactFloat64(),
			task.Statistics.TotalPnL.InexactFloat64()))

		// 交易记录
		report.WriteString("└─ 交易记录:\n")
		if len(task.Events) == 0 {
			report.WriteString("   无交易记录\n")
		} else {
			for _, event := range task.Events {
				eventTime := event.Timestamp.Format("15:04:05")
				eventType := event.Type

				// 处理特殊状态
				var tradeType string
				if event.OrderType == OrderTypeCancelled {
					tradeType = "撤单"
				} else if event.OrderType == OrderTypeFailed {
					tradeType = "失败"
				} else {
					// 正常成交，显示订单类型和实际成交方式
					orderTypeDesc := "限价"
					if event.OrderType == OrderTypeMarket {
						orderTypeDesc = "市价"
					}

					executionTypeDesc := ""
					if event.ExecutionType == ExecutionTypeMaker {
						executionTypeDesc = "(挂单)"
					} else if event.ExecutionType == ExecutionTypeTaker {
						executionTypeDesc = "(吃单)"
					}

					tradeType = orderTypeDesc + executionTypeDesc
				}

				report.WriteString(fmt.Sprintf("   %s | %s | %s | 理论价: %.2f | 实际价: %.2f | 损耗: %+.2f | 订单ID: %s\n",
					eventTime, eventType, tradeType,
					event.TheoreticalPrice.InexactFloat64(),
					event.ActualPrice.InexactFloat64(),
					event.PriceLoss.InexactFloat64(),
					event.OrderID))
			}
		}
		report.WriteString("\n")
	}

	report.WriteString("=== 报告结束 ===\n")
	return report.String()
}

// generateListReport 生成任务列表报告
func generateListReport(taskManager *TaskManager) string {
	var report strings.Builder

	report.WriteString("=== 任务列表 ===\n")
	report.WriteString(fmt.Sprintf("生成时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	tasks := taskManager.GetAllTasks()
	if len(tasks) == 0 {
		report.WriteString("当前没有运行的任务\n")
		return report.String()
	}

	// 转换为切片并按交易次数排序
	type TaskInfo struct {
		Task      *HedgeTask
		ID        string
		StartTime time.Time
	}

	var taskList []TaskInfo
	for taskID, task := range tasks {
		// 从任务ID中提取时间戳
		startTime := task.CreatedAt
		taskList = append(taskList, TaskInfo{
			Task:      task,
			ID:        taskID,
			StartTime: startTime,
		})
	}

	// 按交易次数降序排序
	sort.Slice(taskList, func(i, j int) bool {
		return taskList[i].Task.Statistics.TotalTrades > taskList[j].Task.Statistics.TotalTrades
	})

	// 计算总统计
	var totalTrades int
	var totalPriceLoss decimal.Decimal
	var totalFees decimal.Decimal

	// 打印表头
	report.WriteString("任务ID  状态 方向 目标价 阈值   数量   仓位状态      交易 开始   持续 总盈亏 平均损耗 手续费 限价模式 来源    来源名\n")
	report.WriteString("------- ---- ---- ------ ------ ------ ------------- ---- ------ ---- ------ -------- ------ -------- ------- ----------------------\n")

	// 打印每个任务
	for _, taskInfo := range taskList {
		task := taskInfo.Task

		// 计算显示信息
		taskIDShort := taskInfo.ID
		if len(taskIDShort) > 7 {
			taskIDShort = taskIDShort[:7]
		}

		// 任务状态
		status := "运行"
		if task.Status == StatusStopped {
			status = "停止"
		} else if task.Status == StatusError {
			status = "错误"
		}

		direction := "L"
		if task.Direction == DirectionShort {
			direction = "S"
		}

		targetPrice := int(task.TargetPrice.InexactFloat64())
		stopRatePercent := task.StopRate.Mul(decimal.NewFromInt(100))
		stopRateStr := stopRatePercent.StringFixed(3) + "%"

		if len(stopRateStr) > 6 {
			stopRateStr = stopRateStr[:6]
		}

		// 设定的下单数量
		amountStr := fmt.Sprintf("%.3f", task.Amount.InexactFloat64())
		// 如果数量字符串太长，截断显示
		if len(amountStr) > 6 {
			amountStr = amountStr[:6]
		}

		// 仓位状态
		positionInfo := "无仓位"
		if task.Position != nil {
			if task.Position.Side == SideLong {
				positionInfo = fmt.Sprintf("L%.2f@%d",
					task.Position.Size.InexactFloat64(),
					int(task.Position.EntryPrice.InexactFloat64()))
			} else {
				positionInfo = fmt.Sprintf("S%.2f@%d",
					task.Position.Size.InexactFloat64(),
					int(task.Position.EntryPrice.InexactFloat64()))
			}
		}
		if len(positionInfo) > 13 {
			positionInfo = positionInfo[:13]
		}

		// 开始时间和持续时间
		startTimeStr := taskInfo.StartTime.Format("15:04")
		duration := time.Since(taskInfo.StartTime)
		durationStr := ""
		if duration.Hours() >= 1 {
			durationStr = fmt.Sprintf("%.0fh", duration.Hours())
		} else {
			durationStr = fmt.Sprintf("%.0fm", duration.Minutes())
		}

		// 统计信息
		totalPnLFloat := task.Statistics.TotalPnL.InexactFloat64()
		totalFeesFloat := task.Statistics.TotalFees.InexactFloat64()

		// 计算平均价格损耗
		avgPriceLoss := 0.0
		if task.Statistics.TotalTrades > 0 {
			avgPriceLoss = task.Statistics.TotalPriceLoss.InexactFloat64() / float64(task.Statistics.TotalTrades)
		}

		// Post Only模式显示
		postOnlyMode := "否"
		if task.PostOnlyMode {
			postOnlyMode = "是"
		}

		// 来源信息
		source := task.Source
		if source == "" {
			source = "manual" // 兼容旧数据
		}

		// 来源名逻辑
		sourceName := "-"
		if source == "options" && task.OptionContract != "" {
			sourceName = task.OptionContract
			// 如果来源名太长，截断显示
			if len(sourceName) > 22 {
				sourceName = sourceName[:19] + "..."
			}
		}

		// 打印任务行
		report.WriteString(fmt.Sprintf("%-7s %-4s %-4s %6d %-6s %-6s %-13s %4d %6s %4s %6.2f %8.2f %6.2f %-8s %-7s %-22s\n",
			taskIDShort, status, direction, targetPrice, stopRateStr, amountStr, positionInfo,
			task.Statistics.TotalTrades, startTimeStr, durationStr,
			totalPnLFloat, avgPriceLoss, totalFeesFloat, postOnlyMode, source, sourceName))

		// 累计总统计
		totalTrades += task.Statistics.TotalTrades
		totalPriceLoss = totalPriceLoss.Add(task.Statistics.TotalPriceLoss)
		totalFees = totalFees.Add(task.Statistics.TotalFees)
	}

	// 计算总平均价格损耗
	totalAvgPriceLoss := 0.0
	if totalTrades > 0 {
		totalAvgPriceLoss = totalPriceLoss.InexactFloat64() / float64(totalTrades)
	}

	// 打印分隔线和合计
	report.WriteString("------- ---- ---- ------ ------ ------ ------------- ---- ------ ---- ------ -------- ------ -------- ------- ----------------------\n")
	report.WriteString(fmt.Sprintf("%-7s %-4s %-4s %6s %-6s %-6s %-13s %4d %6s %4s %6s %8.2f %6.2f %-8s %-7s %-22s\n",
		"合计", "", "", "", "", "", "", totalTrades, "", "", "",
		totalAvgPriceLoss, totalFees.InexactFloat64(), "", "", ""))

	return report.String()
}

// saveReportToFile 保存报告到文件
func saveReportToFile(listReport, detailReport string) error {
	// 创建export文件夹
	exportDir := "export"
	err := os.MkdirAll(exportDir, 0755)
	if err != nil {
		return fmt.Errorf("创建export文件夹失败: %v", err)
	}

	// 生成文件名（以当前时间命名）
	now := time.Now()
	filename := fmt.Sprintf("hedge_report_%s.txt", now.Format("20060102_150405"))
	filepath := fmt.Sprintf("%s/%s", exportDir, filename)

	// 创建文件内容
	var content strings.Builder
	content.WriteString("=== 动态对冲系统导出报告 ===\n")
	content.WriteString(fmt.Sprintf("导出时间: %s\n\n", now.Format("2006-01-02 15:04:05")))

	// 添加任务列表
	content.WriteString(listReport)
	content.WriteString("\n\n")

	// 添加详细报告
	content.WriteString(detailReport)

	// 写入文件
	err = os.WriteFile(filepath, []byte(content.String()), 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	log.Printf("✅ 报告已保存到文件: %s", filepath)
	return nil
}

// debugMarketData 调试市场数据订阅状态
func debugMarketData() {
	log.Printf("🔍 开始调试市场数据订阅状态...")

	mdm := GetMarketDataManager()
	if mdm == nil {
		log.Printf("❌ 市场数据管理器未初始化")
		return
	}

	// 打印订阅状态
	mdm.PrintSubscriptionStatus()

	// 打印统计信息
	stats := mdm.GetStats()
	log.Printf("📊 市场数据统计: %+v", stats)
}

// fixMarketDataSubscriptions 修复市场数据订阅问题
func fixMarketDataSubscriptions(taskManager *TaskManager) {
	log.Printf("🔧 开始修复市场数据订阅问题...")

	mdm := GetMarketDataManager()
	if mdm == nil {
		log.Printf("❌ 市场数据管理器未初始化")
		return
	}

	// 获取所有活跃任务
	tasks := taskManager.GetAllTasks()
	log.Printf("📋 发现 %d 个任务", len(tasks))

	for _, task := range tasks {
		if task.SmartOrderEnabled && task.OrderBookChan == nil {
			log.Printf("🔧 任务 %s: 检测到缺失市场数据订阅，正在修复...", task.ID)

			// 重新订阅市场数据
			orderBookChan, err := mdm.Subscribe(task.ID, task.Symbol)
			if err != nil {
				log.Printf("❌ 任务 %s: 重新订阅失败: %v", task.ID, err)
			} else {
				task.OrderBookChan = orderBookChan
				log.Printf("✅ 任务 %s: 重新订阅成功", task.ID)
			}
		} else if task.SmartOrderEnabled && task.OrderBookChan != nil {
			log.Printf("✅ 任务 %s: 市场数据订阅正常", task.ID)
		} else {
			log.Printf("ℹ️ 任务 %s: 未启用智能订单，跳过", task.ID)
		}
	}

	// 再次打印状态
	mdm.PrintSubscriptionStatus()
}

// testWebSocketConnection 测试WebSocket连接
func testWebSocketConnection() {
	log.Printf("🔍 开始测试WebSocket连接...")

	mdm := GetMarketDataManager()
	if mdm == nil {
		log.Printf("❌ 市场数据管理器未初始化")
		return
	}

	// 打印当前连接状态
	mdm.PrintSubscriptionStatus()

	// 测试创建一个新的WebSocket连接
	symbol := "ETHUSDT"
	log.Printf("🔗 测试创建%s的WebSocket连接...", symbol)

	// 构建测试URL
	streamName := strings.ToLower(symbol) + "@depth20@100ms"

	// 测试期货端点
	futuresURL := fmt.Sprintf("wss://fstream.binance.com/ws/%s", streamName)
	log.Printf("🔗 期货端点: %s", futuresURL)

	// 测试现货端点
	spotURL := fmt.Sprintf("wss://stream.binance.com:9443/ws/%s", streamName)
	log.Printf("🔗 现货端点: %s", spotURL)

	// 检查当前使用的端点
	stats := mdm.GetStats()
	log.Printf("📊 当前连接统计: %+v", stats)
}

// checkOrderStatus 检查特定订单的状态
func checkOrderStatus(clientOrderId string) {
	log.Printf("🔍 开始查询订单状态: %s", clientOrderId)

	// 从clientOrderId中提取任务ID
	parts := strings.Split(clientOrderId, "_")
	if len(parts) < 2 {
		log.Printf("❌ 无效的客户端订单ID格式: %s", clientOrderId)
		return
	}

	taskID := parts[0]
	log.Printf("🔍 提取的任务ID: %s", taskID)

	// 获取任务管理器
	if globalTaskManager == nil {
		log.Printf("❌ 任务管理器未初始化")
		return
	}

	// 查找任务
	task := globalTaskManager.GetTask(taskID)
	if task == nil {
		log.Printf("❌ 未找到任务: %s", taskID)
		return
	}

	log.Printf("✅ 找到任务: %s", taskID)
	log.Printf("📋 任务状态: Symbol=%s, Direction=%s", task.Symbol, task.Direction)

	// 检查智能订单管理器状态
	if task.SmartOrderManager != nil {
		if task.SmartOrderManager.orderTracker != nil {
			log.Printf("📋 智能订单状态: OrderID=%s, ClientOrderID=%s",
				task.SmartOrderManager.orderTracker.OrderID,
				task.SmartOrderManager.orderTracker.ClientOrderID)
		} else {
			log.Printf("📋 智能订单状态: 无活跃订单跟踪器")
		}
	} else {
		log.Printf("📋 智能订单状态: 管理器未初始化")
	}

	// 检查仓位信息
	if task.Position != nil {
		log.Printf("📋 仓位信息: Side=%s, Size=%s", task.Position.Side, task.Position.Size.String())
	} else {
		log.Printf("📋 仓位信息: 无仓位")
	}

	// 尝试通过REST API查询订单状态
	log.Printf("🔍 通过REST API查询订单状态...")
	// 这里可以添加REST API查询逻辑
}
