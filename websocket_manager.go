package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"math/rand/v2"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// WebSocket管理器
type WebSocketManager struct {
	connection        *WSConnection
	eventChan         chan WSEvent
	stopChan          chan struct{}
	config            SmartOrderConfig
	mutex             sync.RWMutex
	isRunning         bool
	reconnectFunc     func() error
	lastMessageTime   time.Time          // 最后收到消息的时间
	connectionQuality *ConnectionQuality // 连接质量监控
	heartbeatTicker   *time.Ticker       // 心跳定时器
}

// 创建WebSocket管理器
func NewWebSocketManager(url string, config SmartOrderConfig) *WebSocketManager {
	return &WebSocketManager{
		connection: &WSConnection{
			url:           url,
			state:         WSStateDisconnected,
			maxReconnects: config.MaxReconnectAttempts,
			stopChan:      make(chan struct{}),
			reconnectChan: make(chan struct{}, 1),
		},
		eventChan:       make(chan WSE<PERSON>, config.ChannelBufferSize),
		stopChan:        make(chan struct{}),
		config:          config,
		lastMessageTime: time.Now(),
		connectionQuality: &ConnectionQuality{
			ConnectedAt:     time.Now(),
			LastMessageTime: time.Now(),
			IsHealthy:       false,
		},
	}
}

// 连接WebSocket
func (wsm *WebSocketManager) Connect() error {
	wsm.mutex.Lock()
	defer wsm.mutex.Unlock()

	if wsm.connection.state == WSStateConnected {
		return nil
	}

	wsm.connection.state = WSStateConnecting

	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
		TLSClientConfig:  &tls.Config{InsecureSkipVerify: false},
	}

	headers := http.Header{}
	headers.Set("User-Agent", "hedge-system/1.0")

	conn, _, err := dialer.Dial(wsm.connection.url, headers)
	if err != nil {
		wsm.connection.state = WSStateDisconnected
		return fmt.Errorf("WebSocket连接失败: %v", err)
	}

	wsm.connection.conn = conn
	wsm.connection.state = WSStateConnected
	wsm.connection.connectedAt = time.Now()
	wsm.connection.lastPing = time.Now()
	wsm.connection.lastPong = time.Now()
	wsm.connection.reconnectCount = 0

	// 更新连接质量统计
	wsm.connectionQuality.ConnectedAt = time.Now()
	wsm.connectionQuality.LastMessageTime = time.Now()
	wsm.connectionQuality.IsHealthy = true
	wsm.lastMessageTime = time.Now()

	log.Printf("WebSocket连接成功: %s", wsm.connection.url)

	// 启动读取、心跳和连接质量监控协程
	go wsm.readLoop()
	go wsm.enhancedHeartbeatLoop()
	go wsm.connectionQualityMonitor()

	return nil
}

// 断开连接
func (wsm *WebSocketManager) Disconnect() error {
	wsm.mutex.Lock()
	defer wsm.mutex.Unlock()

	if wsm.connection.state == WSStateDisconnected {
		return nil
	}

	wsm.connection.state = WSStateClosed
	close(wsm.connection.stopChan)

	if wsm.connection.conn != nil {
		wsm.connection.conn.Close()
		wsm.connection.conn = nil
	}

	log.Printf("WebSocket连接已断开: %s", wsm.connection.url)
	return nil
}

// 发送消息
func (wsm *WebSocketManager) SendMessage(message interface{}) error {
	log.Printf("🔵 WebSocket准备发送消息，连接状态检查...")

	wsm.mutex.RLock()
	defer wsm.mutex.RUnlock()

	if wsm.connection.state != WSStateConnected || wsm.connection.conn == nil {
		log.Printf("❌ WebSocket未连接，状态: %v, 连接: %v", wsm.connection.state, wsm.connection.conn != nil)
		return fmt.Errorf("WebSocket未连接")
	}
	log.Printf("✅ WebSocket连接状态正常")

	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("❌ 消息序列化失败: %v", err)
		return fmt.Errorf("消息序列化失败: %v", err)
	}

	// 添加发送消息的调试日志
	log.Printf("🔍 WebSocket发送消息: %s", string(data))
	log.Printf("📏 消息长度: %d 字节", len(data))

	err = wsm.connection.conn.WriteMessage(websocket.TextMessage, data)
	if err != nil {
		log.Printf("❌ WebSocket发送消息失败: %v", err)
		// 触发重连
		select {
		case wsm.connection.reconnectChan <- struct{}{}:
		default:
		}
		return err
	}

	log.Printf("✅ WebSocket消息发送成功")
	return nil
}

// 获取事件通道
func (wsm *WebSocketManager) GetEventChannel() <-chan WSEvent {
	return wsm.eventChan
}

// 读取循环
func (wsm *WebSocketManager) readLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("WebSocket读取循环异常: %v", r)
		}
	}()

	for {
		select {
		case <-wsm.connection.stopChan:
			return
		default:
		}

		wsm.mutex.RLock()
		conn := wsm.connection.conn
		wsm.mutex.RUnlock()

		if conn == nil {
			time.Sleep(100 * time.Millisecond)
			continue
		}

		messageType, data, err := conn.ReadMessage()
		if err != nil {
			log.Printf("WebSocket读取消息失败: %v", err)
			wsm.handleConnectionError(err)
			return
		}

		if messageType == websocket.PongMessage {
			wsm.connection.lastPong = time.Now()
			wsm.connectionQuality.UpdatePongReceived()
			log.Printf("🏓 收到pong响应")
			continue
		}

		if messageType != websocket.TextMessage {
			continue
		}

		// 更新连接质量统计
		wsm.lastMessageTime = time.Now()
		wsm.connectionQuality.UpdateMessageReceived()

		// 解析消息并发送到事件通道
		rawMessage, err := wsm.parseJSONWithNumbers(data)
		if err != nil {
			log.Printf("❌ WS解析失败: %v", err)
			continue
		}

		eventType := wsm.determineEventType(rawMessage)

		// 精简日志：只记录重要事件
		if eventType == WSEventUserData || eventType == WSEventTradingResponse {
			if eventStr, ok := rawMessage["e"].(string); ok {
				log.Printf("📨 WS[%s]: %s", wsm.getSourceFromURL(), eventStr)
			} else if id, ok := rawMessage["id"].(string); ok {
				log.Printf("📨 WS[%s]: %s (%s)", wsm.getSourceFromURL(), eventType, id)

				// 🔍 调试：如果是 trading_response，输出完整的原始数据
				if eventType == WSEventTradingResponse {
					log.Printf("🔍 [调试] trading_response 原始数据: %+v", rawMessage)

					// 🔍 调试：输出原始 JSON 字符串
					if jsonBytes, err := json.Marshal(rawMessage); err == nil {
						log.Printf("🔍 [调试] trading_response JSON: %s", string(jsonBytes))
					} else {
						log.Printf("⚠️ [调试] trading_response JSON序列化失败: %v", err)
					}
				}
			}
		}

		event := WSEvent{
			Type:      eventType,
			Timestamp: time.Now(),
			Data:      rawMessage,
			Source:    wsm.getSourceFromURL(),
		}

		select {
		case wsm.eventChan <- event:
			// 成功发送，无需日志
		case <-time.After(100 * time.Millisecond):
			log.Printf("⚠️ WS事件通道满")
		}
	}
}

// 心跳循环
func (wsm *WebSocketManager) pingLoop() {
	ticker := time.NewTicker(3 * time.Minute) // 每3分钟ping一次
	defer ticker.Stop()

	for {
		select {
		case <-wsm.connection.stopChan:
			return
		case <-ticker.C:
			wsm.sendPing()
		case <-wsm.connection.reconnectChan:
			go wsm.handleReconnect()
		}
	}
}

// 优化的心跳循环 - 减少频率，避免过度心跳导致连接断开
func (wsm *WebSocketManager) enhancedHeartbeatLoop() {
	// 安全检查：确保时间间隔不为0
	pingInterval := wsm.config.PingInterval
	if pingInterval <= 0 {
		pingInterval = 180 * time.Second // 默认3分钟（币安推荐）
		log.Printf("⚠️ ping间隔配置无效，使用默认值: %v", pingInterval)
	}

	// 禁用主动pong发送，只响应服务器的ping
	pongInterval := wsm.config.PongInterval
	pongEnabled := pongInterval > 0
	if !pongEnabled {
		log.Printf("🔧 主动pong发送已禁用，只响应服务器ping")
	}

	// 使用安全的ping间隔
	wsm.heartbeatTicker = time.NewTicker(pingInterval)
	defer func() {
		if wsm.heartbeatTicker != nil {
			wsm.heartbeatTicker.Stop()
		}
	}()

	// 使用安全的pong间隔
	pongTicker := time.NewTicker(pongInterval)
	defer pongTicker.Stop()

	for {
		select {
		case <-wsm.heartbeatTicker.C:
			wsm.mutex.RLock()
			if wsm.connection.state != WSStateConnected {
				wsm.mutex.RUnlock()
				return
			}
			conn := wsm.connection.conn
			wsm.mutex.RUnlock()

			if conn != nil {
				wsm.connection.lastPing = time.Now()
				wsm.connectionQuality.UpdatePingSent()

				if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
					log.Printf("❌ 增强心跳发送ping失败: %v", err)
					wsm.triggerReconnect()
					return
				}
				log.Printf("💓 增强心跳发送ping消息")
			}

		case <-pongTicker.C:
			// 禁用主动pong发送，避免过度心跳导致连接断开
			if pongEnabled {
				wsm.mutex.RLock()
				if wsm.connection.state != WSStateConnected {
					wsm.mutex.RUnlock()
					return
				}
				conn := wsm.connection.conn
				wsm.mutex.RUnlock()

				if conn != nil {
					// 主动发送pong消息（空载荷）
					if err := conn.WriteMessage(websocket.PongMessage, []byte{}); err != nil {
						log.Printf("❌ 心跳pong发送失败: %v", err)
						wsm.triggerReconnect()
						return
					}
					log.Printf("🏓 心跳pong发送成功")
				}
			}

		case <-wsm.stopChan:
			return
		}
	}
}

// 触发重连
func (wsm *WebSocketManager) triggerReconnect() {
	select {
	case wsm.connection.reconnectChan <- struct{}{}:
	default:
	}
}

// 连接质量监控
func (wsm *WebSocketManager) connectionQualityMonitor() {
	// 安全检查：确保健康检查间隔不为0
	healthCheckInterval := wsm.config.HealthCheckInterval
	if healthCheckInterval <= 0 {
		healthCheckInterval = 5 * time.Second // 默认5秒
		log.Printf("⚠️ 健康检查间隔配置无效，使用默认值: %v", healthCheckInterval)
	}

	// 使用安全的健康检查间隔
	ticker := time.NewTicker(healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			wsm.checkConnectionHealth()
		case <-wsm.stopChan:
			return
		}
	}
}

// 检查连接健康状态
func (wsm *WebSocketManager) checkConnectionHealth() {
	wsm.mutex.RLock()
	if wsm.connection.state != WSStateConnected {
		wsm.mutex.RUnlock()
		return
	}
	wsm.mutex.RUnlock()

	// 检查静默时间
	silenceDuration := time.Since(wsm.lastMessageTime)

	// 安全检查：确保静默超时不为0
	silenceTimeout := wsm.config.SilenceTimeout
	if silenceTimeout <= 0 {
		silenceTimeout = 120 * time.Second // 默认2分钟
	}

	warningThreshold := silenceTimeout / 2 // 警告阈值为超时时间的一半

	if silenceDuration > warningThreshold {
		//log.Printf("⚠️ WebSocket连接静默超过警告阈值，静默时长: %v", silenceDuration)
		wsm.connectionQuality.IsHealthy = false

		// 如果静默超过配置的超时时间，主动重连
		if silenceDuration > silenceTimeout {
			log.Printf("🔄 WebSocket连接静默超过超时阈值(%v)，触发重连", silenceTimeout)
			wsm.triggerReconnect()
		}
	} else {
		wsm.connectionQuality.IsHealthy = true
	}

	// 记录连接质量信息
	healthy, _, _ := wsm.connectionQuality.GetQuality()
	if !healthy {
		//		log.Printf("📊 连接质量: 健康=%v", healthy)
	}
}

// 发送ping
func (wsm *WebSocketManager) sendPing() {
	wsm.mutex.RLock()
	conn := wsm.connection.conn
	wsm.mutex.RUnlock()

	if conn == nil {
		return
	}

	err := conn.WriteMessage(websocket.PingMessage, []byte{})
	if err != nil {
		log.Printf("WebSocket ping失败: %v", err)
		select {
		case wsm.connection.reconnectChan <- struct{}{}:
		default:
		}
		return
	}

	wsm.connection.lastPing = time.Now()

	// 检查pong超时
	if time.Since(wsm.connection.lastPong) > 10*time.Minute {
		log.Printf("WebSocket pong超时，触发重连")
		select {
		case wsm.connection.reconnectChan <- struct{}{}:
		default:
		}
	}
}

// 处理连接错误
func (wsm *WebSocketManager) handleConnectionError(err error) {
	wsm.mutex.Lock()
	wsm.connection.state = WSStateDisconnected
	if wsm.connection.conn != nil {
		wsm.connection.conn.Close()
		wsm.connection.conn = nil
	}
	wsm.mutex.Unlock()

	// 发送错误事件
	errorEvent := WSEvent{
		Type:      WSEventError,
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"error":  err.Error(),
			"source": wsm.getSourceFromURL(),
		},
		Source: wsm.getSourceFromURL(),
	}

	select {
	case wsm.eventChan <- errorEvent:
	case <-time.After(100 * time.Millisecond):
	}

	// 触发重连
	select {
	case wsm.connection.reconnectChan <- struct{}{}:
	default:
	}
}

// 处理重连
func (wsm *WebSocketManager) handleReconnect() {
	if wsm.connection.state == WSStateClosed {
		return
	}

	wsm.mutex.Lock()
	if wsm.connection.reconnectCount >= wsm.connection.maxReconnects {
		log.Printf("WebSocket重连次数超限，停止重连")
		wsm.connection.state = WSStateClosed
		wsm.mutex.Unlock()
		return
	}

	wsm.connection.reconnectCount++
	wsm.connection.state = WSStateReconnecting
	wsm.connectionQuality.UpdateReconnect()
	wsm.mutex.Unlock()

	log.Printf("WebSocket开始重连，第%d次尝试", wsm.connection.reconnectCount)

	// 指数退避重连策略
	backoffDuration := wsm.calculateBackoffDuration(wsm.connection.reconnectCount)
	log.Printf("🔄 重连退避等待: %v", backoffDuration)
	time.Sleep(backoffDuration)

	// 执行重连
	if wsm.reconnectFunc != nil {
		err := wsm.reconnectFunc()
		if err != nil {
			log.Printf("WebSocket重连失败: %v", err)
			// 继续重连
			select {
			case wsm.connection.reconnectChan <- struct{}{}:
			default:
			}
			return
		}
	} else {
		err := wsm.Connect()
		if err != nil {
			log.Printf("WebSocket重连失败: %v", err)
			// 继续重连
			select {
			case wsm.connection.reconnectChan <- struct{}{}:
			default:
			}
			return
		}
	}

	log.Printf("WebSocket重连成功")

	// 发送重连事件
	reconnectEvent := WSEvent{
		Type:      WSEventReconnect,
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"reconnect_count": wsm.connection.reconnectCount,
			"source":          wsm.getSourceFromURL(),
		},
		Source: wsm.getSourceFromURL(),
	}

	select {
	case wsm.eventChan <- reconnectEvent:
	case <-time.After(100 * time.Millisecond):
	}
}

// 计算指数退避时间
func (wsm *WebSocketManager) calculateBackoffDuration(attempt int) time.Duration {
	// 基础间隔：1秒
	baseInterval := 1 * time.Second

	// 最大间隔：60秒
	maxInterval := 60 * time.Second

	// 指数退避：2^attempt * baseInterval
	backoff := time.Duration(1<<uint(attempt-1)) * baseInterval

	// 限制最大间隔
	if backoff > maxInterval {
		backoff = maxInterval
	}

	// 添加随机抖动（±25%）避免雷群效应
	jitter := time.Duration(float64(backoff) * 0.25 * (2*rand.Float64() - 1))
	backoff += jitter

	// 确保最小间隔为1秒
	if backoff < baseInterval {
		backoff = baseInterval
	}

	return backoff
}

// 确定事件类型
func (wsm *WebSocketManager) determineEventType(data map[string]interface{}) WSEventType {
	// 检查是否是交易API响应
	if _, hasID := data["id"]; hasID {
		if _, hasStatus := data["status"]; hasStatus {
			return WSEventTradingResponse
		}
	}

	// 检查是否是用户数据事件
	if eventType, ok := data["e"].(string); ok {
		switch eventType {
		case "ORDER_TRADE_UPDATE", "ACCOUNT_UPDATE":
			return WSEventUserData
		}
	}

	// 检查是否是订单簿数据
	if _, hasBids := data["b"]; hasBids {
		if _, hasAsks := data["a"]; hasAsks {
			return WSEventOrderBook
		}
	}

	return WSEventError
}

// 从URL获取来源
func (wsm *WebSocketManager) getSourceFromURL() string {
	if wsm.connection.url == "" {
		return "unknown"
	}

	if contains(wsm.connection.url, "ws-fapi") {
		return "trading"
	} else if contains(wsm.connection.url, "/ws/") {
		return "userdata"
	} else {
		return "market"
	}
}

// 设置重连函数
func (wsm *WebSocketManager) SetReconnectFunc(fn func() error) {
	wsm.reconnectFunc = fn
}

// 获取连接状态
func (wsm *WebSocketManager) GetState() WSConnectionState {
	wsm.mutex.RLock()
	defer wsm.mutex.RUnlock()
	return wsm.connection.state
}

// 检查是否连接
func (wsm *WebSocketManager) IsConnected() bool {
	wsm.mutex.RLock()
	defer wsm.mutex.RUnlock()
	return wsm.connection.state == WSStateConnected
}

// parseJSONWithNumbers 使用 json.Number 解析JSON，避免大整数精度丢失
func (wsm *WebSocketManager) parseJSONWithNumbers(data []byte) (map[string]interface{}, error) {
	//log.Printf("🔍 [parseJSONWithNumbers] 开始解析JSON，数据长度: %d", len(data))

	decoder := json.NewDecoder(strings.NewReader(string(data)))
	decoder.UseNumber()

	var rawMessage map[string]interface{}
	if err := decoder.Decode(&rawMessage); err != nil {
		log.Printf("🔍 [parseJSONWithNumbers] JSON解码失败: %v", err)
		return nil, err
	}

	//log.Printf("🔍 [parseJSONWithNumbers] JSON解码成功，开始处理订单ID字段")

	// 递归处理所有可能的订单ID字段，将 json.Number 转换为字符串
	wsm.processOrderIDFields(rawMessage)

	//log.Printf("🔍 [parseJSONWithNumbers] 订单ID字段处理完成")

	return rawMessage, nil
}

// processOrderIDFields 递归处理订单ID字段，将 json.Number 转换为字符串
func (wsm *WebSocketManager) processOrderIDFields(data map[string]interface{}) {
	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	//log.Printf("🔍 [processOrderIDFields] 开始处理字段，数据键: %v", keys)

	for key, value := range data {
		//log.Printf("🔍 [processOrderIDFields] 处理字段 %s, 类型: %T, 值: %v", key, value, value)

		switch v := value.(type) {
		case json.Number:
			// 检查是否是订单ID字段
			if wsm.isOrderIDField(key) {
				// 将 json.Number 转换为字符串，保持精度
				oldValue := string(v)
				data[key] = oldValue
				//log.Printf("🔍 [JSON解析] 转换订单ID字段 %s: %s -> %s", key, v, oldValue)
			} else {
				//log.Printf("🔍 [processOrderIDFields] 字段 %s 是 json.Number 但不是订单ID字段，跳过转换", key)
			}
		case map[string]interface{}:
			//log.Printf("🔍 [processOrderIDFields] 字段 %s 是嵌套对象，递归处理", key)
			// 递归处理嵌套对象
			wsm.processOrderIDFields(v)
		case []interface{}:
			//log.Printf("🔍 [processOrderIDFields] 字段 %s 是数组，处理数组中的对象", key)
			// 处理数组中的对象
			for _, item := range v {
				if itemMap, ok := item.(map[string]interface{}); ok {
					wsm.processOrderIDFields(itemMap)
				}
			}
		default:
			//log.Printf("🔍 [processOrderIDFields] 字段 %s 类型 %T，无需处理", key, value)
		}
	}

	//	log.Printf("🔍 [processOrderIDFields] 字段处理完成")
}

// isOrderIDField 检查字段名是否是订单ID相关字段
func (wsm *WebSocketManager) isOrderIDField(fieldName string) bool {
	orderIDFields := []string{
		"orderId", "i", "id", "clientOrderId", "origClientOrderId",
		"tradeId", "t", // 交易ID
	}

	for _, field := range orderIDFields {
		if fieldName == field {
			return true
		}
	}
	return false
}

// 辅助函数：检查字符串是否包含子串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr ||
		(len(s) > len(substr) && s[:len(substr)] == substr) ||
		(len(s) > len(substr) && findSubstring(s, substr))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
