#!/usr/bin/env python3
"""
快速诊断程序
专门针对Go程序中遇到的WebSocket问题进行快速诊断

主要问题：
1. websocket: close 1008 (policy violation)
2. websocket: RSV2 set, RSV3 set
3. 智能下单无响应
"""

import asyncio
import websockets
import json
import time
import hmac
import hashlib
import urllib.parse
import logging

# 简化日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickDiagnosis:
    """快速诊断工具"""
    
    def __init__(self):
        self.results = {}
    
    async def test_market_data_basic(self):
        """测试基础市场数据连接"""
        logger.info("🔍 测试基础市场数据连接...")
        
        url = "wss://fstream.binance.com/ws/ethusdt@depth5@100ms"
        
        try:
            async with websockets.connect(url, timeout=10) as ws:
                logger.info("✅ 市场数据连接成功")
                
                # 接收3条消息
                for i in range(3):
                    try:
                        message = await asyncio.wait_for(ws.recv(), timeout=5)
                        data = json.loads(message)
                        
                        if 'b' in data and 'a' in data and data['b'] and data['a']:
                            bid = float(data['b'][0][0])
                            ask = float(data['a'][0][0])
                            logger.info(f"📊 消息 {i+1}: 买一={bid}, 卖一={ask}")
                        else:
                            logger.warning(f"⚠️ 数据格式异常: {data}")
                            
                    except asyncio.TimeoutError:
                        logger.error(f"❌ 消息 {i+1} 超时")
                        return False
                
                logger.info("✅ 市场数据测试成功")
                return True
                
        except Exception as e:
            logger.error(f"❌ 市场数据连接失败: {e}")
            return False
    
    async def test_trading_api_connection(self):
        """测试交易API连接（重点测试1008错误）"""
        logger.info("🔍 测试交易API连接...")
        
        url = "wss://ws-fapi.binance.com/ws-fapi/v1"
        
        try:
            # 测试不同的连接参数
            connection_configs = [
                {"timeout": 10},
                {"timeout": 10, "ping_interval": 20},
                {"timeout": 10, "compression": None},
            ]
            
            for i, config in enumerate(connection_configs):
                logger.info(f"尝试配置 {i+1}: {config}")
                
                try:
                    async with websockets.connect(url, **config) as ws:
                        logger.info(f"✅ 配置 {i+1} 连接成功")
                        
                        # 发送ping测试
                        ping_msg = {"id": f"ping_{i}", "method": "ping"}
                        await ws.send(json.dumps(ping_msg))
                        logger.info(f"📤 发送ping: {ping_msg}")
                        
                        # 等待响应
                        try:
                            response = await asyncio.wait_for(ws.recv(), timeout=10)
                            data = json.loads(response)
                            logger.info(f"📥 收到响应: {data}")
                            
                            if data.get('id') == f"ping_{i}":
                                logger.info(f"✅ 配置 {i+1} ping成功")
                                return True
                            else:
                                logger.warning(f"⚠️ 配置 {i+1} 响应ID不匹配")
                                
                        except asyncio.TimeoutError:
                            logger.error(f"❌ 配置 {i+1} ping超时")
                            
                except websockets.exceptions.ConnectionClosedError as e:
                    logger.error(f"❌ 配置 {i+1} 连接被关闭: {e}")
                    if "1008" in str(e):
                        logger.error("🚨 检测到1008错误 - 这是Go程序遇到的问题！")
                        logger.info("💡 1008错误通常表示：")
                        logger.info("   - API认证失败")
                        logger.info("   - 请求格式不正确")
                        logger.info("   - 缺少必要的headers")
                        
                except Exception as e:
                    logger.error(f"❌ 配置 {i+1} 其他错误: {e}")
            
            logger.error("❌ 所有配置都失败了")
            return False
            
        except Exception as e:
            logger.error(f"❌ 交易API连接测试失败: {e}")
            return False
    
    async def test_websocket_frame_handling(self):
        """测试WebSocket帧处理（重点测试RSV错误）"""
        logger.info("🔍 测试WebSocket帧处理...")
        
        url = "wss://fstream.binance.com/ws/ethusdt@depth5@100ms"
        
        try:
            # 使用不同的WebSocket库配置
            configs = [
                {"max_size": 2**20, "max_queue": 32},  # 1MB, 32消息队列
                {"max_size": 2**16, "max_queue": 8},   # 64KB, 8消息队列
                {"compression": "deflate"},             # 启用压缩
                {"compression": None},                  # 禁用压缩
            ]
            
            for i, config in enumerate(configs):
                logger.info(f"测试配置 {i+1}: {config}")
                
                try:
                    async with websockets.connect(url, **config) as ws:
                        logger.info(f"✅ 配置 {i+1} 连接成功")
                        
                        # 快速接收多条消息，测试帧处理
                        message_count = 0
                        start_time = time.time()
                        
                        while message_count < 10 and time.time() - start_time < 30:
                            try:
                                message = await asyncio.wait_for(ws.recv(), timeout=3)
                                message_count += 1
                                
                                # 检查消息长度和格式
                                if len(message) > 10000:
                                    logger.info(f"📊 配置 {i+1} 大消息: {len(message)} bytes")
                                
                                # 尝试解析JSON
                                try:
                                    json.loads(message)
                                except json.JSONDecodeError:
                                    logger.warning(f"⚠️ 配置 {i+1} JSON解析失败")
                                    
                            except asyncio.TimeoutError:
                                logger.warning(f"⚠️ 配置 {i+1} 消息超时")
                                break
                            except websockets.exceptions.ConnectionClosedError as e:
                                if "RSV" in str(e):
                                    logger.error(f"🚨 配置 {i+1} 检测到RSV错误: {e}")
                                    logger.info("💡 RSV错误通常表示：")
                                    logger.info("   - WebSocket帧格式错误")
                                    logger.info("   - 数据传输损坏")
                                    logger.info("   - 压缩算法不兼容")
                                else:
                                    logger.error(f"❌ 配置 {i+1} 连接关闭: {e}")
                                break
                        
                        logger.info(f"✅ 配置 {i+1} 接收了 {message_count} 条消息")
                        
                        if message_count >= 5:
                            return True
                            
                except Exception as e:
                    logger.error(f"❌ 配置 {i+1} 失败: {e}")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 帧处理测试失败: {e}")
            return False
    
    async def test_order_request_format(self):
        """测试下单请求格式"""
        logger.info("🔍 测试下单请求格式...")
        
        # 模拟Go程序的下单请求
        go_style_request = {
            "id": "5440000_place_order_1749216768826634000",
            "method": "order.place",
            "params": {
                "symbol": "ETHUSDT",
                "side": "long",  # Go程序使用的格式
                "type": "LIMIT",
                "quantity": "0.01",
                "priceMatch": "QUEUE",
                "timeInForce": "GTX",
                "newClientOrderId": "5440000_long_1749216768826629000_001",
                "timestamp": int(time.time() * 1000)
            }
        }
        
        # 正确的币安API格式
        correct_request = {
            "id": "correct_place_order_" + str(int(time.time())),
            "method": "order.place", 
            "params": {
                "symbol": "ETHUSDT",
                "side": "BUY",  # 正确的格式
                "type": "LIMIT",
                "quantity": "0.01",
                "priceMatch": "QUEUE",
                "timeInForce": "GTX",
                "newClientOrderId": "correct_order_" + str(int(time.time())),
                "timestamp": int(time.time() * 1000)
            }
        }
        
        logger.info("📋 Go程序的请求格式:")
        logger.info(json.dumps(go_style_request, indent=2))
        
        logger.info("📋 正确的请求格式:")
        logger.info(json.dumps(correct_request, indent=2))
        
        # 关键差异分析
        logger.info("🔍 关键差异分析:")
        logger.info("1. side参数:")
        logger.info(f"   Go程序使用: '{go_style_request['params']['side']}'")
        logger.info(f"   应该使用:   '{correct_request['params']['side']}'")
        
        logger.info("2. 其他参数格式看起来正确")
        
        return True
    
    async def run_quick_diagnosis(self):
        """运行快速诊断"""
        logger.info("🚀 开始快速诊断")
        logger.info("专门针对Go程序的WebSocket问题")
        logger.info("=" * 60)
        
        tests = [
            ("市场数据连接", self.test_market_data_basic),
            ("交易API连接", self.test_trading_api_connection),
            ("WebSocket帧处理", self.test_websocket_frame_handling),
            ("下单请求格式", self.test_order_request_format),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 {test_name}测试:")
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"结果: {status}")
            except Exception as e:
                logger.error(f"❌ {test_name}测试异常: {e}")
                results[test_name] = False
        
        # 输出诊断结论
        logger.info("\n" + "=" * 60)
        logger.info("📊 快速诊断结论:")
        logger.info("=" * 60)
        
        for test_name, result in results.items():
            status = "✅" if result else "❌"
            logger.info(f"{status} {test_name}")
        
        # 针对Go程序的具体建议
        logger.info("\n💡 针对Go程序的修复建议:")
        
        if not results.get("交易API连接", True):
            logger.info("🔧 交易API连接问题:")
            logger.info("   1. 检查WebSocket API认证机制")
            logger.info("   2. 确认API密钥权限设置")
            logger.info("   3. 验证请求headers格式")
        
        if not results.get("WebSocket帧处理", True):
            logger.info("🔧 WebSocket帧处理问题:")
            logger.info("   1. 检查gorilla/websocket库版本")
            logger.info("   2. 调整连接参数（压缩、缓冲区大小）")
            logger.info("   3. 添加更严格的错误处理")
        
        logger.info("🔧 下单参数问题:")
        logger.info("   1. 将side参数从'long'/'short'改为'BUY'/'SELL'")
        logger.info("   2. 添加API签名到WebSocket请求")
        logger.info("   3. 验证timestamp格式")
        
        return results

async def main():
    """主函数"""
    print("⚡ WebSocket快速诊断工具")
    print("专门诊断Go程序中的WebSocket问题")
    print("=" * 50)
    
    diagnosis = QuickDiagnosis()
    
    try:
        results = await diagnosis.run_quick_diagnosis()
        
        # 保存简化报告
        with open('quick_diagnosis_report.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"\n📄 诊断报告已保存: quick_diagnosis_report.json")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ 诊断被中断")
    except Exception as e:
        logger.error(f"\n❌ 诊断异常: {e}")

if __name__ == "__main__":
    try:
        import websockets
    except ImportError:
        print("❌ 请安装websockets: pip install websockets")
        exit(1)
    
    asyncio.run(main())
