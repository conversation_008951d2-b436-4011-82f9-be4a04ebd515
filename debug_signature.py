#!/usr/bin/env python3
"""
调试WebSocket签名问题
详细分析签名生成过程
"""

import asyncio
import websockets
import json
import time
import hmac
import hashlib
import logging
from config import BINANCE_CONFIG

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class SignatureDebugger:
    def __init__(self):
        self.api_key = BINANCE_CONFIG["api_key"]
        self.secret_key = BINANCE_CONFIG["secret_key"]
        self.ws_api = "wss://ws-fapi.binance.com/ws-fapi/v1"
    
    def generate_signature_debug(self, params: dict) -> tuple:
        """生成签名并返回调试信息"""
        # 1. 排除signature参数
        filtered_params = {k: v for k, v in params.items() if k != 'signature'}
        
        # 2. 按字母顺序排序
        sorted_params = sorted(filtered_params.items())
        
        # 3. 构建查询字符串
        query_parts = []
        for key, value in sorted_params:
            query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 4. 生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return query_string, signature, sorted_params
    
    async def test_different_formats(self):
        """测试不同的参数格式"""
        logger.info("🔍 测试不同的下单参数格式...")
        
        base_timestamp = int(time.time() * 1000)
        
        # 测试格式1：基础格式
        params1 = {
            "apiKey": self.api_key,
            "symbol": "ETHUSDT",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": "0.01",
            "timeInForce": "GTX",
            "priceMatch": "QUEUE",
            "newClientOrderId": f"test_order_{base_timestamp}",
            "timestamp": base_timestamp,
            "test": True
        }
        
        # 测试格式2：添加价格
        params2 = {
            "apiKey": self.api_key,
            "symbol": "ETHUSDT",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": "0.01",
            "price": "2500.00",  # 添加价格
            "timeInForce": "GTX",
            "priceMatch": "QUEUE",
            "newClientOrderId": f"test_order_{base_timestamp + 1}",
            "timestamp": base_timestamp + 1,
            "test": True
        }
        
        # 测试格式3：不使用priceMatch
        params3 = {
            "apiKey": self.api_key,
            "symbol": "ETHUSDT",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": "0.01",
            "price": "2500.00",
            "timeInForce": "GTX",
            "newClientOrderId": f"test_order_{base_timestamp + 2}",
            "timestamp": base_timestamp + 2,
            "test": True
        }
        
        # 测试格式4：数字类型的timestamp
        params4 = {
            "apiKey": self.api_key,
            "symbol": "ETHUSDT",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": "0.01",
            "price": "2500.00",
            "timeInForce": "GTX",
            "newClientOrderId": f"test_order_{base_timestamp + 3}",
            "timestamp": str(base_timestamp + 3),  # 字符串格式
            "test": True
        }
        
        test_cases = [
            ("基础格式（priceMatch + 无price）", params1),
            ("添加price参数", params2),
            ("移除priceMatch", params3),
            ("字符串timestamp", params4),
        ]
        
        for test_name, params in test_cases:
            logger.info(f"\n📋 测试: {test_name}")
            
            # 生成签名
            query_string, signature, sorted_params = self.generate_signature_debug(params)
            
            logger.info(f"参数列表:")
            for key, value in sorted_params:
                logger.info(f"  {key}: {value} ({type(value).__name__})")
            
            logger.info(f"查询字符串: {query_string}")
            logger.info(f"签名: {signature}")
            
            # 测试请求
            params["signature"] = signature
            request = {
                "id": f"debug_request_{int(time.time())}",
                "method": "order.place",
                "params": params
            }
            
            try:
                async with websockets.connect(self.ws_api) as ws:
                    await ws.send(json.dumps(request))
                    response = await asyncio.wait_for(ws.recv(), timeout=10)
                    data = json.loads(response)
                    
                    if data.get('status') == 200:
                        logger.info(f"✅ {test_name} 成功!")
                        logger.info(f"响应: {data}")
                        return True, test_name, params
                    else:
                        error = data.get('error', {})
                        logger.error(f"❌ {test_name} 失败: {error}")
                        
                        # 分析具体错误
                        if error.get('code') == -1022:
                            logger.error("   签名验证失败")
                        elif error.get('code') == -1121:
                            logger.error("   无效的交易对")
                        elif error.get('code') == -2010:
                            logger.error("   余额不足")
                        elif error.get('code') == -1013:
                            logger.error("   参数错误")
                        
            except Exception as e:
                logger.error(f"❌ {test_name} 异常: {e}")
        
        return False, None, None
    
    async def test_rest_api_comparison(self):
        """对比REST API的签名方式"""
        logger.info("\n🔍 对比REST API签名方式...")
        
        import aiohttp
        
        timestamp = int(time.time() * 1000)
        
        # REST API参数（不排序）
        rest_params = {
            "symbol": "ETHUSDT",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": "0.01",
            "price": "2500.00",
            "timeInForce": "GTX",
            "newClientOrderId": f"rest_test_{timestamp}",
            "timestamp": timestamp,
            "test": "true"  # REST API中test参数是字符串
        }
        
        # 生成REST API签名（不排序）
        query_parts = []
        for key, value in rest_params.items():
            query_parts.append(f"{key}={value}")
        rest_query_string = "&".join(query_parts)
        
        rest_signature = hmac.new(
            self.secret_key.encode('utf-8'),
            rest_query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        logger.info(f"REST API查询字符串: {rest_query_string}")
        logger.info(f"REST API签名: {rest_signature}")
        
        # 测试REST API
        url = "https://fapi.binance.com/fapi/v1/order"
        headers = {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        rest_params["signature"] = rest_signature
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=rest_params) as response:
                    rest_response = await response.json()
                    logger.info(f"REST API响应: {rest_response}")
                    
                    if response.status == 200:
                        logger.info("✅ REST API下单成功")
                    else:
                        logger.error(f"❌ REST API下单失败: {rest_response}")
        
        except Exception as e:
            logger.error(f"❌ REST API测试异常: {e}")
    
    async def test_minimal_request(self):
        """测试最简单的请求"""
        logger.info("\n🔍 测试最简单的WebSocket请求...")
        
        timestamp = int(time.time() * 1000)
        
        # 最简单的参数
        minimal_params = {
            "apiKey": self.api_key,
            "symbol": "ETHUSDT",
            "side": "BUY",
            "type": "MARKET",  # 市价单
            "quantity": "0.01",
            "timestamp": timestamp,
            "test": True
        }
        
        query_string, signature, sorted_params = self.generate_signature_debug(minimal_params)
        
        logger.info(f"最简参数:")
        for key, value in sorted_params:
            logger.info(f"  {key}: {value}")
        
        logger.info(f"查询字符串: {query_string}")
        logger.info(f"签名: {signature}")
        
        minimal_params["signature"] = signature
        request = {
            "id": f"minimal_request_{timestamp}",
            "method": "order.place",
            "params": minimal_params
        }
        
        try:
            async with websockets.connect(self.ws_api) as ws:
                await ws.send(json.dumps(request))
                response = await asyncio.wait_for(ws.recv(), timeout=10)
                data = json.loads(response)
                
                logger.info(f"最简请求响应: {data}")
                
                if data.get('status') == 200:
                    logger.info("✅ 最简请求成功!")
                    return True
                else:
                    error = data.get('error', {})
                    logger.error(f"❌ 最简请求失败: {error}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 最简请求异常: {e}")
            return False

async def main():
    """主函数"""
    print("🔧 WebSocket签名调试程序")
    print("=" * 50)
    
    debugger = SignatureDebugger()
    
    # 测试不同格式
    success, working_format, working_params = await debugger.test_different_formats()
    
    if success:
        print(f"\n🎉 找到工作的格式: {working_format}")
        print("✅ 可以用这个格式修改Go程序")
    else:
        print("\n❌ 所有格式都失败了，继续调试...")
        
        # 对比REST API
        await debugger.test_rest_api_comparison()
        
        # 测试最简请求
        await debugger.test_minimal_request()

if __name__ == "__main__":
    try:
        import aiohttp
        import websockets
    except ImportError:
        print("❌ 请安装依赖: pip install websockets aiohttp")
        exit(1)
    
    asyncio.run(main())
