#!/usr/bin/env python3
"""
高级WebSocket测试程序
专门诊断Go程序中遇到的WebSocket问题

重点测试：
1. WebSocket协议兼容性
2. 并发连接处理
3. 错误恢复机制
4. API签名验证
"""

import asyncio
import websockets
import json
import time
import hmac
import hashlib
import urllib.parse
import logging
import sys
import traceback
from typing import Dict, Any, Optional, List
import ssl
import socket

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebSocketDiagnostic:
    """WebSocket诊断工具"""
    
    def __init__(self, api_key: str, secret_key: str):
        self.api_key = api_key
        self.secret_key = secret_key
        self.test_results = {}
        
    async def test_basic_connectivity(self):
        """测试基础网络连接"""
        logger.info("🔍 测试基础网络连接...")
        
        endpoints = [
            "fstream.binance.com",
            "ws-fapi.binance.com", 
            "testnet.binancefuture.com"
        ]
        
        results = {}
        for endpoint in endpoints:
            try:
                # DNS解析测试
                ip = socket.gethostbyname(endpoint)
                logger.info(f"✅ {endpoint} DNS解析成功: {ip}")
                
                # TCP连接测试
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((endpoint, 443))
                sock.close()
                
                if result == 0:
                    logger.info(f"✅ {endpoint} TCP连接成功")
                    results[endpoint] = True
                else:
                    logger.error(f"❌ {endpoint} TCP连接失败: {result}")
                    results[endpoint] = False
                    
            except Exception as e:
                logger.error(f"❌ {endpoint} 连接测试异常: {e}")
                results[endpoint] = False
        
        return results
    
    async def test_websocket_protocols(self):
        """测试WebSocket协议兼容性"""
        logger.info("🔍 测试WebSocket协议兼容性...")
        
        test_urls = [
            "wss://fstream.binance.com/ws/ethusdt@depth5@100ms",
            "wss://ws-fapi.binance.com/ws-fapi/v1",
            "wss://stream.binancefuture.com/ws/ethusdt@depth5@100ms"
        ]
        
        results = {}
        
        for url in test_urls:
            logger.info(f"测试URL: {url}")
            try:
                # 测试不同的WebSocket配置
                configs = [
                    {"compression": None},
                    {"compression": "deflate"},
                    {"ping_interval": 20, "ping_timeout": 10},
                    {"close_timeout": 10}
                ]
                
                for i, config in enumerate(configs):
                    try:
                        logger.info(f"  配置 {i+1}: {config}")
                        
                        async with websockets.connect(url, **config) as ws:
                            logger.info(f"  ✅ 连接成功")
                            
                            # 发送ping测试
                            await ws.ping()
                            logger.info(f"  ✅ Ping成功")
                            
                            # 接收数据测试
                            try:
                                message = await asyncio.wait_for(ws.recv(), timeout=5)
                                logger.info(f"  ✅ 数据接收成功: {len(message)} bytes")
                                
                                # 检查数据格式
                                try:
                                    data = json.loads(message)
                                    logger.info(f"  ✅ JSON解析成功")
                                except:
                                    logger.warning(f"  ⚠️ 非JSON数据")
                                    
                            except asyncio.TimeoutError:
                                logger.warning(f"  ⚠️ 5秒内无数据")
                            
                            results[f"{url}_config_{i}"] = True
                            break  # 成功一个配置就够了
                            
                    except Exception as e:
                        logger.error(f"  ❌ 配置 {i+1} 失败: {e}")
                        results[f"{url}_config_{i}"] = False
                        
            except Exception as e:
                logger.error(f"❌ {url} 测试失败: {e}")
                results[url] = False
        
        return results
    
    async def test_concurrent_connections(self):
        """测试并发连接"""
        logger.info("🔍 测试并发WebSocket连接...")
        
        url = "wss://fstream.binance.com/ws/ethusdt@depth5@100ms"
        connection_count = 5
        
        async def create_connection(conn_id):
            try:
                async with websockets.connect(url) as ws:
                    logger.info(f"连接 {conn_id}: 建立成功")
                    
                    # 保持连接30秒
                    for i in range(6):  # 30秒，每5秒检查一次
                        try:
                            message = await asyncio.wait_for(ws.recv(), timeout=5)
                            logger.info(f"连接 {conn_id}: 接收数据 {i+1}/6")
                        except asyncio.TimeoutError:
                            logger.warning(f"连接 {conn_id}: 5秒无数据")
                    
                    logger.info(f"连接 {conn_id}: 测试完成")
                    return True
                    
            except Exception as e:
                logger.error(f"连接 {conn_id}: 失败 - {e}")
                return False
        
        # 并发创建连接
        tasks = [create_connection(i) for i in range(connection_count)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        logger.info(f"并发连接测试: {success_count}/{connection_count} 成功")
        
        return {"concurrent_connections": success_count == connection_count}
    
    async def test_websocket_api_authentication(self):
        """测试WebSocket API认证"""
        logger.info("🔍 测试WebSocket API认证...")
        
        url = "wss://ws-fapi.binance.com/ws-fapi/v1"
        
        try:
            async with websockets.connect(url) as ws:
                logger.info("✅ 交易API WebSocket连接成功")
                
                # 测试1: 无认证的ping
                ping_request = {
                    "id": "test_ping_1",
                    "method": "ping"
                }
                
                await ws.send(json.dumps(ping_request))
                logger.info(f"📤 发送ping: {ping_request}")
                
                try:
                    response = await asyncio.wait_for(ws.recv(), timeout=10)
                    data = json.loads(response)
                    logger.info(f"📥 ping响应: {data}")
                    
                    if data.get('id') == 'test_ping_1':
                        logger.info("✅ ping测试成功")
                    else:
                        logger.error(f"❌ ping响应ID不匹配")
                        
                except asyncio.TimeoutError:
                    logger.error("❌ ping超时")
                    return False
                
                # 测试2: 需要认证的请求（应该失败）
                timestamp = int(time.time() * 1000)
                
                # 故意不签名的请求
                order_request = {
                    "id": "test_order_1",
                    "method": "order.place",
                    "params": {
                        "symbol": "ETHUSDT",
                        "side": "BUY",
                        "type": "LIMIT",
                        "quantity": "0.01",
                        "price": "2000",
                        "timeInForce": "GTC",
                        "timestamp": timestamp
                    }
                }
                
                await ws.send(json.dumps(order_request))
                logger.info(f"📤 发送未签名下单请求")
                
                try:
                    response = await asyncio.wait_for(ws.recv(), timeout=10)
                    data = json.loads(response)
                    logger.info(f"📥 下单响应: {data}")
                    
                    if 'error' in data:
                        error_code = data['error'].get('code')
                        if error_code == -1022:  # 签名错误
                            logger.info("✅ 正确返回签名错误（符合预期）")
                            return True
                        else:
                            logger.warning(f"⚠️ 返回其他错误: {error_code}")
                            return True  # 至少有错误响应
                    else:
                        logger.error("❌ 未签名请求竟然成功了？")
                        return False
                        
                except asyncio.TimeoutError:
                    logger.error("❌ 下单请求超时")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ WebSocket API认证测试失败: {e}")
            return False
    
    async def test_error_scenarios(self):
        """测试各种错误场景"""
        logger.info("🔍 测试错误场景处理...")
        
        scenarios = []
        
        # 场景1: 无效URL
        try:
            await websockets.connect("wss://invalid.binance.com/ws/test", timeout=5)
            scenarios.append(("invalid_url", False))
        except Exception as e:
            logger.info(f"✅ 无效URL正确失败: {e}")
            scenarios.append(("invalid_url", True))
        
        # 场景2: 连接后立即断开
        try:
            ws = await websockets.connect("wss://fstream.binance.com/ws/ethusdt@depth5@100ms")
            await ws.close()
            logger.info("✅ 主动断开连接成功")
            scenarios.append(("manual_disconnect", True))
        except Exception as e:
            logger.error(f"❌ 主动断开失败: {e}")
            scenarios.append(("manual_disconnect", False))
        
        # 场景3: 发送无效数据
        try:
            async with websockets.connect("wss://ws-fapi.binance.com/ws-fapi/v1") as ws:
                await ws.send("invalid json data")
                
                try:
                    response = await asyncio.wait_for(ws.recv(), timeout=5)
                    logger.info(f"✅ 无效数据得到响应: {response}")
                    scenarios.append(("invalid_data", True))
                except asyncio.TimeoutError:
                    logger.info("✅ 无效数据被忽略（符合预期）")
                    scenarios.append(("invalid_data", True))
                    
        except Exception as e:
            logger.error(f"❌ 无效数据测试失败: {e}")
            scenarios.append(("invalid_data", False))
        
        return dict(scenarios)
    
    async def run_comprehensive_diagnostic(self):
        """运行综合诊断"""
        logger.info("🚀 开始WebSocket综合诊断")
        logger.info("=" * 80)
        
        all_results = {}
        
        # 1. 基础连接测试
        logger.info("\n📋 步骤1: 基础网络连接测试")
        connectivity_results = await self.test_basic_connectivity()
        all_results.update(connectivity_results)
        
        # 2. WebSocket协议测试
        logger.info("\n📋 步骤2: WebSocket协议兼容性测试")
        protocol_results = await self.test_websocket_protocols()
        all_results.update(protocol_results)
        
        # 3. 并发连接测试
        logger.info("\n📋 步骤3: 并发连接测试")
        concurrent_results = await self.test_concurrent_connections()
        all_results.update(concurrent_results)
        
        # 4. API认证测试
        logger.info("\n📋 步骤4: WebSocket API认证测试")
        auth_result = await self.test_websocket_api_authentication()
        all_results['api_authentication'] = auth_result
        
        # 5. 错误场景测试
        logger.info("\n📋 步骤5: 错误场景测试")
        error_results = await self.test_error_scenarios()
        all_results.update(error_results)
        
        # 输出诊断报告
        self.generate_diagnostic_report(all_results)
        
        return all_results
    
    def generate_diagnostic_report(self, results: Dict[str, bool]):
        """生成诊断报告"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 WebSocket诊断报告")
        logger.info("=" * 80)
        
        categories = {
            "网络连接": [k for k in results.keys() if ".com" in k and "config" not in k],
            "协议兼容": [k for k in results.keys() if "config" in k],
            "并发处理": [k for k in results.keys() if "concurrent" in k],
            "API认证": [k for k in results.keys() if "authentication" in k],
            "错误处理": [k for k in results.keys() if k in ["invalid_url", "manual_disconnect", "invalid_data"]]
        }
        
        for category, keys in categories.items():
            if not keys:
                continue
                
            logger.info(f"\n🔍 {category}:")
            for key in keys:
                if key in results:
                    status = "✅ 通过" if results[key] else "❌ 失败"
                    logger.info(f"  {key:30} : {status}")
        
        # 总结
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        logger.info("\n" + "=" * 80)
        logger.info(f"📈 总结: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有诊断测试通过！")
        else:
            logger.warning(f"⚠️ 有 {total_tests - passed_tests} 个测试失败")
            
            # 给出具体建议
            failed_tests = [k for k, v in results.items() if not v]
            logger.info("\n💡 失败测试的可能原因:")
            
            for test in failed_tests:
                if ".com" in test:
                    logger.info(f"  {test}: 网络连接问题，检查防火墙和DNS")
                elif "config" in test:
                    logger.info(f"  {test}: WebSocket配置问题，可能需要调整连接参数")
                elif "concurrent" in test:
                    logger.info(f"  {test}: 并发连接限制，可能触发了服务器限制")
                elif "authentication" in test:
                    logger.info(f"  {test}: API认证问题，检查密钥和签名算法")

async def main():
    """主函数"""
    print("🔧 高级WebSocket诊断工具")
    print("专门用于诊断Go程序中的WebSocket问题")
    print("=" * 80)
    
    # 这里可以使用任意API密钥，因为我们主要测试连接性
    API_KEY = "test_key"
    SECRET_KEY = "test_secret"
    
    diagnostic = WebSocketDiagnostic(API_KEY, SECRET_KEY)
    
    try:
        results = await diagnostic.run_comprehensive_diagnostic()
        
        # 保存结果到文件
        with open('websocket_diagnostic_report.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"\n📄 详细报告已保存到: websocket_diagnostic_report.json")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ 诊断被用户中断")
    except Exception as e:
        logger.error(f"\n❌ 诊断过程异常: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    # 检查依赖
    try:
        import websockets
    except ImportError:
        print("❌ 缺少websockets包，请安装: pip install websockets")
        sys.exit(1)
    
    asyncio.run(main())
