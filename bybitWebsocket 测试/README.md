# Bybit WebSocket 期权盘口数据测试程序

这是一个简单的 Go 程序，用于测试 Bybit WebSocket API 订阅期权盘口数据的功能。程序专门订阅 `ETH-4JUL25-2600-C-USDT` 期权合约的 5 档盘口数据。

## 🔄 最新更新 (v1.1)

### 修复和改进内容：

1. **修复期权订阅响应格式处理**
   - 添加了 `OptionSubscribeResponse` 结构体来处理期权特有的订阅响应格式
   - 期权订阅响应格式: `{"success": true, "data": {"failTopics": [], "successTopics": [...]}, "type": "COMMAND_RESP"}`
   - 与普通订阅响应格式不同，需要特殊处理

2. **改进盘口数据检测逻辑**
   - **原始问题**: 程序实际一直在正常接收数据，但用户误以为没有接收到
   - **检测逻辑优化**: 简化了主题检测代码，移除了冗余的调试信息
   - **性能提升**: 减少了不必要的日志输出，提高程序运行效率

3. **修复 Channel 重复关闭问题**
   - 在 `Close()` 函数中添加了安全检查，避免重复关闭 channel 导致 panic
   - 使用 `select` 语句检查 channel 状态

4. **数据接收验证**
   - 程序经过实际测试，成功接收到：
     - ✅ 快照数据 (snapshot): 完整的盘口数据
     - ✅ 增量数据 (delta): 实时价格变化
     - ✅ 心跳响应 (pong): 连接保活
     - ✅ 订阅确认: 期权特有格式

### 技术细节说明：

**盘口数据检测逻辑改进前后对比：**

```go
// 改进前 - 包含大量调试信息
if topic, exists := response["topic"]; exists {
    if topicStr, ok := topic.(string); ok {
        log.Printf("检测到主题消息: %s", topicStr)
        if len(topicStr) >= 9 && topicStr[:9] == "orderbook" {
            log.Printf("确认为盘口数据，开始处理...")
            c.handleOrderbookData(data)
        } else {
            log.Printf("主题不是盘口数据: %s", topicStr)
        }
    } else {
        log.Printf("主题类型转换失败: %v", topic)
    }
} else {
    log.Printf("消息中没有找到 topic 字段")
}

// 改进后 - 简洁高效
if topic, exists := response["topic"]; exists {
    if topicStr, ok := topic.(string); ok {
        if len(topicStr) >= 9 && topicStr[:9] == "orderbook" {
            c.handleOrderbookData(data)
        }
    }
}
```

**期权订阅响应处理：**

```go
// 新增期权订阅响应结构
type OptionSubscribeResponse struct {
    Success bool   `json:"success"`
    ConnID  string `json:"conn_id"`
    Data    struct {
        FailTopics    []string `json:"failTopics"`
        SuccessTopics []string `json:"successTopics"`
    } `json:"data"`
    Type string `json:"type"` // "COMMAND_RESP"
}

// 处理期权特有的订阅响应格式
if responseType, exists := response["type"]; exists {
    if responseType == "COMMAND_RESP" {
        var optionResp OptionSubscribeResponse
        if err := json.Unmarshal(data, &optionResp); err == nil {
            if len(optionResp.Data.SuccessTopics) > 0 {
                log.Printf("期权订阅成功: %v", optionResp.Data.SuccessTopics)
            }
            if len(optionResp.Data.FailTopics) > 0 {
                log.Printf("期权订阅失败: %v", optionResp.Data.FailTopics)
            }
        }
        return
    }
}
```

## 功能特性

- ✅ **WebSocket 连接**: 连接到 Bybit 期权 WebSocket 端点
- ✅ **身份验证**: 支持 API Key 和 Secret 的 HMAC-SHA256 签名认证
- ✅ **盘口订阅**: 订阅指定期权合约的盘口数据
- ✅ **实时数据**: 处理快照(snapshot)和增量(delta)数据
- ✅ **心跳维护**: 自动发送心跳包保持连接活跃
- ✅ **优雅退出**: 支持 Ctrl+C 优雅关闭连接
- ✅ **详细注释**: 代码包含详细的中文注释

## 项目结构

```
bybit-websocket-test/
├── main.go          # 主程序文件 (包含所有功能)
├── go.mod           # Go 模块文件
├── go.sum           # 依赖版本锁定文件
└── README.md        # 使用说明文档
```

## 环境要求

- Go 1.21 或更高版本
- 网络连接 (能够访问 Bybit WebSocket 端点)

## 安装和运行

### 1. 克隆或下载项目

```bash
# 如果是从 git 仓库克隆
git clone <repository-url>
cd bybit-websocket-test

# 或者直接下载源码文件到目录中
```

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置 API 密钥 (可选)

打开 `main.go` 文件，找到以下配置部分：

```go
// API 配置 - 请在这里设置您的 Bybit API Key 和 Secret
var (
    API_KEY    = "YOUR_API_KEY_HERE"    // 请替换为您的 API Key
    API_SECRET = "YOUR_API_SECRET_HERE" // 请替换为您的 API Secret
)
```

**注意**: 
- 对于公共盘口数据，**无需设置 API 密钥**即可正常使用
- 如果您需要测试认证功能，请将上述配置替换为您的真实 API 密钥
- API 密钥可以在 [Bybit 官网](https://www.bybit.com) 的 API 管理页面获取

### 4. 运行程序

```bash
go run main.go
```

### 5. 停止程序

按 `Ctrl+C` 优雅退出程序。

## 程序输出示例

```
=== Bybit WebSocket 期权盘口数据测试程序 ===
目标合约: ETH-4JUL25-2600-C-USDT
WebSocket 端点: wss://stream.bybit.com/v5/public/option
盘口深度: 25档 (显示前5档)
==========================================

注意: 当前使用默认 API 配置
如需认证功能，请在源码中设置您的 API_KEY 和 API_SECRET
对于公共盘口数据，无需认证即可使用

2024/01/15 10:30:15 正在连接到 Bybit WebSocket: wss://stream.bybit.com/v5/public/option
2024/01/15 10:30:15 WebSocket 连接成功!
2024/01/15 10:30:15 注意: 使用默认 API 配置，跳过认证 (公共频道不需要认证)
2024/01/15 10:30:15 已订阅盘口数据: orderbook.25.ETH-4JUL25-2600-C-USDT
2024/01/15 10:30:15 订阅响应: {"success":true,"ret_msg":"","op":"subscribe","conn_id":"..."}

=== 盘口数据 (snapshot) ===
合约: ETH-4JUL25-2600-C-USDT
时间: 2024-01-15 10:30:16
更新ID: 12345678, 序列号: 987654321

买盘 (Bids) - 前5档:
价格		数量
------------------------
150.50		0.1
150.25		0.2
150.00		0.5
149.75		0.3
149.50		0.1

卖盘 (Asks) - 前5档:
价格		数量
------------------------
151.00		0.2
151.25		0.1
151.50		0.3
151.75		0.2
152.00		0.4
========================
```

## 技术实现说明

### WebSocket 连接
- 使用 `gorilla/websocket` 库建立 WebSocket 连接
- 连接到 Bybit 期权公共频道: `wss://stream.bybit.com/v5/public/option`

### 身份验证 (可选)
- **重要**: 订单簿数据属于公共数据，**无需认证**
- 程序仍实现了完整的 HMAC-SHA256 签名认证机制供学习参考
- 签名字符串格式: `GET/realtime{expires}`
- 过期时间设置为当前时间 + 1 分钟

### 数据订阅
- 订阅主题格式: `orderbook.{depth}.{symbol}`
- 本程序订阅: `orderbook.25.ETH-4JUL25-2600-C-USDT`
- 期权订阅响应格式特殊，程序已适配处理
- 显示前 5 档买卖盘数据

### 心跳机制
- 每 20 秒发送一次 ping 消息 (符合官方建议)
- 防止连接因空闲而被服务器关闭

### 数据处理
- 处理快照 (snapshot) 数据: 完整的盘口数据
- 处理增量 (delta) 数据: 盘口变化数据
- 实时显示买卖盘前 5 档价格和数量
- 支持期权特有的订阅响应格式

## 配置说明

可以在 `main.go` 中修改以下配置:

```go
const (
    // WebSocket 端点 (可切换主网/测试网)
    BYBIT_WS_URL = "wss://stream.bybit.com/v5/public/option"
    
    // 期权合约符号 (可修改为其他期权合约)
    OPTION_SYMBOL = "ETH-4JUL25-2600-C-USDT"
    
    // 盘口深度 (期权支持 25 或 100)
    ORDERBOOK_DEPTH = 25
    
    // 心跳间隔 (秒)
    PING_INTERVAL = 20
)
```

## 故障排除

### 1. 连接失败
- 检查网络连接
- 确认防火墙设置允许 WebSocket 连接
- 尝试使用测试网端点

### 2. 订阅失败
- 确认期权合约符号正确
- 检查合约是否存在且处于交易状态

### 3. 无数据接收 ⚠️ **常见误解**
- **重要**: 如果程序显示 "期权订阅成功" 但您认为没有数据，请检查：
  - 程序实际上可能正在接收数据，只是合约交易不活跃
  - 确认您查看的合约符号是否与程序中设置的一致
  - 期权合约可能已过期或交易量很低
- **调试方法**:
  - 查看程序启动时的合约符号显示
  - 确认合约在交易时间内且有交易活动
  - 检查 WebSocket 连接状态和心跳响应

### 4. 合约符号问题 🔍 **新增**
- **常见问题**: 源码中设置的合约可能与实际活跃合约不同
- **解决方案**:
  - 检查 Bybit 官网当前可用的期权合约
  - 修改源码中的 `OPTION_SYMBOL` 常量
  - 确保合约格式正确: `ETH-{日期}-{行权价}-{类型}-USDT`
- **示例**: `ETH-4JUN25-2600-C-USDT` (看涨期权) 或 `ETH-4JUN25-2600-P-USDT` (看跌期权)

## 依赖库

- `github.com/gorilla/websocket`: WebSocket 客户端库
- Go 标准库:
  - `crypto/hmac`: HMAC 签名
  - `crypto/sha256`: SHA256 哈希
  - `encoding/json`: JSON 处理
  - `net/url`: URL 解析
  - `time`: 时间处理

## 许可证

本项目仅用于学习和测试目的。

## 版本历史

### v1.1 (当前版本) - 2025/06/03
- ✅ 修复期权订阅响应格式处理
- ✅ 改进盘口数据检测逻辑，移除冗余调试信息
- ✅ 修复 Channel 重复关闭问题
- ✅ 添加期权特有订阅响应结构体
- ✅ 优化程序性能和稳定性
- ✅ 完善故障排除文档

### v1.0 - 初始版本
- ✅ 基本 WebSocket 连接功能
- ✅ 期权盘口数据订阅
- ✅ HMAC-SHA256 签名认证（可选）
- ✅ 心跳维护机制
- ✅ 实时数据显示

## 免责声明

- 本程序仅用于测试 Bybit WebSocket API 的连通性和数据格式
- 不构成任何投资建议
- 使用前请确保遵守相关法律法规和交易所规则
- 作者不对使用本程序造成的任何损失承担责任
