package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
)

// 配置常量
const (
	// Bybit 期权 WebSocket 端点 (主网)
	BYBIT_WS_URL = "wss://stream.bybit.com/v5/public/option"
	// 测试网端点 (如需要可以切换)
	// BYBIT_WS_URL = "wss://stream-testnet.bybit.com/v5/public/option"

	// 期权合约符号
	OPTION_SYMBOL = "ETH-4JUN25-2600-C-USDT"

	// 盘口深度 (5档数据)
	ORDERBOOK_DEPTH = 25 // 期权最小支持25档，我们只显示前5档

	// 心跳间隔 (秒)
	PING_INTERVAL = 20
)

// API 配置 - 请在这里设置您的 Bybit API Key 和 Secret
var (
	API_KEY    = "YOUR_API_KEY_HERE"    // 请替换为您的 API Key
	API_SECRET = "YOUR_API_SECRET_HERE" // 请替换为您的 API Secret
)

// WebSocket 消息结构
type WSMessage struct {
	ReqID string      `json:"req_id,omitempty"`
	Op    string      `json:"op"`
	Args  interface{} `json:"args,omitempty"`
}

// 认证消息结构
type AuthArgs struct {
	APIKey    string `json:"api_key"`
	Expires   int64  `json:"expires"`
	Signature string `json:"signature"`
}

// 订阅响应结构 (通用)
type SubscribeResponse struct {
	Success bool   `json:"success"`
	RetMsg  string `json:"ret_msg"`
	Op      string `json:"op"`
	ConnID  string `json:"conn_id"`
}

// 期权订阅响应结构 (特殊格式)
type OptionSubscribeResponse struct {
	Success bool   `json:"success"`
	ConnID  string `json:"conn_id"`
	Data    struct {
		FailTopics    []string `json:"failTopics"`
		SuccessTopics []string `json:"successTopics"`
	} `json:"data"`
	Type string `json:"type"` // "COMMAND_RESP"
}

// 盘口数据结构
type OrderbookData struct {
	Topic string `json:"topic"`
	Type  string `json:"type"` // snapshot 或 delta
	TS    int64  `json:"ts"`
	Data  struct {
		Symbol string     `json:"s"`
		Bids   [][]string `json:"b"` // [价格, 数量]
		Asks   [][]string `json:"a"` // [价格, 数量]
		U      int64      `json:"u"` // 更新ID
		Seq    int64      `json:"seq"`
	} `json:"data"`
	CTS int64 `json:"cts,omitempty"`
}

// WebSocket 客户端结构
type WSClient struct {
	conn      *websocket.Conn
	url       string
	apiKey    string
	apiSecret string
	done      chan struct{}
}

// 创建新的 WebSocket 客户端
func NewWSClient(wsURL, apiKey, apiSecret string) *WSClient {
	return &WSClient{
		url:       wsURL,
		apiKey:    apiKey,
		apiSecret: apiSecret,
		done:      make(chan struct{}),
	}
}

// 生成 HMAC-SHA256 签名
func (c *WSClient) generateSignature(expires int64) string {
	// 构造签名字符串: GET/realtime{expires}
	message := fmt.Sprintf("GET/realtime%d", expires)

	// 使用 HMAC-SHA256 生成签名
	h := hmac.New(sha256.New, []byte(c.apiSecret))
	h.Write([]byte(message))

	return hex.EncodeToString(h.Sum(nil))
}

// 连接到 WebSocket
func (c *WSClient) Connect() error {
	log.Printf("正在连接到 Bybit WebSocket: %s", c.url)

	// 解析 URL
	u, err := url.Parse(c.url)
	if err != nil {
		return fmt.Errorf("解析 URL 失败: %v", err)
	}

	// 建立 WebSocket 连接
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("WebSocket 连接失败: %v", err)
	}

	c.conn = conn
	log.Println("WebSocket 连接成功!")

	return nil
}

// 认证连接 (期权公共频道不需要认证，但保留此功能以备将来使用)
func (c *WSClient) Authenticate() error {
	if c.apiKey == "YOUR_API_KEY_HERE" || c.apiSecret == "YOUR_API_SECRET_HERE" {
		log.Println("注意: 使用默认 API 配置，跳过认证 (公共频道不需要认证)")
		return nil
	}

	// 生成过期时间戳 (当前时间 + 1 分钟)
	expires := time.Now().Unix()*1000 + 60000

	// 生成签名
	signature := c.generateSignature(expires)

	// 构造认证消息
	authMsg := WSMessage{
		ReqID: "auth_" + strconv.FormatInt(time.Now().Unix(), 10),
		Op:    "auth",
		Args:  []interface{}{c.apiKey, expires, signature},
	}

	// 发送认证消息
	if err := c.conn.WriteJSON(authMsg); err != nil {
		return fmt.Errorf("发送认证消息失败: %v", err)
	}

	log.Println("认证消息已发送")
	return nil
}

// 订阅盘口数据
func (c *WSClient) SubscribeOrderbook() error {
	// 构造订阅消息
	topic := fmt.Sprintf("orderbook.%d.%s", ORDERBOOK_DEPTH, OPTION_SYMBOL)

	subMsg := WSMessage{
		ReqID: "sub_" + strconv.FormatInt(time.Now().Unix(), 10),
		Op:    "subscribe",
		Args:  []string{topic},
	}

	// 发送订阅消息
	if err := c.conn.WriteJSON(subMsg); err != nil {
		return fmt.Errorf("发送订阅消息失败: %v", err)
	}

	log.Printf("已订阅盘口数据: %s", topic)
	return nil
}

// 发送心跳
func (c *WSClient) SendPing() error {
	pingMsg := WSMessage{
		ReqID: "ping_" + strconv.FormatInt(time.Now().Unix(), 10),
		Op:    "ping",
	}

	if err := c.conn.WriteJSON(pingMsg); err != nil {
		return fmt.Errorf("发送心跳失败: %v", err)
	}

	log.Println("心跳已发送")
	return nil
}

// 启动心跳定时器
func (c *WSClient) startHeartbeat() {
	ticker := time.NewTicker(PING_INTERVAL * time.Second)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				if err := c.SendPing(); err != nil {
					log.Printf("心跳发送失败: %v", err)
				}
			case <-c.done:
				return
			}
		}
	}()
}

// 处理接收到的消息
func (c *WSClient) handleMessage(messageType int, data []byte) {
	// 尝试解析为通用响应
	var response map[string]interface{}
	if err := json.Unmarshal(data, &response); err != nil {
		log.Printf("解析消息失败: %v", err)
		return
	}

	// 检查是否为订阅响应
	if op, exists := response["op"]; exists {
		switch op {
		case "subscribe":
			log.Printf("订阅响应: %s", string(data))
		case "pong":
			log.Println("收到心跳响应")
		case "auth":
			log.Printf("认证响应: %s", string(data))
		}
		return
	}

	// 检查是否为期权订阅响应 (特殊格式)
	if responseType, exists := response["type"]; exists {
		if responseType == "COMMAND_RESP" {
			var optionResp OptionSubscribeResponse
			if err := json.Unmarshal(data, &optionResp); err == nil {
				if len(optionResp.Data.SuccessTopics) > 0 {
					log.Printf("期权订阅成功: %v", optionResp.Data.SuccessTopics)
				}
				if len(optionResp.Data.FailTopics) > 0 {
					log.Printf("期权订阅失败: %v", optionResp.Data.FailTopics)
				}
			}
			return
		}
	}

	// 检查是否为盘口数据
	if topic, exists := response["topic"]; exists {
		if topicStr, ok := topic.(string); ok {
			if len(topicStr) >= 9 && topicStr[:9] == "orderbook" {
				c.handleOrderbookData(data)
			}
		}
	}
}

// 处理盘口数据
func (c *WSClient) handleOrderbookData(data []byte) {
	var orderbook OrderbookData
	if err := json.Unmarshal(data, &orderbook); err != nil {
		log.Printf("解析盘口数据失败: %v", err)
		return
	}

	// 打印盘口数据摘要
	fmt.Printf("\n=== 盘口数据 (%s) ===\n", orderbook.Type)
	fmt.Printf("合约: %s\n", orderbook.Data.Symbol)
	fmt.Printf("时间: %s\n", time.Unix(orderbook.TS/1000, 0).Format("2006-01-02 15:04:05"))
	fmt.Printf("更新ID: %d, 序列号: %d\n", orderbook.Data.U, orderbook.Data.Seq)

	// 显示前5档买盘 (Bids)
	fmt.Println("\n买盘 (Bids) - 前5档:")
	fmt.Println("价格\t\t数量")
	fmt.Println("------------------------")
	for i, bid := range orderbook.Data.Bids {
		if i >= 5 { // 只显示前5档
			break
		}
		if len(bid) >= 2 {
			fmt.Printf("%s\t\t%s\n", bid[0], bid[1])
		}
	}

	// 显示前5档卖盘 (Asks)
	fmt.Println("\n卖盘 (Asks) - 前5档:")
	fmt.Println("价格\t\t数量")
	fmt.Println("------------------------")
	for i, ask := range orderbook.Data.Asks {
		if i >= 5 { // 只显示前5档
			break
		}
		if len(ask) >= 2 {
			fmt.Printf("%s\t\t%s\n", ask[0], ask[1])
		}
	}

	fmt.Println("========================\n")
}

// 启动消息监听
func (c *WSClient) Listen() {
	defer close(c.done)

	for {
		messageType, message, err := c.conn.ReadMessage()
		if err != nil {
			log.Printf("读取消息失败: %v", err)
			return
		}

		c.handleMessage(messageType, message)
	}
}

// 关闭连接
func (c *WSClient) Close() {
	if c.conn != nil {
		c.conn.Close()
	}
	// 安全关闭 done channel (避免重复关闭)
	select {
	case <-c.done:
		// channel 已经关闭
	default:
		close(c.done)
	}
}

// 主函数
func main() {
	// 打印程序信息
	fmt.Println("=== Bybit WebSocket 期权盘口数据测试程序 ===")
	fmt.Printf("目标合约: %s\n", OPTION_SYMBOL)
	fmt.Printf("WebSocket 端点: %s\n", BYBIT_WS_URL)
	fmt.Printf("盘口深度: %d档 (显示前5档)\n", ORDERBOOK_DEPTH)
	fmt.Println("==========================================\n")

	// 检查 API 配置
	if API_KEY == "YOUR_API_KEY_HERE" || API_SECRET == "YOUR_API_SECRET_HERE" {
		fmt.Println("注意: 当前使用默认 API 配置")
		fmt.Println("如需认证功能，请在源码中设置您的 API_KEY 和 API_SECRET")
		fmt.Println("对于公共盘口数据，无需认证即可使用\n")
	}

	// 创建 WebSocket 客户端
	client := NewWSClient(BYBIT_WS_URL, API_KEY, API_SECRET)

	// 连接到 WebSocket
	if err := client.Connect(); err != nil {
		log.Fatalf("连接失败: %v", err)
	}
	defer client.Close()

	// 认证 (可选，公共频道不需要)
	if err := client.Authenticate(); err != nil {
		log.Printf("认证失败: %v", err)
	}

	// 订阅盘口数据
	if err := client.SubscribeOrderbook(); err != nil {
		log.Fatalf("订阅失败: %v", err)
	}

	// 启动心跳
	client.startHeartbeat()

	// 设置信号处理，优雅退出
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt, syscall.SIGTERM)

	// 启动消息监听 (在单独的 goroutine 中)
	go client.Listen()

	// 等待中断信号
	<-interrupt
	log.Println("收到中断信号，正在关闭连接...")

	// 发送关闭消息
	err := client.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	if err != nil {
		log.Printf("发送关闭消息失败: %v", err)
	}

	// 等待连接关闭
	select {
	case <-client.done:
	case <-time.After(time.Second):
	}

	fmt.Println("程序已退出")
}
