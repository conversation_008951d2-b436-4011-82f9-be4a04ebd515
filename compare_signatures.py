#!/usr/bin/env python3
"""
对比Python和Go的签名生成
"""

import base64
from cryptography.hazmat.primitives.asymmetric import ed25519
from cryptography.hazmat.primitives import serialization
from ed25519_config import ED25519_PRIVATE_KEY_PEM

def test_signature_with_fixed_params():
    """使用固定参数测试签名"""
    
    # 加载Ed25519私钥
    ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
        serialization.load_pem_private_key(
            ED25519_PRIVATE_KEY_PEM.encode('utf-8'),
            password=None
        ).private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
    )
    
    # 显示私钥信息
    private_key_hex = ed25519_private_key.private_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PrivateFormat.Raw,
        encryption_algorithm=serialization.NoEncryption()
    ).hex()
    
    print("🔑 Python程序密钥信息:")
    print(f"私钥 (Hex): {private_key_hex}")
    print()
    
    # 测试用例1: 简单的timestamp签名
    print("📋 测试用例1: 简单timestamp签名")
    print("-" * 50)
    test_string_1 = "timestamp=1749225000000"
    signature_1 = ed25519_private_key.sign(test_string_1.encode('utf-8'))
    signature_1_base64 = base64.b64encode(signature_1).decode('utf-8')
    
    print(f"签名字符串: {test_string_1}")
    print(f"Python签名: {signature_1_base64}")
    print()
    
    # 测试用例2: 复杂的下单参数签名
    print("📋 测试用例2: 复杂下单参数签名")
    print("-" * 50)
    test_string_2 = "symbol=ETHUSDT&side=BUY&type=LIMIT&quantity=0.01&price=2500.00&timeInForce=GTC&newClientOrderId=test_12345&timestamp=1749225000000&test=true"
    signature_2 = ed25519_private_key.sign(test_string_2.encode('utf-8'))
    signature_2_base64 = base64.b64encode(signature_2).decode('utf-8')
    
    print(f"签名字符串: {test_string_2}")
    print(f"Python签名: {signature_2_base64}")
    print()
    
    # 测试用例3: 账户信息查询
    print("📋 测试用例3: 账户信息查询")
    print("-" * 50)
    test_string_3 = "timestamp=1749225000000"
    signature_3 = ed25519_private_key.sign(test_string_3.encode('utf-8'))
    signature_3_base64 = base64.b64encode(signature_3).decode('utf-8')
    
    print(f"签名字符串: {test_string_3}")
    print(f"Python签名: {signature_3_base64}")
    print()
    
    print("=" * 60)
    print("🔧 请在Go程序中使用相同的测试字符串进行签名对比")
    print("如果Go程序的签名结果与上述Python签名不同，")
    print("说明签名算法实现有差异。")
    print("=" * 60)

if __name__ == "__main__":
    test_signature_with_fixed_params()
