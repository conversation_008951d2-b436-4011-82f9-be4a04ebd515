package main

import (
	"fmt"
	"log"

	"github.com/shopspring/decimal"
)

// EmbeddedConfig 内嵌配置结构
type EmbeddedConfig struct {
	// 币安API配置
	Binance struct {
		ApiKey            string // Ed25519 API密钥
		SecretKey         string // 保留用于向后兼容
		Ed25519PrivateKey string // Ed25519私钥 (Hex格式)
	}

	// 交易配置
	Trading struct {
		MakerFeeRate decimal.Decimal // 挂单费率 0.02% (Maker)
		TakerFeeRate decimal.Decimal // 吃单费率 0.04% (Taker)
	}

	// 服务器配置
	Server struct {
		Port int
		Host string
	}

	// 日志配置
	Logging struct {
		Level      string // debug, info, warn, error
		File       string
		MaxSize    int // MB
		MaxBackups int
		MaxAge     int // days
	}

	// 默认交易参数
	Defaults struct {
		SlippageTolerance decimal.Decimal // 滑点容忍度
		StopSlippage      decimal.Decimal // 止损滑点
		MarketTimeout     int             // 市价单超时时间(秒)
		VolumeThreshold   decimal.Decimal // 成交量阈值
	}

	// 风险控制
	Risk struct {
		MaxTasks        int             // 最大并发任务数
		MaxPositionSize decimal.Decimal // 最大单个仓位大小
		DailyLossLimit  decimal.Decimal // 日损失限制 (USDT)
	}

	// 监控配置
	Monitoring struct {
		StatusInterval      int // 状态输出间隔 (秒)
		HealthCheckInterval int // 健康检查间隔 (秒)
	}

	// 订单监控配置 (极简方案)
	OrderMonitor struct {
		RestPollMs int // REST API轮询间隔毫秒
		RankParam  int // 排名参数1-20
		ModifyMs   int // 修改间隔毫秒
		MaxCount   int // 最大修改次数
	}

	// WebSocket连接配置
	WebSocket struct {
		ReconnectIntervalMs  int // 重连间隔毫秒
		MaxReconnectAttempts int // 最大重连次数
		PingIntervalMs       int // ping间隔毫秒
		PongIntervalMs       int // pong间隔毫秒
		HealthCheckMs        int // 健康检查间隔毫秒
		SilenceTimeoutMs     int // 静默超时毫秒
		ChannelBufferSize    int // 通道缓冲区大小
		ListenKeyRenewalMs   int // listenKey续期间隔毫秒
	}

	// 初始化配置
	Initialization struct {
		MaxRetries              int  // 最大重试次数
		RetryIntervalMs         int  // 重试间隔毫秒
		ConnectionTimeoutMs     int  // 连接超时毫秒
		AuthenticationTimeoutMs int  // 认证超时毫秒
		ListenKeyTimeoutMs      int  // listenKey创建超时毫秒
		EnableAsyncInit         bool // 启用异步初始化
		EnableRollback          bool // 启用失败回滚
	}
}

// GetEmbeddedConfig 返回内嵌的配置
func GetEmbeddedConfig() *EmbeddedConfig {
	config := &EmbeddedConfig{}

	// 币安API配置 - Ed25519密钥配置
	config.Binance.ApiKey = "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso"            // 在币安创建的Ed25519 API密钥
	config.Binance.SecretKey = "legacy_hmac_key"                                                          // 保留字段，不再使用
	config.Binance.Ed25519PrivateKey = "6744730860246040c6389827880a2d52e14ebce72d18eea45dda64b76415d5e8" // 生成的Ed25519私钥

	// 交易配置
	config.Trading.MakerFeeRate = decimal.NewFromFloat(0.0002) // 0.02%
	config.Trading.TakerFeeRate = decimal.NewFromFloat(0.0004) // 0.04%

	// 服务器配置
	config.Server.Port = 5899
	config.Server.Host = "localhost"

	// 日志配置
	config.Logging.Level = "info"
	config.Logging.File = "hedge.log"
	config.Logging.MaxSize = 100
	config.Logging.MaxBackups = 3
	config.Logging.MaxAge = 28

	// 默认交易参数
	config.Defaults.SlippageTolerance = decimal.NewFromFloat(0.0005) // 0.05%
	config.Defaults.StopSlippage = decimal.NewFromFloat(0.0005)      // 0.05%
	config.Defaults.MarketTimeout = 5                                // 5秒
	config.Defaults.VolumeThreshold = decimal.NewFromFloat(0.5)      // 50%

	// 风险控制
	config.Risk.MaxTasks = 20
	config.Risk.MaxPositionSize = decimal.NewFromFloat(1.0)   // 1.0 ETH
	config.Risk.DailyLossLimit = decimal.NewFromFloat(1000.0) // 1000 USDT

	// 监控配置
	config.Monitoring.StatusInterval = 10      // 10秒
	config.Monitoring.HealthCheckInterval = 30 // 30秒

	// 订单监控配置 (极简方案)
	config.OrderMonitor.RestPollMs = 1000 // REST API轮询间隔200ms
	config.OrderMonitor.RankParam = 1     // 默认排名参数1 (QUEUE)
	config.OrderMonitor.ModifyMs = 50     // 修改间隔1000ms
	config.OrderMonitor.MaxCount = 100    // 最大修改次数100

	// WebSocket连接配置 - 优化心跳频率，避免过度心跳导致连接断开
	config.WebSocket.ReconnectIntervalMs = 1000   // 重连间隔1秒
	config.WebSocket.MaxReconnectAttempts = 10    // 最大重连10次
	config.WebSocket.PingIntervalMs = 180000      // ping间隔3分钟（币安推荐）
	config.WebSocket.PongIntervalMs = 0           // 禁用主动pong发送
	config.WebSocket.HealthCheckMs = 30000        // 健康检查30秒
	config.WebSocket.SilenceTimeoutMs = 300000    // 静默超时5分钟
	config.WebSocket.ChannelBufferSize = 1000     // 通道缓冲区1000
	config.WebSocket.ListenKeyRenewalMs = 1200000 // listenKey续期20分钟

	// 初始化配置
	config.Initialization.MaxRetries = 3                 // 最大重试3次
	config.Initialization.RetryIntervalMs = 2000         // 重试间隔2秒
	config.Initialization.ConnectionTimeoutMs = 10000    // 连接超时10秒
	config.Initialization.AuthenticationTimeoutMs = 5000 // 认证超时5秒
	config.Initialization.ListenKeyTimeoutMs = 5000      // listenKey创建超时5秒
	config.Initialization.EnableAsyncInit = true         // 启用异步初始化
	config.Initialization.EnableRollback = true          // 启用失败回滚

	return config
}

// GetBinanceConfig 获取币安配置
func GetBinanceConfig() (apiKey, secretKey string) {
	config := GetEmbeddedConfig()

	// 检查Ed25519 API密钥是否已配置
	if config.Binance.ApiKey == "" || config.Binance.ApiKey == "请替换为你的Ed25519_API密钥" {
		log.Fatal("❌ 请在 embedded_config.go 中配置你的Ed25519 API密钥")
	}

	log.Printf("🔑 使用Ed25519 API密钥: %s...", config.Binance.ApiKey[:8])
	return config.Binance.ApiKey, config.Binance.SecretKey
}

// GetTradingConfig 获取交易配置
func GetTradingConfig() (makerFeeRate, takerFeeRate float64) {
	config := GetEmbeddedConfig()
	return config.Trading.MakerFeeRate.InexactFloat64(), config.Trading.TakerFeeRate.InexactFloat64()
}

// LoadEmbeddedConfig 加载内嵌配置（保持兼容性）
func LoadEmbeddedConfig() error {
	// 验证Ed25519配置
	config := GetEmbeddedConfig()

	if config.Binance.ApiKey == "" || config.Binance.ApiKey == "请替换为你的Ed25519_API密钥" {
		return fmt.Errorf("请在 embedded_config.go 中配置你的Ed25519 API密钥")
	}

	if config.Binance.Ed25519PrivateKey == "" {
		return fmt.Errorf("请在 embedded_config.go 中配置Ed25519私钥")
	}

	log.Printf("✅ 内嵌配置加载成功")
	log.Printf("🔧 API密钥: %s...", config.Binance.ApiKey[:8])
	log.Printf("🔴 使用币安主网 - 请确保你了解风险！")
	log.Printf("🔑 使用Ed25519密钥进行API签名")

	return nil
}
