package main

import (
	"fmt"
	"log"
	"time"

	"github.com/shopspring/decimal"
)

// NewTaskManager 创建新的任务管理器
func NewTaskManager() *TaskManager {
	tm := &TaskManager{
		tasks: make(map[string]*HedgeTask),
	}

	// 初始化数据管理器
	tm.dataManager = NewDataManager()

	// 初始化事件日志管理器
	tm.eventLogger = NewEventLogger()

	return tm
}

// AddTask 添加新的对冲任务
func (tm *TaskManager) AddTask(req *TaskRequest) (*HedgeTask, error) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 应用默认参数（如果请求中的参数为空或零值）
	tm.applyDefaultParams(req)

	// 验证参数
	if err := tm.validateTaskRequest(req); err != nil {
		return nil, err
	}

	// 生成任务ID - 根据来源使用不同的ID格式
	var taskID string
	now := time.Now()
	timestamp := now.UnixNano()

	if req.Source == "options" {
		// 期权系统任务使用 M + 6位数字格式
		taskID = fmt.Sprintf("M%06d", timestamp%1000000)
	} else {
		// 基础任务使用7位数字格式
		taskID = fmt.Sprintf("%07d", timestamp%10000000)
	}

	// 设置来源标识，默认为手动
	source := "manual"
	if req.Source != "" {
		source = req.Source
	}

	// 创建任务
	task := &HedgeTask{
		ID:                taskID,
		Symbol:            req.Symbol,
		Direction:         req.Direction,
		TargetPrice:       decimal.NewFromFloat(req.TargetPrice),
		StopRate:          decimal.NewFromFloat(req.StopRate),
		Amount:            decimal.NewFromFloat(req.Amount),
		SlippageTolerance: decimal.NewFromFloat(req.SlippageTolerance),
		StopSlippage:      decimal.NewFromFloat(req.StopSlippage),
		MarketTimeout:     req.MarketTimeout,
		VolumeThreshold:   decimal.NewFromFloat(req.VolumeThreshold),
		PostOnlyMode:      req.PostOnlyMode,
		Source:            source,
		OptionContract:    req.OptionContract,
		Status:            StatusRunning,
		Events:            make([]Event, 0),
		Statistics:        &Stats{},
		StopChan:          make(chan struct{}),
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
		onDataChange:      func() { tm.dataManager.TriggerSave() },

		// 智能订单相关字段
		SmartOrderEnabled: req.SmartOrderEnabled,
		SmartOrderConfig:  DefaultSmartOrderConfig(),
	}

	// 初始化极简订单监控字段
	task.orderBusy.Store(false)
	// stopChan 在需要时动态创建

	// 存储任务
	tm.tasks[taskID] = task

	// 启动任务
	go task.Run()

	// 触发数据保存
	tm.dataManager.TriggerSave()

	// 记录任务创建事件
	tm.eventLogger.LogTaskStatus(taskID, "", StatusRunning, "任务创建")

	log.Printf("创建对冲任务: %s, 交易对: %s, 方向: %s, 目标价: %s, 来源: %s",
		taskID, req.Symbol, req.Direction, task.TargetPrice.String(), source)

	return task, nil
}

// StopTask 停止对冲任务
func (tm *TaskManager) StopTask(taskID string) error {
	return tm.StopTaskWithOptions(taskID, false)
}

// StopTaskWithOptions 停止对冲任务（带选项）
func (tm *TaskManager) StopTaskWithOptions(taskID string, skipClosePosition bool) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	if task.Status == StatusStopped {
		return fmt.Errorf("任务已停止: %s", taskID)
	}

	oldStatus := task.Status

	// 设置跳过平仓标志
	if skipClosePosition {
		task.skipCloseOnStop = true
	}

	// 发送停止信号
	
	close(task.StopChan)
	task.Status = StatusStopped
	task.UpdatedAt = time.Now()

	// 触发数据保存
	tm.dataManager.TriggerSave()

	// 记录任务停止事件
	reason := "手动停止"
	if skipClosePosition {
		reason = "手动停止(跳过平仓)"
	}
	tm.eventLogger.LogTaskStatus(taskID, oldStatus, StatusStopped, reason)

	log.Printf("停止对冲任务: %s", taskID)
	return nil
}

// GetTask 获取任务信息
func (tm *TaskManager) GetTask(taskID string) (*HedgeTask, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	return task, nil
}

// GetAllTasks 获取所有任务
func (tm *TaskManager) GetAllTasks() map[string]*HedgeTask {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	// 创建副本避免并发问题
	result := make(map[string]*HedgeTask)
	for id, task := range tm.tasks {
		result[id] = task
	}

	return result
}

// applyDefaultParams 应用默认参数（如果请求中的参数为空或零值）
func (tm *TaskManager) applyDefaultParams(req *TaskRequest) {
	// 获取embedded config中的默认参数
	embeddedConfig := GetEmbeddedConfig()

	// 如果可选参数为零值，则使用embedded_config中的默认值
	if req.SlippageTolerance == 0 {
		req.SlippageTolerance = embeddedConfig.Defaults.SlippageTolerance.InexactFloat64()
	}

	if req.StopSlippage == 0 {
		req.StopSlippage = embeddedConfig.Defaults.StopSlippage.InexactFloat64()
	}

	if req.MarketTimeout == 0 {
		req.MarketTimeout = embeddedConfig.Defaults.MarketTimeout
	}

	if req.VolumeThreshold == 0 {
		req.VolumeThreshold = embeddedConfig.Defaults.VolumeThreshold.InexactFloat64()
	}

	// 对于期权系统，如果没有指定PostOnlyMode，默认设置为true
	if req.Source == "options" && !req.PostOnlyMode {
		req.PostOnlyMode = true
	}

	log.Printf("📋 应用默认参数: 滑点容忍度=%.4f, 停止滑点=%.4f, 市价超时=%ds, 盘口量阈值=%.2f, 限价模式=%t",
		req.SlippageTolerance, req.StopSlippage, req.MarketTimeout, req.VolumeThreshold, req.PostOnlyMode)
}

// validateTaskRequest 验证任务请求参数
func (tm *TaskManager) validateTaskRequest(req *TaskRequest) error {
	// 验证交易对
	if req.Symbol != SymbolETHUSDC && req.Symbol != SymbolETHUSDT {
		return fmt.Errorf("不支持的交易对: %s", req.Symbol)
	}

	// 验证对冲方向
	if req.Direction != DirectionLong && req.Direction != DirectionShort {
		return fmt.Errorf("无效的对冲方向: %s", req.Direction)
	}

	// 验证目标价格
	if req.TargetPrice <= 0 {
		return fmt.Errorf("目标价格必须大于0: %f", req.TargetPrice)
	}

	// 验证对冲阈值
	if req.StopRate <= 0 || req.StopRate >= 1 {
		return fmt.Errorf("对冲阈值必须在0-1之间: %f", req.StopRate)
	}

	// 验证下单数量
	if req.Amount <= 0 {
		return fmt.Errorf("下单数量必须大于0: %f", req.Amount)
	}

	// 验证滑点容忍度
	if req.SlippageTolerance < 0 || req.SlippageTolerance >= 1 {
		return fmt.Errorf("滑点容忍度必须在0-1之间: %f", req.SlippageTolerance)
	}

	// 验证停止滑点
	if req.StopSlippage < 0 || req.StopSlippage >= 1 {
		return fmt.Errorf("停止滑点必须在0-1之间: %f", req.StopSlippage)
	}

	// 验证市价超时
	if req.MarketTimeout <= 0 {
		return fmt.Errorf("市价超时必须大于0: %d", req.MarketTimeout)
	}

	// 验证盘口量阈值
	if req.VolumeThreshold <= 0 || req.VolumeThreshold > 1 {
		return fmt.Errorf("盘口量阈值必须在0-1之间: %f", req.VolumeThreshold)
	}

	// 对于期权系统，验证必要的期权合约参数
	if req.Source == "options" {
		if req.OptionContract == "" {
			return fmt.Errorf("期权系统任务必须提供期权合约名称")
		}
	}

	return nil
}

// LoadData 加载持久化数据
func (tm *TaskManager) LoadData() error {
	// 加载任务数据
	tasks, err := tm.dataManager.LoadTasks()
	if err != nil {
		return fmt.Errorf("加载任务数据失败: %v", err)
	}

	// 加载事件数据
	events, err := tm.dataManager.LoadEvents()
	if err != nil {
		return fmt.Errorf("加载事件数据失败: %v", err)
	}

	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 恢复任务数据
	tm.tasks = tasks

	// 恢复事件数据到对应任务，并设置回调函数
	for taskID, taskEvents := range events {
		if task, exists := tm.tasks[taskID]; exists {
			task.Events = taskEvents
			// 为恢复的任务设置数据变化回调
			task.onDataChange = func() { tm.dataManager.TriggerSave() }
		}
	}

	// 为没有事件数据的任务也设置回调函数
	for _, task := range tm.tasks {
		if task.onDataChange == nil {
			task.onDataChange = func() { tm.dataManager.TriggerSave() }
		}
	}

	log.Printf("✅ 数据恢复完成: %d个任务, %d个任务有事件记录", len(tasks), len(events))
	return nil
}

// StartDataManager 启动数据管理器
func (tm *TaskManager) StartDataManager() {
	tm.dataManager.StartAutoSave(tm)
}

// SaveData 手动保存数据
func (tm *TaskManager) SaveData() error {
	// 获取所有任务
	tasks := tm.GetAllTasks()

	// 提取事件数据
	events := make(map[string][]Event)
	for taskID, task := range tasks {
		task.mutex.RLock()
		events[taskID] = make([]Event, len(task.Events))
		copy(events[taskID], task.Events)
		task.mutex.RUnlock()
	}

	// 保存任务数据
	if err := tm.dataManager.SaveTasks(tasks); err != nil {
		return fmt.Errorf("保存任务数据失败: %v", err)
	}

	// 保存事件数据
	if err := tm.dataManager.SaveEvents(events); err != nil {
		return fmt.Errorf("保存事件数据失败: %v", err)
	}

	log.Println("💾 数据手动保存完成")
	return nil
}

// StartAllTasks 启动所有已停止的任务
func (tm *TaskManager) StartAllTasks() (int, error) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	count := 0
	for taskID, task := range tm.tasks {
		if task.Status == StatusStopped {
			// 重新初始化停止通道
			task.StopChan = make(chan struct{})
			task.Status = StatusRunning
			task.UpdatedAt = time.Now()
			task.skipCloseOnStop = false // 重置跳过平仓标志

			// 启动任务
			go task.Run()

			// 记录事件
			tm.eventLogger.LogTaskStatus(taskID, StatusStopped, StatusRunning, "批量启动")
			count++
		}
	}

	// 触发数据保存
	tm.dataManager.TriggerSave()

	log.Printf("✅ 批量启动完成，共启动 %d 个任务", count)
	return count, nil
}

// StopAllTasks 停止所有运行中的任务
func (tm *TaskManager) StopAllTasks(skipClosePosition bool) (int, error) {
	tm.mutex.RLock()
	taskIDs := make([]string, 0)
	for taskID, task := range tm.tasks {
		if task.Status == StatusRunning {
			taskIDs = append(taskIDs, taskID)
		}
	}
	tm.mutex.RUnlock()

	count := 0
	for _, taskID := range taskIDs {
		if err := tm.StopTaskWithOptions(taskID, skipClosePosition); err == nil {
			count++
		}
	}

	log.Printf("✅ 批量停止完成，共停止 %d 个任务", count)
	return count, nil
}

// GetTaskEvents 获取指定任务的事件日志
func (tm *TaskManager) GetTaskEvents(taskID string) ([]string, error) {
	// 检查任务是否存在
	tm.mutex.RLock()
	_, exists := tm.tasks[taskID]
	tm.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	// 从事件日志管理器获取事件
	return tm.eventLogger.GetTaskEvents(taskID)
}

// SetAutoSaveInterval 设置自动保存间隔
func (tm *TaskManager) SetAutoSaveInterval(interval time.Duration) {
	tm.dataManager.SetSaveInterval(interval)
}

// EnableBackup 启用备份功能
func (tm *TaskManager) EnableBackup() {
	tm.dataManager.EnableBackup()
}

// DisableBackup 禁用备份功能
func (tm *TaskManager) DisableBackup() {
	tm.dataManager.DisableBackup()
}

// IsBackupEnabled 检查备份功能是否启用
func (tm *TaskManager) IsBackupEnabled() bool {
	return tm.dataManager.IsBackupEnabled()
}

// DeleteTask 删除指定任务及其所有记录
func (tm *TaskManager) DeleteTask(taskID string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	// 如果任务正在运行，先停止它
	if task.Status == StatusRunning {
		close(task.StopChan)
		task.Status = StatusStopped
	}

	// 从任务列表中删除
	delete(tm.tasks, taskID)

	// 触发数据保存
	tm.dataManager.TriggerSave()

	// 记录删除事件
	tm.eventLogger.LogTaskStatus(taskID, task.Status, "DELETED", "任务已删除")

	log.Printf("🗑️  任务已删除: %s", taskID)
	return nil
}

// UpdateTaskAmount 更新任务数量
func (tm *TaskManager) UpdateTaskAmount(taskID string, newAmount float64, updateType string) (*UpdateTaskAmountResponse, error) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	task.mutex.Lock()
	defer task.mutex.Unlock()

	oldAmount := task.Amount.InexactFloat64()
	task.Amount = decimal.NewFromFloat(newAmount)
	task.UpdatedAt = time.Now()

	// 触发数据保存
	tm.dataManager.TriggerSave()

	// 记录更新事件
	tm.eventLogger.LogTaskStatus(taskID, task.Status, task.Status,
		fmt.Sprintf("数量更新: %s (%.6f -> %.6f)", updateType, oldAmount, newAmount))

	log.Printf("✅ 任务数量已更新: %s, %.6f -> %.6f (%s)", taskID, oldAmount, newAmount, updateType)

	return &UpdateTaskAmountResponse{
		Success:   true,
		TaskID:    taskID,
		OldAmount: oldAmount,
		NewAmount: newAmount,
		Message:   "任务数量更新成功",
	}, nil
}

// GetTaskByOptionContract 根据期权合约查询任务
func (tm *TaskManager) GetTaskByOptionContract(optionContract string) (*QueryTaskByOptionResponse, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	for taskID, task := range tm.tasks {
		if task.OptionContract == optionContract {
			return &QueryTaskByOptionResponse{
				Success: true,
				TaskID:  taskID,
				Exists:  true,
			}, nil
		}
	}

	return &QueryTaskByOptionResponse{
		Success: true,
		Exists:  false,
	}, nil
}
