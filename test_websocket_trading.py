#!/usr/bin/env python3
"""
币安期货WebSocket智能下单测试程序
用于验证WebSocket API的完整交易流程

功能：
1. 测试WebSocket连接建立
2. 验证API签名和认证
3. 测试智能下单流程
4. 验证订单状态查询
5. 测试用户数据流
"""

import asyncio
import websockets
import json
import time
import hmac
import hashlib
import urllib.parse
from typing import Dict, Any, Optional
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('websocket_test.log')
    ]
)
logger = logging.getLogger(__name__)

class BinanceWebSocketTester:
    """币安WebSocket测试器"""
    
    def __init__(self, api_key: str, secret_key: str, testnet: bool = True):
        self.api_key = api_key
        self.secret_key = secret_key
        self.testnet = testnet
        
        # WebSocket端点
        if testnet:
            self.ws_base = "wss://stream.binancefuture.com/ws/"
            self.ws_api = "wss://testnet.binancefuture.com/ws-fapi/v1"
            self.rest_base = "https://testnet.binancefuture.com"
        else:
            self.ws_base = "wss://fstream.binance.com/ws/"
            self.ws_api = "wss://ws-fapi.binance.com/ws-fapi/v1"
            self.rest_base = "https://fapi.binance.com"
        
        self.listen_key = None
        self.request_id = 0
        self.pending_requests = {}
        
    def generate_signature(self, params: str) -> str:
        """生成API签名"""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            params.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def get_timestamp(self) -> int:
        """获取当前时间戳（毫秒）"""
        return int(time.time() * 1000)
    
    async def create_listen_key(self) -> Optional[str]:
        """创建listenKey"""
        import aiohttp
        
        url = f"{self.rest_base}/fapi/v1/listenKey"
        headers = {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.listen_key = data['listenKey']
                        logger.info(f"✅ listenKey创建成功: {self.listen_key}")
                        return self.listen_key
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 创建listenKey失败: {response.status} - {error_text}")
                        return None
        except Exception as e:
            logger.error(f"❌ 创建listenKey异常: {e}")
            return None
    
    async def test_market_data_websocket(self):
        """测试市场数据WebSocket"""
        logger.info("🔍 测试市场数据WebSocket连接...")
        
        symbol = "ethusdt"
        stream = f"{symbol}@depth5@100ms"
        uri = f"{self.ws_base}{stream}"
        
        try:
            async with websockets.connect(uri) as websocket:
                logger.info(f"✅ 市场数据WebSocket连接成功: {uri}")
                
                # 接收几条消息测试
                for i in range(3):
                    message = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(message)
                    
                    if 'b' in data and 'a' in data:
                        bids = data['b']
                        asks = data['a']
                        if bids and asks:
                            bid_price = float(bids[0][0])
                            ask_price = float(asks[0][0])
                            logger.info(f"📊 盘口数据 {i+1}: 买一={bid_price}, 卖一={ask_price}")
                        else:
                            logger.warning(f"⚠️ 盘口数据为空: {data}")
                    else:
                        logger.warning(f"⚠️ 数据格式异常: {data}")
                
                logger.info("✅ 市场数据WebSocket测试完成")
                return True
                
        except asyncio.TimeoutError:
            logger.error("❌ 市场数据WebSocket超时")
            return False
        except Exception as e:
            logger.error(f"❌ 市场数据WebSocket错误: {e}")
            return False
    
    async def test_user_data_websocket(self):
        """测试用户数据流WebSocket"""
        logger.info("🔍 测试用户数据流WebSocket...")
        
        if not self.listen_key:
            logger.error("❌ 缺少listenKey，无法测试用户数据流")
            return False
        
        uri = f"{self.ws_base}{self.listen_key}"
        
        try:
            async with websockets.connect(uri) as websocket:
                logger.info(f"✅ 用户数据流WebSocket连接成功")
                
                # 等待用户数据事件（如果有的话）
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5)
                    data = json.loads(message)
                    logger.info(f"📨 收到用户数据事件: {data}")
                except asyncio.TimeoutError:
                    logger.info("ℹ️ 5秒内无用户数据事件（正常）")
                
                logger.info("✅ 用户数据流WebSocket测试完成")
                return True
                
        except Exception as e:
            logger.error(f"❌ 用户数据流WebSocket错误: {e}")
            return False
    
    def generate_request_id(self) -> str:
        """生成请求ID"""
        self.request_id += 1
        return f"test_request_{int(time.time())}_{self.request_id}"

    def generate_websocket_signature(self, params: dict) -> str:
        """生成WebSocket API签名（WebSocket API需要按字母顺序排序参数）"""
        # 1. 排除signature参数本身
        filtered_params = {k: v for k, v in params.items() if k != 'signature'}

        # 2. 按字母顺序排序参数（WebSocket API要求）
        sorted_params = sorted(filtered_params.items())

        # 3. 构建查询字符串
        query_parts = []
        for key, value in sorted_params:
            query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)

        # 4. 生成HMAC SHA256签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        logger.debug(f"签名字符串: {query_string}")
        logger.debug(f"生成签名: {signature}")

        return signature
    
    async def test_trading_websocket_connection(self):
        """测试交易WebSocket连接"""
        logger.info("🔍 测试交易WebSocket连接...")
        
        try:
            async with websockets.connect(self.ws_api) as websocket:
                logger.info(f"✅ 交易WebSocket连接成功: {self.ws_api}")
                
                # 测试ping
                ping_request = {
                    "id": self.generate_request_id(),
                    "method": "ping"
                }
                
                await websocket.send(json.dumps(ping_request))
                logger.info(f"📤 发送ping请求: {ping_request}")
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(response)
                    logger.info(f"📥 收到响应: {data}")
                    
                    if data.get('id') == ping_request['id']:
                        logger.info("✅ 交易WebSocket ping测试成功")
                        return True
                    else:
                        logger.error(f"❌ 响应ID不匹配: 期望={ping_request['id']}, 实际={data.get('id')}")
                        return False
                        
                except asyncio.TimeoutError:
                    logger.error("❌ 交易WebSocket ping超时")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 交易WebSocket连接错误: {e}")
            return False
    
    async def test_order_place(self, symbol: str = "ETHUSDT", side: str = "BUY",
                              quantity: str = "0.01", test_mode: bool = True):
        """测试下单功能"""
        logger.info("🔍 测试WebSocket下单功能...")

        try:
            async with websockets.connect(self.ws_api) as websocket:
                logger.info("✅ 交易WebSocket连接成功")

                # 构建下单请求
                timestamp = self.get_timestamp()
                client_order_id = f"test_order_{timestamp}"

                # 构建参数（按照币安WebSocket API要求）
                params = {
                    "apiKey": self.api_key,  # 添加API密钥
                    "symbol": symbol,
                    "side": side,
                    "type": "LIMIT",
                    "quantity": quantity,
                    "timeInForce": "GTX",  # PostOnly
                    "priceMatch": "QUEUE",  # 使用队列价格
                    "newClientOrderId": client_order_id,
                    "timestamp": timestamp
                }

                # 如果是测试模式，添加测试标识
                if test_mode:
                    params["test"] = True

                # 生成签名（按字母顺序排序参数）
                signature = self.generate_websocket_signature(params)
                params["signature"] = signature

                request = {
                    "id": self.generate_request_id(),
                    "method": "order.place",
                    "params": params
                }

                logger.info(f"📤 发送下单请求:")
                logger.info(f"   ID: {request['id']}")
                logger.info(f"   Method: {request['method']}")
                logger.info(f"   Symbol: {params['symbol']}")
                logger.info(f"   Side: {params['side']}")
                logger.info(f"   Quantity: {params['quantity']}")
                logger.info(f"   TimeInForce: {params['timeInForce']}")
                logger.info(f"   PriceMatch: {params['priceMatch']}")
                logger.info(f"   Signature: {params['signature'][:20]}...")

                await websocket.send(json.dumps(request))

                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(response)
                    logger.info(f"📥 收到下单响应: {data}")

                    if data.get('status') == 200:
                        logger.info("✅ 下单请求成功")
                        return True, data
                    else:
                        error = data.get('error', {})
                        logger.error(f"❌ 下单失败: {error}")
                        return False, data

                except asyncio.TimeoutError:
                    logger.error("❌ 下单请求超时")
                    return False, None

        except Exception as e:
            logger.error(f"❌ 下单测试异常: {e}")
            return False, None
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始WebSocket智能下单综合测试")
        logger.info("=" * 60)
        
        test_results = {}
        
        # 1. 创建listenKey
        logger.info("📋 步骤1: 创建listenKey")
        listen_key_success = await self.create_listen_key()
        test_results['listen_key'] = listen_key_success is not None
        
        # 2. 测试市场数据WebSocket
        logger.info("\n📋 步骤2: 测试市场数据WebSocket")
        test_results['market_data'] = await self.test_market_data_websocket()
        
        # 3. 测试用户数据流WebSocket
        logger.info("\n📋 步骤3: 测试用户数据流WebSocket")
        test_results['user_data'] = await self.test_user_data_websocket()
        
        # 4. 测试交易WebSocket连接
        logger.info("\n📋 步骤4: 测试交易WebSocket连接")
        test_results['trading_connection'] = await self.test_trading_websocket_connection()
        
        # 5. 测试下单功能
        logger.info("\n📋 步骤5: 测试WebSocket下单")
        order_success, _ = await self.test_order_place(test_mode=True)
        test_results['order_place'] = order_success
        
        # 输出测试结果
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试结果汇总:")
        logger.info("=" * 60)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name:20} : {status}")
        
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        
        logger.info("=" * 60)
        logger.info(f"总测试数: {total_tests}, 通过: {passed_tests}, 失败: {total_tests - passed_tests}")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！WebSocket智能下单系统正常")
        else:
            logger.warning(f"⚠️ 有 {total_tests - passed_tests} 个测试失败，需要检查配置")
        
        return test_results

async def main():
    """主函数"""
    print("🔧 币安期货WebSocket智能下单测试程序")
    print("=" * 60)

    # 尝试从配置文件加载API密钥
    try:
        from config import BINANCE_CONFIG
        API_KEY = BINANCE_CONFIG["api_key"]
        SECRET_KEY = BINANCE_CONFIG["secret_key"]
        USE_TESTNET = BINANCE_CONFIG.get("use_testnet", True)

        if API_KEY == "your_binance_api_key_here" or SECRET_KEY == "your_binance_secret_key_here":
            raise ValueError("请配置真实的API密钥")

    except (ImportError, KeyError, ValueError) as e:
        print("❌ 配置文件问题:")
        print(f"   {e}")
        print("📋 请按以下步骤配置:")
        print("   1. 复制 config_example.py 为 config.py")
        print("   2. 在 config.py 中填入真实的API密钥")
        print("   3. 重新运行测试程序")
        return
    
    # 创建测试器
    tester = BinanceWebSocketTester(API_KEY, SECRET_KEY, USE_TESTNET)
    
    # 运行测试
    try:
        results = await tester.run_comprehensive_test()
        
        # 根据测试结果给出建议
        if not results.get('market_data'):
            print("\n💡 市场数据测试失败建议:")
            print("   - 检查网络连接")
            print("   - 确认币安服务器可访问")
        
        if not results.get('listen_key'):
            print("\n💡 listenKey创建失败建议:")
            print("   - 检查API密钥是否正确")
            print("   - 确认API密钥有期货权限")
        
        if not results.get('trading_connection'):
            print("\n💡 交易WebSocket连接失败建议:")
            print("   - 检查WebSocket端点是否正确")
            print("   - 确认防火墙设置")
        
        if not results.get('order_place'):
            print("\n💡 下单测试失败建议:")
            print("   - 检查API签名算法")
            print("   - 确认请求参数格式")
            print("   - 验证API权限设置")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")

if __name__ == "__main__":
    # 安装依赖提示
    try:
        import websockets
        import aiohttp
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install websockets aiohttp")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
