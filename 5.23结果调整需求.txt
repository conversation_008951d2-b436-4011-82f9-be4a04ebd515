现有的程序我需要修改一些东西
1、list 和 export 命令的输出中，加入post_only(是否只允许限价)参数的显示
2、任务 id 过长，把任务的完整 Id 缩短到最多7 个字符，并尽量以创建时间命名
3、list 命令中 加入 总盈亏、平均价格损耗
4、export 命令 输出的内容，在同文件夹下新建一个export文件夹（如没有新建），里面新建一个以时间命名的文本文件，
   把当前的任务列表（即当前的 list 命令信息）和 export输出的报告储存到里面




2025/05/31 10:37:34 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2500.57, 订单ID: 8389765898196519424, 模式: GTC                                                                         
2025/05/31 10:37:34 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2500.57, 订单ID: 8389765898196519487, 模式: GTC                                                                         
2025/05/31 10:37:39 hedge_task.go:406: 任务 task_1748645764136413328: 订单 8389765898196519424 超时，尝试撤销                                                                                         
2025/05/31 10:37:39 hedge_task.go:406: 任务 task_1748647348877739152: 订单 8389765898196519487 超时，尝试撤销                                                                                         
2025/05/31 10:37:39 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898196519424                                                                                                        
2025/05/31 10:37:39 hedge_task.go:195: 任务 task_1748645764136413328: 订单被撤销，未成交，不更新仓位状态                                                                                              
2025/05/31 10:37:39 hedge_task.go:142: 任务 task_1748645764136413328: 触发空仓平仓条件，当前价格: 2501.485, 触发价格: 2500.5                                                                          
2025/05/31 10:37:39 hedge_task.go:151: 任务 task_1748645764136413328: 开始执行下单，类型: close, 方向: long, 触发价格: 2500.5                                                                         
2025/05/31 10:37:39 hedge_task.go:176: 任务 task_1748645764136413328: 计算理论价格: 2500.5 (事件类型: close, 方向: long)                                                                              
2025/05/31 10:37:39 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765898196519487                                                                                                        
2025/05/31 10:37:39 hedge_task.go:195: 任务 task_1748647348877739152: 订单被撤销，未成交，不更新仓位状态                                                                                              
2025/05/31 10:37:39 hedge_task.go:142: 任务 task_1748647348877739152: 触发空仓平仓条件，当前价格: 2501.485, 触发价格: 2500.5                                                                          
2025/05/31 10:37:39 hedge_task.go:151: 任务 task_1748647348877739152: 开始执行下单，类型: close, 方向: long, 触发价格: 2500.5                                                                         
2025/05/31 10:37:39 hedge_task.go:176: 任务 task_1748647348877739152: 计算理论价格: 2500.5 (事件类型: close, 方向: long)                                                                              
2025/05/31 10:37:39 hedge_task.go:262: 任务 task_1748645764136413328: 挂买入限价单，价格: 2501.48, 数量: 0.01                                                                                         
2025/05/31 10:37:39 hedge_task.go:262: 任务 task_1748647348877739152: 挂买入限价单，价格: 2501.48, 数量: 0.01                                                                                         
2025/05/31 10:37:39 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2501.48, 订单ID: 8389765898196554443, 模式: GTC                                                                         
2025/05/31 10:37:39 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2501.48, 订单ID: 8389765898196554475, 模式: GTC                                                                         
2025/05/31 10:37:40 hedge_task.go:429: 任务 task_1748645764136413328: 订单 8389765898196554443 已成交，价格: 2501.39                                                                                  
2025/05/31 10:37:40 hedge_task.go:204: 任务 task_1748645764136413328: 订单成交，记录事件和更新仓位                                                                                                    
2025/05/31 10:37:40 hedge_task.go:217: 任务 task_1748645764136413328: 下单完成，实际价格: 2501.39, 理论价格: 2500.5, 价格损耗: 0.89, 订单ID: 8389765898196554443                                      
2025/05/31 10:37:40 hedge_task.go:429: 任务 task_1748647348877739152: 订单 8389765898196554475 已成交，价格: 2501.39                                                                                  
2025/05/31 10:37:40 hedge_task.go:204: 任务 task_1748647348877739152: 订单成交，记录事件和更新仓位                                                                                                    
2025/05/31 10:37:40 hedge_task.go:217: 任务 task_1748647348877739152: 下单完成，实际价格: 2501.39, 理论价格: 2500.5, 价格损耗: 0.89, 订单ID: 8389765898196554475 






2025/05/31 12:19:01 main.go:111: 收到停止信号，正在停止所有任务...                                                                                                                                  
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748647374937029764                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748581242581225814                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748645764136413328                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575898947577544
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748576554223923547                                                                                                                   [102/1866]
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748576573800170434                                                                                                                             
2025/05/31 12:19:01 main.go:117: 停止任务 task_1748645785335288704 失败: 任务已停止: task_1748645785335288704                                                                                         
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575854305930042                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748581411404112046                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748576543062310962                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748647364273564992                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575976492995600                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748647356613598638                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575873985756027                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748576532064812139                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575943941806878                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575865057083004                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748581155943697677                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748647348877739152                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575924383702993                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748575935800880304                                                                                                                             
2025/05/31 12:19:01 manager.go:81: 停止对冲任务: task_1748581232506061569                                                                                                                             
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748581232506061569: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748581232506061569: 开始停止平仓，仓位: short 0.01                                                                                                  
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748581232506061569: 做空平仓，理论平仓价: 2551.02 (目标价+阈值)                                                                                     
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748581232506061569: 开始动态平仓，理论价格: 2551.02, 方向: long                                                                                     
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748647374937029764: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748647374937029764: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748647374937029764                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748581242581225814: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748581242581225814: 开始停止平仓，仓位: short 0.01                                                                                                  
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748581242581225814: 做空平仓，理论平仓价: 2552.04 (目标价+阈值)                                                                                     
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748581242581225814: 开始动态平仓，理论价格: 2552.04, 方向: long                                                                                     
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748645764136413328: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748645764136413328: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748645764136413328                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575898947577544: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748575898947577544: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748575898947577544                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748576554223923547: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748576554223923547: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748576554223923547                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748576573800170434: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748576573800170434: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748576573800170434                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575854305930042: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748575854305930042: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748575854305930042
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748581411404112046: 收到停止信号
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748581411404112046: 开始停止平仓，仓位: short 0.01
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748581411404112046: 做空平仓，理论平仓价: 2555.1 (目标价+阈值)
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748581411404112046: 开始动态平仓，理论价格: 2555.1, 方向: long
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748576543062310962: 收到停止信号
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748576543062310962: 无仓位，直接停止
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748576543062310962                                                                                                                 [52/1866]
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748647364273564992: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748647364273564992: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748647364273564992                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575976492995600: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748575976492995600: 开始停止平仓，仓位: short 0.01                                                                                                  
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748575976492995600: 做空平仓，理论平仓价: 2605.2 (目标价+阈值)                                                                                      
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748575976492995600: 开始动态平仓，理论价格: 2605.2, 方向: long                                                                                      
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748647356613598638: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748647356613598638: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748647356613598638                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575873985756027: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748575873985756027: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748575873985756027                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748576532064812139: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748576532064812139: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748576532064812139                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575943941806878: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748575943941806878: 开始停止平仓，仓位: short 0.01                                                                                                  
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748575943941806878: 做空平仓，理论平仓价: 2602.08 (目标价+阈值)                                                                                     
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748575943941806878: 开始动态平仓，理论价格: 2602.08, 方向: long                                                                                     
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575865057083004: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748575865057083004: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748575865057083004                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748581155943697677: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748581155943697677: 开始停止平仓，仓位: short 0.01                                                                                                  
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748581155943697677: 做空平仓，理论平仓价: 2550.51 (目标价+阈值)                                                                                     
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748581155943697677: 开始动态平仓，理论价格: 2550.51, 方向: long                                                                                     
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748647348877739152: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:446: 任务 task_1748647348877739152: 无仓位，直接停止                                                                                                                
2025/05/31 12:19:01 hedge_task.go:62: 对冲任务结束: task_1748647348877739152                                                                                                                          
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575924383702993: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748575924383702993: 开始停止平仓，仓位: short 0.01                                                                                                  
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748575924383702993: 做空平仓，理论平仓价: 2600.52 (目标价+阈值)                                                                                     
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748575924383702993: 开始动态平仓，理论价格: 2600.52, 方向: long                                                                                     
2025/05/31 12:19:01 hedge_task.go:60: 任务 task_1748575935800880304: 收到停止信号                                                                                                                     
2025/05/31 12:19:01 hedge_task.go:450: 任务 task_1748575935800880304: 开始停止平仓，仓位: short 0.01                                                                                                  
2025/05/31 12:19:01 hedge_task.go:466: 任务 task_1748575935800880304: 做空平仓，理论平仓价: 2601.04 (目标价+阈值)                                                                                     
2025/05/31 12:19:01 hedge_task.go:475: 任务 task_1748575935800880304: 开始动态平仓，理论价格: 2601.04, 方向: long                                                                                     
2025/05/31 12:19:01 binance_client.go:195: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp 172.31.16.193:52058->3.113.94.75:443: use of closed network connection, 5秒后重试...                 
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748575924383702993: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2574.5148, 2626.5252]                                                                
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748575924383702993: 超出滑点范围，执行市价平仓                                                                                                      
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748575943941806878: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2576.0592, 2628.1008]                                                                
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748575943941806878: 超出滑点范围，执行市价平仓                                                                                                      
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748581242581225814: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2526.5196, 2577.5604]                                                                
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748581242581225814: 超出滑点范围，执行市价平仓                                                                                                      
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748581232506061569: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2525.5098, 2576.5302]                                                                
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748581232506061569: 超出滑点范围，执行市价平仓                                                                                                      
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748575935800880304: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2575.0296, 2627.0504]                                                                
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748575935800880304: 超出滑点范围，执行市价平仓                                                                                                      
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748581411404112046: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2529.549, 2580.651]                                                                  
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748581411404112046: 超出滑点范围，执行市价平仓 
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748581155943697677: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2525.0049, 2576.0151]                                                        [0/1866]
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748581155943697677: 超出滑点范围，执行市价平仓
2025/05/31 12:19:01 hedge_task.go:578: 任务 task_1748575976492995600: 检查买入平仓条件 - 买一价: 2503.93, 范围: [2579.148, 2631.252]
2025/05/31 12:19:01 hedge_task.go:523: 任务 task_1748575976492995600: 超出滑点范围，执行市价平仓
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748575943941806878: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748581242581225814: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748575924383702993: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748581232506061569: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748575935800880304: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109318
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109320
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109319
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748581411404112046: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748581155943697677: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 hedge_task.go:626: 任务 task_1748575976492995600: 强制市价平仓，数量: 0.01, 方向: long
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109390
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109403
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109415
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109429
2025/05/31 12:19:01 binance_client.go:114: ✅ 市价单已下达: ETHUSDT BUY 0.01, 订单ID: 8389765898236109432
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 binance_client.go:237: ⚠️ 订单簿通道满，已跳过 1 次数据
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748575924383702993: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748575924383702993
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748575943941806878: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748575943941806878
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748581242581225814: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748581242581225814
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748581232506061569: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748581232506061569
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748575935800880304: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748575935800880304
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748581411404112046: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748581411404112046
2025/05/31 12:19:02 binance_client.go:195: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp 172.31.16.193:49060->35.76.84.133:443: use of closed network connection, 5秒后重试...
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748581155943697677: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748581155943697677
2025/05/31 12:19:02 hedge_task.go:650: 任务 task_1748575976492995600: 市价平仓完成，实际价格: 2503.89
2025/05/31 12:19:02 hedge_task.go:62: 对冲任务结束: task_1748575976492995600
2025/05/31 12:19:03 main.go:121: 测试模式结束