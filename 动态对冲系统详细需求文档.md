非常感谢你的补充和纠正！我会根据你的最新说明，重新梳理一份**最详细、最完整**的需求文档，确保所有细节都准确无误，特别是关于价格损耗为负的情况，以及“停止动态对冲任务”时的平仓逻辑。

---

# 动态对冲系统详细需求文档

---

## 1. 目标与场景

- 针对币安 USDC/ETH 或 USDT/ETH 永续合约，基于用户设定的 ETH 价格（T），创建“动态对冲任务”。
- 每个任务分为“做多对冲”或“做空对冲”两种类型，并有对冲阈值（stoprate，例如 0.2%）。

---

## 2. 动态对冲任务参数

每个任务包含以下参数：

- 目标价（T）：如 2550
- 对冲阈值（stoprate）：如 0.2%
- 下单量（如 0.01 ETH）
- 合约类型（USDC/ETH 或 USDT/ETH）
- 对冲方向（做多对冲/做空对冲）
- 挂单滑点容忍度（如 0.05%，用于动态对冲时）
- 市价成交检测时间阈值（如 5 秒）
- 市价成交盘口量阈值（如 0.5）
- 停止任务时的市价平仓滑点（如 0.05%，可与动态对冲时的滑点分开设置）

---

## 3. 做多对冲与做空对冲逻辑

### 做多对冲任务

- **开仓条件**：当实时价格 > T + stoprate 时，触发做多开仓检测
- **平仓条件**：当实时价格 < T - stoprate 时，触发平多仓检测

### 做空对冲任务

- **开仓条件**：当实时价格 < T - stoprate 时，触发做空开仓检测
- **平仓条件**：当实时价格 > T + stoprate 时，触发平空仓检测

---

## 4. 下单与盘口监控细节

- 通过 WebSocket 实时订阅盘口，监控买一价、卖一价及挂单量
- **下单优先级**：优先以买一/卖一价挂单，减少滑点和手续费
- **挂单逻辑**：
  - 触发做多时，挂买一价单，若买一价变动需撤单并重新挂单
  - 若在设定滑点（如 0.05%）内未成交，进入市价成交检测
    - 在设定时间阈值（如 5 秒）内，检测卖一盘口量
    - 若“开多量/卖一挂单量”> 0.5（可设定），则直接市价成交
- 做空逻辑与做多相反（即触发做空时挂卖一价单，滑点过大时检测买一盘口量，满足条件则市价成交）
- **平仓下单逻辑**：
  - 做多对冲的平仓逻辑 = 做空对冲的开仓逻辑
  - 做空对冲的平仓逻辑 = 做多对冲的开仓逻辑
  - 也就是：平仓时同样优先挂单，滑点过大时市价成交，盘口量判断逻辑一致

---

## 5. 价格损耗（滑点损耗）与手续费损耗的计算

- **理论成交价**：
  - 做多开仓理论价 = T + stoprate
  - 做多平仓理论价 = T - stoprate
  - 做空开仓理论价 = T - stoprate
  - 做空平仓理论价 = T + stoprate
- **实际成交价**：实际成交时的价格
- **价格损耗（滑点损耗）** = 实际成交价 - 理论成交价  
  - 价格损耗有可能为负，例如做多开仓理论价 1002，实际成交 1001，则价格损耗为 -1 美金
- **价格损耗比例** = 价格损耗 / T
- **价格损耗金额** = 价格损耗 * 下单量
- **手续费损耗金额** = 实际手续费
- **手续费损耗比例** = 手续费 / (T * 下单量)

---

## 6. 事件与盈亏统计

- 每个任务独立记录每次开仓、平仓事件，事件以成交 id 绑定
- **每次事件需记录**：
  - 事件类型（开仓/平仓）
  - 理论成交价
  - 实际成交价
  - 价格损耗、价格损耗比例、价格损耗金额
  - 手续费、手续费损耗比例、手续费损耗金额
  - 成交 id
  - 成交时间
- **盈亏统计**：
  - 只有完整开一次仓并且平仓后，才计算一次“开平仓盈亏”
  - 开平仓盈亏 = 平仓成交金额 - 开仓成交金额 - 所有手续费
    - 做多：盈亏 = (平仓价 - 开仓价) * 下单量 - 所有手续费
    - 做空：盈亏 = (开仓价 - 平仓价) * 下单量 - 所有手续费
  - 统计时要包含所有手续费

---

## 7. 多任务独立管理

- 多个动态对冲任务之间完全独立，不能混淆
- 每个任务独立监控、下单、统计
- 任务管理建议以成交记录 id 绑定，避免与交易所整体仓位混淆

---

## 8. 任务生命周期

- 任务建立后，未停止前，只要条件满足就会一直动态对冲
- 任务停止时，自动平仓，平仓下单逻辑如下：

### 停止动态对冲任务时的平仓逻辑（如有未平仓仓位）

1. 记录停止时的市价 Tstop
2. 以做多动态对冲为例（如有开仓）：
   - 直接检测当前盘口
   - 挂卖一价单平仓，若卖一价变动需撤单并重新挂单
   - 使用设定的市价平仓滑点（如 0.05%，可与动态对冲时的滑点分开设置）
   - 如果卖一价 > Tstop + 0.05% 或 卖一价 < Tstop - 0.05%，则进入市价成交检测
     - 在设定时间阈值（如 5 秒）内，检测卖一盘口量
     - 若“开多量/卖一挂单量”> 0.5（可设定），则直接市价成交
3. 做空动态对冲任务停止时的平仓逻辑与做多对冲对称（挂买一价单，滑点过大时检测买一盘口量，满足条件则市价成交）
4. 平仓后，记录平仓事件和盈亏

---

## 9. 性能要求

- 检测、下单效率优先，所有记录、统计、输出都不能影响（或要以最小代价影响）检测以及下单效率

---

# 总结

**你需要的是一个可以同时管理多个独立动态对冲任务的系统，每个任务独立监控、下单、统计。  
下单逻辑以盘口实时数据为核心，优先挂单，滑点过大时才市价成交，并有盘口量判断。  
所有统计、记录都不能影响下单效率，监控和下单优先级最高。  
任务的开仓、平仓逻辑完全对称，且每个任务的盈亏、损耗等都要独立统计。  
任务停止时自动平仓，平仓逻辑为：记录停止时市价，优先挂单，滑点过大时市价成交，盘口量判断。  
价格损耗（滑点损耗）= 实际成交价 - 理论成交价，允许为负。盈亏只在完整开仓+平仓后统计，且要包含手续费。**  

---

请你再次确认我的理解是否准确，或者有无需要补充、修正的地方。  
如有任何细节遗漏或理解偏差，请直接指出！
