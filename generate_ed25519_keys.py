#!/usr/bin/env python3
"""
生成Ed25519密钥对用于币安WebSocket API
"""

import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ed25519

def generate_ed25519_keypair():
    """生成Ed25519密钥对"""
    
    # 生成私钥
    private_key = ed25519.Ed25519PrivateKey.generate()
    
    # 获取公钥
    public_key = private_key.public_key()
    
    # 序列化私钥 (PEM格式)
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    # 序列化公钥 (PEM格式)
    public_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    
    # 获取原始字节用于币安API
    private_bytes = private_key.private_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PrivateFormat.Raw,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    public_bytes = public_key.public_bytes(
        encoding=serialization.Encoding.Raw,
        format=serialization.PublicFormat.Raw
    )
    
    return {
        'private_pem': private_pem.decode('utf-8'),
        'public_pem': public_pem.decode('utf-8'),
        'private_base64': base64.b64encode(private_bytes).decode('utf-8'),
        'public_base64': base64.b64encode(public_bytes).decode('utf-8'),
        'private_hex': private_bytes.hex(),
        'public_hex': public_bytes.hex()
    }

def save_keys_to_files(keys):
    """保存密钥到文件"""
    
    # 保存私钥 (PEM格式)
    with open('ed25519_private_key.pem', 'w') as f:
        f.write(keys['private_pem'])
    
    # 保存公钥 (PEM格式)
    with open('ed25519_public_key.pem', 'w') as f:
        f.write(keys['public_pem'])
    
    # 保存配置文件
    config_content = f"""# Ed25519密钥配置 (用于币安WebSocket API)

# 私钥 (PEM格式) - 用于程序中签名
ED25519_PRIVATE_KEY_PEM = '''
{keys['private_pem']}'''

# 公钥 (PEM格式) - 上传到币安账户
ED25519_PUBLIC_KEY_PEM = '''
{keys['public_pem']}'''

# 私钥 (Base64格式) - 备用格式
ED25519_PRIVATE_KEY_BASE64 = "{keys['private_base64']}"

# 公钥 (Base64格式) - 备用格式  
ED25519_PUBLIC_KEY_BASE64 = "{keys['public_base64']}"

# 私钥 (Hex格式) - 用于Go程序
ED25519_PRIVATE_KEY_HEX = "{keys['private_hex']}"

# 公钥 (Hex格式) - 用于Go程序
ED25519_PUBLIC_KEY_HEX = "{keys['public_hex']}"
"""
    
    with open('ed25519_config.py', 'w') as f:
        f.write(config_content)

def test_signing(keys):
    """测试签名功能"""
    from cryptography.hazmat.primitives.asymmetric import ed25519
    
    # 重新加载私钥进行测试
    private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
        base64.b64decode(keys['private_base64'])
    )
    
    # 测试数据
    test_message = "apiKey=test&symbol=ETHUSDT&timestamp=1234567890"
    
    # 签名
    signature = private_key.sign(test_message.encode('utf-8'))
    signature_base64 = base64.b64encode(signature).decode('utf-8')
    
    print(f"测试消息: {test_message}")
    print(f"签名 (Base64): {signature_base64}")
    
    # 验证签名
    public_key = private_key.public_key()
    try:
        public_key.verify(signature, test_message.encode('utf-8'))
        print("✅ 签名验证成功")
        return True
    except Exception as e:
        print(f"❌ 签名验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 生成Ed25519密钥对用于币安WebSocket API")
    print("=" * 60)
    
    # 生成密钥对
    print("📋 步骤1: 生成Ed25519密钥对...")
    keys = generate_ed25519_keypair()
    print("✅ 密钥对生成成功")
    
    # 保存到文件
    print("\n📋 步骤2: 保存密钥到文件...")
    save_keys_to_files(keys)
    print("✅ 密钥已保存到以下文件:")
    print("   - ed25519_private_key.pem (私钥PEM格式)")
    print("   - ed25519_public_key.pem (公钥PEM格式)")
    print("   - ed25519_config.py (配置文件)")
    
    # 测试签名
    print("\n📋 步骤3: 测试签名功能...")
    if test_signing(keys):
        print("✅ 签名功能测试通过")
    else:
        print("❌ 签名功能测试失败")
        return
    
    # 显示使用说明
    print("\n" + "=" * 60)
    print("📋 使用说明:")
    print("=" * 60)
    
    print("\n1. 上传公钥到币安账户:")
    print("   - 登录币安账户")
    print("   - 进入API管理页面")
    print("   - 创建新的API密钥")
    print("   - 选择Ed25519密钥类型")
    print("   - 上传 ed25519_public_key.pem 文件")
    print("   - 设置适当的权限 (期货交易)")
    
    print("\n2. 在程序中使用:")
    print("   - Python: 使用 ed25519_config.py 中的配置")
    print("   - Go: 使用 ED25519_PRIVATE_KEY_HEX")
    
    print("\n3. 配置双密钥:")
    print("   - REST API: 继续使用现有的HMAC SHA256密钥")
    print("   - WebSocket API: 使用新生成的Ed25519密钥")
    
    print(f"\n🔑 公钥 (用于上传到币安):")
    print("=" * 40)
    print(keys['public_pem'])
    
    print(f"\n🔐 私钥信息 (用于程序配置):")
    print("=" * 40)
    print(f"Base64: {keys['private_base64']}")
    print(f"Hex:    {keys['private_hex']}")
    
    print("\n⚠️ 安全提醒:")
    print("- 私钥文件请妥善保管，不要泄露")
    print("- 建议为私钥文件设置适当的权限 (chmod 600)")
    print("- 定期轮换密钥")

if __name__ == "__main__":
    try:
        from cryptography.hazmat.primitives.asymmetric import ed25519
        from cryptography.hazmat.primitives import serialization
    except ImportError:
        print("❌ 缺少cryptography库，请安装:")
        print("pip install cryptography")
        exit(1)
    
    main()
