# 动态对冲系统 API 接口文档

## 概述

动态对冲系统提供了完整的 RESTful API 接口，支持基础对冲任务管理和期权系统专用功能。系统采用 HTTP JSON 格式进行数据交互，默认运行在 `http://localhost:5879`。

## 启动方式

```bash
# 启动API服务模式（在交互模式基础上额外开启API服务）
./hedge-system --api

# 指定端口
./hedge-system --api --port 5879
```

## API 接口分类

### 1. 基础 API 接口
- 适用于手动创建和管理对冲任务
- 路径前缀：`/task`、`/tasks`

### 2. 期权系统专用 API 接口
- 专为期权交易系统设计，支持来源标识和期权合约绑定
- 路径前缀：`/api/tasks`

---

## 基础 API 接口

### 1.1 创建对冲任务

**接口地址：** `POST /task`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| symbol | string | 是 | 交易对 | "ETHUSDT" |
| direction | string | 是 | 对冲方向 | "long" 或 "short" |
| target_price | number | 是 | 目标价格 | 2500.0 |
| amount | number | 是 | 下单数量 | 0.1 |
| stop_rate | number | 是 | 对冲阈值 | 0.0002 (0.02%) |
| slippage_tolerance | number | 否 | 滑点容忍度 | 0.0005 (0.05%) |
| stop_slippage | number | 否 | 停止平仓滑点 | 0.0005 (0.05%) |
| market_timeout | integer | 否 | 市价单超时时间(秒) | 5 |
| volume_threshold | number | 否 | 盘口量阈值 | 0.5 (50%) |
| post_only_mode | boolean | 否 | 只允许限价成交 | true |

**请求示例：**
```json
{
  "symbol": "ETHUSDT",
  "direction": "long",
  "target_price": 2500.0,
  "amount": 0.1,
  "stop_rate": 0.0002,
  "slippage_tolerance": 0.0005,
  "stop_slippage": 0.0005,
  "market_timeout": 5,
  "volume_threshold": 0.5,
  "post_only_mode": true
}
```

**响应示例：**
```json
{
  "task_id": "1234567",
  "status": "success",
  "message": "任务创建成功"
}
```

### 1.2 获取所有任务

**接口地址：** `GET /tasks`

**响应示例：**
```json
{
  "1234567": {
    "id": "1234567",
    "symbol": "ETHUSDT",
    "direction": "long",
    "target_price": 2500.0,
    "status": "running",
    "position": {
      "side": "long",
      "size": 0.1,
      "entry_price": 2501.5,
      "entry_time": "2024-01-15T10:30:00Z"
    },
    "events": [],
    "statistics": {
      "total_trades": 2,
      "total_profit": 15.5,
      "win_rate": 0.5
    }
  }
}
```

### 1.3 获取单个任务

**接口地址：** `GET /task/{task_id}`

**响应示例：**
```json
{
  "id": "1234567",
  "symbol": "ETHUSDT",
  "direction": "long",
  "target_price": 2500.0,
  "stop_rate": 0.0002,
  "amount": 0.1,
  "status": "running",
  "position": {
    "side": "long",
    "size": 0.1,
    "entry_price": 2501.5,
    "entry_time": "2024-01-15T10:30:00Z",
    "order_id": "12345678901234567890"
  },
  "events": [
    {
      "id": "evt_001",
      "type": "open",
      "order_type": "limit",
      "execution_type": "maker",
      "theoretical_price": 2501.0,
      "actual_price": 2501.5,
      "price_loss": 0.5,
      "price_loss_ratio": 0.0002,
      "fee": 0.05,
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ],
  "statistics": {
    "total_trades": 2,
    "total_profit": 15.5,
    "win_rate": 0.5,
    "avg_holding_time": 3600
  },
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 1.4 停止任务

**接口地址：** `POST /task/stop`

**请求参数：**
```json
{
  "task_id": "1234567"
}
```

**响应示例：**
```json
{
  "task_id": "1234567",
  "status": "success",
  "message": "任务停止成功"
}
```

---

## 期权系统专用 API 接口

### 2.1 创建对冲任务（期权系统）

**接口地址：** `POST /api/tasks`

**必要参数：**
- `symbol`：交易对
- `direction`：对冲方向
- `target_price`：目标价格
- `amount`：下单量
- `stop_rate`：对冲阈值
- `source`：来源标识（固定为 "options"）
- `option_contract`：期权合约名称

**可选参数：**
如果未提供或为空，将使用 `embedded_config` 中的默认参数：
- `slippage_tolerance`：默认 0.0005 (0.05%)
- `stop_slippage`：默认 0.0005 (0.05%)
- `market_timeout`：默认 5 秒
- `volume_threshold`：默认 0.5 (50%)
- `post_only_mode`：默认 true

**请求示例（仅必要参数）：**
```json
{
  "symbol": "ETHUSDT",
  "direction": "long",
  "target_price": 2500.0,
  "amount": 0.1,
  "stop_rate": 0.0002,
  "source": "options",
  "option_contract": "ETHUSDT-2JUN25-2500-C"
}
```

**请求示例（完整参数）：**
```json
{
  "symbol": "ETHUSDT",
  "direction": "short",
  "target_price": 2400.0,
  "amount": 0.05,
  "stop_rate": 0.0003,
  "slippage_tolerance": 0.0005,
  "stop_slippage": 0.0005,
  "market_timeout": 5,
  "volume_threshold": 0.5,
  "post_only_mode": true,
  "source": "options",
  "option_contract": "ETHUSDT-2JUN25-2400-P"
}
```

**响应示例：**
```json
{
  "task_id": "M123456",
  "status": "success",
  "message": "任务创建成功"
}
```

### 2.2 更新任务数量

**接口地址：** `PUT /api/tasks/{task_id}/amount`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| amount | number | 是 | 新的总数量 | 0.3 |
| update_type | string | 是 | 更新类型 | "sell_open", "buy_close", "exercise" |
| source | string | 是 | 来源标识 | "options" |
| option_contract | string | 是 | 期权合约名称 | "ETHUSDT-2JUN25-2500-C" |

**请求示例：**
```json
{
  "amount": 0.3,
  "update_type": "sell_open",
  "source": "options",
  "option_contract": "ETHUSDT-2JUN25-2500-C"
}
```

**响应示例：**
```json
{
  "success": true,
  "task_id": "M123456",
  "old_amount": 0.1,
  "new_amount": 0.3,
  "message": "任务数量更新成功"
}
```

### 2.3 根据期权合约查询绑定任务

**接口地址：** `GET /api/tasks/by-option/{option_contract}`

**请求示例：**
```
GET /api/tasks/by-option/ETHUSDT-2JUN25-2500-C
```

**响应示例：**
```json
{
  "success": true,
  "task_id": "M123456",
  "exists": true
}
```

**响应示例（任务不存在）：**
```json
{
  "success": true,
  "exists": false
}
```

---

## Embedded Config 默认参数

系统内置的默认配置参数如下：

```go
// 默认交易参数
Defaults: {
    SlippageTolerance: 0.0005,  // 0.05% 滑点容忍度
    StopSlippage:      0.0005,  // 0.05% 止损滑点
    MarketTimeout:     5,       // 5秒 市价单超时时间
    VolumeThreshold:   0.5,     // 50% 成交量阈值
}

// 风险控制
Risk: {
    MaxTasks:        20,     // 最大并发任务数
    MaxPositionSize: 1.0,    // 1.0 ETH 最大单个仓位大小
    DailyLossLimit:  1000.0, // 1000 USDT 日损失限制
}

// 监控配置
Monitoring: {
    StatusInterval:      10, // 10秒 状态输出间隔
    HealthCheckInterval: 30, // 30秒 健康检查间隔
}
```

---

## 错误响应格式

所有API接口在出现错误时，都会返回统一的错误响应格式：

```json
{
  "error": "错误描述信息",
  "code": 400,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**常见错误码：**
- `400`：请求参数错误
- `404`：任务不存在
- `405`：请求方法不支持
- `500`：服务器内部错误

---

## 使用示例

### cURL 命令示例

```bash
# 创建基础对冲任务
curl -X POST http://localhost:5879/task \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "ETHUSDT",
    "direction": "long",
    "target_price": 2500,
    "amount": 0.1,
    "stop_rate": 0.0002
  }'

# 创建期权系统对冲任务（仅必要参数）
curl -X POST http://localhost:5879/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "ETHUSDT",
    "direction": "long",
    "target_price": 3000,
    "amount": 0.01,
    "stop_rate": 0.0002,
    "source": "options",
    "option_contract": "ETHUSDT-4JUN25-3000-C"
  }'

# 更新任务数量
curl -X PUT http://localhost:5879/api/tasks/M892000/amount \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 0.03,
    "update_type": "sell_open",
    "source": "options",
    "option_contract": "ETHUSDT-4JUN25-3000-C"
  }'

# 查询期权绑定任务
curl http://localhost:5879/api/tasks/by-option/ETHUSDT-4JUN25-3000-C

# 获取所有任务
curl http://localhost:5879/tasks

# 停止任务
curl -X POST http://localhost:5879/task/stop \
  -H "Content-Type: application/json" \
  -d '{"task_id":"M123456"}'
```

### Python 示例

参考 `test_api.py` 文件中的完整测试代码，包含所有API接口的使用示例。

---

## 注意事项

1. **参数优先级**：期权系统API中，如果提供了可选参数，将使用提供的值；否则使用 embedded_config 中的默认值。

2. **任务ID格式**：
   - 基础任务：7位数字字符串（如 "1234567"）
   - 期权任务：M + 6位数字（如 "M123456"）

3. **来源标识**：
   - `"manual"`：手动创建的任务
   - `"options"`：期权系统创建的任务

4. **对冲方向**：
   - `"long"`：做多对冲（价格上涨时开多仓）
   - `"short"`：做空对冲（价格下跌时开空仓）

5. **更新类型**：
   - `"sell_open"`：卖出开仓
   - `"buy_close"`：买入平仓
   - `"exercise"`：期权行权

6. **CORS支持**：所有API接口都支持跨域请求。

---

## 测试工具

使用 `test_api.py` 脚本可以完整测试所有API接口：

```bash
python3 test_api.py
```

测试脚本包含：
- ✅ 基础API接口测试
- ✅ 期权系统专用API测试  
- ✅ 参数验证和错误处理测试
- ✅ 任务生命周期管理测试
