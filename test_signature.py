#!/usr/bin/env python3
"""
币安API签名测试程序
验证HMAC SHA256签名算法的正确性
"""

import hmac
import hashlib
import time
import urllib.parse
import json

def generate_signature(params, secret_key, sort_params=False):
    """生成币安API签名"""
    # 1. 排除signature参数本身
    filtered_params = {k: v for k, v in params.items() if k != 'signature'}

    # 2. 根据需要排序参数
    if sort_params:
        # WebSocket API需要排序
        sorted_params = sorted(filtered_params.items())
    else:
        # REST API保持原始顺序
        sorted_params = list(filtered_params.items())

    # 3. 构建查询字符串
    query_parts = []
    for key, value in sorted_params:
        query_parts.append(f"{key}={value}")
    query_string = "&".join(query_parts)

    # 4. 生成HMAC SHA256签名
    signature = hmac.new(
        secret_key.encode('utf-8'),
        query_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

    return query_string, signature

def test_rest_api_signature():
    """测试REST API签名（参考币安官方文档）"""
    print("🔍 测试REST API签名算法...")

    # 币安官方文档示例
    secret_key = "NhqPtmdSJYdKjVHjA7PZj4Mge3R5YNiP1e3UZjInClVN65XAbvqqM6A7H5fATj0j"

    # 使用有序字典保持参数顺序（按照币安文档的顺序）
    from collections import OrderedDict
    params = OrderedDict([
        ("symbol", "LTCBTC"),
        ("side", "BUY"),
        ("type", "LIMIT"),
        ("timeInForce", "GTC"),
        ("quantity", "1"),
        ("price", "0.1"),
        ("recvWindow", "5000"),
        ("timestamp", "1499827319559")
    ])

    query_string, signature = generate_signature(params, secret_key, sort_params=False)

    print(f"参数: {dict(params)}")
    print(f"查询字符串: {query_string}")
    print(f"生成签名: {signature}")

    # 期望的签名（来自币安官方文档）
    expected_signature = "c8db56825ae71d6d79447849e617115f4a920fa2acdcab2b053c4b2838bd6b71"

    if signature == expected_signature:
        print("✅ REST API签名算法正确")
        return True
    else:
        print(f"❌ REST API签名不匹配")
        print(f"期望: {expected_signature}")
        print(f"实际: {signature}")
        return False

def test_websocket_api_signature():
    """测试WebSocket API签名"""
    print("\n🔍 测试WebSocket API签名算法...")
    
    # 使用测试密钥
    api_key = "test_api_key"
    secret_key = "test_secret_key"
    
    # 构建WebSocket下单请求参数
    timestamp = int(time.time() * 1000)
    
    params = {
        "apiKey": api_key,
        "symbol": "ETHUSDT",
        "side": "BUY",
        "type": "LIMIT",
        "quantity": "0.01",
        "timeInForce": "GTX",
        "priceMatch": "QUEUE",
        "newClientOrderId": f"test_order_{timestamp}",
        "timestamp": timestamp
    }
    
    query_string, signature = generate_signature(params, secret_key, sort_params=True)
    
    print(f"WebSocket参数:")
    for key, value in sorted(params.items()):
        print(f"  {key}: {value}")
    
    print(f"\n查询字符串: {query_string}")
    print(f"生成签名: {signature}")
    
    # 构建完整的WebSocket请求
    params["signature"] = signature
    
    websocket_request = {
        "id": f"test_request_{timestamp}",
        "method": "order.place",
        "params": params
    }
    
    print(f"\n完整WebSocket请求:")
    print(json.dumps(websocket_request, indent=2))
    
    return True

def test_parameter_ordering():
    """测试参数排序的重要性"""
    print("\n🔍 测试参数排序的重要性...")
    
    secret_key = "test_secret"
    
    # 相同参数，不同顺序
    params1 = {"c": "3", "a": "1", "b": "2"}
    params2 = {"a": "1", "b": "2", "c": "3"}
    params3 = {"b": "2", "c": "3", "a": "1"}
    
    _, sig1 = generate_signature(params1, secret_key, sort_params=True)
    _, sig2 = generate_signature(params2, secret_key, sort_params=True)
    _, sig3 = generate_signature(params3, secret_key, sort_params=True)
    
    print(f"参数1 {params1} -> 签名: {sig1}")
    print(f"参数2 {params2} -> 签名: {sig2}")
    print(f"参数3 {params3} -> 签名: {sig3}")
    
    if sig1 == sig2 == sig3:
        print("✅ 参数排序算法正确，不同顺序产生相同签名")
        return True
    else:
        print("❌ 参数排序有问题")
        return False

def test_go_program_format():
    """测试Go程序应该使用的格式"""
    print("\n🔍 测试Go程序应该使用的格式...")
    
    # 模拟Go程序的参数
    api_key = "your_api_key"
    secret_key = "your_secret_key"
    timestamp = int(time.time() * 1000)
    
    # 错误格式（Go程序当前使用的）
    wrong_params = {
        "symbol": "ETHUSDT",
        "side": "long",  # 错误：应该是BUY
        "type": "LIMIT",
        "quantity": "0.01",
        "priceMatch": "QUEUE",
        "timeInForce": "GTX",
        "newClientOrderId": f"5440000_long_{timestamp}_001",
        "timestamp": timestamp
    }
    
    # 正确格式
    correct_params = {
        "apiKey": api_key,  # 添加API密钥
        "symbol": "ETHUSDT",
        "side": "BUY",  # 正确：使用BUY
        "type": "LIMIT",
        "quantity": "0.01",
        "priceMatch": "QUEUE",
        "timeInForce": "GTX",
        "newClientOrderId": f"correct_order_{timestamp}",
        "timestamp": timestamp
    }
    
    print("❌ 错误格式（Go程序当前使用）:")
    wrong_query, wrong_sig = generate_signature(wrong_params, secret_key, sort_params=True)
    print(f"  查询字符串: {wrong_query}")
    print(f"  签名: {wrong_sig}")

    print("\n✅ 正确格式（应该修改为）:")
    correct_query, correct_sig = generate_signature(correct_params, secret_key, sort_params=True)
    print(f"  查询字符串: {correct_query}")
    print(f"  签名: {correct_sig}")
    
    print("\n🔧 Go程序需要修改的地方:")
    print("1. side参数: 'long' -> 'BUY', 'short' -> 'SELL'")
    print("2. 添加apiKey参数")
    print("3. 实现HMAC SHA256签名算法")
    print("4. 按字母顺序排序所有参数")
    
    return True

def main():
    """主函数"""
    print("🔧 币安API签名测试程序")
    print("=" * 50)
    
    tests = [
        ("REST API签名验证", test_rest_api_signature),
        ("WebSocket API签名测试", test_websocket_api_signature),
        ("参数排序测试", test_parameter_ordering),
        ("Go程序格式对比", test_go_program_format),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, result in results if result)
    
    print("=" * 50)
    print(f"总测试数: {total_tests}, 通过: {passed_tests}, 失败: {total_tests - passed_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有签名测试通过！")
    else:
        print(f"⚠️ 有 {total_tests - passed_tests} 个测试失败")

if __name__ == "__main__":
    main()
