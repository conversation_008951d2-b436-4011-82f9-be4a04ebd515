#!/usr/bin/env python3
"""
双密钥解决方案测试程序
验证REST API (HMAC SHA256) + WebSocket API (Ed25519) 的可行性
"""

import asyncio
import websockets
import json
import time
import base64
import hmac
import hashlib
import logging
import aiohttp
from cryptography.hazmat.primitives.asymmetric import ed25519
from config import BINANCE_CONFIG
from ed25519_config import ED25519_PRIVATE_KEY_PEM

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class DualKeyTester:
    def __init__(self):
        # REST API配置 (HMAC SHA256)
        self.rest_api_key = BINANCE_CONFIG["rest_api_key"]
        self.rest_secret_key = BINANCE_CONFIG["rest_secret_key"]
        
        # WebSocket API配置 (Ed25519)
        self.ws_api_key = BINANCE_CONFIG.get("ws_api_key", "your_ed25519_api_key_here")
        
        # 加载Ed25519私钥
        from cryptography.hazmat.primitives import serialization
        self.ed25519_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
            serialization.load_pem_private_key(
                ED25519_PRIVATE_KEY_PEM.encode('utf-8'),
                password=None
            ).private_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PrivateFormat.Raw,
                encryption_algorithm=serialization.NoEncryption()
            )
        )
        
        # 端点配置
        self.rest_base = "https://fapi.binance.com"
        self.ws_api = "wss://ws-fapi.binance.com/ws-fapi/v1"
        
        self.request_id = 0
    
    def generate_hmac_signature(self, params: dict) -> str:
        """生成HMAC SHA256签名（REST API）"""
        # 构建查询字符串（不排序）
        query_parts = []
        for key, value in params.items():
            if key != "signature":
                query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 生成HMAC SHA256签名
        signature = hmac.new(
            self.rest_secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def generate_ed25519_signature(self, params: dict) -> str:
        """生成Ed25519签名（WebSocket API）"""
        # 排除signature参数并排序
        filtered_params = {k: v for k, v in params.items() if k != 'signature'}
        sorted_params = sorted(filtered_params.items())
        
        # 构建查询字符串
        query_parts = []
        for key, value in sorted_params:
            query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 生成Ed25519签名
        signature_bytes = self.ed25519_private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        return signature_base64
    
    async def test_rest_api_order(self):
        """测试REST API下单（HMAC SHA256）"""
        logger.info("🔍 测试REST API下单（HMAC SHA256）...")
        
        timestamp = int(time.time() * 1000)
        
        # REST API参数
        params = {
            "symbol": "ETHUSDT",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": "0.01",
            "price": "2500.00",
            "timeInForce": "GTC",
            "newClientOrderId": f"rest_dual_test_{timestamp}",
            "timestamp": timestamp,
            "test": "true"  # 测试模式
        }
        
        # 生成HMAC签名
        signature = self.generate_hmac_signature(params)
        params["signature"] = signature
        
        # 发送REST API请求
        url = f"{self.rest_base}/fapi/v1/order"
        headers = {
            'X-MBX-APIKEY': self.rest_api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=params) as response:
                    result = await response.json()
                    
                    logger.info(f"📥 REST API响应: {result}")
                    
                    if response.status == 200:
                        logger.info("✅ REST API下单成功（HMAC SHA256签名正常）")
                        return True
                    else:
                        logger.error(f"❌ REST API下单失败: {result}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ REST API测试异常: {e}")
            return False
    
    async def test_websocket_session_login(self):
        """测试WebSocket会话登录（Ed25519）"""
        logger.info("🔍 测试WebSocket会话登录（Ed25519）...")
        
        if self.ws_api_key == "your_ed25519_api_key_here":
            logger.error("❌ 请先在币安创建Ed25519 API密钥并更新配置")
            return False, None
        
        try:
            ws = await websockets.connect(self.ws_api)
            logger.info("✅ WebSocket连接成功")
            
            # 构建登录请求
            timestamp = int(time.time() * 1000)
            login_params = {
                "apiKey": self.ws_api_key,
                "timestamp": timestamp
            }
            
            # 生成Ed25519签名
            signature = self.generate_ed25519_signature(login_params)
            login_params["signature"] = signature
            
            login_request = {
                "id": f"dual_login_{timestamp}",
                "method": "session.logon",
                "params": login_params
            }
            
            logger.info(f"📤 发送WebSocket登录请求（Ed25519签名）")
            await ws.send(json.dumps(login_request))
            
            # 等待登录响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)
            
            logger.info(f"📥 WebSocket登录响应: {data}")
            
            if data.get('status') == 200:
                logger.info("✅ WebSocket会话登录成功（Ed25519签名正常）")
                return True, ws
            else:
                error = data.get('error', {})
                logger.error(f"❌ WebSocket会话登录失败: {error}")
                await ws.close()
                return False, None
                
        except Exception as e:
            logger.error(f"❌ WebSocket登录测试异常: {e}")
            return False, None
    
    async def test_websocket_authenticated_order(self, ws):
        """测试已认证WebSocket下单"""
        logger.info("🔍 测试已认证WebSocket下单...")
        
        try:
            timestamp = int(time.time() * 1000)
            
            # 构建下单请求（不需要apiKey和signature）
            order_params = {
                "symbol": "ETHUSDT",
                "side": "BUY",
                "type": "LIMIT",
                "quantity": "0.01",
                "price": "2500.00",
                "timeInForce": "GTC",
                "newClientOrderId": f"ws_dual_test_{timestamp}",
                "test": True  # 测试模式
            }
            
            order_request = {
                "id": f"dual_order_{timestamp}",
                "method": "order.place",
                "params": order_params
            }
            
            logger.info(f"📤 发送WebSocket下单请求（无需签名）")
            await ws.send(json.dumps(order_request))
            
            # 等待下单响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)
            
            logger.info(f"📥 WebSocket下单响应: {data}")
            
            if data.get('status') == 200:
                logger.info("✅ WebSocket已认证下单成功")
                return True
            else:
                error = data.get('error', {})
                logger.error(f"❌ WebSocket已认证下单失败: {error}")
                return False
                
        except Exception as e:
            logger.error(f"❌ WebSocket下单测试异常: {e}")
            return False
    
    async def run_dual_key_test(self):
        """运行双密钥方案测试"""
        logger.info("🚀 开始双密钥方案综合测试")
        logger.info("验证REST API (HMAC) + WebSocket API (Ed25519)")
        logger.info("=" * 70)
        
        results = {}
        
        # 测试1: REST API (HMAC SHA256)
        logger.info("\n📋 测试1: REST API下单（HMAC SHA256签名）")
        rest_success = await self.test_rest_api_order()
        results['rest_api_hmac'] = rest_success
        
        # 测试2: WebSocket会话登录 (Ed25519)
        logger.info("\n📋 测试2: WebSocket会话登录（Ed25519签名）")
        ws_login_success, authenticated_ws = await self.test_websocket_session_login()
        results['websocket_ed25519_login'] = ws_login_success
        
        # 测试3: 已认证WebSocket下单
        if ws_login_success and authenticated_ws:
            logger.info("\n📋 测试3: 已认证WebSocket下单")
            ws_order_success = await self.test_websocket_authenticated_order(authenticated_ws)
            results['websocket_authenticated_order'] = ws_order_success
            
            # 关闭WebSocket连接
            await authenticated_ws.close()
        else:
            results['websocket_authenticated_order'] = False
        
        # 输出测试结果
        logger.info("\n" + "=" * 70)
        logger.info("📊 双密钥方案测试结果:")
        logger.info("=" * 70)
        
        test_descriptions = {
            'rest_api_hmac': 'REST API (HMAC SHA256)',
            'websocket_ed25519_login': 'WebSocket登录 (Ed25519)',
            'websocket_authenticated_order': 'WebSocket下单 (已认证)'
        }
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            description = test_descriptions.get(test_name, test_name)
            logger.info(f"{description:30} : {status}")
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        logger.info("=" * 70)
        logger.info(f"总测试数: {total_tests}, 通过: {passed_tests}, 失败: {total_tests - passed_tests}")
        
        # 分析结果
        if results.get('rest_api_hmac') and results.get('websocket_ed25519_login'):
            logger.info("🎉 双密钥方案验证成功!")
            logger.info("💡 可以在Go程序中实现以下架构:")
            logger.info("   - REST API: 使用现有HMAC SHA256密钥")
            logger.info("   - WebSocket API: 使用Ed25519密钥")
            logger.info("   - 保持现有程序逻辑不变")
            
            if results.get('websocket_authenticated_order'):
                logger.info("   - WebSocket会话认证后可以无签名下单")
            
            return True
        else:
            logger.info("❌ 双密钥方案需要进一步配置")
            
            if not results.get('rest_api_hmac'):
                logger.info("   - REST API HMAC签名有问题")
            
            if not results.get('websocket_ed25519_login'):
                logger.info("   - 需要创建Ed25519 API密钥")
                logger.info("   - 上传公钥到币安账户")
                logger.info("   - 更新配置文件中的ws_api_key")
            
            return False

async def main():
    """主函数"""
    print("🔧 双密钥解决方案测试程序")
    print("验证REST API (HMAC SHA256) + WebSocket API (Ed25519)")
    print("=" * 60)
    
    tester = DualKeyTester()
    success = await tester.run_dual_key_test()
    
    if success:
        print("\n🎯 下一步行动:")
        print("1. 在Go程序中实现双密钥架构")
        print("2. REST API继续使用HMAC SHA256")
        print("3. WebSocket API使用Ed25519签名")
        print("4. 保持现有智能订单逻辑")
    else:
        print("\n📋 需要完成的配置:")
        print("1. 在币安创建Ed25519 API密钥")
        print("2. 上传生成的公钥文件")
        print("3. 更新config.py中的ws_api_key")
        print("4. 重新运行测试")

if __name__ == "__main__":
    try:
        import aiohttp
        import websockets
        from cryptography.hazmat.primitives.asymmetric import ed25519
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install aiohttp websockets cryptography")
        exit(1)
    
    asyncio.run(main())
