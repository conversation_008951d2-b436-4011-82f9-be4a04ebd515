#!/usr/bin/env python3
"""
使用Ed25519密钥测试币安WebSocket API
验证双密钥方案的可行性
"""

import asyncio
import websockets
import json
import time
import base64
import logging
from cryptography.hazmat.primitives.asymmetric import ed25519
from cryptography.hazmat.primitives import serialization

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class Ed25519WebSocketTester:
    def __init__(self, api_key: str, private_key_pem: str):
        self.api_key = api_key
        self.private_key = ed25519.Ed25519PrivateKey.from_private_bytes(
            serialization.load_pem_private_key(
                private_key_pem.encode('utf-8'),
                password=None
            ).private_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PrivateFormat.Raw,
                encryption_algorithm=serialization.NoEncryption()
            )
        )
        self.ws_api = "wss://ws-fapi.binance.com/ws-fapi/v1"
        self.request_id = 0
    
    def generate_request_id(self) -> str:
        """生成请求ID"""
        self.request_id += 1
        return f"ed25519_test_{int(time.time())}_{self.request_id}"
    
    def generate_ed25519_signature(self, params: dict) -> str:
        """生成Ed25519签名"""
        # 1. 排除signature参数
        filtered_params = {k: v for k, v in params.items() if k != 'signature'}
        
        # 2. 按字母顺序排序
        sorted_params = sorted(filtered_params.items())
        
        # 3. 构建查询字符串
        query_parts = []
        for key, value in sorted_params:
            query_parts.append(f"{key}={value}")
        query_string = "&".join(query_parts)
        
        # 4. 生成Ed25519签名
        signature_bytes = self.private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        logger.debug(f"签名字符串: {query_string}")
        logger.debug(f"Ed25519签名: {signature_base64}")
        
        return signature_base64
    
    async def test_session_login(self):
        """测试会话登录"""
        logger.info("🔍 测试Ed25519会话登录...")
        
        try:
            async with websockets.connect(self.ws_api) as ws:
                logger.info("✅ WebSocket连接成功")
                
                # 构建登录请求
                timestamp = int(time.time() * 1000)
                login_params = {
                    "apiKey": self.api_key,
                    "timestamp": timestamp
                }
                
                # 生成签名
                signature = self.generate_ed25519_signature(login_params)
                login_params["signature"] = signature
                
                login_request = {
                    "id": self.generate_request_id(),
                    "method": "session.logon",
                    "params": login_params
                }
                
                logger.info(f"📤 发送登录请求:")
                logger.info(f"   API Key: {self.api_key[:20]}...")
                logger.info(f"   Timestamp: {timestamp}")
                logger.info(f"   Signature: {signature[:20]}...")
                
                await ws.send(json.dumps(login_request))
                
                # 等待登录响应
                response = await asyncio.wait_for(ws.recv(), timeout=10)
                data = json.loads(response)
                
                logger.info(f"📥 登录响应: {data}")
                
                if data.get('status') == 200:
                    logger.info("✅ Ed25519会话登录成功!")
                    return True, ws
                else:
                    error = data.get('error', {})
                    logger.error(f"❌ 会话登录失败: {error}")
                    return False, None
                    
        except Exception as e:
            logger.error(f"❌ 会话登录异常: {e}")
            return False, None
    
    async def test_authenticated_order(self, ws):
        """测试已认证会话的下单"""
        logger.info("🔍 测试已认证会话的下单...")
        
        try:
            # 构建下单请求 (不需要apiKey和signature)
            timestamp = int(time.time() * 1000)
            order_params = {
                "symbol": "ETHUSDT",
                "side": "BUY",
                "type": "LIMIT",
                "quantity": "0.01",
                "price": "2500.00",
                "timeInForce": "GTC",
                "newClientOrderId": f"ed25519_test_{timestamp}",
                "test": True  # 测试模式
            }
            
            order_request = {
                "id": self.generate_request_id(),
                "method": "order.place",
                "params": order_params
            }
            
            logger.info(f"📤 发送下单请求 (无需签名):")
            logger.info(f"   Symbol: {order_params['symbol']}")
            logger.info(f"   Side: {order_params['side']}")
            logger.info(f"   Quantity: {order_params['quantity']}")
            logger.info(f"   Price: {order_params['price']}")
            
            await ws.send(json.dumps(order_request))
            
            # 等待下单响应
            response = await asyncio.wait_for(ws.recv(), timeout=10)
            data = json.loads(response)
            
            logger.info(f"📥 下单响应: {data}")
            
            if data.get('status') == 200:
                logger.info("✅ 已认证会话下单成功!")
                return True
            else:
                error = data.get('error', {})
                logger.error(f"❌ 已认证会话下单失败: {error}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 已认证会话下单异常: {e}")
            return False
    
    async def test_individual_request_signing(self):
        """测试单独请求签名"""
        logger.info("🔍 测试单独请求的Ed25519签名...")
        
        try:
            async with websockets.connect(self.ws_api) as ws:
                logger.info("✅ WebSocket连接成功")
                
                # 构建带签名的下单请求
                timestamp = int(time.time() * 1000)
                order_params = {
                    "apiKey": self.api_key,
                    "symbol": "ETHUSDT",
                    "side": "BUY",
                    "type": "LIMIT",
                    "quantity": "0.01",
                    "price": "2500.00",
                    "timeInForce": "GTC",
                    "newClientOrderId": f"ed25519_individual_{timestamp}",
                    "timestamp": timestamp,
                    "test": True
                }
                
                # 生成签名
                signature = self.generate_ed25519_signature(order_params)
                order_params["signature"] = signature
                
                order_request = {
                    "id": self.generate_request_id(),
                    "method": "order.place",
                    "params": order_params
                }
                
                logger.info(f"📤 发送带签名的下单请求:")
                logger.info(f"   API Key: {self.api_key[:20]}...")
                logger.info(f"   Signature: {signature[:20]}...")
                
                await ws.send(json.dumps(order_request))
                
                # 等待响应
                response = await asyncio.wait_for(ws.recv(), timeout=10)
                data = json.loads(response)
                
                logger.info(f"📥 带签名下单响应: {data}")
                
                if data.get('status') == 200:
                    logger.info("✅ 单独请求签名下单成功!")
                    return True
                else:
                    error = data.get('error', {})
                    logger.error(f"❌ 单独请求签名下单失败: {error}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 单独请求签名下单异常: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始Ed25519 WebSocket API综合测试")
        logger.info("=" * 60)
        
        results = {}
        
        # 测试1: 会话登录
        logger.info("\n📋 测试1: Ed25519会话登录")
        login_success, authenticated_ws = await self.test_session_login()
        results['session_login'] = login_success
        
        if login_success and authenticated_ws:
            # 测试2: 已认证会话下单
            logger.info("\n📋 测试2: 已认证会话下单")
            auth_order_success = await self.test_authenticated_order(authenticated_ws)
            results['authenticated_order'] = auth_order_success
            
            # 关闭已认证的连接
            await authenticated_ws.close()
        else:
            results['authenticated_order'] = False
        
        # 测试3: 单独请求签名
        logger.info("\n📋 测试3: 单独请求Ed25519签名")
        individual_success = await self.test_individual_request_signing()
        results['individual_signing'] = individual_success
        
        # 输出结果
        logger.info("\n" + "=" * 60)
        logger.info("📊 Ed25519测试结果汇总:")
        logger.info("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name:20} : {status}")
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        logger.info("=" * 60)
        logger.info(f"总测试数: {total_tests}, 通过: {passed_tests}, 失败: {total_tests - passed_tests}")
        
        if passed_tests > 0:
            logger.info("🎉 Ed25519签名方案可行!")
            logger.info("💡 可以在Go程序中实现双密钥方案")
        else:
            logger.info("❌ Ed25519签名方案需要进一步调试")
        
        return results

async def main():
    """主函数"""
    print("🔧 Ed25519 WebSocket API测试程序")
    print("=" * 50)
    
    # 检查是否有Ed25519配置
    try:
        from ed25519_config import ED25519_PRIVATE_KEY_PEM
        from config import BINANCE_CONFIG
        
        api_key = BINANCE_CONFIG["api_key"]
        
        # 注意：这里需要使用Ed25519 API密钥，不是HMAC密钥
        print("⚠️ 注意：请确保使用的是Ed25519 API密钥")
        print("如果还没有Ed25519密钥，请先运行 generate_ed25519_keys.py")
        
        tester = Ed25519WebSocketTester(api_key, ED25519_PRIVATE_KEY_PEM)
        results = await tester.run_comprehensive_test()
        
        if any(results.values()):
            print("\n🎉 Ed25519方案验证成功!")
            print("现在可以在Go程序中实现双密钥方案:")
            print("- REST API: 使用现有HMAC SHA256密钥")
            print("- WebSocket API: 使用Ed25519密钥")
        
    except ImportError:
        print("❌ 缺少Ed25519配置文件")
        print("请先运行: python3 generate_ed25519_keys.py")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    try:
        from cryptography.hazmat.primitives.asymmetric import ed25519
        import websockets
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install cryptography websockets")
        exit(1)
    
    asyncio.run(main())
