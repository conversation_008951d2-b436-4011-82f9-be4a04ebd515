#!/usr/bin/env python3
"""
测试成交监控功能
模拟订单成交监控，验证监控机制是否正常工作
"""

import asyncio
import aiohttp
import time
import base64
import json
import websockets
from cryptography.hazmat.primitives.asymmetric import ed25519

class FillMonitoringTester:
    def __init__(self):
        # 使用与主程序相同的API配置
        self.api_key = "3C6E57rXbU42jM58knm9ZG6y5ALt574jThFNpm3ZSdSQIc5Hhy8uTeV6iPsjaLso"
        self.private_key_hex = "6744730860246040c6389827880a2d52e14ebce72d18eea45dda64b76415d5e8"
        self.base_url = "https://fapi.binance.com"
        
        # 转换私钥
        private_key_bytes = bytes.fromhex(self.private_key_hex)
        self.private_key = ed25519.Ed25519PrivateKey.from_private_bytes(private_key_bytes)
        
    def generate_ed25519_signature_rest(self, params: dict) -> str:
        """生成Ed25519签名（REST API格式 - 不排序）"""
        # 构建查询字符串（不排序，保持原始顺序）
        query_parts = []
        for key, value in params.items():
            if key != "signature":
                query_parts.append(f"{key}={value}")
        
        query_string = "&".join(query_parts)
        
        # 生成Ed25519签名
        signature_bytes = self.private_key.sign(query_string.encode('utf-8'))
        signature_base64 = base64.b64encode(signature_bytes).decode('utf-8')
        
        return signature_base64

    async def query_order_status(self, order_id):
        """查询订单状态"""
        try:
            timestamp = int(time.time() * 1000)
            
            # 构建查询参数
            params = {
                'symbol': 'ETHUSDT',
                'orderId': order_id,
                'timestamp': timestamp
            }
            
            # 生成签名
            signature = self.generate_ed25519_signature_rest(params)
            params['signature'] = signature
            
            # 发送REST API请求
            url = f"{self.base_url}/fapi/v1/order"
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data
                    else:
                        error_text = await response.text()
                        print(f"❌ 查询失败: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            return None

    async def get_listen_key(self):
        """获取用户数据流listenKey"""
        try:
            timestamp = int(time.time() * 1000)
            
            # 构建请求参数
            params = {
                'timestamp': timestamp
            }
            
            # 生成签名
            signature = self.generate_ed25519_signature_rest(params)
            params['signature'] = signature
            
            # 发送REST API请求
            url = f"{self.base_url}/fapi/v1/listenKey"
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('listenKey')
                    else:
                        error_text = await response.text()
                        print(f"❌ 获取listenKey失败: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ 获取listenKey异常: {e}")
            return None

    async def test_rest_monitoring(self, order_id, duration=10):
        """测试REST API监控"""
        print(f"\n🔍 测试REST API监控 (持续{duration}秒)")
        print("="*50)
        
        start_time = time.time()
        query_count = 0
        
        while time.time() - start_time < duration:
            query_count += 1
            order_info = await self.query_order_status(order_id)
            
            if order_info:
                status = order_info.get('status')
                executed_qty = order_info.get('executedQty', '0')
                print(f"✅ 查询#{query_count}: 状态={status}, 成交={executed_qty}")
                
                if status in ['FILLED', 'PARTIALLY_FILLED']:
                    print(f"🎯 REST API检测到订单成交! (查询#{query_count})")
                    return True
            else:
                print(f"❌ 查询#{query_count}: 未获取到订单信息")
            
            await asyncio.sleep(0.5)  # 每0.5秒查询一次
        
        print(f"⏰ REST API监控结束 (共查询{query_count}次)")
        return False

    async def test_user_stream_monitoring(self, order_id, duration=10):
        """测试用户数据流监控"""
        print(f"\n🔍 测试用户数据流监控 (持续{duration}秒)")
        print("="*50)
        
        try:
            # 获取listenKey
            listen_key = await self.get_listen_key()
            if not listen_key:
                print("❌ 获取listenKey失败")
                return False
            
            print(f"✅ 获取listenKey成功: {listen_key[:20]}...")
            
            # 连接用户数据流WebSocket
            user_stream_url = f"wss://fstream.binance.com/ws/{listen_key}"
            
            async with websockets.connect(user_stream_url) as user_ws:
                print("✅ 用户数据流WebSocket连接成功")
                
                start_time = time.time()
                message_count = 0
                
                while time.time() - start_time < duration:
                    try:
                        # 设置较短的超时
                        message = await asyncio.wait_for(user_ws.recv(), timeout=1.0)
                        data = json.loads(message)
                        message_count += 1
                        
                        print(f"📥 消息#{message_count}: {data.get('e', 'unknown')}")
                        
                        # 检查是否是ORDER_TRADE_UPDATE事件
                        if data.get('e') == 'ORDER_TRADE_UPDATE':
                            order_data = data.get('o', {})
                            msg_order_id = order_data.get('i')
                            status = order_data.get('X')
                            
                            print(f"🔍 ORDER_TRADE_UPDATE: orderId={msg_order_id}, status={status}")
                            
                            if str(msg_order_id) == str(order_id) and status in ['FILLED', 'PARTIALLY_FILLED']:
                                print(f"🎯 用户数据流检测到订单成交!")
                                return True
                        
                    except asyncio.TimeoutError:
                        # 超时是正常的，继续循环
                        continue
                    except Exception as e:
                        print(f"❌ 消息处理异常: {e}")
                
                print(f"⏰ 用户数据流监控结束 (收到{message_count}条消息)")
                return False
                
        except Exception as e:
            print(f"❌ 用户数据流监控异常: {e}")
            return False

    async def test_monitoring_systems(self, order_id):
        """测试监控系统"""
        print(f"🔧 测试订单成交监控系统")
        print(f"订单ID: {order_id}")
        print("="*60)
        
        # 首先查询订单当前状态
        print("\n📋 步骤1: 查询订单当前状态")
        order_info = await self.query_order_status(order_id)
        if order_info:
            status = order_info.get('status')
            executed_qty = order_info.get('executedQty', '0')
            print(f"✅ 当前状态: {status}, 成交数量: {executed_qty}")
            
            if status in ['FILLED', 'PARTIALLY_FILLED']:
                print("⚠️  订单已经成交，无法测试监控功能")
                return
        else:
            print("❌ 无法查询订单状态")
            return
        
        # 测试两种监控方式
        print("\n📋 步骤2: 并发测试两种监控方式")
        
        # 创建两个监控任务
        rest_task = asyncio.create_task(self.test_rest_monitoring(order_id, 30))
        stream_task = asyncio.create_task(self.test_user_stream_monitoring(order_id, 30))
        
        # 等待任一任务完成
        done, pending = await asyncio.wait(
            [rest_task, stream_task], 
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # 取消未完成的任务
        for task in pending:
            task.cancel()
        
        # 检查结果
        for task in done:
            if task.result():
                print(f"\n🎉 监控成功检测到订单成交!")
                return
        
        print(f"\n⏰ 监控测试完成，未检测到成交")

async def main():
    """主函数"""
    print("🔧 成交监控功能测试")
    print("="*60)
    
    # 使用已知的已成交订单进行测试
    order_id = "8389765902075360805"
    
    tester = FillMonitoringTester()
    await tester.test_monitoring_systems(order_id)

if __name__ == "__main__":
    try:
        import aiohttp
        import websockets
        from cryptography.hazmat.primitives.asymmetric import ed25519
    except ImportError:
        print("❌ 缺少依赖包，请安装:")
        print("pip install aiohttp websockets cryptography")
        exit(1)
    
    asyncio.run(main())
