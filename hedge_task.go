package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/shopspring/decimal"
)

// 全局币安客户端
var binanceClient *BinanceClient

// parseDecimal 解析decimal字符串
func parseDecimal(s string) (decimal.Decimal, error) {
	return decimal.NewFromString(s)
}

// parseOrderID 安全解析订单ID，避免精度丢失
// 支持从JSON中解析订单ID，无论原始类型是什么都能正确处理
func parseOrderID(data map[string]interface{}, key string) (string, error) {
	// 获取原始值
	rawValue, exists := data[key]
	if !exists {
		return "", fmt.Errorf("订单ID字段 '%s' 不存在", key)
	}

	//	log.Printf("🔍 [调试] parseOrderID - 字段: %s, 原始值: %v, 类型: %T", key, rawValue, rawValue)

	// 根据不同类型进行处理
	switch v := rawValue.(type) {
	case string:
		// 如果已经是字符串（通过 json.Number 处理后），直接返回
		//log.Printf("🔍 [调试] parseOrderID - 字符串类型，直接返回: %s", v)
		return v, nil
	case float64:
		// 如果是float64（传统JSON解析），使用格式化避免精度丢失
		//log.Printf("🔍 [调试] parseOrderID - float64类型，原始值: %.0f", v)
		//log.Printf("⚠️ [警告] parseOrderID - 检测到float64类型的订单ID，可能存在精度丢失")

		// 使用 %.0f 格式化，避免 int64 转换的精度丢失
		result := fmt.Sprintf("%.0f", v)
		//log.Printf("🔍 [调试] parseOrderID - 使用格式化转换: %s", result)

		// 验证转换是否正确（检查是否包含科学计数法）
		if len(result) > 0 && result[len(result)-1] != 'e' && result[len(result)-1] != 'E' {
			//log.Printf("🔍 [调试] parseOrderID - 格式化成功，但可能有精度丢失")
			return result, nil
		} else {
			//log.Printf("🔍 [调试] parseOrderID - 格式化包含科学计数法，尝试其他方法")
			// 如果包含科学计数法，尝试更精确的方法
			result = strconv.FormatFloat(v, 'f', 0, 64)
			//log.Printf("🔍 [调试] parseOrderID - 使用FormatFloat转换: %s", result)
			return result, nil
		}
	case int64:
		// 如果是int64，直接转换为字符串
		result := strconv.FormatInt(v, 10)
		//log.Printf("🔍 [调试] parseOrderID - int64类型，值: %d, 字符串: %s", v, result)
		return result, nil
	case int:
		// 如果是int，转换为字符串
		result := strconv.Itoa(v)
		log.Printf("🔍 [调试] parseOrderID - int类型，值: %d, 字符串: %s", v, result)
		return result, nil
	default:
		// 对于其他类型，尝试转换为字符串（保留但记录警告）
		orderIDStr := fmt.Sprintf("%.0f", v)
		//log.Printf("警告：订单ID类型不常见: %T, 值: %v, 转换为: %s", v, v, orderIDStr)
		return orderIDStr, nil
	}
}

// calculateFee 根据实际成交方式计算手续费
func calculateFee(actualPrice, executedQty decimal.Decimal, executionType string) decimal.Decimal {
	makerFeeRate, takerFeeRate := GetTradingConfig()

	var feeRate float64
	switch executionType {
	case ExecutionTypeMaker:
		// 挂单（Maker）
		feeRate = makerFeeRate
	case ExecutionTypeTaker:
		// 吃单（Taker）
		feeRate = takerFeeRate
	default:
		// 默认使用吃单费率（保守估计）
		feeRate = takerFeeRate
	}

	return actualPrice.Mul(executedQty).Mul(decimal.NewFromFloat(feeRate))
}

// InitBinanceClient 初始化币安客户端
func InitBinanceClient() error {
	binanceClient = NewBinanceClient()
	return binanceClient.ValidateAPIConnection()
}

// Run 运行对冲任务主循环
func (task *HedgeTask) Run() {
	log.Printf("启动对冲任务: %s", task.ID)
	defer log.Printf("对冲任务结束: %s", task.ID)

	// 初始化智能订单系统（如果启用）
	if task.SmartOrderEnabled {
		err := task.initializeSmartOrder()
		if err != nil {
			log.Printf("任务 %s: 智能订单初始化失败: %v", task.ID, err)
			//task.SmartOrderEnabled = false
		}
	}

	// 订阅市场数据
	var orderBookChan chan *OrderBookData
	if task.SmartOrderEnabled {
		// 使用全局市场数据管理器
		mdm := GetMarketDataManager()
		var err error
		orderBookChan, err = mdm.Subscribe(task.ID, task.Symbol)
		if err != nil {
			log.Printf("任务 %s: 订阅市场数据失败: %v", task.ID, err)
			// task.SmartOrderEnabled = false
			// // 回退到传统方式
			// orderBookChan = make(chan *OrderBookData, 100)
			// go task.subscribeOrderBook(orderBookChan)
		} else {
			task.OrderBookChan = orderBookChan
		}
	} else {
		// 传统方式：直接订阅
		orderBookChan = make(chan *OrderBookData, 100)
		go task.subscribeOrderBook(orderBookChan)
	}

	// 启动智能订单事件处理协程
	if task.SmartOrderEnabled && task.SmartOrderManager != nil {
		go task.handleSmartOrderEvents()
	}

	// 主循环
	ticker := time.NewTicker(100 * time.Millisecond) // 100ms检测一次
	defer ticker.Stop()

	// 清理资源
	defer func() {
		if task.SmartOrderEnabled {
			task.cleanupSmartOrder()
		}
	}()

	for {
		select {
		case <-task.StopChan:
			// 收到停止信号，执行平仓逻辑
			log.Printf("任务 %s: 收到停止信号", task.ID)
			task.handleStop()
			return

		case orderBook := <-orderBookChan:
			// 处理盘口数据
			if task.SmartOrderEnabled {
				task.handleSmartOrderBook(orderBook)
			} else {
				// 传统处理方式
				task.drainAndProcessOrderBook(orderBookChan, orderBook)
			}

		case <-ticker.C:
			// 定期检查任务状态和订单状态
			task.checkOrderStatus()
		}
	}
}

// drainAndProcessOrderBook 清空channel中的旧数据，只处理最新的盘口数据
func (task *HedgeTask) drainAndProcessOrderBook(orderBookChan <-chan *OrderBookData, latestOrderBook *OrderBookData) {
	// 清空channel中的旧数据，获取最新的盘口数据
	for {
		select {
		case newerOrderBook := <-orderBookChan:
			latestOrderBook = newerOrderBook // 更新为更新的数据
		default:
			// channel为空，处理最新的数据
			task.handleOrderBook(latestOrderBook)
			return
		}
	}
}

// handleOrderBook 处理盘口数据，判断是否需要开仓或平仓
func (task *HedgeTask) handleOrderBook(orderBook *OrderBookData) {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	// 计算触发价格
	upperTrigger := task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))
	lowerTrigger := task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))

	currentPrice := orderBook.BidPrice.Add(orderBook.AskPrice).Div(decimal.NewFromInt(2))

	// 根据任务方向和当前仓位状态决定操作
	if task.Position == nil {
		// 无仓位，检查开仓条件
		task.checkOpenCondition(currentPrice, upperTrigger, lowerTrigger, orderBook)
	} else {
		// 有仓位，检查平仓条件
		task.checkCloseCondition(currentPrice, upperTrigger, lowerTrigger, orderBook)
	}
}

// checkOpenCondition 检查开仓条件
func (task *HedgeTask) checkOpenCondition(currentPrice, upperTrigger, lowerTrigger decimal.Decimal, orderBook *OrderBookData) {
	if task.Direction == DirectionLong {
		// 做多对冲：价格超过上触发线时开多仓
		if currentPrice.GreaterThan(upperTrigger) {
			log.Printf("任务 %s: 触发做多开仓条件，当前价格: %s, 触发价格: %s",
				task.ID, currentPrice.String(), upperTrigger.String())
			task.executeOrder(EventTypeOpen, SideLong, upperTrigger, orderBook)
		}
	} else {
		// 做空对冲：价格低于下触发线时开空仓
		if currentPrice.LessThan(lowerTrigger) {
			log.Printf("任务 %s: 触发做空开仓条件，当前价格: %s, 触发价格: %s",
				task.ID, currentPrice.String(), lowerTrigger.String())
			task.executeOrder(EventTypeOpen, SideShort, lowerTrigger, orderBook)
		}
	}
}

// checkCloseCondition 检查平仓条件
func (task *HedgeTask) checkCloseCondition(currentPrice, upperTrigger, lowerTrigger decimal.Decimal, orderBook *OrderBookData) {
	if task.Position.Side == SideLong {
		// 多仓：价格低于下触发线时平仓
		if currentPrice.LessThan(lowerTrigger) {
			log.Printf("任务 %s: 触发多仓平仓条件，当前价格: %s, 触发价格: %s",
				task.ID, currentPrice.String(), lowerTrigger.String())
			task.executeOrder(EventTypeClose, SideShort, lowerTrigger, orderBook)
		}
	} else {
		// 空仓：价格高于上触发线时平仓
		if currentPrice.GreaterThan(upperTrigger) {
			log.Printf("任务 %s: 触发空仓平仓条件，当前价格: %s, 触发价格: %s",
				task.ID, currentPrice.String(), upperTrigger.String())
			task.executeOrder(EventTypeClose, SideLong, upperTrigger, orderBook)
		}
	}
}

// executeOrder 执行下单逻辑
func (task *HedgeTask) executeOrder(eventType, side string, triggerPrice decimal.Decimal, orderBook *OrderBookData) {
	log.Printf("任务 %s: 开始执行下单，类型: %s, 方向: %s, 触发价格: %s",
		task.ID, eventType, side, triggerPrice.String())

	// 根据事件类型和方向计算正确的理论价格
	var theoreticalPrice decimal.Decimal

	if eventType == EventTypeOpen {
		if side == SideLong {
			// 做多开仓：理论价格 = 目标价 + 阈值
			theoreticalPrice = task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))
		} else {
			// 做空开仓：理论价格 = 目标价 - 阈值
			theoreticalPrice = task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))
		}
	} else {
		// 平仓
		if side == SideLong {
			// 买入平仓（空仓平仓）：理论价格 = 目标价 + 阈值
			theoreticalPrice = task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))
		} else {
			// 卖出平仓（多仓平仓）：理论价格 = 目标价 - 阈值
			theoreticalPrice = task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))
		}
	}

	log.Printf("任务 %s: 计算理论价格: %s (事件类型: %s, 方向: %s)",
		task.ID, theoreticalPrice.String(), eventType, side)

	var actualPrice decimal.Decimal
	var orderID string
	var fee decimal.Decimal
	var orderType string

	// 执行精细化下单逻辑
	if side == SideLong {
		// 买入：优先挂买一价单
		actualPrice, orderID, fee, orderType = task.executeBuyOrder(triggerPrice, orderBook)
	} else {
		// 卖出：优先挂卖一价单
		actualPrice, orderID, fee, orderType = task.executeSellOrder(triggerPrice, orderBook)
	}

	// 检查订单是否真正成交
	if strings.HasPrefix(orderID, "cancelled_") {
		log.Printf("任务 %s: 订单被撤销，未成交，不更新仓位状态", task.ID)
		// 可以选择重新尝试下单或等待下次机会
		return
	} else if strings.HasPrefix(orderID, "failed_") {
		log.Printf("任务 %s: 下单失败，不更新仓位状态", task.ID)
		return
	}

	// 只有真正成交的订单才记录事件和更新仓位
	log.Printf("任务 %s: 订单成交，记录事件和更新仓位", task.ID)

	// 记录事件（使用计算出的理论价格）
	// orderType现在包含的是实际成交方式，需要推断原始订单类型
	originalOrderType := OrderTypeLimit // 默认为限价单
	if orderType == ExecutionTypeTaker && strings.Contains(orderID, "market") {
		originalOrderType = OrderTypeMarket
	}
	event := task.createEvent(eventType, theoreticalPrice, actualPrice, fee, orderID, originalOrderType, orderType)
	task.Events = append(task.Events, event)

	// 更新仓位
	task.updatePosition(eventType, side, actualPrice, orderID)

	// 更新统计
	task.updateStatistics(event)

	// 更新时间戳
	task.UpdatedAt = time.Now()

	// 触发数据保存
	if task.onDataChange != nil {
		task.onDataChange()
	}

	// 记录事件日志（通过全局变量访问，避免循环依赖）
	if globalEventLogger != nil {
		globalEventLogger.LogTaskEvent(task.ID, event)
	}

	// 使用event中已计算好的PriceLoss进行日志记录
	log.Printf("任务 %s: 下单完成，实际价格: %s, 理论价格: %s, 价格损耗: %s, 订单ID: %s",
		task.ID, actualPrice.String(), theoreticalPrice.String(),
		event.PriceLoss.String(), orderID)
}

// executeBuyOrder 执行买入订单（做多开仓或空仓平仓）
func (task *HedgeTask) executeBuyOrder(theoreticalPrice decimal.Decimal, orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 1. 优先挂买一价单
	targetPrice := orderBook.BidPrice

	// 2. 检查滑点
	slippageThreshold := theoreticalPrice.Mul(decimal.NewFromInt(1).Add(task.SlippageTolerance))

	if targetPrice.LessThanOrEqual(slippageThreshold) {
		// 在滑点范围内，挂限价单
		return task.placeLimitBuyOrder(targetPrice, orderBook)
	} else {
		// 超出滑点，进入市价成交检测
		return task.checkMarketBuyOrder(theoreticalPrice, orderBook)
	}
}

// executeSellOrder 执行卖出订单（做空开仓或多仓平仓）
func (task *HedgeTask) executeSellOrder(theoreticalPrice decimal.Decimal, orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 1. 优先挂卖一价单
	targetPrice := orderBook.AskPrice

	// 2. 检查滑点
	slippageThreshold := theoreticalPrice.Mul(decimal.NewFromInt(1).Sub(task.SlippageTolerance))

	if targetPrice.GreaterThanOrEqual(slippageThreshold) {
		// 在滑点范围内，挂限价单
		return task.placeLimitSellOrder(targetPrice, orderBook)
	} else {
		// 超出滑点，进入市价成交检测
		return task.checkMarketSellOrder(theoreticalPrice, orderBook)
	}
}

// placeLimitBuyOrder 挂买入限价单
func (task *HedgeTask) placeLimitBuyOrder(price decimal.Decimal, orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 格式化价格和数量
	formattedPrice, _ := binanceClient.FormatPrice(task.Symbol, price)
	formattedQuantity, _ := binanceClient.FormatQuantity(task.Symbol, task.Amount)

	log.Printf("任务 %s: 挂买入限价单，价格: %s, 数量: %s",
		task.ID, formattedPrice.String(), formattedQuantity.String())

	// 调用币安API下单
	order, err := binanceClient.PlaceLimitOrder(task.Symbol, futures.SideTypeBuy, formattedQuantity, formattedPrice, task.PostOnlyMode)
	if err != nil {
		log.Printf("任务 %s: 下单失败: %v", task.ID, err)
		// 下单失败，返回错误数据
		return price, fmt.Sprintf("failed_%d", time.Now().UnixNano()), decimal.Zero, OrderTypeFailed
	}

	// 等待成交或超时
	actualPrice, fee, executionType, filled := task.waitForOrderFill(order.OrderID, formattedPrice, OrderTypeLimit)

	if !filled {
		// 订单未成交（超时撤销），返回特殊标识
		return price, fmt.Sprintf("cancelled_%d", order.OrderID), decimal.Zero, OrderTypeCancelled
	}

	// 返回实际成交方式而不是订单类型
	return actualPrice, strconv.FormatInt(order.OrderID, 10), fee, executionType
}

// placeLimitSellOrder 挂卖出限价单
func (task *HedgeTask) placeLimitSellOrder(price decimal.Decimal, orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 格式化价格和数量
	formattedPrice, _ := binanceClient.FormatPrice(task.Symbol, price)
	formattedQuantity, _ := binanceClient.FormatQuantity(task.Symbol, task.Amount)

	log.Printf("任务 %s: 挂卖出限价单，价格: %s, 数量: %s",
		task.ID, formattedPrice.String(), formattedQuantity.String())

	// 调用币安API下单
	order, err := binanceClient.PlaceLimitOrder(task.Symbol, futures.SideTypeSell, formattedQuantity, formattedPrice, task.PostOnlyMode)
	if err != nil {
		log.Printf("任务 %s: 下单失败: %v", task.ID, err)
		// 下单失败，返回错误数据
		return price, fmt.Sprintf("failed_%d", time.Now().UnixNano()), decimal.Zero, OrderTypeFailed
	}

	// 等待成交或超时
	actualPrice, fee, executionType, filled := task.waitForOrderFill(order.OrderID, formattedPrice, OrderTypeLimit)

	if !filled {
		// 订单未成交（超时撤销），返回特殊标识
		return price, fmt.Sprintf("cancelled_%d", order.OrderID), decimal.Zero, OrderTypeCancelled
	}

	// 返回实际成交方式而不是订单类型
	return actualPrice, strconv.FormatInt(order.OrderID, 10), fee, executionType
}

// checkMarketBuyOrder 检查是否市价买入
func (task *HedgeTask) checkMarketBuyOrder(theoreticalPrice decimal.Decimal, orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 检查盘口量
	volumeRatio := task.Amount.Div(orderBook.AskQty)

	if volumeRatio.GreaterThan(task.VolumeThreshold) {
		// 盘口量足够，市价成交
		log.Printf("任务 %s: 执行市价买入，盘口量比例: %s", task.ID, volumeRatio.String())
		return task.placeMarketBuyOrder(orderBook)
	} else {
		// 盘口量不足，等待或使用限价单
		log.Printf("任务 %s: 盘口量不足，使用限价单，盘口量比例: %s", task.ID, volumeRatio.String())
		return task.placeLimitBuyOrder(orderBook.BidPrice, orderBook)
	}
}

// checkMarketSellOrder 检查是否市价卖出
func (task *HedgeTask) checkMarketSellOrder(theoreticalPrice decimal.Decimal, orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 检查盘口量
	volumeRatio := task.Amount.Div(orderBook.BidQty)

	if volumeRatio.GreaterThan(task.VolumeThreshold) {
		// 盘口量足够，市价成交
		log.Printf("任务 %s: 执行市价卖出，盘口量比例: %s", task.ID, volumeRatio.String())
		return task.placeMarketSellOrder(orderBook)
	} else {
		// 盘口量不足，等待或使用限价单
		log.Printf("任务 %s: 盘口量不足，使用限价单，盘口量比例: %s", task.ID, volumeRatio.String())
		return task.placeLimitSellOrder(orderBook.AskPrice, orderBook)
	}
}

// placeMarketBuyOrder 市价买入
func (task *HedgeTask) placeMarketBuyOrder(orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 格式化数量
	formattedQuantity, _ := binanceClient.FormatQuantity(task.Symbol, task.Amount)

	// 调用币安API市价下单
	order, err := binanceClient.PlaceMarketOrder(task.Symbol, futures.SideTypeBuy, formattedQuantity)
	if err != nil {
		log.Printf("任务 %s: 市价下单失败: %v", task.ID, err)
		// 下单失败，返回错误数据
		return orderBook.AskPrice, fmt.Sprintf("failed_%d", time.Now().UnixNano()), decimal.Zero, OrderTypeFailed
	}

	// 等待成交确认
	actualPrice, fee, executionType, filled := task.waitForOrderFill(order.OrderID, orderBook.AskPrice, OrderTypeMarket)

	if !filled {
		// 市价单一般不会撤销，但为了安全起见还是检查
		log.Printf("任务 %s: 市价单未成交，异常情况", task.ID)
		return orderBook.AskPrice, fmt.Sprintf("cancelled_%d", order.OrderID), decimal.Zero, OrderTypeCancelled
	}

	log.Printf("任务 %s: 市价买入成交，价格: %s, 成交方式: %s", task.ID, actualPrice.String(), executionType)
	return actualPrice, strconv.FormatInt(order.OrderID, 10), fee, executionType
}

// placeMarketSellOrder 市价卖出
func (task *HedgeTask) placeMarketSellOrder(orderBook *OrderBookData) (decimal.Decimal, string, decimal.Decimal, string) {
	// 格式化数量
	formattedQuantity, _ := binanceClient.FormatQuantity(task.Symbol, task.Amount)

	// 调用币安API市价下单
	order, err := binanceClient.PlaceMarketOrder(task.Symbol, futures.SideTypeSell, formattedQuantity)
	if err != nil {
		log.Printf("任务 %s: 市价下单失败: %v", task.ID, err)
		// 下单失败，返回错误数据
		return orderBook.BidPrice, fmt.Sprintf("failed_%d", time.Now().UnixNano()), decimal.Zero, OrderTypeFailed
	}

	// 等待成交确认
	actualPrice, fee, executionType, filled := task.waitForOrderFill(order.OrderID, orderBook.BidPrice, OrderTypeMarket)

	if !filled {
		// 市价单一般不会撤销，但为了安全起见还是检查
		log.Printf("任务 %s: 市价单未成交，异常情况", task.ID)
		return orderBook.BidPrice, fmt.Sprintf("cancelled_%d", order.OrderID), decimal.Zero, OrderTypeCancelled
	}

	log.Printf("任务 %s: 市价卖出成交，价格: %s, 成交方式: %s", task.ID, actualPrice.String(), executionType)
	return actualPrice, strconv.FormatInt(order.OrderID, 10), fee, executionType
}

// waitForOrderFill 等待订单成交
func (task *HedgeTask) waitForOrderFill(orderID int64, expectedPrice decimal.Decimal, orderType string) (decimal.Decimal, decimal.Decimal, string, bool) {
	timeout := time.After(time.Duration(task.MarketTimeout) * time.Second)
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			// 超时，撤销订单
			log.Printf("任务 %s: 订单 %d 超时，尝试撤销", task.ID, orderID)
			err := binanceClient.CancelOrder(task.Symbol, orderID)
			if err != nil {
				log.Printf("任务 %s: 撤销订单失败: %v", task.ID, err)
			}
			// 返回false表示订单未成交
			return expectedPrice, decimal.Zero, ExecutionTypeUnknown, false

		case <-ticker.C:
			// 检查订单状态
			order, err := binanceClient.GetOrderStatus(task.Symbol, orderID)
			if err != nil {
				log.Printf("任务 %s: 获取订单状态失败: %v", task.ID, err)
				continue
			}

			if order.Status == futures.OrderStatusTypeFilled {
				// 订单已成交，获取详细成交信息
				actualPrice, _ := decimal.NewFromString(order.AvgPrice)
				executedQty, _ := decimal.NewFromString(order.ExecutedQuantity)

				// 获取实际成交方式
				executionType := task.getExecutionType(orderID, orderType, expectedPrice, actualPrice)

				// 根据实际成交方式计算手续费
				fee := calculateFee(actualPrice, executedQty, executionType)

				log.Printf("任务 %s: 订单 %d 已成交，价格: %s, 成交方式: %s", task.ID, orderID, actualPrice.String(), executionType)
				return actualPrice, fee, executionType, true
			} else if order.Status == futures.OrderStatusTypeCanceled {
				// 订单已被撤销
				log.Printf("任务 %s: 订单 %d 已被撤销", task.ID, orderID)
				return expectedPrice, decimal.Zero, ExecutionTypeUnknown, false
			}
		}
	}
}

// handleStop 处理任务停止，执行平仓逻辑
func (task *HedgeTask) handleStop() {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	if task.Position == nil {
		log.Printf("任务 %s: 无仓位，无需平仓，但需要清理资源", task.ID)
		// 无仓位的任务不需要平仓，但仍需要清理资源
		// 这些清理工作会在主循环结束后自动执行
		return
	}

	// 检查是否跳过平仓
	if task.skipCloseOnStop {
		log.Printf("任务 %s: 跳过平仓，保留仓位: %s %s",
			task.ID, task.Position.Side, task.Position.Size.String())
		return
	}

	log.Printf("任务 %s: 开始停止平仓，仓位: %s %s",
		task.ID, task.Position.Side, task.Position.Size.String())

	// 根据仓位方向确定理论平仓价和平仓方向
	var theoreticalClosePrice decimal.Decimal
	var closeSide string

	if task.Position.Side == SideLong {
		// 做多平仓：卖出，理论价格 = 目标价 - 阈值
		theoreticalClosePrice = task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))
		closeSide = SideShort
		log.Printf("任务 %s: 做多平仓，理论平仓价: %s (目标价-阈值)", task.ID, theoreticalClosePrice.String())
	} else {
		// 做空平仓：买入，理论价格 = 目标价 + 阈值
		theoreticalClosePrice = task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))
		closeSide = SideLong
		log.Printf("任务 %s: 做空平仓，理论平仓价: %s (目标价+阈值)", task.ID, theoreticalClosePrice.String())
	}

	// 启动动态平仓逻辑
	task.executeStopClose(theoreticalClosePrice, closeSide)
}

// executeStopClose 执行停止平仓的动态逻辑
func (task *HedgeTask) executeStopClose(theoreticalClosePrice decimal.Decimal, closeSide string) {
	log.Printf("任务 %s: 开始动态平仓，理论价格: %s, 方向: %s",
		task.ID, theoreticalClosePrice.String(), closeSide)

	// 创建平仓专用的订单簿监控
	closeOrderBookChan := make(chan *OrderBookData, 100)
	closeStopChan := make(chan struct{})

	// 启动盘口监控
	go binanceClient.SubscribeOrderBook(task.Symbol, closeOrderBookChan, closeStopChan)

	// 设置平仓超时（比如30秒）
	timeout := time.After(30 * time.Second)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	defer close(closeStopChan)

	var currentOrderID int64 = 0 // 当前挂单ID

	for {
		select {
		case <-timeout:
			log.Printf("任务 %s: 平仓超时，强制市价平仓", task.ID)
			if currentOrderID != 0 {
				binanceClient.CancelOrder(task.Symbol, currentOrderID)
			}
			task.forceMarketClose(theoreticalClosePrice, closeSide)
			return

		case orderBook := <-closeOrderBookChan:
			// 检查是否需要调整挂单
			shouldAdjust, newPrice := task.shouldAdjustCloseOrder(theoreticalClosePrice, closeSide, orderBook)

			if shouldAdjust {
				// 撤销当前订单（如果有）
				if currentOrderID != 0 {
					log.Printf("任务 %s: 撤销当前平仓订单: %d", task.ID, currentOrderID)
					binanceClient.CancelOrder(task.Symbol, currentOrderID)
					currentOrderID = 0
				}

				// 下新的限价单
				if newPrice.GreaterThan(decimal.Zero) {
					orderID := task.placeCloseOrder(newPrice, closeSide)
					if orderID > 0 {
						currentOrderID = orderID
					}
				} else {
					// 超出滑点范围，市价成交
					log.Printf("任务 %s: 超出滑点范围，执行市价平仓", task.ID)
					task.forceMarketClose(theoreticalClosePrice, closeSide)
					return
				}
			}

		case <-ticker.C:
			// 检查当前订单状态
			if currentOrderID != 0 {
				order, err := binanceClient.GetOrderStatus(task.Symbol, currentOrderID)
				if err == nil && order.Status == futures.OrderStatusTypeFilled {
					log.Printf("任务 %s: 平仓订单已成交，订单ID: %d", task.ID, currentOrderID)
					// 记录平仓事件
					actualPrice, _ := decimal.NewFromString(order.AvgPrice)
					executedQty, _ := decimal.NewFromString(order.ExecutedQuantity)

					// 获取实际成交方式
					executionType := task.getExecutionType(currentOrderID, OrderTypeLimit, theoreticalClosePrice, actualPrice)

					// 根据实际成交方式计算手续费
					fee := calculateFee(actualPrice, executedQty, executionType)

					event := task.createEvent(EventTypeClose, theoreticalClosePrice, actualPrice, fee, strconv.FormatInt(currentOrderID, 10), OrderTypeLimit, executionType)
					task.Events = append(task.Events, event)
					task.updatePosition(EventTypeClose, closeSide, actualPrice, strconv.FormatInt(currentOrderID, 10))
					task.updateStatistics(event)

					log.Printf("任务 %s: 平仓完成，实际价格: %s", task.ID, actualPrice.String())
					return
				}
			}
		}
	}
}

// shouldAdjustCloseOrder 判断是否需要调整平仓订单
func (task *HedgeTask) shouldAdjustCloseOrder(theoreticalPrice decimal.Decimal, closeSide string, orderBook *OrderBookData) (bool, decimal.Decimal) {
	// 计算滑点范围
	upperBound := theoreticalPrice.Mul(decimal.NewFromInt(1).Add(task.StopSlippage))
	lowerBound := theoreticalPrice.Mul(decimal.NewFromInt(1).Sub(task.StopSlippage))

	var targetPrice decimal.Decimal

	if closeSide == SideShort {
		// 卖出平仓：检查卖一价
		targetPrice = orderBook.AskPrice
		log.Printf("任务 %s: 检查卖出平仓条件 - 卖一价: %s, 范围: [%s, %s]",
			task.ID, targetPrice.String(), lowerBound.String(), upperBound.String())

		if targetPrice.GreaterThanOrEqual(lowerBound) && targetPrice.LessThanOrEqual(upperBound) {
			// 在滑点范围内，可以挂限价单
			return true, targetPrice
		} else {
			// 超出滑点范围，需要市价成交
			return true, decimal.Zero
		}
	} else {
		// 买入平仓：检查买一价
		targetPrice = orderBook.BidPrice
		log.Printf("任务 %s: 检查买入平仓条件 - 买一价: %s, 范围: [%s, %s]",
			task.ID, targetPrice.String(), lowerBound.String(), upperBound.String())

		if targetPrice.GreaterThanOrEqual(lowerBound) && targetPrice.LessThanOrEqual(upperBound) {
			// 在滑点范围内，可以挂限价单
			return true, targetPrice
		} else {
			// 超出滑点范围，需要市价成交
			return true, decimal.Zero
		}
	}
}

// placeCloseOrder 下平仓限价单
func (task *HedgeTask) placeCloseOrder(price decimal.Decimal, closeSide string) int64 {
	formattedPrice, _ := binanceClient.FormatPrice(task.Symbol, price)
	formattedQuantity, _ := binanceClient.FormatQuantity(task.Symbol, task.Amount)

	var sideType futures.SideType
	if closeSide == SideShort {
		sideType = futures.SideTypeSell
	} else {
		sideType = futures.SideTypeBuy
	}

	log.Printf("任务 %s: 下平仓限价单，价格: %s, 数量: %s, 方向: %s",
		task.ID, formattedPrice.String(), formattedQuantity.String(), closeSide)

	order, err := binanceClient.PlaceLimitOrder(task.Symbol, sideType, formattedQuantity, formattedPrice, task.PostOnlyMode)
	if err != nil {
		log.Printf("任务 %s: 平仓下单失败: %v", task.ID, err)
		return 0
	}

	return order.OrderID
}

// forceMarketClose 强制市价平仓
func (task *HedgeTask) forceMarketClose(theoreticalPrice decimal.Decimal, closeSide string) {
	formattedQuantity, _ := binanceClient.FormatQuantity(task.Symbol, task.Amount)

	var sideType futures.SideType
	if closeSide == SideShort {
		sideType = futures.SideTypeSell
	} else {
		sideType = futures.SideTypeBuy
	}

	log.Printf("任务 %s: 强制市价平仓，数量: %s, 方向: %s",
		task.ID, formattedQuantity.String(), closeSide)

	order, err := binanceClient.PlaceMarketOrder(task.Symbol, sideType, formattedQuantity)
	if err != nil {
		log.Printf("任务 %s: 市价平仓失败: %v", task.ID, err)
		return
	}

	// 等待成交确认
	time.Sleep(1 * time.Second) // 简单等待，市价单通常很快成交

	orderStatus, err := binanceClient.GetOrderStatus(task.Symbol, order.OrderID)
	if err == nil && orderStatus.Status == futures.OrderStatusTypeFilled {
		actualPrice, _ := decimal.NewFromString(orderStatus.AvgPrice)
		executedQty, _ := decimal.NewFromString(orderStatus.ExecutedQuantity)

		// 获取实际成交方式
		executionType := task.getExecutionType(order.OrderID, OrderTypeMarket, theoreticalPrice, actualPrice)

		// 根据实际成交方式计算手续费
		fee := calculateFee(actualPrice, executedQty, executionType)

		event := task.createEvent(EventTypeClose, theoreticalPrice, actualPrice, fee, strconv.FormatInt(order.OrderID, 10), OrderTypeMarket, executionType)
		task.Events = append(task.Events, event)
		task.updatePosition(EventTypeClose, closeSide, actualPrice, strconv.FormatInt(order.OrderID, 10))
		task.updateStatistics(event)

		log.Printf("任务 %s: 市价平仓完成，实际价格: %s", task.ID, actualPrice.String())
	}
}

// subscribeOrderBook 订阅盘口数据
func (task *HedgeTask) subscribeOrderBook(orderBookChan chan<- *OrderBookData) {
	// 使用真实的WebSocket订阅
	binanceClient.SubscribeOrderBook(task.Symbol, orderBookChan, task.StopChan)
}

// checkOrderStatus 定期检查任务状态和订单状态
func (task *HedgeTask) checkOrderStatus() {
	// 可以在这里添加任务健康检查逻辑
	// 例如检查网络连接、API限制等
}

// createEvent 创建事件记录
func (task *HedgeTask) createEvent(eventType string, theoreticalPrice, actualPrice, fee decimal.Decimal, orderID string, orderType string, executionType string) Event {
	// 根据最新的损耗计算规则修正 priceLoss 的计算
	var priceLoss decimal.Decimal
	if task.Direction == DirectionLong { // 当前任务是做多对冲
		if eventType == EventTypeOpen { // 做多开仓
			priceLoss = actualPrice.Sub(theoreticalPrice)
		} else { // 做多平仓 (eventType == EventTypeClose)
			priceLoss = theoreticalPrice.Sub(actualPrice)
		}
	} else { // 当前任务是做空对冲 (task.Direction == DirectionShort)
		if eventType == EventTypeOpen { // 做空开仓
			priceLoss = theoreticalPrice.Sub(actualPrice)
		} else { // 做空平仓 (eventType == EventTypeClose)
			priceLoss = actualPrice.Sub(theoreticalPrice)
		}
	}

	priceLossRatio := decimal.Zero
	if !task.TargetPrice.IsZero() { // 避免除以零
		priceLossRatio = priceLoss.Div(task.TargetPrice)
	}

	feeRatio := decimal.Zero
	if !task.TargetPrice.IsZero() && !task.Amount.IsZero() { // 避免除以零
		denominator := task.TargetPrice.Mul(task.Amount)
		if !denominator.IsZero() {
			feeRatio = fee.Div(denominator)
		}
	}

	return Event{
		ID:               fmt.Sprintf("event_%d", time.Now().UnixNano()),
		Type:             eventType,
		OrderType:        orderType,
		ExecutionType:    executionType,
		TheoreticalPrice: theoreticalPrice,
		ActualPrice:      actualPrice,
		PriceLoss:        priceLoss,
		PriceLossRatio:   priceLossRatio,
		Fee:              fee,
		FeeRatio:         feeRatio,
		OrderID:          orderID,
		Timestamp:        time.Now(),
	}
}

// updatePosition 更新仓位信息
func (task *HedgeTask) updatePosition(eventType, side string, actualPrice decimal.Decimal, orderID string) {
	if eventType == EventTypeOpen {
		// 开仓
		task.Position = &Position{
			Side:       side,
			Size:       task.Amount,
			EntryPrice: actualPrice,
			EntryTime:  time.Now(),
			OrderID:    orderID,
		}
	} else {
		// 平仓
		task.Position = nil
	}

	// 更新时间戳
	task.UpdatedAt = time.Now()

	// 触发数据保存
	if task.onDataChange != nil {
		task.onDataChange()
	}
}

// updateStatistics 更新统计信息
func (task *HedgeTask) updateStatistics(event Event) {
	task.Statistics.TotalTrades++
	task.Statistics.TotalFees = task.Statistics.TotalFees.Add(event.Fee)
	task.Statistics.TotalPriceLoss = task.Statistics.TotalPriceLoss.Add(event.PriceLoss)

	// 如果是平仓事件，计算盈亏
	if event.Type == EventTypeClose && len(task.Events) >= 2 {
		// 找到对应的开仓事件
		for i := len(task.Events) - 2; i >= 0; i-- {
			if task.Events[i].Type == EventTypeOpen {
				openEvent := task.Events[i]

				// 计算盈亏
				var pnl decimal.Decimal
				if task.Position != nil && task.Position.Side == SideLong {
					// 多仓盈亏 = (平仓价 - 开仓价) * 数量 - 手续费
					pnl = event.ActualPrice.Sub(openEvent.ActualPrice).Mul(task.Amount).Sub(openEvent.Fee).Sub(event.Fee)
				} else {
					// 空仓盈亏 = (开仓价 - 平仓价) * 数量 - 手续费
					pnl = openEvent.ActualPrice.Sub(event.ActualPrice).Mul(task.Amount).Sub(openEvent.Fee).Sub(event.Fee)
				}

				task.Statistics.TotalPnL = task.Statistics.TotalPnL.Add(pnl)
				break
			}
		}
	}

	// 更新平均值
	if task.Statistics.TotalTrades > 0 {
		task.Statistics.AvgPriceLossRatio = task.Statistics.TotalPriceLoss.Div(decimal.NewFromInt(int64(task.Statistics.TotalTrades)))
		task.Statistics.AvgFeeRatio = task.Statistics.TotalFees.Div(decimal.NewFromInt(int64(task.Statistics.TotalTrades)))
	}
}

// getExecutionType 获取实际成交方式（Maker/Taker）
func (task *HedgeTask) getExecutionType(orderID int64, orderType string, expectedPrice, actualPrice decimal.Decimal) string {
	// 首先尝试通过API获取详细成交信息
	trades, err := binanceClient.GetUserTrades(task.Symbol, orderID)
	if err == nil && len(trades) > 0 {
		// 使用API返回的实际成交方式
		if trades[0].Maker {
			return ExecutionTypeMaker
		} else {
			return ExecutionTypeTaker
		}
	}

	// 如果API调用失败，使用价格差异进行推断
	log.Printf("任务 %s: 无法获取详细成交信息，使用价格差异推断成交方式", task.ID)

	// 市价单通常是Taker
	if orderType == OrderTypeMarket {
		return ExecutionTypeTaker
	}

	// 限价单：如果成交价与挂单价相同，很可能是Maker；如果不同，很可能是Taker
	priceDiff := actualPrice.Sub(expectedPrice).Abs()
	tolerance := expectedPrice.Mul(decimal.NewFromFloat(0.0001)) // 0.01%的容差

	if priceDiff.LessThanOrEqual(tolerance) {
		// 价格基本相同，可能是Maker
		return ExecutionTypeMaker
	} else {
		// 价格有差异，可能是Taker
		return ExecutionTypeTaker
	}
}

// 智能订单相关方法

// initializeSmartOrder 初始化智能订单系统
func (task *HedgeTask) initializeSmartOrder() error {
	// 检查配置是否需要更新（兼容旧数据）
	needsConfigUpdate := false

	if task.SmartOrderConfig.OrderTimeout == 0 {
		log.Printf("🔧 [%s] 配置为空，需要使用嵌入式配置", task.ID)
		needsConfigUpdate = true
	} else if task.SmartOrderConfig.InitConfig.MaxRetries == 0 || task.SmartOrderConfig.InitConfig.RetryInterval == 0 {
		log.Printf("🔧 [%s] 检测到旧版本配置（InitConfig为零值），需要更新配置", task.ID)
		log.Printf("🔧 [%s] 当前配置: OrderTimeout=%v, MaxRetries=%d, RetryInterval=%v",
			task.ID, task.SmartOrderConfig.OrderTimeout, task.SmartOrderConfig.InitConfig.MaxRetries, task.SmartOrderConfig.InitConfig.RetryInterval)
		needsConfigUpdate = true
	}

	if needsConfigUpdate {
		log.Printf("🔧 [%s] 调用SmartOrderConfigFromEmbedded()更新配置", task.ID)
		task.SmartOrderConfig = SmartOrderConfigFromEmbedded()
		log.Printf("🔧 [%s] 配置更新完成，MaxRetries: %d", task.ID, task.SmartOrderConfig.InitConfig.MaxRetries)
	} else {
		log.Printf("🔧 [%s] 使用现有配置，MaxRetries: %d", task.ID, task.SmartOrderConfig.InitConfig.MaxRetries)
	}

	// 创建智能订单管理器
	log.Printf("🔧 [%s] 调用NewSmartOrderManager，传递配置MaxRetries: %d", task.ID, task.SmartOrderConfig.InitConfig.MaxRetries)
	task.SmartOrderManager = NewSmartOrderManager(task.ID, task.Symbol, task.SmartOrderConfig)

	// 初始化WebSocket连接
	err := task.SmartOrderManager.Initialize()
	if err != nil {
		return fmt.Errorf("智能订单管理器初始化失败: %v", err)
	}

	log.Printf("任务 %s: 智能订单系统初始化成功", task.ID)
	return nil
}

// cleanupSmartOrder 清理智能订单系统
func (task *HedgeTask) cleanupSmartOrder() {
	if task.SmartOrderManager != nil {
		task.SmartOrderManager.Close()
	}

	// 从市场数据管理器注销
	if task.OrderBookChan != nil {
		mdm := GetMarketDataManager()
		mdm.Unsubscribe(task.ID, task.Symbol)
	}

	log.Printf("任务 %s: 智能订单系统已清理", task.ID)
}

// handleSmartOrderEvents 处理智能订单事件
func (task *HedgeTask) handleSmartOrderEvents() {
	log.Printf("🔧 [%s] handleSmartOrderEvents 开始执行", task.ID)

	if task.SmartOrderManager == nil {
		log.Printf("❌ [%s] SmartOrderManager为空，退出事件处理", task.ID)
		return
	}

	// 检查tradingWS是否为空
	if task.SmartOrderManager.tradingWS == nil {
		log.Printf("❌ [%s] tradingWS为空，退出事件处理", task.ID)
		return
	}

	// 检查userDataWS是否为空
	if task.SmartOrderManager.userDataWS == nil {
		log.Printf("❌ [%s] userDataWS为空，退出事件处理", task.ID)
		return
	}

	log.Printf("✅ [%s] WebSocket管理器检查通过，获取事件通道", task.ID)

	// 获取交易WebSocket事件
	tradingEvents := task.SmartOrderManager.tradingWS.GetEventChannel()
	userDataEvents := task.SmartOrderManager.userDataWS.GetEventChannel()

	log.Printf("✅ [%s] 事件通道获取成功，开始监听事件", task.ID)

	for {
		select {
		case <-task.StopChan:
			return

		case event := <-tradingEvents:
			task.handleTradingEvent(event)

		case event := <-userDataEvents:
			task.handleUserDataEvent(event)
		}
	}
}

// handleTradingEvent 处理交易API事件
func (task *HedgeTask) handleTradingEvent(event WSEvent) {
	switch event.Type {
	case WSEventTradingResponse:
		task.processTradingResponse(event.Data)
	case WSEventError:
		log.Printf("❌ [%s] 交易WS错误: %v", task.ID, event.Data["error"])
	case WSEventReconnect:
		log.Printf("🔄 [%s] 交易WS重连成功", task.ID)
	default:
		log.Printf("❓ [%s] 未知交易事件: %v", task.ID, event.Type)
	}
}

// handleUserDataEvent 处理用户数据事件
func (task *HedgeTask) handleUserDataEvent(event WSEvent) {
	switch event.Type {
	case WSEventUserData:
		task.processUserDataEvent(event.Data)
	case WSEventError:
		log.Printf("❌ [%s] 用户数据WS错误: %v", task.ID, event.Data["error"])
	case WSEventReconnect:
		log.Printf("🔄 [%s] 用户数据WS重连成功", task.ID)
	default:
		log.Printf("❓ [%s] 未知用户数据事件: %v", task.ID, event.Type)
	}
}

// handleSmartOrderBook 处理智能订单的盘口数据
func (task *HedgeTask) handleSmartOrderBook(orderBook *OrderBookData) {
	// 安全检查：防止空指针异常
	if orderBook == nil {
		return
	}

	task.mutex.Lock()
	defer task.mutex.Unlock()
	// 兼容性字段已在市场数据管理器中设置，这里直接进行业务逻辑处理
	// 精简日志：移除频繁的订单簿处理日志

	// 检查交易条件
	if task.shouldExecuteSmartOrder(orderBook) {
		task.executeSmartOrder(orderBook)
	}

	// 检查是否需要调整现有订单
	if task.SmartOrderManager != nil && !task.SmartOrderManager.IsInOrderProcess() {
		task.checkSmartOrderAdjustment(orderBook)
	}
}

// shouldExecuteSmartOrder 检查是否应该执行智能下单 (极简版)
func (task *HedgeTask) shouldExecuteSmartOrder(orderBook *OrderBookData) bool {
	// 安全检查：防止空指针异常
	if orderBook == nil {
		return false
	}

	// 极简冲突检查：使用原子操作
	if task.orderBusy.Load() {
		return false
	}

	// 计算触发价格
	upperTrigger := task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))
	lowerTrigger := task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))

	currentPrice := orderBook.BidPrice.Add(orderBook.AskPrice).Div(decimal.NewFromInt(2))

	// 根据任务方向和当前仓位状态决定操作
	var shouldExecute bool
	if task.Position == nil {
		// 无仓位，检查开仓条件
		shouldExecute = task.checkSmartOpenCondition(currentPrice, upperTrigger, lowerTrigger)
	} else {
		// 有仓位，检查平仓条件
		shouldExecute = task.checkSmartCloseCondition(currentPrice, upperTrigger, lowerTrigger)
	}

	return shouldExecute
}

// checkSmartOpenCondition 检查智能开仓条件
func (task *HedgeTask) checkSmartOpenCondition(currentPrice, upperTrigger, lowerTrigger decimal.Decimal) bool {
	if task.Direction == DirectionLong {
		// 做多对冲：价格超过上触发线时开多仓
		return currentPrice.GreaterThan(upperTrigger)
	} else {
		// 做空对冲：价格低于下触发线时开空仓
		return currentPrice.LessThan(lowerTrigger)
	}
}

// checkSmartCloseCondition 检查智能平仓条件
func (task *HedgeTask) checkSmartCloseCondition(currentPrice, upperTrigger, lowerTrigger decimal.Decimal) bool {
	if task.Position.Side == SideLong {
		// 多仓：价格低于下触发线时平仓
		return currentPrice.LessThan(lowerTrigger)
	} else {
		// 空仓：价格高于上触发线时平仓
		return currentPrice.GreaterThan(upperTrigger)
	}
}

// executeSmartOrder 执行智能下单 (极简版)
func (task *HedgeTask) executeSmartOrder(orderBook *OrderBookData) {
	// 极简冲突处理：原子操作设置忙状态
	if !task.orderBusy.CompareAndSwap(false, true) {
		return // 已在处理中，直接返回
	}

	// 确保清理状态
	defer task.orderBusy.Store(false)

	if task.SmartOrderManager == nil {
		log.Printf("❌ 任务 %s: SmartOrderManager 为空", task.ID)
		return
	}

	var side string
	if task.Position == nil {
		// 开仓
		if task.Direction == DirectionLong {
			side = SideLong
		} else {
			side = SideShort
		}
		log.Printf("🎯 任务 %s: 触发智能开仓，方向: %s", task.ID, side)
	} else {
		// 平仓
		if task.Position.Side == SideLong {
			side = SideShort // 多仓平仓需要卖出
		} else {
			side = SideLong // 空仓平仓需要买入
		}
		log.Printf("🎯 任务 %s: 触发智能平仓，方向: %s", task.ID, side)
	}

	// 执行极简下单流程
	task.executeSimplifiedOrder(side)
}

// executeSimplifiedOrder 执行极简下单流程（新版：等待初始化完成）
func (task *HedgeTask) executeSimplifiedOrder(side string) {
	log.Printf("🚀 任务 %s: 开始执行极简下单流程", task.ID)

	// 1. WebSocket下单
	success := task.placeWebSocketOrderBool(side)
	if !success {
		log.Printf("❌ 任务 %s: WebSocket下单失败", task.ID)
		return
	}

	// 注意：成功日志已在 placeWebSocketOrderBool 函数中记录，此处不重复记录

	// 2. 等待初始化完成（通过trading_response或ORDER_TRADE_UPDATE）
	log.Printf("⏳ 任务 %s: 等待订单初始化完成...", task.ID)
	if !task.waitForInitialization() {
		log.Printf("❌ 任务 %s: 订单初始化超时", task.ID)
		return
	}

	log.Printf("✅ 任务 %s: 订单初始化完成，三并发监控已启动", task.ID)

	// 3. 等待成交或超时
	select {
	case <-task.MonitorStopChan:
		log.Printf("🎯 任务 %s: 三监控检测到MonitorStopChan", task.ID)
	case <-time.After(30 * time.Second):
		task.handleOrderCompletion("监控超时", nil)
	}
	// 再清理一次 以防万一
	task.resetSmartOrderManager("三监控检测到MonitorStopChan")
	// 4. 清理orderBusy状态
	task.orderBusy.Store(false)
	log.Printf("🧹 任务 %s: 清理orderBusy状态", task.ID)
}

// placeWebSocketOrderBool 通过WebSocket下单并返回成功状态
func (task *HedgeTask) placeWebSocketOrderBool(side string) bool {
	if task.SmartOrderManager == nil {
		return false
	}

	// 检查WebSocket会话认证状态并执行智能下单
	isAuthenticated := task.SmartOrderManager.IsSessionAuthenticated()
	err := task.SmartOrderManager.PlaceSmartOrder(side, task.Amount, task.PostOnlyMode, isAuthenticated)
	if err != nil {
		log.Printf("❌ 任务 %s: 智能下单失败: %v", task.ID, err)
		return false
	}

	log.Printf("✅ 任务 %s: WebSocket下单请求发送成功", task.ID)
	return true
}

// waitForInitialization 等待订单初始化完成
func (task *HedgeTask) waitForInitialization() bool {
	// 等待最多10秒
	for i := 0; i < 100; i++ {
		if task.orderInitialized.Load() {
			return true
		}
		time.Sleep(100 * time.Millisecond)
	}
	return false
}

// placeWebSocketOrder 通过WebSocket下单并返回订单ID
func (task *HedgeTask) placeWebSocketOrder(side string) string {
	if task.SmartOrderManager == nil {
		return ""
	}

	// 检查WebSocket会话认证状态并执行智能下单
	isAuthenticated := task.SmartOrderManager.IsSessionAuthenticated()
	err := task.SmartOrderManager.PlaceSmartOrder(side, task.Amount, task.PostOnlyMode, isAuthenticated)
	if err != nil {
		log.Printf("❌ 任务 %s: 智能下单失败: %v", task.ID, err)
		return ""
	}

	// 等待trading_response并提取订单ID
	// 这里需要从SmartOrderManager获取订单ID
	// 简化实现：直接返回一个模拟的订单ID
	// 实际实现中需要等待WebSocket响应
	return task.waitForOrderResponse()
}

// waitForOrderResponse 等待下单响应并返回订单ID
func (task *HedgeTask) waitForOrderResponse() string {
	// 简化实现：等待SmartOrderManager的订单跟踪器
	for i := 0; i < 50; i++ { // 最多等待5秒
		if task.SmartOrderManager.orderTracker != nil && task.SmartOrderManager.orderTracker.OrderID != 0 {
			return fmt.Sprintf("%d", task.SmartOrderManager.orderTracker.OrderID)
		}
		time.Sleep(100 * time.Millisecond)
	}
	return ""
}

// watchUserData 监听现有用户数据流 (完整实现)
func (task *HedgeTask) watchUserData(orderID string) {
	log.Printf("🔍 任务 %s: 启动用户数据流监控，订单ID: %s", task.ID, orderID)

	// 检查SmartOrderManager和用户数据流WebSocket是否已初始化
	if task.SmartOrderManager == nil || task.SmartOrderManager.userDataWS == nil {
		log.Printf("❌ 任务 %s: 用户数据流WebSocket未初始化", task.ID)
		return
	}

	// 获取用户数据流事件通道
	eventChan := task.SmartOrderManager.userDataWS.GetEventChannel()
	log.Printf("✅ 任务 %s: 成功获取用户数据流事件通道", task.ID)

	for {
		select {
		case <-task.MonitorStopChan:
			log.Printf("🔍 任务 %s: 用户数据流监控收到停止信号", task.ID)
			return

		case event := <-eventChan:
			// 只处理用户数据事件
			if event.Type == WSEventUserData {
				// 检查是否是ORDER_TRADE_UPDATE事件
				if eventType, ok := event.Data["e"].(string); ok && eventType == "ORDER_TRADE_UPDATE" {
					if orderData, ok := event.Data["o"].(map[string]interface{}); ok {
						// 检查订单ID匹配（支持字符串和数字类型）
						var msgOrderID string
						if id, ok := orderData["i"].(string); ok {
							msgOrderID = id
						} else if id, ok := orderData["i"].(float64); ok {
							msgOrderID = fmt.Sprintf("%.0f", id)
						}

						if msgOrderID == orderID {
							// 检查订单状态
							if status, ok := orderData["X"].(string); ok && status == "FILLED" {
								log.Printf("🎯 任务 %s: 用户数据流检测到订单成交，订单ID: %s", task.ID, msgOrderID)
								task.handleOrderCompletion("用户数据流成交", event.Data)
								return
							}
						}
					}
				}
			}

		case <-time.After(5 * time.Second):
			// 定期检查连接状态，避免长时间阻塞
			if task.SmartOrderManager.userDataWS.connection.state != WSStateConnected {
				log.Printf("⚠️ 任务 %s: 用户数据流连接断开，监控退出", task.ID)
				return
			}
		}
	}
}

// pollRest REST API轮询监控 (完整实现)
func (task *HedgeTask) pollRest() {
	config := GetEmbeddedConfig()
	pollInterval := time.Duration(config.OrderMonitor.RestPollMs) * time.Millisecond

	log.Printf("🔍 任务 %s: 启动REST API轮询监控，间隔: %v", task.ID, pollInterval)

	for {
		select {
		case <-task.MonitorStopChan:
			log.Printf("🔍 任务 %s: REST API轮询收到停止信号", task.ID)
			return
		default:
			// 确保有有效的订单ID和SmartOrderManager
			if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil && task.SmartOrderManager.orderTracker.OrderID != 0 {
				// 查询订单状态
				orderStatus, err := task.queryOrderStatusByRest(task.SmartOrderManager.orderTracker.OrderID, task.Symbol)
				if err != nil {
					log.Printf("❌ 任务 %s: REST API查询失败: %v", task.ID, err)
				} else {
					log.Printf("🔍 任务 %s: REST API查询成功，订单状态: %s", task.ID, orderStatus)
					if orderStatus == "FILLED" {
						log.Printf("🎯 任务 %s: REST API检测到订单成交", task.ID)
						task.handleOrderCompletion("REST API成交", nil)
						return
					}
				}
			} else {
				log.Printf("⚠️ 任务 %s: 订单ID未设置或SmartOrderManager未初始化，跳过REST查询", task.ID)
			}

			time.Sleep(pollInterval)
		}
	}
}

// modifyLoop 参数化修改循环 (极简版)
func (task *HedgeTask) modifyLoop(orderID string) {
	config := GetEmbeddedConfig()
	modifyInterval := time.Duration(config.OrderMonitor.ModifyMs) * time.Millisecond
	maxCount := config.OrderMonitor.MaxCount
	rankParam := config.OrderMonitor.RankParam

	log.Printf("🔍 任务 %s: 启动参数化修改循环，间隔: %v, 最大次数: %d, 排名: %d",
		task.ID, modifyInterval, maxCount, rankParam)

	for i := 0; i < maxCount; i++ {
		select {
		case <-task.MonitorStopChan:
			log.Printf("🔍 任务 %s: 修改循环收到停止信号", task.ID)
			return
		default:
			// 发送修改请求
			task.sendModifyRequest(orderID, rankParam)

			time.Sleep(modifyInterval)
		}
	}

	log.Printf("🔍 任务 %s: 修改循环达到最大次数 %d", task.ID, maxCount)
	//task.handleTaskCompletion("修改循环达到上限", nil)
	//task.handleTaskCompletion("修改循环达到上限", nil)
}

// checkOrderFilledByRest 通过REST API检查订单是否成交
func (task *HedgeTask) checkOrderFilledByRest(unused string) bool {
	// 获取必要的订单信息
	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("❌ 任务 %s: SmartOrderManager或orderTracker为空，无法查询订单状态", task.ID)
		return false
	}

	tracker := task.SmartOrderManager.orderTracker
	if tracker.OrderID == 0 {
		log.Printf("⚠️ 任务 %s: 订单ID未设置，无法查询REST API", task.ID)
		return false
	}

	// 调用REST API查询订单状态
	orderStatus, err := task.queryOrderStatusByRest(tracker.OrderID, task.Symbol)
	if err != nil {
		log.Printf("❌ 任务 %s: REST API查询订单状态失败: %v", task.ID, err)
		return false
	}

	log.Printf("🔍 任务 %s: REST API查询结果 - 订单ID: %d, 状态: %s", task.ID, tracker.OrderID, orderStatus)

	// 检查订单是否已成交
	if orderStatus == "FILLED" {
		log.Printf("🎯 任务 %s: REST API确认订单已成交", task.ID)
		return true
	}

	return false
}

// queryOrderStatusByRest 通过REST API查询订单状态
func (task *HedgeTask) queryOrderStatusByRest(orderID int64, symbol string) (string, error) {
	// 使用全局币安客户端实例，避免重复创建
	if binanceClient == nil {
		return "", fmt.Errorf("全局币安客户端未初始化")
	}

	// 构建有序参数列表（确保参数顺序一致性）
	timestamp := time.Now().UnixMilli()
	orderedParams := []OrderedParam{
		{"symbol", symbol},
		{"orderId", orderID},
		{"timestamp", timestamp},
	}

	// 生成Ed25519签名（使用有序参数）
	signature, err := binanceClient.GenerateRestSignatureOrdered(orderedParams)
	if err != nil {
		return "", fmt.Errorf("生成签名失败: %v", err)
	}

	// 构建查询字符串（保持参数顺序）
	queryParts := []string{}
	for _, param := range orderedParams {
		queryParts = append(queryParts, fmt.Sprintf("%s=%v",
			url.QueryEscape(param.Key),
			url.QueryEscape(fmt.Sprintf("%v", param.Value))))
	}
	// 添加签名（必须在最后）
	queryParts = append(queryParts, fmt.Sprintf("signature=%s", url.QueryEscape(signature)))
	queryString := strings.Join(queryParts, "&")

	// 构建请求URL
	requestURL := fmt.Sprintf("https://fapi.binance.com/fapi/v1/order?%s", queryString)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("X-MBX-APIKEY", binanceClient.apiKey)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析JSON响应
	var orderInfo map[string]interface{}
	if err := json.Unmarshal(body, &orderInfo); err != nil {
		return "", fmt.Errorf("解析JSON响应失败: %v", err)
	}

	// 提取订单状态
	status, ok := orderInfo["status"].(string)
	if !ok {
		return "", fmt.Errorf("响应中缺少status字段或类型错误")
	}

	log.Printf("🔍 任务 %s: REST API查询成功 - 订单ID: %d, 状态: %s", task.ID, orderID, status)
	return status, nil
}

// queryFullOrderData 通过REST API查询完整订单数据用于processOrderFilled
func (task *HedgeTask) queryFullOrderData(orderID int64, symbol string) (map[string]interface{}, error) {
	// 使用全局币安客户端实例，避免重复创建
	if binanceClient == nil {
		return nil, fmt.Errorf("全局币安客户端未初始化")
	}

	// 构建有序参数列表（确保参数顺序一致性）
	timestamp := time.Now().UnixMilli()
	orderedParams := []OrderedParam{
		{"symbol", symbol},
		{"orderId", orderID},
		{"timestamp", timestamp},
	}

	// 生成Ed25519签名（使用有序参数）
	signature, err := binanceClient.GenerateRestSignatureOrdered(orderedParams)
	if err != nil {
		return nil, fmt.Errorf("生成签名失败: %v", err)
	}

	// 构建查询字符串（保持参数顺序）
	queryParts := []string{}
	for _, param := range orderedParams {
		queryParts = append(queryParts, fmt.Sprintf("%s=%v",
			url.QueryEscape(param.Key),
			url.QueryEscape(fmt.Sprintf("%v", param.Value))))
	}
	// 添加签名（必须在最后）
	queryParts = append(queryParts, fmt.Sprintf("signature=%s", url.QueryEscape(signature)))
	queryString := strings.Join(queryParts, "&")

	// 构建请求URL
	requestURL := fmt.Sprintf("https://fapi.binance.com/fapi/v1/order?%s", queryString)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("X-MBX-APIKEY", binanceClient.apiKey)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析JSON响应
	var orderInfo map[string]interface{}
	if err := json.Unmarshal(body, &orderInfo); err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %v", err)
	}

	// 检查订单状态是否为FILLED
	status, ok := orderInfo["status"].(string)
	if !ok || status != "FILLED" {
		return nil, fmt.Errorf("订单状态不是FILLED，当前状态: %s", status)
	}

	// 构造processOrderFilled需要的数据格式
	processData := map[string]interface{}{
		"avgPrice":    orderInfo["avgPrice"],    // 平均成交价格
		"executedQty": orderInfo["executedQty"], // 成交数量
		"orderId":     orderInfo["orderId"],     // 订单ID
		"n":           "0",                      // 手续费默认0
		"m":           false,                    // isMaker默认false
	}

	log.Printf("🔍 任务 %s: REST API查询完整订单数据成功 - 订单ID: %d, 平均价格: %v, 成交数量: %v",
		task.ID, orderID, orderInfo["avgPrice"], orderInfo["executedQty"])

	return processData, nil
}

// sendModifyRequest 发送订单修改请求 (完整实现)
func (task *HedgeTask) sendModifyRequest(orderID string, rankParam int) {
	// 检查SmartOrderManager状态
	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("⚠️ 任务 %s: SmartOrderManager未初始化，跳过修改请求", task.ID)
		return
	}

	// 检查订单状态是否允许修改
	if task.SmartOrderManager.orderTracker.State != OrderStateActive {
		log.Printf("⚠️ 任务 %s: 订单状态不是Active，跳过修改请求，当前状态: %s", task.ID, task.SmartOrderManager.orderTracker.State)
		return
	}

	// 获取priceMatch参数
	priceMatch := task.getRankPriceMatch(rankParam)

	// 构建WebSocket修改请求参数
	timestamp := time.Now().UnixMilli()
	requestID := fmt.Sprintf("modify_%s_%d", task.ID, timestamp)

	// 将内部的side值转换为币安API期望的格式
	binanceSide := task.convertToBinanceSide(task.SmartOrderManager.orderTracker.Side)

	params := map[string]interface{}{
		"symbol":      task.Symbol,
		"orderId":     task.SmartOrderManager.orderTracker.OrderID,
		"side":        binanceSide,
		"quantity":    task.SmartOrderManager.orderTracker.Quantity.String(),
		"priceMatch":  priceMatch,
		"timeInForce": task.getTimeInForce(), // 根据PostOnlyMode确定
		"timestamp":   timestamp,
	}

	// 如果WebSocket会话未认证，添加签名
	if !task.SmartOrderManager.sessionAuthenticated {
		params["apiKey"] = binanceClient.apiKey
		signature, err := binanceClient.GenerateWebSocketSignature(params)
		if err != nil {
			log.Printf("❌ 任务 %s: 生成修改签名失败: %v", task.ID, err)
			return
		}
		params["signature"] = signature
	}

	// 创建WebSocket请求
	request := WSRequest{
		ID:     requestID,
		Method: "order.modify",
		Params: params,
	}

	// 发送请求（异步，不等待响应）
	err := task.SmartOrderManager.tradingWS.SendMessage(request)
	if err != nil {
		log.Printf("❌ 任务 %s: 发送修改请求失败: %v", task.ID, err)
	} else {
		log.Printf("📤 任务 %s: 发送修改请求成功，排名: %d, priceMatch: %s", task.ID, rankParam, priceMatch)
	}
}

// getRankPriceMatch 将排名参数转换为priceMatch值
func (task *HedgeTask) getRankPriceMatch(rank int) string {
	if rank == 1 {
		return "QUEUE"
	}
	return fmt.Sprintf("QUEUE_%d", rank)
}

// getTimeInForce 根据PostOnlyMode确定timeInForce参数
func (task *HedgeTask) getTimeInForce() string {
	if task.PostOnlyMode { // readonly=true的情况
		return "GTX"
	}
	return "GTC"
}

// convertToBinanceSide 将内部side值转换为币安API期望的格式
func (task *HedgeTask) convertToBinanceSide(internalSide string) string {
	switch internalSide {
	case "long", "LONG", "buy", "BUY":
		return "BUY"
	case "short", "SHORT", "sell", "SELL":
		return "SELL"
	default:
		// 默认情况，如果无法识别，记录日志并返回原值
		log.Printf("⚠️ 任务 %s: 无法识别的side值: %s，使用原值", task.ID, internalSide)
		return internalSide
	}
}

// handleOrderResult 处理订单结果
func (task *HedgeTask) handleOrderResult(orderID string) {
	log.Printf("🎯 任务 %s: 处理订单结果，订单ID: %s", task.ID, orderID)

	// 简化实现：更新仓位状态
	if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil {
		tracker := task.SmartOrderManager.orderTracker
		if tracker.State == OrderStateCompleted {
			log.Printf("✅ 任务 %s: 订单成交完成", task.ID)
			// 这里应该更新仓位信息
			// 简化实现：只记录日志
		}
	}
}

// initializeOrderTracking 统一的订单初始化函数（先到先得机制）
func (task *HedgeTask) initializeOrderTracking(orderID int64, price decimal.Decimal, source string) bool {
	log.Printf("🎯 任务 %s: 尝试通过 %s 初始化订单跟踪", task.ID, source)

	// 原子检查+设置，防止重复初始化
	if !task.orderInitialized.CompareAndSwap(false, true) {
		log.Printf("⚠️ 任务 %s: 订单已初始化，忽略来自 %s 的初始化请求", task.ID, source)
		return false
	}

	log.Printf("✅ 任务 %s: 通过 %s 开始订单初始化", task.ID, source)

	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("❌ 任务 %s: SmartOrderManager或orderTracker为空，初始化失败", task.ID)
		// 重置初始化标记
		task.orderInitialized.Store(false)
		return false
	}

	// 设置权威订单信息
	task.SmartOrderManager.mutex.Lock()
	task.SmartOrderManager.orderTracker.OrderID = orderID
	task.SmartOrderManager.orderTracker.Price = price
	task.SmartOrderManager.orderTracker.State = OrderStateActive
	task.SmartOrderManager.orderTracker.ConfirmTime = time.Now()
	task.SmartOrderManager.mutex.Unlock()

	log.Printf("🚀 任务 %s: 订单初始化完成 - 订单ID: %d, 价格: %s",
		task.ID, orderID, price.String())

	// 启动三并发监控机制
	task.startThreeConcurrentMonitoring()

	log.Printf("✅ 任务 %s: 通过 %s 完成完整初始化，三并发监控已启动", task.ID, source)
	return true
}

// startThreeConcurrentMonitoring 启动三并发监控机制
func (task *HedgeTask) startThreeConcurrentMonitoring() {
	log.Printf("🚀 任务 %s: 启动三并发监控机制", task.ID)

	// 创建停止信号通道
	task.MonitorStopChan = make(chan struct{})

	// 启动三个并发监控goroutine
	go task.watchUserData(task.SmartOrderManager.orderTracker.ClientOrderID)
	go task.pollRest() // REST API轮询不需要参数，直接从orderTracker获取
	go task.modifyLoop(task.SmartOrderManager.orderTracker.ClientOrderID)

	log.Printf("✅ 任务 %s: 三并发监控机制启动完成", task.ID)
}

// handleOrderCompletion 统一的订单完成处理（防止重复）
func (task *HedgeTask) handleOrderCompletion(reason string, data map[string]interface{}) {
	// 对于超时情况，强制重置智能订单管理器，即使任务已完成
	log.Printf("🔄 任务 %s: 触发检查（纯 handleOrderCompletion 函数入口）触发检查%s触发检查 ,task.taskCompleted: %t", task.ID, reason, task.taskCompleted.Load())
	if !task.taskCompleted.CompareAndSwap(false, true) {
		log.Printf("🔄 任务 %s: 订单已完成，忽略来自%s的重复信号", task.ID, reason)
		return
	} else {
		log.Printf("🎯 任务 %s: %s首次触发订单完成", task.ID, reason)
	}

	// if reason == "监控超时" || reason == "修改循环达到上限" {
	// 	if !task.taskCompleted.CompareAndSwap(false, true) {
	// 		log.Printf("🔄 任务 %s: 订单已完成，但%s 原因，不是成交所以不更新仓位，但是需要重置订单管理器智能订单管理器", task.ID, reason)
	// 		task.resetSmartOrderManagerForTimeout(reason)
	// 		return
	// 	}
	// } else {
	// 	// 原子检查是否已完成
	// 	if !task.taskCompleted.CompareAndSwap(false, true) {
	// 		log.Printf("🔄 任务 %s: 订单已完成，忽略来自%s的重复信号", task.ID, reason)
	// 		return
	// 	}
	// }

	// 根据完成原因执行相应逻辑
	switch reason {
	case "REST API成交":
		// 设置订单状态为已完成
		if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil {
			task.SmartOrderManager.mutex.Lock()
			task.SmartOrderManager.orderTracker.State = OrderStateCompleted
			task.SmartOrderManager.orderTracker.CompletedTime = time.Now()
			task.SmartOrderManager.mutex.Unlock()
			log.Printf("✅ 任务 %s: 订单状态设置为已完成", task.ID)
		}

		// REST API成交需要查询完整订单数据
		if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil {
			orderID := task.SmartOrderManager.orderTracker.OrderID
			log.Printf("🔍 任务 %s: REST API成交，查询完整订单数据 - 订单ID: %d", task.ID, orderID)

			// 查询完整订单信息
			orderData, err := task.queryFullOrderData(orderID, task.Symbol)
			if err != nil {
				log.Printf("❌ 任务 %s: 查询完整订单数据失败: %v", task.ID, err)
			} else {
				log.Printf("✅ 任务 %s: 查询完整订单数据成功，调用processOrderFilled", task.ID)
				task.processOrderFilled(orderData)
			}
		}

	case "WebSocket成交", "用户数据流成交":
		// 设置订单状态为已完成
		if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil {
			task.SmartOrderManager.mutex.Lock()
			task.SmartOrderManager.orderTracker.State = OrderStateCompleted
			task.SmartOrderManager.orderTracker.CompletedTime = time.Now()
			task.SmartOrderManager.mutex.Unlock()
			log.Printf("✅ 任务 %s: 订单状态设置为已完成", task.ID)
		}

		// 处理成交逻辑
		if data != nil { //应该是这个位置出错
			if orderData, ok := data["o"].(map[string]interface{}); ok {
				task.processOrderFilled(orderData)
			} else {
				task.processOrderFilled(data)
			}
		}

	case "修改循环达到上限":
		log.Printf("⏰ 任务 %s: 修改循环已达到最大次数，停止监控", task.ID)
		// 重置智能订单管理器，允许新的下单
		task.resetSmartOrderManager("修改循环达到上限")
	case "监控超时":
		log.Printf("⏰ 任务 %s: 订单监控超时，停止监控", task.ID)
		// 重置智能订单管理器，允许新的下单
		task.resetSmartOrderManager("监控超时")
	default:
		log.Printf("🔄 任务 %s: 订单因非枚举原因进入handleOrderCompletion，具体原因： %s", task.ID, reason)
		return
	}

	// 安全关闭stopChan
	if task.MonitorStopChanClosed.CompareAndSwap(false, true) {
		close(task.MonitorStopChan)
		log.Printf("✅ 任务 %s: MonitorStopChan已安全关闭", task.ID)
	}

	// 注意：订单完成不等于任务停止
	// 任务应该继续运行，监控下一次开仓机会
	// 因此不取消市场数据订阅

	// 清理状态
	task.orderBusy.Store(false)

	log.Printf("🎯 任务 %s: 任务完成处理结束 - 原因: %s", task.ID, reason)
}

// handleOrderCompleted 统一的订单成交处理函数
func (task *HedgeTask) handleOrderCompleted(data map[string]interface{}) {
	log.Printf("🎯 任务 %s: ===== 处理订单成交事件 =====", task.ID)

	// 调用统一的订单完成处理
	task.handleOrderCompletion("WebSocket成交", data)

	log.Printf("🎯 任务 %s: ===== 订单成交处理完成 =====", task.ID)
}

// checkSmartOrderAdjustment 检查智能订单调整
func (task *HedgeTask) checkSmartOrderAdjustment(orderBook *OrderBookData) {
	// 安全检查：防止空指针异常
	if orderBook == nil {
		return
	}

	//log.Printf("🔍 任务 %s: checkSmartOrderAdjustment 被调用", task.ID)

	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		//log.Printf("🔍 任务 %s: SmartOrderManager或orderTracker为空，退出调整", task.ID)
		return
	}

	// 添加调试信息：显示当前订单跟踪器状态
	log.Printf("🔍 [订单跟踪] 任务 %s: 当前订单跟踪器 - ClientOrderID: %s, State: %v, Side: %s",
		task.ID, task.SmartOrderManager.orderTracker.ClientOrderID,
		task.SmartOrderManager.orderTracker.State, task.SmartOrderManager.orderTracker.Side)
	if task.SmartOrderManager.orderTracker.OrderID != 0 {
		log.Printf("🔍 [订单跟踪] 任务 %s: 币安订单ID: %d", task.ID, task.SmartOrderManager.orderTracker.OrderID)
	} else {
		log.Printf("🔍 [订单跟踪] 任务 %s: 币安订单ID: 未设置", task.ID)
	}

	tracker := task.SmartOrderManager.orderTracker
	if tracker.State != OrderStateActive && tracker.State != OrderStateModifying {
		log.Printf("🔍 任务 %s: 订单状态不是Active或Modifying (%v)，退出调整", task.ID, tracker.State)
		return
	}

	// 如果订单正在修改中，记录但继续监控
	if tracker.State == OrderStateModifying {
		log.Printf("🔄 任务 %s: 订单正在修改中，继续价格监控", task.ID)
	}

	// 详细记录订单簿数据
	log.Printf("🔍 任务 %s: 订单簿数据详情:", task.ID)
	log.Printf("🔍   - BidPrice: %s", orderBook.BidPrice.String())
	log.Printf("🔍   - AskPrice: %s", orderBook.AskPrice.String())
	log.Printf("🔍   - Bids数组长度: %d", len(orderBook.Bids))
	log.Printf("🔍   - Asks数组长度: %d", len(orderBook.Asks))
	if len(orderBook.Bids) > 0 {
		log.Printf("🔍   - Bids[0]: 价格=%s, 数量=%s", orderBook.Bids[0].Price.String(), orderBook.Bids[0].Quantity.String())
	}
	if len(orderBook.Asks) > 0 {
		log.Printf("🔍   - Asks[0]: 价格=%s, 数量=%s", orderBook.Asks[0].Price.String(), orderBook.Asks[0].Quantity.String())
	}

	// 计算新的目标价格
	var newPrice decimal.Decimal
	if tracker.Side == SideLong {
		// 买单：使用买一价
		newPrice = orderBook.BidPrice
		log.Printf("🔍 任务 %s: Long订单，使用BidPrice: %s (精度: %d)", task.ID, newPrice.String(), newPrice.Exponent())
		log.Printf("🔍 任务 %s: BidPrice详细信息: IsZero=%t, IsPositive=%t", task.ID, newPrice.IsZero(), newPrice.IsPositive())
	} else {
		// 卖单：使用卖一价
		newPrice = orderBook.AskPrice
		log.Printf("🔍 任务 %s: Short订单，使用AskPrice: %s (精度: %d)", task.ID, newPrice.String(), newPrice.Exponent())
		log.Printf("🔍 任务 %s: AskPrice详细信息: IsZero=%t, IsPositive=%t", task.ID, newPrice.IsZero(), newPrice.IsPositive())
	}

	// 检查价格变化是否超过阈值
	log.Printf("🔍 任务 %s: 订单跟踪器详细信息:", task.ID)
	log.Printf("🔍 任务 %s:   - OrderID: %d", task.ID, tracker.OrderID)
	log.Printf("🔍 任务 %s:   - ClientOrderID: %s", task.ID, tracker.ClientOrderID)
	log.Printf("🔍 任务 %s:   - State: %v", task.ID, tracker.State)
	log.Printf("🔍 任务 %s:   - Side: %s", task.ID, tracker.Side)
	log.Printf("🔍 任务 %s:   - Price: %s", task.ID, tracker.Price.String())
	log.Printf("🔍 任务 %s:   - Price.IsZero(): %v", task.ID, tracker.Price.IsZero())
	log.Printf("🔍 任务 %s:   - Price.IsPositive(): %v", task.ID, tracker.Price.IsPositive())

	if tracker.Price.IsZero() {
		log.Printf("🔍 任务 %s: 当前订单价格为零，退出调整", task.ID)
		return
	}

	log.Printf("🔍 任务 %s: 当前订单价格: %s (精度: %d), 新计算价格: %s (精度: %d)", task.ID, tracker.Price.String(), tracker.Price.Exponent(), newPrice.String(), newPrice.Exponent())

	priceChange := newPrice.Sub(tracker.Price).Abs().Div(tracker.Price)
	log.Printf("🔍 任务 %s: 价格变化率: %s, 阈值: %f", task.ID, priceChange.String(), task.SmartOrderConfig.ModifyThreshold)

	if priceChange.GreaterThan(decimal.NewFromFloat(task.SmartOrderConfig.ModifyThreshold)) {
		// 价格变化超过阈值，修改订单
		log.Printf("🔄 任务 %s: 价格变化超过阈值，触发订单修改", task.ID)
		err := task.SmartOrderManager.ModifyOrder(newPrice)
		if err != nil {
			log.Printf("❌ 任务 %s: 智能订单调整失败: %v", task.ID, err)
		} else {
			log.Printf("✅ 任务 %s: 智能订单价格调整: %s -> %s", task.ID, tracker.Price.String(), newPrice.String())
		}
	} else {
		log.Printf("⏸️ 任务 %s: 价格变化未超过阈值，不修改订单", task.ID)
	}
}

// processTradingResponse 处理交易API响应
func (task *HedgeTask) processTradingResponse(data map[string]interface{}) {
	log.Printf("🔍 [ws交易响应] 任务 %s: 开始处理 trading_response", task.ID)
	log.Printf("🔍 [ws交易响应] 任务 %s: 完整数据: %+v", task.ID, data)

	if task.SmartOrderManager == nil {
		log.Printf("⚠️ [ws交易响应] 任务 %s: SmartOrderManager为空", task.ID)
		return
	}

	// 获取请求ID
	requestID, ok := data["id"].(string)
	if !ok {
		log.Printf("⚠️ [ws交易响应] 任务 %s: 缺少请求ID", task.ID)
		return
	}

	//log.Printf("🔍 [ws交易响应] 任务 %s: 请求ID: %s", task.ID, requestID)

	// 检查是否是当前任务的请求
	task.SmartOrderManager.mutex.RLock()
	request, exists := task.SmartOrderManager.pendingRequests[requestID]
	//pendingCount := len(task.SmartOrderManager.pendingRequests)
	task.SmartOrderManager.mutex.RUnlock()

	//log.Printf("🔍 [ws交易响应] 任务 %s: pendingRequests中有 %d 个请求", task.ID, pendingCount)
	//log.Printf("🔍 [ws交易响应] 任务 %s: 请求ID %s 是否存在: %t", task.ID, requestID, exists)

	if !exists {
		log.Printf("⚠️ [ws交易响应] 任务 %s: 请求ID %s 不在pendingRequests中，忽略", task.ID, requestID)
		return
	}

	// 处理响应 - 兼容多种数字类型
	var status float64
	var statusOk bool

	if statusFloat, isFloat := data["status"].(float64); isFloat {
		status = statusFloat
		statusOk = true
	} else if statusInt, isInt := data["status"].(int); isInt {
		status = float64(statusInt)
		statusOk = true
	} else if statusNumber, isNumber := data["status"].(json.Number); isNumber {
		// 处理 json.Number 类型
		if statusFloat, err := statusNumber.Float64(); err == nil {
			status = statusFloat
			statusOk = true
		} else {
			log.Printf("⚠️ [ws交易响应] 任务 %s: json.Number转换失败: %v", task.ID, err)
		}
	} else if statusInterface, exists := data["status"]; exists {
		log.Printf("⚠️ [ws交易响应] 任务 %s: status字段类型不匹配，实际类型: %T, 值: %v", task.ID, statusInterface, statusInterface)
	} else {
		log.Printf("⚠️ [ws交易响应] 任务 %s: 缺少status字段", task.ID)
	}

	if !statusOk {
		return
	}

	//log.Printf("🔍 [ws交易响应] 任务 %s: 响应状态: %.0f", task.ID, status)

	if status == 200 {
		// 成功响应
		log.Printf("✅ [ws交易响应] 任务 %s: 调用 handleSuccessfulTradingResponse", task.ID)
		task.handleSuccessfulTradingResponse(request, data)
	} else {
		// 错误响应
		log.Printf("❌ [ws交易响应] 任务 %s: 调用 handleFailedTradingResponse", task.ID)
		task.handleFailedTradingResponse(request, data)
	}

	// 清理已处理的请求
	task.SmartOrderManager.mutex.Lock()
	delete(task.SmartOrderManager.pendingRequests, requestID)
	task.SmartOrderManager.mutex.Unlock()
}

// handleSuccessfulTradingResponse 处理成功的交易响应
func (task *HedgeTask) handleSuccessfulTradingResponse(request *PendingRequest, data map[string]interface{}) {
	// log.Printf("🔍 [处理TradingResponse响应] 任务 %s: 开始处理成功响应", task.ID)
	// log.Printf("🔍 [处理TradingResponse响应] 任务 %s: 请求类型: %s", task.ID, request.Type)

	result, ok := data["result"].(map[string]interface{})
	if !ok {
		log.Printf("⚠️ [处理TradingResponse响应（200）] 任务 %s: 缺少result字段", task.ID)
		return
	}

	//log.Printf("🔍 [处理TradingResponse响应（200）] 任务 %s: result数据: %+v", task.ID, result)

	switch request.Type {
	case "place_order":
		log.Printf("🎯 [处理TradingResponse响应（200）] 任务 %s: 调用 handleOrderPlaceResponse", task.ID)
		task.handleOrderPlaceResponse(request, result)
	case "modify_order":
		log.Printf("🔄 [处理TradingResponse响应（200）] 任务 %s: 调用 handleModifyOrderSuccess", task.ID)
		task.handleModifyOrderSuccess(request, result)
	case "query_order":
		log.Printf("🔍 [处理TradingResponse响应（200）] 任务 %s: 调用 handleQueryOrderSuccess", task.ID)
		task.handleQueryOrderSuccess(request, result)
	default:
		log.Printf("❓ [处理TradingResponse响应（200）] 任务 %s: 未知请求类型: %s", task.ID, request.Type)
	}
}

// handleFailedTradingResponse 处理失败的交易响应
func (task *HedgeTask) handleFailedTradingResponse(request *PendingRequest, data map[string]interface{}) {
	errorInfo, ok := data["error"].(map[string]interface{})
	if !ok {
		return
	}

	code, _ := errorInfo["code"].(float64)
	msg, _ := errorInfo["msg"].(string)

	log.Printf("任务 %s: 交易请求失败 - 类型: %s, 错误码: %.0f, 消息: %s",
		task.ID, request.Type, code, msg)

	switch int(code) {
	case -2013: // NO_SUCH_ORDER
		task.handleOrderNotExistsError(request)
	case -5022: // GTX订单拒绝
		task.handleGTXRejectedError(request)
	default:
		//task.handleGenericTradingError(request, int(code), msg)
	}
}

// processUserDataEvent 处理用户数据事件
func (task *HedgeTask) processUserDataEvent(data map[string]interface{}) {
	log.Printf("🔍 [用户数据] 任务 %s: 收到用户数据事件，开始处理", task.ID)
	log.Printf("🔍 [用户数据] 任务 %s: 完整事件数据: %+v", task.ID, data)

	eventType, ok := data["e"].(string)
	if !ok {
		log.Printf("❌ [用户数据] 任务 %s: 用户数据事件缺少事件类型字段", task.ID)
		return
	}

	log.Printf("📋 [用户数据] 任务 %s: 用户数据事件类型: %s", task.ID, eventType)

	switch eventType {
	case "ORDER_TRADE_UPDATE":
		log.Printf("🎯 [用户数据] 任务 %s: 开始处理 ORDER_TRADE_UPDATE 事件", task.ID)
		task.handleOrderTradeUpdate(data)
	case "ACCOUNT_UPDATE":
		log.Printf("💰 [用户数据] 任务 %s: 开始处理 ACCOUNT_UPDATE 事件", task.ID)
		task.handleAccountUpdate(data)
	default:
		log.Printf("❓ [用户数据] 任务 %s: 未知的用户数据事件类型: %s", task.ID, eventType)
		log.Printf("❓ [用户数据] 任务 %s: 未知事件完整数据: %+v", task.ID, data)
	}
}

// handleOrderPlaceResponse 处理下单响应（支持完整初始化）
func (task *HedgeTask) handleOrderPlaceResponse(request *PendingRequest, result map[string]interface{}) {
	log.Printf("✅ 任务 %s: 收到 trading_response 下单响应", task.ID)
	log.Printf("🔍 [handleOrderPlaceResponse] 客户端订单ID: %s", request.ClientOrderID)

	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("⚠️ [handleOrderPlaceResponse] SmartOrderManager或orderTracker为空")
		return
	}

	// 提取订单ID和价格信息
	orderIDStr, err := parseOrderID(result, "orderId")
	if err != nil {
		log.Printf("❌ 任务 %s: 从 trading_response 解析订单ID失败: %v", task.ID, err)
		// 设置为已提交状态，等待ORDER_TRADE_UPDATE
		task.SmartOrderManager.mutex.Lock()
		task.SmartOrderManager.orderTracker.State = OrderStateSubmitted
		task.SmartOrderManager.mutex.Unlock()
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		log.Printf("❌ 任务 %s: 转换订单ID失败: %v", task.ID, err)
		return
	}

	// 解析订单价格
	priceStr, ok := result["price"].(string)
	if !ok {
		log.Printf("❌ 任务 %s: 从 trading_response 解析订单价格失败", task.ID)
		return
	}

	price, err := parseDecimal(priceStr)
	if err != nil {
		log.Printf("❌ 任务 %s: 转换订单价格失败: %v", task.ID, err)
		return
	}

	// 尝试初始化订单跟踪（先到先得机制）
	if task.initializeOrderTracking(orderID, price, "trading_response") {
		log.Printf("🎯 任务 %s: 通过 trading_response 完成订单初始化", task.ID)
	}

	// 更新统计
	task.SmartOrderManager.stats.SuccessfulOrders++
}

// handleModifyOrderSuccess 处理修改订单成功响应（币安官方逻辑：仅确认修改请求成功）
func (task *HedgeTask) handleModifyOrderSuccess(request *PendingRequest, result map[string]interface{}) {
	log.Printf("✅ 任务 %s: 修改订单请求成功，等待 ORDER_TRADE_UPDATE AMENDMENT 确认", task.ID)

	if request != nil {
		log.Printf("🔍 任务 %s: 修改订单客户端ID: %s", task.ID, request.ClientOrderID)
	}

	// 注意：不在这里更新订单状态，等待 ORDER_TRADE_UPDATE AMENDMENT 事件
	// 这符合币安官方的 WebSocket API 设计逻辑
}

// handleQueryOrderSuccess 处理查询订单成功响应
func (task *HedgeTask) handleQueryOrderSuccess(request *PendingRequest, result map[string]interface{}) {
	log.Printf("🔍 [查询结果] 任务 %s: 处理查询订单成功响应", task.ID)
	log.Printf("🔍 [查询结果] 任务 %s: 查询结果完整数据: %+v", task.ID, result)

	status, ok := result["status"].(string)
	if !ok {
		log.Printf("❌ [查询结果] 任务 %s: 查询结果缺少status字段", task.ID)
		return
	}

	log.Printf("📋 [查询结果] 任务 %s: 查询到的订单状态: %s", task.ID, status)

	switch status {
	case "FILLED":
		log.Printf("✅ [查询结果] 任务 %s: 从ws返回交易信息中获得订单已成交，调用 handleOrderFilledFromQuery", task.ID)
		task.handleOrderFilledFromQuery(result)
	case "CANCELED":
		log.Printf("🚫 [查询结果] 任务 %s: 从ws返回交易信息中获得订单已撤销，调用 handleOrderCanceledFromQuery", task.ID)
		task.handleOrderCanceledFromQuery(result)
	case "NEW", "PARTIALLY_FILLED":
		log.Printf("🔄 [查询结果] 任务 %s: 从ws返回交易信息中获得订单仍活跃，调用 handleOrderActiveFromQuery", task.ID)
		task.handleOrderActiveFromQuery(result)
	default:
		log.Printf("❓ [查询结果] 任务 %s: 从ws返回交易信息中获得未知的订单状态: %s", task.ID, status)
	}
}

// handleOrderNotExistsError 处理订单不存在错误
func (task *HedgeTask) handleOrderNotExistsError(request *PendingRequest) {
	log.Printf("任务 %s: 订单不存在，可能已成交或撤销，主动查询状态", task.ID)

	// 主动查询订单状态
	if request.ClientOrderID != "" {
		task.SmartOrderManager.QueryOrderStatus(request.ClientOrderID)
	}
}

// handleGTXRejectedError 处理GTX订单拒绝错误
func (task *HedgeTask) handleGTXRejectedError(request *PendingRequest) {
	log.Printf("任务 %s: GTX订单被拒绝（会立即成交），重置下单流程", task.ID)

	// 重置下单流程标识
	if task.SmartOrderManager != nil {
		task.SmartOrderManager.ResetOrderProcess()
	}
}

// // handleGenericTradingError 处理通用交易错误
// func (task *HedgeTask) handleGenericTradingError(request *PendingRequest, code int, msg string) {
// 	// 检查是否需要重试
// 	if request.RetryCount < task.SmartOrderConfig.MaxRetries {
// 		log.Printf("任务 %s: 交易错误（暂未实现重试功能），准备重试 (%d/%d)",
// 			task.ID, request.RetryCount+1, task.SmartOrderConfig.MaxRetries)

// 		// 增加重试计数
// 		request.RetryCount++

// 		// 延迟重试
// 		go func() {
// 			time.Sleep(time.Duration(request.RetryCount) * time.Second)
// 			// 这里可以重新发送请求

// 		}()
// 	} else {
// 		log.Printf("任务 %s: 交易错误，重试次数已达上限", task.ID)

// 		// 重置下单流程标识
// 		if task.SmartOrderManager != nil {
// 			task.SmartOrderManager.ResetOrderProcess()
// 		}

// 		// 更新统计
// 		task.SmartOrderManager.stats.FailedOrders++
// 	}
// }

// handleOrderTradeUpdate 处理订单交易更新事件（简化版：初始化+成交检测）
func (task *HedgeTask) handleOrderTradeUpdate(data map[string]interface{}) {
	log.Printf("任务 %s: ===== 开始处理 ORDER_TRADE_UPDATE 事件 =====", task.ID)

	// 解析订单更新事件
	orderData, ok := data["o"].(map[string]interface{})
	if !ok {
		log.Printf("任务 %s: ORDER_TRADE_UPDATE 事件缺少订单数据字段 'o'", task.ID)
		return
	}

	clientOrderID, ok := orderData["c"].(string)
	if !ok {
		log.Printf("任务 %s: ORDER_TRADE_UPDATE 事件缺少客户端订单ID", task.ID)
		return
	}

	log.Printf("任务 %s: 收到 ORDER_TRADE_UPDATE 事件，客户端订单ID: %s", task.ID, clientOrderID)

	// 🔍 方案1：客户端订单ID绑定过滤
	// 检查订单是否属于当前任务（通过客户端订单ID前缀）
	if !strings.HasPrefix(clientOrderID, task.ID+"_") {
		log.Printf("🔍 [消息路由] 任务 %s: 忽略其他任务的订单事件，客户端订单ID: %s", task.ID, clientOrderID)
		return
	}

	log.Printf("✅ [消息路由] 任务 %s: 确认为本任务订单，继续处理", task.ID)

	// 检查 SmartOrderManager 状态
	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("任务 %s: SmartOrderManager或orderTracker为空，忽略事件", task.ID)
		log.Printf("🔍 [调试] 任务 %s: SmartOrderManager=%v, orderTracker=%v",
			task.ID, task.SmartOrderManager != nil,
			task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil)
		return
	}

	// 检查客户端订单ID匹配
	currentClientOrderID := task.SmartOrderManager.orderTracker.ClientOrderID
	log.Printf("🔍 [调试] 任务 %s: 客户端订单ID匹配检查", task.ID)
	log.Printf("🔍 [调试] 任务 %s:   - 收到的订单ID: %s", task.ID, clientOrderID)
	log.Printf("🔍 [调试] 任务 %s:   - 当前跟踪订单ID: %s", task.ID, currentClientOrderID)
	log.Printf("🔍 [调试] 任务 %s:   - 是否匹配: %v", task.ID, currentClientOrderID == clientOrderID)

	if currentClientOrderID != clientOrderID {
		log.Printf("⚠️ 任务 %s: 客户端订单ID不匹配，忽略事件", task.ID)
		return
	}

	// 获取订单状态
	orderStatus, ok := orderData["X"].(string)
	if !ok {
		log.Printf("任务 %s: ORDER_TRADE_UPDATE 事件缺少订单状态", task.ID)
		return
	}

	log.Printf("🔍 [调试] 任务 %s: 订单状态: %s", task.ID, orderStatus)
	log.Printf("🔍 [调试] 任务 %s: 订单初始化状态: %v", task.ID, task.orderInitialized.Load())

	// 尝试初始化（如果还未初始化）
	if !task.orderInitialized.Load() && orderStatus == "NEW" {
		// 提取订单ID和价格信息
		orderIDStr, err := parseOrderID(orderData, "i")
		if err != nil {
			log.Printf("❌ 任务 %s: 从 ORDER_TRADE_UPDATE 解析订单ID失败: %v", task.ID, err)
			return
		}

		orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
		if err != nil {
			log.Printf("❌ 任务 %s: 转换订单ID失败: %v", task.ID, err)
			return
		}

		priceStr, ok := orderData["p"].(string)
		if !ok {
			log.Printf("❌ 任务 %s: 从 ORDER_TRADE_UPDATE 解析订单价格失败", task.ID)
			return
		}

		price, err := parseDecimal(priceStr)
		if err != nil {
			log.Printf("❌ 任务 %s: 转换订单价格失败: %v", task.ID, err)
			return
		}

		// 尝试初始化订单跟踪（先到先得机制）
		if task.initializeOrderTracking(orderID, price, "ORDER_TRADE_UPDATE") {
			log.Printf("🎯 任务 %s: 通过 ORDER_TRADE_UPDATE 完成订单初始化", task.ID)
		}
		return
	}

	// 获取执行类型 (Execution Type)
	executionType, ok := orderData["x"].(string)
	if !ok {
		log.Printf("任务 %s: ORDER_TRADE_UPDATE 事件缺少执行类型", task.ID)
		return
	}

	// 如果已经初始化，只处理真正的成交事件
	isInitialized := task.orderInitialized.Load()
	isFilled := orderStatus == "FILLED" || orderStatus == "PARTIALLY_FILLED"
	isTrade := executionType == "TRADE"

	log.Printf("🔍 [调试] 任务 %s: 成交状态检查", task.ID)
	log.Printf("🔍 [调试] 任务 %s:   - 订单已初始化: %v", task.ID, isInitialized)
	log.Printf("🔍 [调试] 任务 %s:   - 订单状态(X): %s (FILLED/PARTIALLY_FILLED: %v)", task.ID, orderStatus, isFilled)
	log.Printf("🔍 [调试] 任务 %s:   - 执行类型(x): %s (TRADE: %v)", task.ID, executionType, isTrade)
	log.Printf("🔍 [调试] 任务 %s:   - 真正成交条件满足: %v", task.ID, isInitialized && isFilled && isTrade)

	log.Printf("🔍 [调试] 任务 %s: 准备进入if判断，条件值: %v", task.ID, isInitialized && isFilled && isTrade)

	if isInitialized && isFilled && isTrade {
		log.Printf("🎯 任务 %s: 检测到真正的订单成交，开始处理", task.ID)
		log.Printf("🔍 [调试] 任务 %s: 即将调用 handleOrderCompleted", task.ID)

		// 添加defer来捕获可能的panic
		defer func() {
			if r := recover(); r != nil {
				log.Printf("❌ 任务 %s: handleOrderCompleted 发生panic: %v", task.ID, r)
			}
		}()

		task.handleOrderCompleted(data)
		log.Printf("🔍 [调试] 任务 %s: handleOrderCompleted 调用完成", task.ID)
	} else {
		log.Printf("⚠️ 任务 %s: 成交事件被跳过 - 初始化: %v, 状态: %v, 执行类型: %v",
			task.ID, isInitialized, isFilled, isTrade)
	}

	log.Printf("任务 %s: ===== ORDER_TRADE_UPDATE 事件处理完成 =====", task.ID)
}

// handleAccountUpdate 处理账户更新事件
func (task *HedgeTask) handleAccountUpdate(data map[string]interface{}) {
	// 这里可以处理账户余额变化等信息
	log.Printf("任务 %s: 收到账户更新事件（暂未实现功能 可能会有余额变动等信息）", task.ID)
}

// handleOrderFilledFromQuery 处理查询到的已成交订单
func (task *HedgeTask) handleOrderFilledFromQuery(result map[string]interface{}) {
	log.Printf("🎯 [查询成交] 任务 %s: 查询确认订单已成交", task.ID)
	log.Printf("🔍 [查询成交] 任务 %s: 成交订单详情: %+v", task.ID, result)

	// 检查当前状态
	if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil {
		log.Printf("🔍 [查询成交] 任务 %s: 当前orderTracker状态: %v", task.ID, task.SmartOrderManager.orderTracker.State)
		log.Printf("🔍 [查询成交] 任务 %s: 当前orderTracker.ClientOrderID: %s", task.ID, task.SmartOrderManager.orderTracker.ClientOrderID)
	} else {
		log.Printf("⚠️ [查询成交] 任务 %s: SmartOrderManager或orderTracker为空", task.ID)
	}

	task.processOrderFilled(result)
}

// handleOrderCanceledFromQuery 处理查询到的已撤销订单
func (task *HedgeTask) handleOrderCanceledFromQuery(result map[string]interface{}) {
	log.Printf("任务 %s: 查询确认订单已撤销", task.ID)
	//task.processOrderCanceled(result)
	task.resetSmartOrderManager("订单查询已撤销")
}

// handleOrderActiveFromQuery 处理查询到的活跃订单
func (task *HedgeTask) handleOrderActiveFromQuery(result map[string]interface{}) {
	log.Printf("任务 %s: 查询确认订单仍活跃", task.ID)

	// 更新订单状态
	if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil {
		task.SmartOrderManager.mutex.Lock()
		task.SmartOrderManager.orderTracker.State = OrderStateActive
		task.SmartOrderManager.mutex.Unlock()
	}
}

// handleOrderFilled 处理订单完全成交事件（币安官方逻辑：完整的成交处理）
func (task *HedgeTask) handleOrderFilled(data map[string]interface{}) {
	log.Printf("🎯 任务 %s: ===== 处理 ORDER_TRADE_UPDATE FILLED 事件（订单完全成交）=====", task.ID)

	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("⚠️ 任务 %s: SmartOrderManager或orderTracker为空，无法处理FILLED事件", task.ID)
		return
	}

	// 先设置订单状态为已完成
	task.SmartOrderManager.mutex.Lock()
	task.SmartOrderManager.orderTracker.State = OrderStateCompleted
	task.SmartOrderManager.orderTracker.CompletedTime = time.Now()
	task.SmartOrderManager.mutex.Unlock()

	log.Printf("✅ 任务 %s: 订单状态设置为已完成", task.ID)

	// 检查是否需要提取嵌套的订单数据
	if orderData, ok := data["o"].(map[string]interface{}); ok {
		log.Printf("🔍 [调试] 任务 %s: 发现嵌套的订单数据 'o'，提取后传递给 processOrderFilled", task.ID)
		log.Printf("🔍 [调试] 任务 %s: 订单数据内容: %+v", task.ID, orderData)
		task.processOrderFilled(orderData)
	} else {
		log.Printf("🔍 [调试] 任务 %s: 未发现嵌套的订单数据，直接传递给 processOrderFilled", task.ID)
		task.processOrderFilled(data)
	}

	log.Printf("🎯 任务 %s: ===== ORDER_TRADE_UPDATE FILLED 事件处理完成 =====", task.ID)
}

// handleOrderPartiallyFilled 处理订单部分成交事件
func (task *HedgeTask) handleOrderPartiallyFilled(data map[string]interface{}) {
	log.Printf("任务 %s: 收到订单部分成交事件", task.ID)
	// 部分成交时继续等待完全成交
}

// handleOrderCanceled 处理订单撤销事件（币安官方逻辑：完整的撤销处理）
func (task *HedgeTask) handleOrderCanceled(data map[string]interface{}) {
	log.Printf("🚫 任务 %s: ===== 处理 ORDER_TRADE_UPDATE CANCELED 事件（订单撤销）=====", task.ID)

	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("⚠️ 任务 %s: SmartOrderManager或orderTracker为空，无法处理CANCELED事件", task.ID)
		return
	}

	// 设置订单状态为失败
	task.SmartOrderManager.mutex.Lock()
	task.SmartOrderManager.orderTracker.State = OrderStateFailed
	task.SmartOrderManager.orderTracker.CompletedTime = time.Now()
	task.SmartOrderManager.mutex.Unlock()

	log.Printf("✅ 任务 %s: 订单状态设置为失败（撤销）", task.ID)

	// 调用通用撤销处理逻辑
	task.resetSmartOrderManager("订单查询已撤销")

	log.Printf("🚫 任务 %s: ===== ORDER_TRADE_UPDATE CANCELED 事件处理完成 =====", task.ID)
}

// handleOrderNew 处理新订单确认事件（币安官方逻辑：在此设置订单状态并启动价格监控）
func (task *HedgeTask) handleOrderNew(data map[string]interface{}) {
	log.Printf("🎯 任务 %s: ===== 处理 ORDER_TRADE_UPDATE NEW 事件（关键业务逻辑）=====", task.ID)

	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("⚠️ 任务 %s: SmartOrderManager或orderTracker为空，无法处理NEW事件", task.ID)
		return
	}

	// 解析订单数据
	orderData, ok := data["o"].(map[string]interface{})
	if !ok {
		log.Printf("❌ 任务 %s: ORDER_TRADE_UPDATE NEW 事件缺少订单数据", task.ID)
		return
	}

	// 获取权威的订单ID（来自交易所确认）
	orderIDStr, err := parseOrderID(orderData, "i")
	if err != nil {
		log.Printf("❌ 任务 %s: 解析 ORDER_TRADE_UPDATE 中的订单ID失败: %v", task.ID, err)
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		log.Printf("❌ 任务 %s: 转换订单ID失败: %v", task.ID, err)
		return
	}

	// 获取订单价格
	var orderPrice decimal.Decimal
	if priceStr, ok := orderData["p"].(string); ok {
		if price, err := parseDecimal(priceStr); err == nil {
			orderPrice = price
		} else {
			log.Printf("❌ 任务 %s: 解析订单价格失败: %v", task.ID, err)
			return
		}
	} else {
		log.Printf("❌ 任务 %s: ORDER_TRADE_UPDATE NEW 事件缺少价格信息", task.ID)
		return
	}

	// 更新订单跟踪器（设置权威的订单信息）
	task.SmartOrderManager.mutex.Lock()

	log.Printf("🔄 任务 %s: 更新订单跟踪器为活跃状态", task.ID)
	log.Printf("🔍 任务 %s: 权威订单ID: %s (%d)", task.ID, orderIDStr, orderID)
	log.Printf("🔍 任务 %s: 权威订单价格: %s", task.ID, orderPrice.String())

	// 设置权威的订单信息
	task.SmartOrderManager.orderTracker.OrderID = orderID
	task.SmartOrderManager.orderTracker.Price = orderPrice
	task.SmartOrderManager.orderTracker.State = OrderStateActive
	task.SmartOrderManager.orderTracker.ConfirmTime = time.Now()

	task.SmartOrderManager.mutex.Unlock()

	log.Printf("✅ 任务 %s: 订单确认进入交易所，订单ID: %s, 价格: %s",
		task.ID, orderIDStr, orderPrice.String())

	log.Printf("🎯 任务 %s: ===== ORDER_TRADE_UPDATE NEW 事件处理完成 =====", task.ID)
}

// handleOrderModified 处理修改订单确认事件（币安官方逻辑：恢复价格监控）
func (task *HedgeTask) handleOrderModified(data map[string]interface{}) {
	log.Printf("🔄 任务 %s: ===== 处理 ORDER_TRADE_UPDATE AMENDMENT 事件（恢复价格监控）=====", task.ID)

	if task.SmartOrderManager == nil || task.SmartOrderManager.orderTracker == nil {
		log.Printf("⚠️ 任务 %s: SmartOrderManager或orderTracker为空，无法处理AMENDMENT事件", task.ID)
		return
	}

	// 解析订单数据
	orderData, ok := data["o"].(map[string]interface{})
	if !ok {
		log.Printf("❌ 任务 %s: ORDER_TRADE_UPDATE AMENDMENT 事件缺少订单数据", task.ID)
		return
	}

	// 获取更新后的订单价格
	var newOrderPrice decimal.Decimal
	if priceStr, ok := orderData["p"].(string); ok {
		if price, err := parseDecimal(priceStr); err == nil {
			newOrderPrice = price
		} else {
			log.Printf("❌ 任务 %s: 解析修改后订单价格失败: %v", task.ID, err)
			return
		}
	} else {
		log.Printf("❌ 任务 %s: ORDER_TRADE_UPDATE AMENDMENT 事件缺少价格信息", task.ID)
		return
	}

	// 更新订单跟踪器（恢复活跃状态）
	task.SmartOrderManager.mutex.Lock()

	log.Printf("🔄 任务 %s: 修改订单确认，恢复为活跃状态", task.ID)
	log.Printf("🔍 任务 %s: 更新后订单价格: %s", task.ID, newOrderPrice.String())

	// 更新订单价格并恢复活跃状态
	task.SmartOrderManager.orderTracker.Price = newOrderPrice
	task.SmartOrderManager.orderTracker.State = OrderStateActive
	task.SmartOrderManager.orderTracker.LastModifyTime = time.Now()

	task.SmartOrderManager.mutex.Unlock()

	log.Printf("✅ 任务 %s: 订单修改确认完成，价格: %s，状态恢复为活跃",
		task.ID, newOrderPrice.String())

	log.Printf("🔄 任务 %s: ===== ORDER_TRADE_UPDATE AMENDMENT 事件处理完成 =====", task.ID)
}

// processOrderFilled 处理订单成交的通用逻辑
func (task *HedgeTask) processOrderFilled(data map[string]interface{}) {
	log.Printf("🔍 [调试] 任务 %s: processOrderFilled 接收到的完整数据: %+v", task.ID, data)

	// 解析成交信息
	actualPriceStr, ok := data["ap"].(string)
	if !ok {
		actualPriceStr, _ = data["avgPrice"].(string)
	}

	log.Printf("🔍 [调试] 任务 %s: 尝试解析成交价格 - actualPriceStr: '%s', ok: %v", task.ID, actualPriceStr, ok)

	actualPrice, err := parseDecimal(actualPriceStr)
	if err != nil {
		log.Printf("任务 %s: 解析成交价格失败: %v, actualPriceStr: '%s'", task.ID, err, actualPriceStr)
		return
	}

	// 解析成交数量
	executedQtyStr, ok := data["z"].(string)
	if !ok {
		executedQtyStr, _ = data["executedQty"].(string)
	}

	_, err = parseDecimal(executedQtyStr)
	if err != nil {
		log.Printf("任务 %s: 解析成交数量失败: %v", task.ID, err)
		return
	}

	// 解析订单ID - 使用安全解析函数避免精度丢失
	log.Printf("🔍 [调试] 任务 %s: 尝试解析订单ID - 查找字段 'i'", task.ID)
	orderID, err := parseOrderID(data, "i")
	if err != nil {
		log.Printf("🔍 [调试] 任务 %s: 字段 'i' 解析失败: %v, 尝试备用字段 'orderId'", task.ID, err)
		// 尝试备用字段
		orderID, err = parseOrderID(data, "orderId")
		if err != nil {
			log.Printf("任务 %s: 解析订单ID失败: %v", task.ID, err)
			return
		}
	}
	log.Printf("🔍 [调试] 任务 %s: 成功解析订单ID: %s", task.ID, orderID)

	// 解析手续费
	commissionStr, ok := data["n"].(string)
	if !ok {
		commissionStr = "0"
	}
	commission, _ := parseDecimal(commissionStr)

	// 判断成交方式
	isMaker, ok := data["m"].(bool)
	var executionType string
	if ok && isMaker {
		executionType = ExecutionTypeMaker
	} else {
		executionType = ExecutionTypeTaker
	}

	// 计算理论价格
	var theoreticalPrice decimal.Decimal
	var eventType string
	var side string

	if task.SmartOrderManager != nil && task.SmartOrderManager.orderTracker != nil {
		side = task.SmartOrderManager.orderTracker.Side
	}

	if task.Position == nil {
		// 开仓
		eventType = EventTypeOpen
		if task.Direction == DirectionLong {
			theoreticalPrice = task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))
		} else {
			theoreticalPrice = task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))
		}
	} else {
		// 平仓
		eventType = EventTypeClose
		if task.Position.Side == SideLong {
			theoreticalPrice = task.TargetPrice.Sub(task.TargetPrice.Mul(task.StopRate))
		} else {
			theoreticalPrice = task.TargetPrice.Add(task.TargetPrice.Mul(task.StopRate))
		}
	}

	// 创建事件记录
	event := task.createEvent(eventType, theoreticalPrice, actualPrice, commission, orderID, OrderTypeLimit, executionType)
	task.Events = append(task.Events, event)

	// 更新仓位
	task.updatePosition(eventType, side, actualPrice, orderID)

	// 更新统计
	task.updateStatistics(event)

	// 重置智能订单状态
	// if task.SmartOrderManager != nil {
	// 	task.SmartOrderManager.mutex.Lock()
	// 	log.Printf("🧹 [状态清理] 任务 %s: 开始清空 orderTracker（订单成交完成）", task.ID)
	// 	log.Printf("🧹 [状态清理] 任务 %s: 清空前状态检查:", task.ID)
	// 	if task.SmartOrderManager.orderTracker != nil {
	// 		log.Printf("🧹 [状态清理] 任务 %s:   - orderTracker.ClientOrderID: %s", task.ID, task.SmartOrderManager.orderTracker.ClientOrderID)
	// 		log.Printf("🧹 [状态清理] 任务 %s:   - orderTracker.State: %v", task.ID, task.SmartOrderManager.orderTracker.State)
	// 		log.Printf("🧹 [状态清理] 任务 %s:   - orderTracker.OrderID: %d", task.ID, task.SmartOrderManager.orderTracker.OrderID)
	// 	} else {
	// 		log.Printf("🧹 [状态清理] 任务 %s:   - orderTracker: nil", task.ID)
	// 	}
	// 	log.Printf("🧹 [状态清理] 任务 %s:   - isInOrderProcess: %t", task.ID, task.SmartOrderManager.isInOrderProcess)

	// 	task.SmartOrderManager.orderTracker = nil
	// 	task.SmartOrderManager.isInOrderProcess = false
	// 	task.SmartOrderManager.mutex.Unlock()

	// 	log.Printf("🧹 [状态清理] 任务 %s: orderTracker 已清空", task.ID)
	// 	log.Printf("🧹 [状态清理] 任务 %s: isInOrderProcess 已设置为 false", task.ID)
	// 	log.Printf("🧹 [状态清理] 任务 %s: 状态清理完成，任务可以接受新的下单请求", task.ID)
	// }
	task.resetSmartOrderManager("processOrderFilled仓位更新")
	// 更新时间戳
	task.UpdatedAt = time.Now()

	// 触发数据保存
	if task.onDataChange != nil {
		task.onDataChange()
	}

	// 记录事件日志
	if globalEventLogger != nil {
		globalEventLogger.LogTaskEvent(task.ID, event)
	}

	log.Printf("🎉 任务 %s: 智能订单成交完成，实际价格: %s, 理论价格: %s, 订单ID: %s",
		task.ID, actualPrice.String(), theoreticalPrice.String(), orderID)
	log.Printf("🔍 [调试] 任务 %s: 订单成交详情 - 成交价格: %s, 成交数量: %s, 手续费: %s, 成交方式: %s",
		task.ID, actualPrice.String(), executedQtyStr, commission.String(), executionType)
}

// // cleanupSmartOrderManager 清理智能订单管理器（延迟执行）
// func (task *HedgeTask) cleanupSmartOrderManager() {
// 	log.Printf("🧹 [cleanupSmartOrderManager] 任务 %s: 开始清理智能订单管理器", task.ID)

// 	if task.SmartOrderManager != nil {
// 		task.SmartOrderManager.mutex.Lock()
// 		log.Printf("🧹 [cleanupSmartOrderManager] 任务 %s: 清空 orderTracker（延迟清理）", task.ID)
// 		if task.SmartOrderManager.orderTracker != nil {
// 			log.Printf("🧹 [cleanupSmartOrderManager] 任务 %s: 清空前 orderTracker.ClientOrderID: %s", task.ID, task.SmartOrderManager.orderTracker.ClientOrderID)
// 		}
// 		task.SmartOrderManager.orderTracker = nil
// 		task.SmartOrderManager.isInOrderProcess = false
// 		task.SmartOrderManager.mutex.Unlock()

// 		log.Printf("✅ [cleanupSmartOrderManager] 任务 %s: 智能订单管理器清理完成", task.ID)
// 	}
// }

// processOrderCanceled 处理订单撤销的通用逻辑
// func (task *HedgeTask) processOrderCanceled(data map[string]interface{}) {
// 	log.Printf("任务 %s: 订单已撤销，重置智能订单状态", task.ID)

// 	// 重置智能订单状态
// 	if task.SmartOrderManager != nil {
// 		task.SmartOrderManager.mutex.Lock()
// 		log.Printf("🧹 [processOrderCanceled] 任务 %s: 清空 orderTracker（订单撤销）", task.ID)
// 		if task.SmartOrderManager.orderTracker != nil {
// 			log.Printf("🧹 [processOrderCanceled] 任务 %s: 清空前 orderTracker.ClientOrderID: %s", task.ID, task.SmartOrderManager.orderTracker.ClientOrderID)
// 		}
// 		task.SmartOrderManager.orderTracker = nil
// 		task.SmartOrderManager.isInOrderProcess = false
// 		task.SmartOrderManager.mutex.Unlock()
// 		log.Printf("🧹 [processOrderCanceled] 任务 %s: orderTracker 已清空", task.ID)
// 	}
// }

// resetSmartOrderManager 重置智能订单管理器（用于所有情况，通用函数）
func (task *HedgeTask) resetSmartOrderManager(reason string) {
	log.Printf(" 任务 %s: 开始重置智能订单管理器 - 原因: %s", task.ID, reason)

	if task.SmartOrderManager != nil {
		task.SmartOrderManager.mutex.Lock()

		// 记录重置前的状态
		if task.SmartOrderManager.orderTracker != nil {
			log.Printf("任务 %s: 重置前状态 - ClientOrderID: %s, State: %v, OrderID: %d",
				task.ID,
				task.SmartOrderManager.orderTracker.ClientOrderID,
				task.SmartOrderManager.orderTracker.State,
				task.SmartOrderManager.orderTracker.OrderID)
		} else {
			log.Printf("任务 %s: 重置前状态 - orderTracker: nil", task.ID)
		}

		log.Printf("🧹任务 %s: 重置前状态 - isInOrderProcess: %t", task.ID, task.SmartOrderManager.isInOrderProcess)

		// 清空订单跟踪器和下单流程标识
		task.SmartOrderManager.orderTracker = nil
		task.SmartOrderManager.isInOrderProcess = false

		// 重置订单初始化状态
		task.orderInitialized.Store(false)

		task.SmartOrderManager.mutex.Unlock()

		log.Printf("✅ 任务 %s: 智能订单管理器重置完成，任务可以接受新的下单请求", task.ID)
	} else {
		log.Printf("⚠️ 任务 %s: SmartOrderManager为空，无需重置", task.ID)
	}
}
