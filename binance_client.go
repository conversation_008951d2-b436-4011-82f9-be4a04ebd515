package main

import (
	"context"
	"crypto/ed25519"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"
	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
)

// OrderedParam 有序参数结构
type OrderedParam struct {
	Key   string
	Value interface{}
}

// BinanceClient 币安客户端
type BinanceClient struct {
	client     *futures.Client
	apiKey     string
	secretKey  string             // 保留用于向后兼容
	privateKey ed25519.PrivateKey // Ed25519私钥
	wsConn     *websocket.Conn
	wsEndpoint string
}

// NewBinanceClient 创建新的币安客户端
func NewBinanceClient() *BinanceClient {
	// 从配置文件获取API密钥
	apiKey, secretKey := GetBinanceConfig()

	// 加载Ed25519私钥
	privateKey, err := loadEd25519PrivateKey()
	if err != nil {
		log.Fatalf("加载Ed25519私钥失败: %v", err)
	}

	// 创建币安期货客户端 - 注意：这里我们仍然创建客户端，但会重写签名方法
	client := binance.NewFuturesClient(apiKey, secretKey)

	log.Println("🔴 使用币安主网 - 请确保你了解风险！")
	log.Println("🔑 使用Ed25519密钥进行API签名")
	log.Printf("🔧 API密钥: %s...", apiKey[:8])

	bc := &BinanceClient{
		client:     client,
		apiKey:     apiKey,
		secretKey:  secretKey,  // 保留用于向后兼容
		privateKey: privateKey, // Ed25519私钥
	}

	// 重要：设置自定义的HTTP客户端来使用Ed25519签名
	bc.setupEd25519HTTPClient()

	return bc
}

// setupEd25519HTTPClient 设置使用Ed25519签名的HTTP客户端
func (bc *BinanceClient) setupEd25519HTTPClient() {
	// 创建自定义的HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建自定义的RoundTripper来拦截请求并添加Ed25519签名
	httpClient.Transport = &Ed25519Transport{
		Base:       http.DefaultTransport,
		apiKey:     bc.apiKey,
		privateKey: bc.privateKey,
	}

	// 设置到binance客户端
	bc.client.HTTPClient = httpClient
	log.Println("✅ Ed25519 HTTP客户端设置完成")
}

// Ed25519Transport 自定义HTTP传输，用于添加Ed25519签名
type Ed25519Transport struct {
	Base       http.RoundTripper
	apiKey     string
	privateKey ed25519.PrivateKey
}

// RoundTrip 实现http.RoundTripper接口，拦截请求并添加Ed25519签名
func (t *Ed25519Transport) RoundTrip(req *http.Request) (*http.Response, error) {
	// 只对币安API请求进行签名
	if strings.Contains(req.URL.Host, "binance.com") {
		err := t.signRequest(req)
		if err != nil {
			log.Printf("❌ Ed25519签名请求失败: %v", err)
			return nil, err
		}
	}

	// 执行原始请求
	if t.Base == nil {
		t.Base = http.DefaultTransport
	}
	return t.Base.RoundTrip(req)
}

// signRequest 使用Ed25519对请求进行签名
func (t *Ed25519Transport) signRequest(req *http.Request) error {
	// 添加API密钥头
	req.Header.Set("X-MBX-APIKEY", t.apiKey)

	// 只对需要签名的请求进行签名（POST, PUT, DELETE等）
	if req.Method == "GET" && !strings.Contains(req.URL.RawQuery, "timestamp") {
		// 不需要签名的GET请求
		return nil
	}

	// 获取查询参数
	query := req.URL.RawQuery

	// 如果是POST请求，需要读取body
	var bodyParams string
	if req.Method == "POST" || req.Method == "PUT" || req.Method == "DELETE" {
		if req.Body != nil {
			bodyBytes, err := io.ReadAll(req.Body)
			if err != nil {
				return fmt.Errorf("读取请求体失败: %v", err)
			}
			bodyParams = string(bodyBytes)
			// 重新设置body
			req.Body = io.NopCloser(strings.NewReader(bodyParams))
		}
	}

	// 构建签名字符串，排除已存在的signature参数
	var signString string
	if bodyParams != "" && query != "" {
		signString = query + "&" + bodyParams
	} else if bodyParams != "" {
		signString = bodyParams
	} else {
		signString = query
	}

	// 移除已存在的signature参数（如果有的话）
	signString = removeSignatureParam(signString)

	// 添加时间戳（如果还没有）
	if !strings.Contains(signString, "timestamp=") {
		timestamp := time.Now().UnixMilli()
		if signString != "" {
			signString += "&"
		}
		signString += fmt.Sprintf("timestamp=%d", timestamp)
	}

	// 生成Ed25519签名
	signature := ed25519.Sign(t.privateKey, []byte(signString))
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	// 添加签名到查询参数
	if req.Method == "GET" {
		// GET请求：添加到URL查询参数
		if req.URL.RawQuery != "" {
			req.URL.RawQuery += "&signature=" + url.QueryEscape(signatureBase64)
		} else {
			req.URL.RawQuery = "signature=" + url.QueryEscape(signatureBase64)
		}
	} else {
		// POST/PUT/DELETE请求：添加到body
		if bodyParams != "" {
			bodyParams += "&signature=" + url.QueryEscape(signatureBase64)
		} else {
			bodyParams = "signature=" + url.QueryEscape(signatureBase64)
		}
		req.Body = io.NopCloser(strings.NewReader(bodyParams))
		req.ContentLength = int64(len(bodyParams))
	}

	log.Printf("🔍 Ed25519签名请求: %s %s", req.Method, req.URL.String())
	log.Printf("🔍 签名字符串: %s", signString)
	log.Printf("🔍 Ed25519签名: %s", signatureBase64)

	return nil
}

// removeSignatureParam 从查询字符串中移除signature参数
func removeSignatureParam(queryString string) string {
	if queryString == "" {
		return queryString
	}

	// 分割参数
	params := strings.Split(queryString, "&")
	var filteredParams []string

	for _, param := range params {
		// 跳过signature参数
		if !strings.HasPrefix(param, "signature=") {
			filteredParams = append(filteredParams, param)
		}
	}

	return strings.Join(filteredParams, "&")
}

// GetOrderBook 获取订单簿数据
func (bc *BinanceClient) GetOrderBook(symbol string) (*OrderBookData, error) {
	depth, err := bc.client.NewDepthService().Symbol(symbol).Limit(5).Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取订单簿失败: %v", err)
	}

	if len(depth.Bids) == 0 || len(depth.Asks) == 0 {
		return nil, fmt.Errorf("订单簿数据为空")
	}

	bidPrice, _ := decimal.NewFromString(depth.Bids[0].Price)
	bidQty, _ := decimal.NewFromString(depth.Bids[0].Quantity)
	askPrice, _ := decimal.NewFromString(depth.Asks[0].Price)
	askQty, _ := decimal.NewFromString(depth.Asks[0].Quantity)

	return &OrderBookData{
		Symbol:    symbol,
		BidPrice:  bidPrice,
		BidQty:    bidQty,
		AskPrice:  askPrice,
		AskQty:    askQty,
		Timestamp: time.Now(),
	}, nil
}

// PlaceLimitOrder 下限价单
func (bc *BinanceClient) PlaceLimitOrder(symbol string, side futures.SideType, quantity, price decimal.Decimal, postOnly bool) (*futures.CreateOrderResponse, error) {
	// 根据postOnly参数选择TimeInForce
	var timeInForce futures.TimeInForceType
	if postOnly {
		timeInForce = futures.TimeInForceTypeGTX // GTX: Good Till Crossing (Post Only)
	} else {
		timeInForce = futures.TimeInForceTypeGTC // Good Till Cancel
	}

	order, err := bc.client.NewCreateOrderService().
		Symbol(symbol).
		Side(side).
		Type(futures.OrderTypeLimit).
		TimeInForce(timeInForce).
		Quantity(quantity.String()).
		Price(price.String()).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("下限价单失败: %v", err)
	}

	timeInForceDesc := "GTC"
	if postOnly {
		timeInForceDesc = "POST_ONLY"
	}
	log.Printf("✅ 限价单已下达: %s %s %s @ %s, 订单ID: %d, 模式: %s",
		symbol, side, quantity.String(), price.String(), order.OrderID, timeInForceDesc)

	return order, nil
}

// PlaceMarketOrder 下市价单
func (bc *BinanceClient) PlaceMarketOrder(symbol string, side futures.SideType, quantity decimal.Decimal) (*futures.CreateOrderResponse, error) {
	order, err := bc.client.NewCreateOrderService().
		Symbol(symbol).
		Side(side).
		Type(futures.OrderTypeMarket).
		Quantity(quantity.String()).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("下市价单失败: %v", err)
	}

	log.Printf("✅ 市价单已下达: %s %s %s, 订单ID: %d",
		symbol, side, quantity.String(), order.OrderID)

	return order, nil
}

// CancelOrder 撤销订单
func (bc *BinanceClient) CancelOrder(symbol string, orderID int64) error {
	_, err := bc.client.NewCancelOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return fmt.Errorf("撤销订单失败: %v", err)
	}

	log.Printf("✅ 订单已撤销: %s, 订单ID: %d", symbol, orderID)
	return nil
}

// GetOrderStatus 获取订单状态
func (bc *BinanceClient) GetOrderStatus(symbol string, orderID int64) (*futures.Order, error) {
	order, err := bc.client.NewGetOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取订单状态失败: %v", err)
	}

	return order, nil
}

// GetPosition 获取仓位信息
func (bc *BinanceClient) GetPosition(symbol string) (*futures.PositionRisk, error) {
	positions, err := bc.client.NewGetPositionRiskService().
		Symbol(symbol).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取仓位失败: %v", err)
	}

	if len(positions) == 0 {
		return nil, fmt.Errorf("未找到仓位信息")
	}

	return positions[0], nil
}

// GetAccountInfo 获取账户信息
func (bc *BinanceClient) GetAccountInfo() (*futures.Account, error) {
	account, err := bc.client.NewGetAccountService().Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取账户信息失败: %v", err)
	}

	return account, nil
}

// GetUserTrades 获取用户成交历史
func (bc *BinanceClient) GetUserTrades(symbol string, orderID int64) ([]*futures.AccountTrade, error) {
	trades, err := bc.client.NewListAccountTradeService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取成交历史失败: %v", err)
	}

	return trades, nil
}

// SubscribeOrderBook 订阅实时订单簿数据
func (bc *BinanceClient) SubscribeOrderBook(symbol string, orderBookChan chan<- *OrderBookData, stopChan <-chan struct{}) {
	wsEndpoint := "wss://fstream.binance.com/ws/"

	// 币安WebSocket要求交易对名称为小写
	lowerSymbol := strings.ToLower(symbol)
	streamName := fmt.Sprintf("%s@depth5@100ms", lowerSymbol)
	wsURL := wsEndpoint + streamName

	for {
		select {
		case <-stopChan:
			if bc.wsConn != nil {
				bc.wsConn.Close()
			}
			return
		default:
			err := bc.connectWebSocket(wsURL, orderBookChan, stopChan)
			if err != nil {
				log.Printf("❌ WebSocket连接失败: %v, 5秒后重试...", err)
				time.Sleep(5 * time.Second)
			}
		}
	}
}

// connectWebSocket 连接WebSocket
func (bc *BinanceClient) connectWebSocket(wsURL string, orderBookChan chan<- *OrderBookData, stopChan <-chan struct{}) error {
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		return err
	}
	defer conn.Close()

	bc.wsConn = conn

	channelFullCount := 0 // 记录通道满的次数

	for {
		select {
		case <-stopChan:
			return nil
		default:
			var msg map[string]interface{}
			err := conn.ReadJSON(&msg)
			if err != nil {
				return fmt.Errorf("读取WebSocket数据失败: %v", err)
			}

			// 解析订单簿数据
			orderBook := bc.parseOrderBookMessage(msg)
			if orderBook != nil {
				select {
				case orderBookChan <- orderBook:
					// 成功发送，重置计数器
					channelFullCount = 0
				default:
					// 通道满了，增加计数器
					channelFullCount++
					// 每100次才输出一次警告，避免日志过多
					if channelFullCount%100 == 1 {
						log.Printf("⚠️ 订单簿通道满，已跳过 %d 次数据", channelFullCount)
					}
				}
			}
		}
	}
}

// parseOrderBookMessage 解析订单簿消息
func (bc *BinanceClient) parseOrderBookMessage(msg map[string]interface{}) *OrderBookData {
	bids, ok := msg["b"].([]interface{})
	if !ok || len(bids) == 0 {
		return nil
	}

	asks, ok := msg["a"].([]interface{})
	if !ok || len(asks) == 0 {
		return nil
	}

	// 解析买一价和买一量
	bidData := bids[0].([]interface{})
	bidPrice, _ := decimal.NewFromString(bidData[0].(string))
	bidQty, _ := decimal.NewFromString(bidData[1].(string))

	// 解析卖一价和卖一量
	askData := asks[0].([]interface{})
	askPrice, _ := decimal.NewFromString(askData[0].(string))
	askQty, _ := decimal.NewFromString(askData[1].(string))

	symbol, _ := msg["s"].(string)

	return &OrderBookData{
		Symbol:    symbol,
		BidPrice:  bidPrice,
		BidQty:    bidQty,
		AskPrice:  askPrice,
		AskQty:    askQty,
		Timestamp: time.Now(),
	}
}

// ValidateAPIConnection 验证API连接
func (bc *BinanceClient) ValidateAPIConnection() error {
	log.Println("🔍 验证Ed25519 API连接...")

	// 使用Ed25519签名验证API连接
	err := bc.validateEd25519Connection()
	if err != nil {
		log.Printf("❌ Ed25519 API验证失败: %v", err)
		log.Println("💡 请检查:")
		log.Println("   1. embedded_config.go中的API密钥是否正确")
		log.Println("   2. 是否使用了Ed25519类型的API密钥")
		log.Println("   3. API密钥是否有期货交易权限")
		log.Println("   4. IP地址是否在白名单中")
		return err
	}

	log.Printf("✅ Ed25519 API连接验证成功!")
	return nil
}

// validateEd25519Connection 使用Ed25519签名验证连接
func (bc *BinanceClient) validateEd25519Connection() error {
	log.Printf("🔍 开始签名对比测试...")

	// 测试用例1: 简单timestamp签名
	log.Printf("📋 测试用例1: 简单timestamp签名")
	testString1 := "timestamp=1749225000000"
	signature1 := ed25519.Sign(bc.privateKey, []byte(testString1))
	signature1Base64 := base64.StdEncoding.EncodeToString(signature1)
	expectedSignature1 := "hll6NnfqwKTQlIUZJCBw8hBVMU1r8f/jPISmA1e0uYxHZMc/S+N+tb1h4gR9JSIjScSfp9hSH3AYcaAX6CD+Dw=="

	log.Printf("签名字符串: %s", testString1)
	log.Printf("Go签名:     %s", signature1Base64)
	log.Printf("Python签名: %s", expectedSignature1)

	if signature1Base64 == expectedSignature1 {
		log.Printf("✅ 测试用例1: 签名一致!")
	} else {
		log.Printf("❌ 测试用例1: 签名不一致!")
		return fmt.Errorf("测试用例1签名不一致")
	}

	// 测试用例2: 复杂下单参数签名
	log.Printf("📋 测试用例2: 复杂下单参数签名")
	testString2 := "symbol=ETHUSDT&side=BUY&type=LIMIT&quantity=0.01&price=2500.00&timeInForce=GTC&newClientOrderId=test_12345&timestamp=1749225000000&test=true"
	signature2 := ed25519.Sign(bc.privateKey, []byte(testString2))
	signature2Base64 := base64.StdEncoding.EncodeToString(signature2)
	expectedSignature2 := "csKpkZGMfv72LJNUwQkWCBeCgzE5clVUcLm6m5vjbcEFmzFBs3MUF3WhN0Fa4CxBXd6mPBcZ0qsTqSH9+BE0AA=="

	log.Printf("签名字符串: %s", testString2)
	log.Printf("Go签名:     %s", signature2Base64)
	log.Printf("Python签名: %s", expectedSignature2)

	if signature2Base64 == expectedSignature2 {
		log.Printf("✅ 测试用例2: 签名一致!")
	} else {
		log.Printf("❌ 测试用例2: 签名不一致!")
		return fmt.Errorf("测试用例2签名不一致")
	}

	// 构建实际的测试下单请求参数
	timestamp := time.Now().UnixMilli()

	// 使用有序参数保持参数顺序（重要！）
	orderedParams := []OrderedParam{
		{"symbol", "ETHUSDT"},
		{"side", "BUY"},
		{"type", "LIMIT"},
		{"quantity", "0.01"},
		// {"priceMatch", "QUEUE"},
		{"price", "2400.00"},
		{"timeInForce", "GTX"},
		{"newClientOrderId", fmt.Sprintf("go_ed25519_test_%d", timestamp)},
		{"timestamp", timestamp},
		{"test", "true"}, // 测试模式
	}

	// 转换为map用于签名
	params := make(map[string]interface{})
	for _, param := range orderedParams {
		params[param.Key] = param.Value
	}

	// 生成Ed25519签名
	signatureStr, err := bc.GenerateRestSignatureOrdered(orderedParams)
	if err != nil {
		return fmt.Errorf("生成签名失败: %v", err)
	}

	// 手动构建请求体，保持参数顺序（重要！）
	var requestParts []string
	for _, param := range orderedParams {
		requestParts = append(requestParts, fmt.Sprintf("%s=%s",
			url.QueryEscape(param.Key),
			url.QueryEscape(fmt.Sprintf("%v", param.Value))))
	}
	// 添加签名（必须在最后）
	requestParts = append(requestParts, fmt.Sprintf("signature=%s", url.QueryEscape(signatureStr)))

	requestBody := strings.Join(requestParts, "&")
	baseURL := "https://fapi.binance.com/fapi/v1/order"

	log.Printf("🔍 请求URL: %s", baseURL)
	log.Printf("🔍 请求体: %s", requestBody)

	// 创建HTTP请求（POST方法，参数在body中，与Python一致）
	req, err := http.NewRequest("POST", baseURL, strings.NewReader(requestBody))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头（与Python程序完全一致）
	req.Header.Set("X-MBX-APIKEY", bc.apiKey)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != 200 {
		var errorResp map[string]interface{}
		json.Unmarshal(body, &errorResp)
		return fmt.Errorf("API请求失败 (状态码: %d): %s", resp.StatusCode, string(body))
	}

	// 解析账户信息
	var accountInfo map[string]interface{}
	err = json.Unmarshal(body, &accountInfo)
	if err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	// 显示账户信息
	if totalBalance, ok := accountInfo["totalWalletBalance"].(string); ok {
		log.Printf("账户总余额: %s USDT", totalBalance)
	}
	if availableBalance, ok := accountInfo["availableBalance"].(string); ok {
		log.Printf("可用余额: %s USDT", availableBalance)
	}

	return nil
}

// GetSymbolInfo 获取交易对信息
func (bc *BinanceClient) GetSymbolInfo(symbol string) (*futures.Symbol, error) {
	exchangeInfo, err := bc.client.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取交易所信息失败: %v", err)
	}

	for _, s := range exchangeInfo.Symbols {
		if s.Symbol == symbol {
			return &s, nil
		}
	}

	return nil, fmt.Errorf("未找到交易对: %s", symbol)
}

// FormatQuantity 根据交易对精度格式化数量
func (bc *BinanceClient) FormatQuantity(symbol string, quantity decimal.Decimal) (decimal.Decimal, error) {
	symbolInfo, err := bc.GetSymbolInfo(symbol)
	if err != nil {
		return decimal.Zero, err
	}

	// 找到LOT_SIZE过滤器
	for _, filter := range symbolInfo.Filters {
		if filter["filterType"] == "LOT_SIZE" {
			stepSize, _ := decimal.NewFromString(filter["stepSize"].(string))

			// 根据stepSize调整数量精度
			if stepSize.GreaterThan(decimal.Zero) {
				return quantity.Div(stepSize).Floor().Mul(stepSize), nil
			}
		}
	}

	return quantity, nil
}

// FormatPrice 根据交易对精度格式化价格
func (bc *BinanceClient) FormatPrice(symbol string, price decimal.Decimal) (decimal.Decimal, error) {
	symbolInfo, err := bc.GetSymbolInfo(symbol)
	if err != nil {
		return decimal.Zero, err
	}

	// 找到PRICE_FILTER过滤器
	for _, filter := range symbolInfo.Filters {
		if filter["filterType"] == "PRICE_FILTER" {
			tickSize, _ := decimal.NewFromString(filter["tickSize"].(string))

			// 根据tickSize调整价格精度
			if tickSize.GreaterThan(decimal.Zero) {
				return price.Div(tickSize).Floor().Mul(tickSize), nil
			}
		}
	}

	return price, nil
}

// CreateListenKey 创建用户数据流listenKey
func (bc *BinanceClient) CreateListenKey() (string, error) {
	listenKey, err := bc.client.NewStartUserStreamService().Do(context.Background())
	if err != nil {
		return "", fmt.Errorf("创建listenKey失败: %v", err)
	}

	log.Printf("✅ listenKey创建成功: %s", listenKey)
	return listenKey, nil
}

// RenewListenKey 续期用户数据流listenKey
func (bc *BinanceClient) RenewListenKey(listenKey string) error {
	err := bc.client.NewKeepaliveUserStreamService().ListenKey(listenKey).Do(context.Background())
	if err != nil {
		return fmt.Errorf("续期listenKey失败: %v", err)
	}

	log.Printf("✅ listenKey续期成功: %s", listenKey)
	return nil
}

// DeleteListenKey 删除用户数据流listenKey
func (bc *BinanceClient) DeleteListenKey(listenKey string) error {
	err := bc.client.NewCloseUserStreamService().ListenKey(listenKey).Do(context.Background())
	if err != nil {
		return fmt.Errorf("删除listenKey失败: %v", err)
	}

	log.Printf("✅ listenKey删除成功: %s", listenKey)
	return nil
}

// loadEd25519PrivateKey 从内嵌配置加载Ed25519私钥
func loadEd25519PrivateKey() (ed25519.PrivateKey, error) {
	// 从内嵌配置获取私钥hex字符串
	embeddedConfig := GetEmbeddedConfig()
	privateKeyHex := embeddedConfig.Binance.Ed25519PrivateKey
	if privateKeyHex == "" {
		return nil, fmt.Errorf("内嵌配置中的Ed25519私钥未设置")
	}

	// 解码hex字符串
	privateKeyBytes, err := hex.DecodeString(privateKeyHex)
	if err != nil {
		return nil, fmt.Errorf("解码Ed25519私钥失败: %v", err)
	}

	// 验证私钥长度
	if len(privateKeyBytes) != ed25519.SeedSize {
		return nil, fmt.Errorf("Ed25519私钥长度错误: 期望%d字节，实际%d字节",
			ed25519.SeedSize, len(privateKeyBytes))
	}

	// 从seed创建私钥
	privateKey := ed25519.NewKeyFromSeed(privateKeyBytes)

	log.Printf("✅ Ed25519私钥加载成功")
	return privateKey, nil
}

// GenerateRestSignature 生成REST API的Ed25519签名（不排序参数）
func (bc *BinanceClient) GenerateRestSignature(params map[string]interface{}) (string, error) {
	// 构建查询字符串（保持原始顺序）
	var queryParts []string
	for key, value := range params {
		if key != "signature" {
			queryParts = append(queryParts, fmt.Sprintf("%s=%v", key, value))
		}
	}
	queryString := strings.Join(queryParts, "&")

	// 生成Ed25519签名
	signature := ed25519.Sign(bc.privateKey, []byte(queryString))
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	return signatureBase64, nil
}

// GenerateWebSocketSignature 生成WebSocket API的Ed25519签名（排序参数）
func (bc *BinanceClient) GenerateWebSocketSignature(params map[string]interface{}) (string, error) {
	// 排除signature参数
	filteredParams := make(map[string]interface{})
	for k, v := range params {
		if k != "signature" {
			filteredParams[k] = v
		}
	}

	// 按字母顺序排序参数
	var keys []string
	for k := range filteredParams {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建查询字符串
	var queryParts []string
	for _, key := range keys {
		queryParts = append(queryParts, fmt.Sprintf("%s=%v", key, filteredParams[key]))
	}
	queryString := strings.Join(queryParts, "&")

	// 生成Ed25519签名
	signature := ed25519.Sign(bc.privateKey, []byte(queryString))
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	return signatureBase64, nil
}

// GenerateRestSignatureOrdered 生成REST API的Ed25519签名（保持参数顺序）
func (bc *BinanceClient) GenerateRestSignatureOrdered(orderedParams []OrderedParam) (string, error) {
	// 构建查询字符串（保持原始顺序）
	var queryParts []string
	for _, param := range orderedParams {
		if param.Key != "signature" {
			queryParts = append(queryParts, fmt.Sprintf("%s=%v", param.Key, param.Value))
		}
	}
	queryString := strings.Join(queryParts, "&")

	// 调试信息
	log.Printf("🔍 签名字符串: %s", queryString)

	// 生成Ed25519签名
	signature := ed25519.Sign(bc.privateKey, []byte(queryString))
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	log.Printf("🔍 Ed25519签名: %s", signatureBase64)

	return signatureBase64, nil
}

// GenerateWebSocketSignatureOrdered 生成WebSocket API的Ed25519签名（保持参数顺序）
func (bc *BinanceClient) GenerateWebSocketSignatureOrdered(orderedParams []OrderedParam) (string, error) {
	// 构建查询字符串（保持原始顺序）
	var queryParts []string
	for _, param := range orderedParams {
		if param.Key != "signature" {
			queryParts = append(queryParts, fmt.Sprintf("%s=%v", param.Key, param.Value))
		}
	}
	queryString := strings.Join(queryParts, "&")

	// 调试信息
	log.Printf("🔍 WebSocket签名字符串: %s", queryString)

	// 生成Ed25519签名
	signature := ed25519.Sign(bc.privateKey, []byte(queryString))
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	log.Printf("🔍 WebSocket Ed25519签名: %s", signatureBase64)

	return signatureBase64, nil
}
